// require('babel-core/register')();
// require('babel-polyfill');

var express = require('express');
var path = require('path');
// var config = require('../server/config');

var app = express();
require('../server/express')(app);
// app.use('/react_ssr',express.static(path.join(__dirname, '../assets')));
app.set('views', path.join(__dirname, '../assets'))
app.use(express.static(path.join(__dirname, '../assets')));
app.use(express.static(path.join(__dirname, '../public')));
app.use('/hospital/assets',express.static(path.join(__dirname, '../assets')));

require('../server/server')(app);
require('../server/error')(app);
require('../bin/www')(app);
