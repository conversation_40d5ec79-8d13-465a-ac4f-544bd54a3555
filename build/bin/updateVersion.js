/* 升级版本号 */
const fs = require('fs');

async function fsReadFile(filePath) {
  return await new Promise((resolve) => {
    fs.readFile(filePath, 'utf-8', (err, res) => {
      if (err) {
        console.log(`---------读取 ${filePath} 失败！---------原因：`);
        console.log(err);
        console.log(`---------读取 ${filePath} 失败！---------以上`);
        return;
      }

      resolve(res);
    });
  });
}

async function fsWriteFile(filePath, newDataStr) {
  return await new Promise((resolve) => {
    fs.writeFile(filePath, newDataStr, (err) => {
      if (err) {
        console.log(`---------写入 ${filePath} 失败！---------原因：`);
        console.log(err);
        console.log(`---------写入 ${filePath} 失败！---------以上`);
        return;
      }

      resolve();
    });
  });
}

async function fsReadAndWriteFile(filePath, replaceRule, replaceStr) {
  const dataStr = await fsReadFile(filePath);
  // 这里简单化处理了
  await fsWriteFile(filePath, dataStr.replace(replaceRule, replaceStr));
}

async function start() {
  const packageStr = await fsReadFile('./package.json');
  const { version } = JSON.parse(packageStr);

  console.log(`---------version: ${version}---------`);

  await fsReadAndWriteFile('src/utils/initMonitor.ts', /H5Version: '([\d.]+)'/, `H5Version: '${version}'`);
  await fsReadAndWriteFile('build/webpack.config.prd.js', /version: '([\d.]+)'/, `version: '${version}'`);
}

start();
