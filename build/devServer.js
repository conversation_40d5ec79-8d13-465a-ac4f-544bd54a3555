// require('babel-core/register')();
// require('babel-polyfill');

var express = require('express');
var logger = require('morgan');
var webpack = require('webpack');
var webpackMiddleware = require('webpack-dev-middleware');
var webpackHotMiddleware = require('webpack-hot-middleware');
var config = require('./webpack.config.dev');
var path = require('path');
var app = express();
var compiler = webpack(config);

require('../server/express')(app);

app.use(webpackMiddleware(compiler, { 
	publicPath: config.output.publicPath,
	stats: {
		colors: true,
		chunks: false
	}
}));
app.use(logger('dev'));//morgan
app.use(webpackHotMiddleware(compiler));
require('../server/server')(app);
require('../server/error')(app);
require('../bin/www')(app);
