const path = require('path');
const SpriteLoaderPlugin = require('svg-sprite-loader/plugin'); // webpack5需要对应的升级
const webpack = require('webpack');
const CSS_BROWSERS = require('./postcss-browsers');

const babelConfig = {
  presets: [
    ['env', {
      modules: false,
      targets: {
        browsers: CSS_BROWSERS,
      },
    }],
    'react',
    'stage-3',
    'es2015',
    'stage-0',
  ],
  plugins: [
    'transform-runtime',
    'add-module-exports',
    'transform-async-to-generator',
    'transform-decorators-legacy',
    'transform-async-to-generator',
    'syntax-dynamic-import',
    ['import', [
      { libraryName: 'zarm', libraryDirectory: 'es', style: true },
      { libraryName: 'zarm-v3', libraryDirectory: 'lib', style: true },
      { libraryName: 'zarm-v3' },
      {
        libraryName: 'src/components/common',
        camel2DashComponentName: false,
        customName: (name, file) => {
          if (name === 'StaticLoading'){
            return 'src/components/common/loading';
          }

          if (name === 'StaticToast'){
            return 'src/components/common/toast';
          }

          if (name === 'SvgIcon'){
            return 'src/components/common/svg';
          }

          if (name === 'Empty'){
            return 'src/components/common/emptyCover';
          }

          if (name === 'BrandSlogan'){
            return 'src/components/common/brand';
          }

          if (name === 'UserBlack'){
            return 'src/components/common/black';
          }

          if (name === 'CompatibleFileInput'){
            return 'src/components/common/fileInput';
          }

          if (name === 'SelectCity'){
            return 'src/components/common/SelectCity';
          }

          if (name === 'PDF'){
            return 'src/components/common/pdf';
          }

          if (name === 'FixedButton'){
            return 'src/components/common/button';
          }

          if (name === 'SelectButton'){
            return 'src/components/common/button/SelectButton';
          }

          if (name === 'FixedButtonFooter'){
            return 'src/components/common/fixedButtonFooter/FixedButtonFooter';
          }

          if (name === 'GJModal'){
            return 'src/components/common/gjModal';
          }

          if (name === 'ServPackItem'){
            return 'src/components/common/drugItem/servPackItem';
          }

          if (name === 'UmpEvaluateComp'){
            return 'src/components/common/umpEvaluate';
          }

          if (name === 'O2OExpress'){
            return 'src/components/common/o2oExpress';
          }

          if (name === 'LinkToPDFPreview'){
            return 'src/components/common/LinkToPDFPreview';
          }

          return `src/components/common/${name.charAt(0).toLowerCase()}${name.slice(1)}`;
        },
        libraryDirectory: '',
        style: false,
      },
    ]],
  ],
};
const svgRegular = /(sprite-(.*?)\.svg)$/; // svg处理规则正则
const config = {
  entry: {
    index: './src/root.tsx',
    pdfViewer: './src/pages/pdfViewer/index.tsx',
  },
  mode: 'development',
  devtool: false,
  optimization: {
    minimize: false,
    runtimeChunk: false,
    splitChunks: {
      cacheGroups:{
        vendor:{
          test: /[\\/]node_modules[\\/]/,
          name: 'vendor',
          chunks: 'initial',
        },
      },
    },
  },
  output: {
    path: path.resolve(__dirname, '../assets'),
  },
  resolve: {
    extensions: [' ', '.ts', '.tsx', '.js', '.jsx', '.scss', '.css'],
    alias: {
      'src': path.resolve(__dirname, '../src'),
      '@svg-icon': path.resolve(__dirname, '../src/components/common/svg/index.ts'),
      '@utils': path.resolve(__dirname, '../src/utils'),
      'react-cup-ui': path.resolve(__dirname, '../react-cup-ui/lib/react-cup-ui.umd.js'),
    },
    modules: ['node_modules'],
    // webpack 5之前会自动对nodejs模块做polyfill，但是在webpack5中需要手动声明, 由于NIM的九州通不使用了，暂时先false加快打包速度
    fallback:{
      crypto: false,
      buffer: false,
      stream: false,
    },
  },
  module: {
    rules: [
      {
        test: /\.(js|jsx)$/,
        // exclude: /node_modules| NIM_Web_SDK_vx.x.x.js/,
        exclude: [/node_modules/,path.resolve(__dirname, '../src/utils/nim/NIM_Web_SDK_v8.7.0.js')],
        use: [
          {
            loader: 'babel-loader',
            options: babelConfig,
          },
        ],
      },
      {
        test: /\.(ts|tsx)$/,
        exclude: /node_modules/,
        use: [
          {
            loader: 'babel-loader',
            options: babelConfig,
          },
          {
            loader: 'awesome-typescript-loader',
          },
        ],
      },
      {
        test: /\.svg(\?.*)?$/,
        exclude: [
          svgRegular,
          /node_modules/,
        ],
        use: [
          {
            loader: 'file-loader',
            options: {
              publicPath: '/hospital/assets',
              name: 'svgs/[name].[hash:8].[ext]',
            },
          },
          'svg-transform-loader',
          'svgo-loader',
        ],
      },
      {
        test: /\.(woff|woff2|ttf|eot)$/,
        type: 'asset/resource',
        exclude: /node_modules/,
        generator: {
          filename: 'font/[name].[hash:8].[ext]',
        },
      },
      { test: /\.html$/i, loader: 'html-loader' },
      {
        test: /\.pdf$/,
        type: 'asset/resource',
        exclude: /node_modules/,
        generator: {
          filename: 'pdf/[name].[hash:8].[ext]',
        },
      },
    ],
  },
  plugins: [
    new SpriteLoaderPlugin({
      plainSprite: true,
      spriteAttrs: {
        id: 'svg-icon',
        className: 'svg-sprite-icon',
      },
    }),
  ],
};

module.exports = config;
