const path = require('path');
const webpack = require('webpack');
const CSS_BROWSERS = require('./postcss-browsers');
const config = require('./webpack.config.base');

config.devtool = 'eval-source-map'; // cheap-module-eval-source-map
config.mode = 'development';
config.entry.index = [
  'webpack-hot-middleware/client', // ?reload=false
  './src/root.tsx',
];

const svgRegular = /(sprite-(.*?)\.svg)$/; // svg处理规则正则

config.output.publicPath = '/hospital/assets/';
config.output.filename = 'js/[name].js';
config.output.chunkFilename = 'js/[name].js';

config.module.rules[0].use[0].options.presets.push('react-hmre');

// dev开发环境 css/scss 以style 插入样式
config.module.rules.push(
  {
    test: /\.(scss|css)$/,
    use: [
      'style-loader',
      { loader: 'css-loader', options: { plugins: [require('autoprefixer')({ browsers: CSS_BROWSERS })], sourceMap: true } },
      { loader: 'postcss-loader', options: { plugins: [require('autoprefixer')({ browsers: CSS_BROWSERS })], sourceMap: true } },
      { loader: 'sass-loader', options: { sourceMap: true, data: '$prefixCls: "zarm-v3";' } },
    ],
  },
);

config.module.rules.push({
  test: /\.(png|jpg|jpeg|gif|webp)$/,
  exclude: /node_modules/,
  // asset默认8k一下asset/inline,否则是asset/resource
  // type: 'asset',
  type: 'javascript/auto',
  use: [
    {
      loader: 'url-loader',
      options: {
        // 设置转换为 Base64 编码的最大文件大小限制（以字节为单位）
        limit: 8192, // 8KB
        publicPath: '/hospital/assets',
        name: 'images/[name].[hash:8].[ext]', // 指定输出文件的命名规则
      },
    },
  ],
});

config.module.rules.push({
  test: /\.svg$/,
  exclude: /node_modules/,
  include: [
    svgRegular,
  ],
  use: [{
    loader: 'svg-sprite-loader',
    options: {
      extract: true,
      esModule: false,
      spriteFilename: 'svgs/sprite.svg',
      plugins: [
        {
          removeStyleElement: false,
          removeDesc: false,
        },
      ],
    },
  }, 'svg-transform-loader', {
    loader: 'svgo-loader',
    options: {
      plugins: [
        {
          removeStyleElement: false,
          removeDesc: false,
        },
      ],
    },
  }],
});

config.plugins.push(new webpack.HotModuleReplacementPlugin());

module.exports = config;
