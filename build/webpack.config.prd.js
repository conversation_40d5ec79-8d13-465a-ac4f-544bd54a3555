/*
 * File: /build/webpack.config.prd.js
 * Project: za-internet-hospital-h5
 * Description: 文件描述~
 * Created Date: Tuesday, 2024-05-14 19:22:12
 * Author: yuchao1 (<EMAIL>>)
 * -----
 * Last Modified: Wednesday, 2025-06-04 14:29:41
 * Modified By: yuchao1 (<EMAIL>>)
 * -----
 * Copyright (c) 2024 Ji<PERSON>
 *
 * History:
 */
// prd 线上环境

const CopyPlugin = require('copy-webpack-plugin');
const HtmlWebPackPlugin = require('html-webpack-plugin');
const MiniCssExtractPlugin = require('mini-css-extract-plugin');
const path = require('path');
const CSS_BROWSERS = require('./postcss-browsers');
const config = require('./webpack.config.base');
const { SeraphWebpackPlugin } = require('@za/sourcemap-webpack-plugin');
// var TerserPlugin = require('terser-webpack-plugin');

// const OptimizeCssAssetsPlugin = require('optimize-css-assets-webpack-plugin');
// var PreloadWebpackPlugin = require('preload-webpack-plugin');
// const BundleAnalyzerPlugin = require('webpack-bundle-analyzer').BundleAnalyzerPlugin
// const SentryPlugin = require('@sentry/webpack-plugin');
config.mode = 'production';
config.optimization.minimize = true;
// config.devtool = 'hidden-source-map';
// config.devtool = false;
config.devtool = 'source-map';

// v5自带
// config.optimization.minimizer = [
//   new TerserPlugin({
//     cache: true,
//     parallel: true, //也可以指定 Number ,即最多并行运行数量
//     sourceMap: true,
//     terserOptions: {
//       output: {
//         comments: false,
//       },
//       compress: {
//         drop_console: false,
//       }
//     },
//   }),
// ];

// config.plugins.push(new SentryPlugin({
//   include: '.',
//   ignore: ['node_modules'],
//   configFile: '.sentryclirc',
//   urlPrefix: '~/static',
//   sourceMapReference: false,
// }));
const svgRegular = /(sprite-(.*?)\.svg)$/; // svg处理规则正则

config.output.publicPath = '<%= publicPath %>/';
config.output.filename = 'js/[name].[chunkhash:8].js';
config.output.chunkFilename = 'js/[name].[chunkhash:8].js';

config.module.rules.push({
  test: /\.(scss|css)$/,
  use: [
    {
      loader: MiniCssExtractPlugin.loader,
      options: {
        publicPath: (resourcePath, context) => '../',
      },
    },
    'css-loader?importLoaders=1',
    {
      loader: 'postcss-loader',
      options: {
        plugins: [
          require('autoprefixer')({
            browsers: CSS_BROWSERS,
          }),
        ],
        minimize: true,
      },
    },
    {
      loader: 'sass-loader',
      options: {
        data: '$prefixCls: "zarm-v3";',
      },
    },
  ],
});

config.module.rules.push({
  test: /\.(png|jpg|jpeg|gif|webp)$/,
  exclude: /node_modules/,
  // asset默认8k一下asset/inline,否则是asset/resource
  // type: 'asset',
  type: 'javascript/auto',
  use: [
    {
      loader: 'url-loader',
      options: {
        // 设置转换为 Base64 编码的最大文件大小限制（以字节为单位）
        limit: 2048, // 8KB
        name: 'images/[name].[hash:8].[ext]', // 指定输出文件的命名规则
      },
    },
  ],
});

config.module.rules.push({
  test: /\.svg$/,
  exclude: /node_modules/,
  include: [
    svgRegular,
  ],
  use: [{
    loader: 'svg-sprite-loader',
    options: {
      extract: true,
      esModule: false,
      spriteFilename: 'svgs/sprite-[hash:6].svg',
      publicPath: '/hospital/assets/',
      plugins: [
        {
          removeStyleElement: false,
          removeDesc: false,
        },
      ],
    },
  }, 'svg-transform-loader', {
    loader: 'svgo-loader',
    options: {
      plugins: [
        {
          removeStyleElement: false,
          removeDesc: false,
        },
      ],
    },
  }],
});

config.plugins.push(
  new SeraphWebpackPlugin({ // sourcemap 插件配置
    env: 'prd',
    companyId: '695001',
    appId: 'e7b0ffffffcf240f',
    version: '1.0.23',
    enable: process.env.NODE_ENV !== 'development',
  }),
);

config.plugins.push(
  new MiniCssExtractPlugin({
    filename: 'css/[name].[contenthash:8].css',
    chunkFilename: 'css/[name].[chunkhash:8].css',
  }),
);

// config.plugins.push(new OptimizeCssAssetsPlugin({}));

// config.plugins.push(new BundleAnalyzerPlugin({ analyzerPort: 8919 }));

config.plugins.push(
  new HtmlWebPackPlugin({
    filename: path.resolve(__dirname, '../assets/index.html'), // 输出
    minify: {
      // 压缩HTML文件
      removeComments: true, // 移除HTML中的注释
      collapseWhitespace: true, // 删除空白符与换行符
    },
    template: path.resolve(__dirname, '../src/views/index.html'), // 输入
    chunks: ['index'],
    // hash: true, // 开启hash 版本
    // template: "./src/views/index.html",//输入
    // inlineSource:  '.(js|css)',// 插入到html的css、js文件都要内联
    // inject: false //是否能注入内容到 输出 的页面去
  }),
);

config.plugins.push(
  new HtmlWebPackPlugin({
    filename: path.resolve(__dirname, '../assets/pdfViewer.html'), // 输出
    minify: {
      // 压缩HTML文件
      removeComments: true, // 移除HTML中的注释
      collapseWhitespace: true, // 删除空白符与换行符
    },
    template: path.resolve(__dirname, '../src/views/pdfViewer.html'), // 输入
    chunks: ['pdfViewer'],
  }),
);

config.plugins.push(
  new CopyPlugin({
    patterns: [
      {
        from: path.resolve(__dirname, '../views/hgsFnq86Cj.txt'), // 暖哇小程序
        to: path.resolve(__dirname, '../assets/hgsFnq86Cj.txt'),
      },
      {
        from: path.resolve(__dirname, '../views/hetzV90NZ2.txt'), // 互医小程序
        to: path.resolve(__dirname, '../assets/hetzV90NZ2.txt'),
      },
      {
        from: path.resolve(__dirname, '../views/PmgmBO8ojX.txt'), // 方伞小程序
        to: path.resolve(__dirname, '../assets/PmgmBO8ojX.txt'),
      },
      {
        from: path.resolve(__dirname, '../views/7Uw0s3xZ2X.txt'), // 健康险抖音小程序
        to: path.resolve(__dirname, '../assets/7Uw0s3xZ2X.txt'),
      },
      {
        from: path.resolve(__dirname, '../views/cncrouPjxT.txt'), // 众安保险小程序
        to: path.resolve(__dirname, '../assets/cncrouPjxT.txt'),
      },
      {
        from: path.resolve(__dirname, '../views/8IPH8QGpXE.txt'), // 瑞华小程序
        to: path.resolve(__dirname, '../assets/8IPH8QGpXE.txt'),
      },
      {
        from: path.resolve(__dirname, '../views/LXXklWIAyh.txt'), // 赠险微信小程序
        to: path.resolve(__dirname, '../assets/LXXklWIAyh.txt'),
      },
      {
        from: path.resolve(__dirname, '../views/OTQyH8SNqp.txt'), // 赠险ETC小程序
        to: path.resolve(__dirname, '../assets/OTQyH8SNqp.txt'),
      },
      {
        from: path.resolve(__dirname, '../views/xbeRvWlEMk.txt'), // 赠险小程序
        to: path.resolve(__dirname, '../assets/xbeRvWlEMk.txt'),
      },
      {
        from: path.resolve(__dirname, '../views/ba730b1f215f845f1f8642d34c82cb64.html'), // 赠险支付宝小程序
        to: path.resolve(__dirname, '../assets/ba730b1f215f845f1f8642d34c82cb64.html'),
      },
      {
        from: path.resolve(__dirname, '../views/d281a77744482ba915d4d846d4e9aae0.html'), // 赠险支付宝小程序
        to: path.resolve(__dirname, '../assets/d281a77744482ba915d4d846d4e9aae0.html'),
      },
      {
        from: path.resolve(__dirname, '../views/eIKyZVT97v.txt'),
        to: path.resolve(__dirname, '../assets/eIKyZVT97v.txt'),
      },
      {
        from: path.resolve(__dirname, '../views/0tElPhSUCt.txt'),
        to: path.resolve(__dirname, '../assets/0tElPhSUCt.txt'),
      },
      {
        from: path.resolve(__dirname, '../views/txQ2fSTrCe.txt'),
        to: path.resolve(__dirname, '../assets/txQ2fSTrCe.txt'),
      },
      {
        from: path.resolve(__dirname, '../views/RV72zMCNpi.txt'),
        to: path.resolve(__dirname, '../assets/RV72zMCNpi.txt'),
      },
      {
        from: path.resolve(__dirname, '../views/6KbbOFMX0F.txt'),
        to: path.resolve(__dirname, '../assets/6KbbOFMX0F.txt'),
      },
      {
        from: path.resolve(__dirname, '../views/dKt0BZkGMv.txt'), // 众安健康商城微信小程序
        to: path.resolve(__dirname, '../assets/dKt0BZkGMv.txt'),
      },
      {
        from: path.resolve(__dirname, '../views/weChatSetTitle.html'), // 一个空 html 用于微信设置标题
        to: path.resolve(__dirname, '../assets/weChatSetTitle.html'),
      },
    ],
  }),
);
module.exports = config;
