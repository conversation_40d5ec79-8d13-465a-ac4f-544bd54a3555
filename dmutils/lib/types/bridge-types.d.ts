import {
	SureType,
	SupportApplyPayType,
	PayChannelType,
	PayChannelExtendType,
	BridgerPayResultType,
	NavigationMethodType,
	ShareMinProgramType,
	ResetNavigationBarType,
	ShareViewType,
	MinProgramEnvType,
	MapIdType,
	RegisterEventListenerType,
	PageCycleServiceType,
	ShareType,
} from '../enum';
export interface IBridgeResult<T> {
	/**返回状态 */
    status: SureType;
	/**返回值 */
    data: T;
}

export interface IBridgeGetZATokenParams {
	/**事业部唯一标识，APP 登录唯一标识 */
    appKey: string;
}
export interface IBridgeGetZATokenResult {
    token: string;
}

export interface IBridgeGetSystemInfoParams {
	/**事业部唯一标识 */
    BUID?: '';
	/**H5接口版本号 */
    H5BridgeVersion?: '';
	/**活动渠道号 */
    bizOrigin?: '';
	/**品牌 */
    brand?: '';
}
export interface IBridgeGetSystemInfoResult {
	/**APP版本 */
    appVersion: string;
	/**APP分发渠道号 */
    channelId: string;
	/**平台 */
    platform: string;
	/**设备号 */
    deviceId: string;
	/**活动渠道号 */
    bizOrigin?: string;
}

export interface IBridgeSetNavigationBarTitleParams {
	/**标题 */
    title: string;
}

export interface IBridgeSetNavigationBarColorParams {
	/**字体颜色 */
    fontColor: string;
	/**背景颜色 */
    backgroundColor: string;
	/**返回按钮颜色 */
    returnButtonColor: string;
}

export interface IBridgeResetNavigationBarColorParams {
    resetArray: ResetNavigationBarType[];
}

export interface IBridgeSetNavigationBarRightButtonParams {
	/**按钮 支持：图片地址（42 * 42）、颜色 */
    buttonIcon: string;
	/**按钮名称为图片时可空 */
    buttonName?: string;
	/**按钮点击事件 app原生地址、http地址、js方法名 */
    buttonListener: string;
	/**是否需要登录 */
    needLogin?: SureType;
	/** 扩展 */
    [key: string]: any;
}
export interface IBridgeSetNavigationBarRightMenuItem {
	/**菜单项名称 */
    menuName: string;
	/**菜单点击事件 */
    menuClickListener: string;
	/**是否需要登录 */
    needLogin?: SureType;
	/** 扩展 */
    [key: string]: any;
}
export interface IBridgeSetNavigationBarRightMenuParams {
	/**右上角图标地址 https地址 */
    menuIcon: string;
	/**选项列表 */
    menuList: IBridgeSetNavigationBarRightMenuItem[];
}

export interface IBridgeSetWebviewBackParams {
	/**返回按钮触发的js方法 传空值清空设置 */
    listener: string;
}

export interface IBridgeShowShareViewParams {
	/**面板显示分享的渠道，1为显示全部 2： 微信聊天界面   3： 微信朋友圈    4： QQ聊天界面   5：QQ空间，默认为1，若要显示多个用逗号隔开，比如@"2,3,4,5,6" */
    shareType: ShareViewType;
	/** 1.url链接 2.小程序链接 3.图片 ，传递的参数为 "1" 或者 "2" 或者 "3" （数据类型应该不存在多个的情况），分享面板仅在url类型时，也就是"1"，才会显示分享给家人。 */
    dataType?: '1' | '2' | '3';
	/** 如果分享的是url类型，可以指定分享的url内容，这个ur仅针对家人分享，其他平台（微信、朋友圈、qq、qq空间）维持之前逻辑。shareUrl为空则不显示家人分享。 */
    shareUrl?: string;
	/**分享面板显示的标题 */
    dialogTitle: string;
	/**分享面板显示的描述 */
    dialogDesc: string;
	/**分享面板左边 icon 支持3.1.0及以上版本 */
    dialogIconUrl?: string;
}
export interface IBridgeShowShareViewResult {
	/**0： 微信好友按钮   1： 微信朋友圈按钮    2： QQ聊天按钮   3：QQ空间按钮 */
    shareType: ShareType;
}

export interface IBridgeShareParams {
	/**分享出去的标题 */
    title?: string;
	/**分享出去的描述 */
    desc?: string;
	/**分享渠道： 0： 微信聊天界面   1： 微信朋友圈    2： QQ聊天界面   3：QQ空间 */
    shareType?: ShareType;
	/**分享出去的网页地址 */
    url?: string;
	/**分享出去的图片地址（确保图片质量小于32K） */
    imageUrl?: string;
	/**是否只分享imageUrl单张图片 */
    isSharePicture?: SureType;
	/**微信分享回调 */
    success?: Function;
	/**微信分享回调 */
    cancel?: Function;
	/**分享渠道：4 */
    shareMinProgramType?: ShareMinProgramType;
	/**小程序原始id */
    miniProgramId?: string;
	/**小程序页面的路径 */
    miniProgramPath?: string;
	/**分享小程序的版本（“0”：正式版，“1”：开发版，“2”：体验版）注：该字段3.0.3版本后有效 */
    miniProgramType?: MinProgramEnvType;
}

export interface IBridgeGetUserInfoResult {
	/**手机号 */
    phoneNo: string;
	/**是否登陆 */
    isLogin: boolean;
	/**是否登陆 */
    userName: string;
	/**姓名 */
    nickName: string;
	/**昵称 */
    gender: string;
	/**证件号 */
    certificateNo: string;
	/**证件类型 */
    certificateType: string;
}

export interface IBridgeGetLocationInfoResult {
	/**经度 */
    longitude: string;
	/**纬度 */
    latitude: string;
	/**详细地址 */
    address: string;
	/**城市 */
    city: string;
	/**国家 */
    country: string;
	/**省/直辖市 */
    province: string;
	/**区 */
    district: string;
	/**城市编码 */
    citycode: string;
	/**区域编码 */
    adcode: string;
	/**街道名称 */
    street: string;
	/**门牌号  */
    number: string;
}

export interface IBridgeMapAppIsInstalledParams {
	/**查询的地图App列表 */
    appid: MapIdType[];
}
export interface IBridgeMapAppIsInstalledResult {
	/**是否安装地图 0: 已安装 1: 未安装  */
    returnCode: SureType[];
}

export interface IBridgeOpenNavigationParams {
	/**Android:（0高德，1百度） iOS:（0高德，1百度，2苹果地图） */
    mapId: MapIdType;
	/**目标纬度 */
    dlat: string;
	/**目标经度 */
    dlon: string;
	/**目标名称 */
    dname: string;
	/**导航方式：0驾驶 1公交 2步行 3骑行 */
    type: NavigationMethodType;
}

export interface IBridgeAddCalendarServiceParams {
	/**日历事件标题 */
    title: string;
	/**日历事件备注 */
    notes: string;
	/**事件开始时间: yyyy-MM-dd HH:mm:ss */
    location: string;
	/**事件结束时间，格式如上 yyyy-MM-dd HH:mm:ss */
    startDate: string;
	/**事件提醒时间，格式如上 yyyy-MM-dd HH:mm:ss */
    endDate: string;
	/**事件提醒时间 yyyy-MM-dd HH:mm:ss */
    alarmDate: string;
}
export interface IBridgeAddCalendarServiceResult {
	/**是否安装地图 “0”失败 “1”成功 */
    returnCode: SureType;
	/**结果描述 */
    resultMessage: string;
}

export interface IBridgeCheckPermissionResult {
	/**有无权限 "0"无权限 “1”有权限 */
    result: SureType;
}

export interface IBridgeSupportApplePayTypeResult {
	/**是否支持 */
    isSupport: SupportApplyPayType;
}
export interface IBridgeSupportAndroidPayTypeResult {
	/**是否支持 */
    isSupport: boolean;
}
export interface IBridgePayToZAServiceParams {
	/**SDK内部会向该地址发送请求，该地址需要返回 charge 的 JSON 字符串 */
    url: string;
	/**1:支付宝 2:微信 支付类型 */
    payChannel: PayChannelType;
}

export interface IBridgeZAPayParams {
	/**支付sdk需要的参数  */
    payParams: string;
    payChannel: PayChannelType | PayChannelExtendType;
}
export interface IBridgeZAPayResult {
    payStatus: BridgerPayResultType;
    message: string;
}

export interface IBridgeNavigationToZAServiceParams {
	/**跳转前是否需要登录 1:需要登录；0:不需要登录（default）*/
    needLogin?: string;
	/**跳转url，zaapp://xxx 跳转原生页面；http/https://xxx 跳转网页*/
    url: string;
}

export interface IBridgeOpenWeChatWebviewResult {
	/** 微信返回的url */
    callbackUrl: string;
	/** 微信是否安装 */
    isWXInstalled: boolean;
}
export interface IBridgeRegisterEventListenerResult {
	/** 事件描述 */
    eventKey: RegisterEventListenerType;
	/** 事件结果 */
    eventData: string;
}

export interface IBridgePageCycleServiceResult {
    pageStatus: PageCycleServiceType;
}
export interface IBridgeQRCodeScanResult {
    stringValue: string;
}
