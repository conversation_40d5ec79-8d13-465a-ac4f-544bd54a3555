import * as BridgeType from './bridge-types';
// import axiosTypes from 'axios/index'
type BridgeResult<T> = BridgeType.IBridgeResult<T>;

export interface IDmUtil {
	/**cookie操作 */
    cookie: ICookie;
	/**url工具 */
    url: IUrl;
	/**h5环境判断 */
    env: IEnv;
	/**众安app h5 api */
    bridge: IBridge;
	/**h5存储 */
    storage: IStorage;
	/**工具 */
    tools: ITools;
}

/** Cookie操作 */
export interface ICookie {
	/**
	 * 设置cookie
	 * @param key 存储key
	 * @param value 存储数据
	 */
    set(key: string, value: string): void;
	/**
	 * 设置cookie
	 * @param key 存储key
	 * @param value 存储数据
	 * @param expired 过期时间(秒)
	 */
    set(key: string, value: string, expired: number): void;
	/**
	 * 设置cookie
	 * @param params object
	 */
    set(params: {
		/**
		 * cookie名称
		 */
        key: string;
		/**
		 * cookie值
		 */
        value: string;
		/**
		 * cookie 域名
		 */
        domain?: string;
		/**
		 * cookie 过期时间(秒)
		 */
        expired?: number;
    }): void;

    get(key: string): string;
	/**
	 * 删除cookie
	 * @param key cookieKey
	 * @param domain domain
	 */
    del(key: string, domain?: string): void;
}
/** URL操作 */
export interface IUrl {
	/**
	 * url转object
	 * @param url 需解析的地址
	 */
    parse(url?: string): { [key: string]: string };
	/**
	 * 解析url字符串
	 * @param url 需解析的地址
	 *
	 * 返回格式: { hostpath: string; params:{[key: string]: string}; hash: string; }
	 *
	 * 例如：{hostpath: 'https://m.zhongan.com/gp/123', params: {templateType=C}, hash: 'QUIT' }
	 */
    parseAll(url?: string): { hostpath: string; params: { [key: string]: string }; hash: string; };
	/**
	 * object转url
	 * @param data 需要转换的object
	 * @param prefix 转换的前置url
	 */
    format(data: { [key: string]: string }, prefix: string): string;
	/**
	 * 获取url指定的值
	 * @param 获取值的名称
	 */
    get(key: string): string;
	/**
	 * 合并两个url
	 * @param u1 需要合并得url1(为'' | undefined | null 默认是当前地址)
	 * @param u2 需要合并得url2
	 * @param options 需要合并额外参数
	 * @param searchExclude 排除合并之后参数不携带的参数列表
	 * @param isEnforceExclude 是否强制排除参数（即：对u2的参数也不进行合并，默认是只排除u1的参数）
	 *
	 */
    merge(u1: string, u2: string, options?: { [key: string]: string }, searchExclude?: string[], isEnforceExclude?: boolean): string;
}
/** 运行环境 */
export interface IEnv {
	/**
	 * 是否微信
	 */
    isWechat: (userAgent?: string) => boolean;
	/**
	 * 是否百度
	 */
    isBaiduApp: (userAgent?: string) => boolean;
	/**
	 * 是否众安app
	 */
    isApp: (userAgent?: string) => boolean;
	/**
	 * 是否ios系统
	 */
    isIos: (userAgent?: string) => boolean;
	/**
	 * 是否Android系统
	 */
    isAndroid: (userAgent?: string) => boolean;
	/**
	 * 是否小程序（支持微信7.0.0以上 否则默认false）
	 */
    isWechatMinProgram: (userAgent?: string) => boolean;
	/**
	 * 是否阿里小程序
	 */
    isAlipayClient: (userAgent?: string) => boolean;
	/**
	 * 是否头条app
	 */
    isToutiao: (userAgent?: string) => boolean;
	/**
	 * 是否抖音app
	 */
    isDouyin: (userAgent?: string) => boolean;
	/**
	 * 是否西瓜视频app
	 */
    isXigua: (userAgent?: string) => boolean;
	/**
	 * 是否快手视频app
	 */
    isKuaishou: (userAgent?: string) => boolean;
	/**
	 * 是否快手极速版小程序
	 */
    isKuaishouSpeedMinProgram: (userAgent?: string) => boolean;
	/**
	 * 是否快手小程序
	 */
    isKuaishouMinProgram: (userAgent?: string) => boolean;
	/**
	 * 是否火山小视频app
	 */
    isHuoshan: (userAgent?: string) => boolean;
	/**
	 * 是否头条系app
	 */
    isToutiaoSeries: (userAgent?: string) => boolean;
	/**
	 * 是否头条系app
	 */
    isToutiaoSeriesApp: (userAgent?: string) => boolean;
	/**
	 * 是否头条系小程序
	 */
    isToutiaoSeriesMinProgram: (userAgent?: string) => boolean;
	/**
	 * 是否阿里系的app，包含支付宝、淘宝、天猫
	 */
    isAliApp: (userAgent?: string) => boolean;
	/**
	 * 是否在第三方app内，包含阿里系、头条系、快手、微信、众安 app
	 */
    isInApp: (userAgent?: string) => boolean;
	/**
	 * 是否在小程序内，包含微信小程序、头条小程序
	 */
    isInMinProgram: (useAgent?: string) => boolean;
}
/** 直营App接口 */
export interface IBridge {
	/**
	 * app登录
	 * @param BUID 事业部ID
	 */
    login(BUID: string): Promise<BridgeResult<null>>;

	/**获取Token */
    getZAToken(
		params?: BridgeType.IBridgeGetZATokenParams,
	): Promise<BridgeResult<BridgeType.IBridgeGetZATokenResult>>;

	/**获取系统信息 */
    getSystemInfo(
		params?: BridgeType.IBridgeGetSystemInfoParams,
	): Promise<BridgeResult<BridgeType.IBridgeGetSystemInfoResult>>;

	/**设定app webview标题 */
    setNavigationBarTitle(title: string): Promise<BridgeResult<null>>;

	/**设置标题栏主题颜色 */
    setNavigationBarColor(
		params: BridgeType.IBridgeSetNavigationBarColorParams,
	): Promise<BridgeResult<null>>;

	/**重置导航设置 */
    resetNavigationBarColor(
		params: BridgeType.IBridgeResetNavigationBarColorParams,
	): void;

	/**设置导航栏右侧按钮 */
    setNavigationBarRightButton(
		params: BridgeType.IBridgeSetNavigationBarRightButtonParams,
		cb: (res: BridgeType.IBridgeSetNavigationBarRightButtonParams) => void,
	): void;

	/**设置导航栏菜单 */
    setNavigationBarRightMenu(
		params: BridgeType.IBridgeSetNavigationBarRightMenuParams,
		cb: (res: BridgeType.IBridgeSetNavigationBarRightMenuItem) => void,
	): void;

	/**设置webview返回键click方法 > 3.0.4 */
    setWebviewBackListener(
		params: BridgeType.IBridgeSetWebviewBackParams,
		cb: (res: BridgeType.IBridgeSetWebviewBackParams) => void,
	): void;

	/**调用分享面板 */
    showShareView(
		params: BridgeType.IBridgeShowShareViewParams,
	): Promise<BridgeResult<BridgeType.IBridgeShowShareViewResult>>;

	/**APP普通分享 */
    share(params: BridgeType.IBridgeShareParams): void;

	/**隐藏导航栏返回键 */
    hiddenNavBackButton(): Promise<BridgeResult<null>>;

	/**打开app原生页面或H5页面*/
    navigateToZAService(
		params: BridgeType.IBridgeNavigationToZAServiceParams,
	): Promise<BridgeResult<null>>;

	/**关闭WebView */
    closeWebView(): void;

	/**获取APP用户信息 */
    getZAUserInfo(): Promise<BridgeResult<BridgeType.IBridgeGetUserInfoResult>>;

	/**获取地理位置接口 */
    getLocationInfo(): Promise<
		BridgeResult<BridgeType.IBridgeGetLocationInfoResult>
	>;

	/**查询地图APP是否安装 */
    queryMapAppIsInstalled(
		params: BridgeType.IBridgeMapAppIsInstalledParams,
	): Promise<BridgeResult<BridgeType.IBridgeMapAppIsInstalledResult>>;

	/**地图导航 */
    openNavigation(
		params: BridgeType.IBridgeOpenNavigationParams,
	): Promise<BridgeResult<null>>;

	/**添加事件至系统日历 */
    addCalendarService(
		params: BridgeType.IBridgeAddCalendarServiceParams,
	): Promise<BridgeResult<BridgeType.IBridgeAddCalendarServiceResult>>;

	/**获取系统权限 */
    checkPermission(
		params: { permission: PermissionType },
	): Promise<BridgeResult<BridgeType.IBridgeCheckPermissionResult>>;

	/**webview上下拉弹簧效果开关 */
    changeWebviewControlSpring(params: SureType): Promise<BridgeResult<null>>;

	/**android支持支付类型查询（app3.0.6版本后支持） */
    querySupportAndroidPayByType(
		params: AndroidPayType,
	): Promise<BridgeResult<BridgeType.IBridgeSupportAndroidPayTypeResult>>;

	/**是否支持apple pay（app3.0.6版本后支持） */
    canSupportApplePay(
		params: IosPayType[],
	): Promise<BridgeResult<BridgeType.IBridgeSupportApplePayTypeResult>>;

	/**支付接口(app2.5.6版本后支持) */
    payToZAService(
		params: BridgeType.IBridgePayToZAServiceParams,
	): Promise<BridgeResult<BridgeType.IBridgeZAPayResult>>;

	/**新支付 */
    zaPay(
		params: BridgeType.IBridgeZAPayParams,
	): Promise<BridgeResult<BridgeType.IBridgeZAPayResult>>;

	/** 唤起微信打开webview */
    openWeChatWebview(
		url: string,
	): Promise<BridgeResult<BridgeType.IBridgeOpenWeChatWebviewResult>>;

	/** 用户事件监听 */
    registerEventListener(
		callback: (
			params: BridgeResult<BridgeType.IBridgeRegisterEventListenerResult>,
		) => void,
	): void;

	/** 注销用户事件监听 */
    unRegisterEventListener(): Promise<BridgeResult<null>>;

	/** app视图生命周期  */
    pageCycleService(
		callback: (
			params: BridgeResult<BridgeType.IBridgePageCycleServiceResult>,
		) => void,
	): void;

	/** 扫码二维码获取字符串 */
    QRCodeScan(): Promise<BridgeResult<BridgeType.IBridgeQRCodeScanResult>>;

	/** 通用调用app bridge 支持单次获取结果 */
    buildPromiseFunc<T>(
		name,
		params,
		listener?,
	): Promise<BridgeType.IBridgeResult<T>>;

	/** 通用调用app bridge 支持多次获取事件 */
    buildPromiseListenerFunc<T>(
		name,
		params,
		listener: (res: BridgeType.IBridgeResult<T>) => void,
	);
}

/** 存储继承接口 */
export interface IStorageBase {
	/**
	 * 存储数据
	 * @param key 存储的key
	 * @param value 存储的值
	 */
    set(key: string, value: string | { [key: string]: string }): void;
	/**
	 * 存储数据
	 * @param key 存储的key
	 * @param value 存储的值
	 * @param expired 过期时间(秒)
	 */
    set(
		key: string,
		value: string | { [key: string]: string },
		expired: number,
	): void;
	/**
	 * 获取存储的数据
	 */
    get(key: string): string | null;
	/**
	 * 删除指定的key
	 * @param key 删除值
	 */
    del(key: string): void;
	/**
	 * 清除所有key
	 */
    clear(): void;
}
/** localStorage */
export interface ISessionStorage extends IStorageBase { }
/** sessionStorage */
export interface ILocalStorage extends IStorageBase { }
/** 存储接口 */
export interface IStorage {
    session: ISessionStorage;
    local: ILocalStorage;
}

export interface IMinProgramsInterface {
    minProgramsType: MinProgramInterfaceType;
    [key: string]: any;
}
export interface ITools {
	/**
	 * 对比版本
	 * @param currentVersion 当前版本
	 * @param compareVersion 对比版本
	 * @returns 当前比对比大 > 1, 当前比对比小 < 0, 相等 = 0
	 */
    compareVersion(currentVersion: string, compareVersion): number;
	/**
	 * 输入版本和app版本对比
	 * @param compareVersion 对比版本
	 * @returns app版本版本输入版本 大 > 1, 当前比对比小 < 0, 相等 = 0
	 */
    compareAppVersion(compareVersion): number;
	/** 获取小程序实例 */
    getMinProgramsInterface(): Promise<IMinProgramsInterface | null>;
	/** 是否客服端 */
    isClient(): boolean;
}

/** cookie操作 */
export const dmCookie: ICookie;
/** 众安appBridge操作 */
export const dmBridge: IBridge;
/** 辅助操作 */
export const dmTools: ITools;
/** 存储操作 */
export const dmStorage: IStorage;
/** 环境判断 */
export const dmEnv: IEnv;
/** url操作 */
export const dmUrl: IUrl;
/**微信sdkApi(部分定义) */
// export var axios    : axiosTypes;

/** 工具类 */
export enum MinProgramInterfaceType {
	/** 微信 */
	Wechat = 1,
	/** 百度  */
	baidu = 2,
	/** 支付宝  */
	alipay = 3,
	/** 头条系 */
	toutiao = 4,
	/** 快手 */
	kuaishou = 5,
}
export enum SureType {
	'No' = '0',
	'Yes' = '1',
}
export enum ShareViewType {
	/**所有 */
	'All' = '1',
	/**微信卡片 */
	'WechatCard' = '2',
	/**朋友圈 */
	'WechatMoments' = '3',
	/**qq卡片 */
	'QQCard' = '4',
	/**qq空间 */
	'QQZone' = '5',
	/**微信卡片和朋友圈 */
	'WechatAll' = '2,3',
	/**qq卡片和qq空间 */
	'QQAll' = '4,5',
	/**微信卡片和qq卡片 */
	'CardAll' = '2,4',
	/**朋友圈和qq空间 */
	'MonentsZone' = '3,5',
}
export enum ShareType {
	/**微信卡片 */
	'WechatCard' = '0',
	/**朋友圈 */
	'WechatMoments' = '1',
	/**qq卡片 */
	'QQCard' = '2',
	/**qq空间 */
	'QQZone' = '3',
}
export enum ShareMinProgramType {
	/**小程序 */
	'MinProgram' = '4',
}
export enum MinProgramEnvType {
	/**正式 */
	'Prd' = '0',
	/**开发 */
	'Dev' = '1',
	/**体验 */
	'Experience' = '2',
}
export enum ResetNavigationBarType {
	'titleText' = 'titleText',
	'titleColor' = 'titleColor',
	'rightButton' = 'rightButton',
	'rightMenu' = 'rightMenu',
	'leftBackButton' = 'leftBackButton',
}
export enum MapIdType {
	'Amap' = '0',
	'Baidu' = '1',
	'Apple' = '2',
}
export enum NavigationMethodType {
	'Drive' = '0',
	'Bus' = '1',
	'walk' = '2',
	'Riding' = '3',
}
export enum PermissionType {
	/**通知开关 */
	'Notification' = 'NOTIFICATION',
	/**读取日历权限 */
	'ReadCalendar' = 'READ_CALENDAR',
	/**修改日历权限 */
	'WriteCalendar' = 'WRITE_CALENDAR',
	/**相机权限 */
	'Camera' = 'CAMERA',
	/**读取联系人 */
	'ReadContacts' = 'READ_CONTACTS',
	/**修改联系人 */
	'WriteContacts' = 'WRITE_CONTACTS',
	/**访问手机账号权限 */
	'GetAccounts' = 'GET_ACCOUNTS',
	/**WIFI定位权限 */
	'AccessCoarseLocation' = 'ACCESS_COARSE_LOCATION',
	/**GPS定位权限 */
	'AccessFineLocation' = 'ACCESS_FINE_LOCATION',
	/**录音权限  */
	'RecordAudio' = 'RECORD_AUDIO',
	/**存储读取权限 */
	'ReadExternalStorage' = 'READ_EXTERNAL_STORAGE',
	/**存储写入权限 */
	'WriteExternalStorage' = 'WRITE_EXTERNAL_STORAGE',
	/**发送短信 */
	'SendSms' = 'SEND_SMS',
	/**接收短信 */
	'ReceiveSms' = 'RECEIVE_SMS',
	/**读取短信内容 */
	'ReadSms' = 'READ_SMS',
	/**接收彩信 */
	'ReceiveMms' = 'RECEIVE_MMS',
	/**访问电话状态 */
	'ReadPhoneState' = 'READ_PHONE_STATE',
	/**允许程序从非系统拨号器里输入电话号码 */
	'CallPhone' = 'CALL_PHONE',
}
export enum AndroidPayType {
	/**华为 */
	'Huawei' = '1',
}
export enum IosPayType {
	/**美国运通卡 */
	Expresscard = '1',
	/**中国银联 */
	UnionPay = '2',
	/**发现卡 */
	DiscoveryCard = '3',
	/**加拿大Interac银行卡 */
	InteracCard = '4',
	/**万事达卡 */
	MasterCard = '5',
	/**信用卡和借记卡 */
	CreditCard = '6',
	/**Visa */
	Visa = '7',
}
export enum SupportApplyPayType {
	'Support' = '0',
	'NotSupport' = '1',
}
export enum PayChannelType {
	/**支付宝 */
	'AliPay' = '1',
	/**微信 */
	'WeChatPay' = '2',
}
export enum PayChannelExtendType {
	/**云闪付(未安装app，sdk会调起对应H5， app 3.0.7支持) */
	'UnionPay' = '3',
	/**Apple Pay（app 3.0.7支持） */
	'ApplePay' = '4',
}
export enum BridgerPayResultType {
	/**支付成功 */
	'Success' = '0',
	/**支付取消 */
	'Cancel' = '1',
	/**支付失败 */
	'Failure' = '2',
	/**没有安装 */
	'NotInstalled' = '3',
	/**参数错误 */
	'ParamsError' = '4',
}
