"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.fetch = function () {
    return {
        get: function (url) {
            return new Promise(function (resolve, reject) {
                var xmlhttp = new XMLHttpRequest();
                if (xmlhttp != null) {
                    xmlhttp.open('GET', url);
                    xmlhttp.setRequestHeader('Content-Type', 'application/json;charset=UTF-8');
                    xmlhttp.onreadystatechange = function () {
                        if (xmlhttp.readyState === 4) {
                            if (xmlhttp.status === 200) {
                                try {
                                    var json = JSON.parse(xmlhttp.responseText);
                                    resolve(json);
                                }
                                catch (e) {
                                    reject('parseError');
                                }
                            }
                            else {
                                reject('Problem retrieving XML data');
                            }
                        }
                    };
                    xmlhttp.send(null);
                }
                else {
                    reject('Your browser does not support XMLHTTP.');
                }
            });
        },
    };
};
