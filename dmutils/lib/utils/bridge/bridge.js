"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
var interface_1 = require("./interface");
exports.buildPromiseFunc = function (name, params, listener) {
    return new Promise(function (resolve) {
        interface_1.default(name, params, function (res) {
            resolve(res);
        }, listener);
    });
};
exports.buildPromiseListenerFunc = function (name, params, listener) {
    interface_1.default(name, params, function (res) { if (listener) {
        listener(res);
    } });
};
