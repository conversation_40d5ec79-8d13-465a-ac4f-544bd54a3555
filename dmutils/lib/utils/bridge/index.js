"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
var login_1 = require("./login");
var getZAToken_1 = require("./getZAToken");
var share_1 = require("./share");
var getSystemInfo_1 = require("./getSystemInfo");
var setNavigationBarTitle_1 = require("./setNavigationBarTitle");
var setNavigationBarColor_1 = require("./setNavigationBarColor");
var resetNavigationBarColor_1 = require("./resetNavigationBarColor");
var setNavigationBarRightButton_1 = require("./setNavigationBarRightButton");
var setNavigationBarRightMenu_1 = require("./setNavigationBarRightMenu");
var setWebviewBackListener_1 = require("./setWebviewBackListener");
var showShareView_1 = require("./showShareView");
var hiddenNavBackButton_1 = require("./hiddenNavBackButton");
var closeWebView_1 = require("./closeWebView");
var getZAUserInfo_1 = require("./getZAUserInfo");
var getLocationInfo_1 = require("./getLocationInfo");
var queryMapAppIsInstalled_1 = require("./queryMapAppIsInstalled");
var openNavigation_1 = require("./openNavigation");
var addCalendarService_1 = require("./addCalendarService");
var checkPermission_1 = require("./checkPermission");
var changeWebviewControlSpring_1 = require("./changeWebviewControlSpring");
var querySupportAndroidPayByType_1 = require("./querySupportAndroidPayByType");
var canSupportApplePay_1 = require("./canSupportApplePay");
var payToZAService_1 = require("./payToZAService");
var zaPay_1 = require("./zaPay");
var navigateToZAService_1 = require("./navigateToZAService");
var openWeChatWebview_1 = require("./openWeChatWebview");
var registerEventListener_1 = require("./registerEventListener");
var unRegisterEventListener_1 = require("./unRegisterEventListener");
var pageCycleService_1 = require("./pageCycleService");
var QRCodeScan_1 = require("./QRCodeScan");
var bridge_1 = require("./bridge");
var result = {
    login: login_1.default,
    getZAToken: getZAToken_1.default,
    getSystemInfo: getSystemInfo_1.default,
    setNavigationBarTitle: setNavigationBarTitle_1.default,
    setNavigationBarColor: setNavigationBarColor_1.default,
    resetNavigationBarColor: resetNavigationBarColor_1.default,
    setNavigationBarRightButton: setNavigationBarRightButton_1.default,
    setNavigationBarRightMenu: setNavigationBarRightMenu_1.default,
    setWebviewBackListener: setWebviewBackListener_1.default,
    showShareView: showShareView_1.default,
    share: share_1.default,
    hiddenNavBackButton: hiddenNavBackButton_1.default,
    closeWebView: closeWebView_1.default,
    getZAUserInfo: getZAUserInfo_1.default,
    getLocationInfo: getLocationInfo_1.default,
    queryMapAppIsInstalled: queryMapAppIsInstalled_1.default,
    openNavigation: openNavigation_1.default,
    addCalendarService: addCalendarService_1.default,
    checkPermission: checkPermission_1.default,
    changeWebviewControlSpring: changeWebviewControlSpring_1.default,
    querySupportAndroidPayByType: querySupportAndroidPayByType_1.default,
    canSupportApplePay: canSupportApplePay_1.default,
    payToZAService: payToZAService_1.default,
    zaPay: zaPay_1.default,
    navigateToZAService: navigateToZAService_1.default,
    openWeChatWebview: openWeChatWebview_1.default,
    registerEventListener: registerEventListener_1.default,
    unRegisterEventListener: unRegisterEventListener_1.default,
    pageCycleService: pageCycleService_1.default,
    QRCodeScan: QRCodeScan_1.default,
    buildPromiseFunc: bridge_1.buildPromiseFunc,
    buildPromiseListenerFunc: bridge_1.buildPromiseListenerFunc,
};
exports.default = result;
