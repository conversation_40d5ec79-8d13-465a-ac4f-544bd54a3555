"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
var cookie_1 = require("../../utils/cookie");
var tools_1 = require("../../utils/tools");
var env_1 = require("../../utils/env");
var isClient = tools_1.default.isClient();
var _window = isClient ? window : {};
var bridgeFunc = isClient ? window.prompt : function (_) { };
var APP_COOKIE_NAME = 'app_version';
var APP_DEFAULT_VERSION = '2.5.5';
var PREFIX = 'ZAJSSDK_';
var compareVersion = function (v1, v2) {
    return tools_1.default.compareVersion(v1, v2);
};
var getCallbackName = function (name) { return PREFIX + name.toUpperCase() + "_CALLBACK_" + Math.random().toString().slice(3); };
var versionMap = {
    payToZAService: '2.5.6',
    pageCycleService: '3.0.7',
    setWebviewBackListener: '3.0.4',
    querySupportAndroidPayByType: '3.0.6',
    registerEventListener: '3.2.4',
};
var callbackNames = {
    'setNavigationBarMenu': function (data, clickListener) {
        data.menuList.forEach(function (item) {
            _window[item.menuClickListener] = function (result) {
                clickListener(item, result);
            };
        });
    },
    'setNavigationBarRightButton': function (data, clickListener) {
        _window[data.buttonListener] = function (result) {
            clickListener(data, result);
        };
    },
    'setWebviewBackListener': function (data, clickListener) {
        _window[data.listener] = function (result) {
            clickListener(data, result);
        };
    },
};
var bridgeInterface = function (name, data, cb, clickListener) {
    if (cb === void 0) { cb = function (res) { console.log(res); }; }
    if (!tools_1.default.isClient()) {
        console.log('不是客户端不可使用app bridge');
    }
    else {
        if (env_1.default.isApp()) {
            var APP_VERSION = cookie_1.default.get(APP_COOKIE_NAME) || APP_DEFAULT_VERSION;
            var version = versionMap[name] || APP_DEFAULT_VERSION;
            if (compareVersion(APP_VERSION, version) >= 0) {
                var callbackName_1 = getCallbackName(name);
                var params = {
                    functionName: name,
                    params: data,
                    complete: callbackName_1,
                };
                if (callbackNames[name]) {
                    callbackNames[name](data, clickListener);
                }
                _window[callbackName_1] = function (result) {
                    cb(result);
                    _window[callbackName_1] = undefined;
                };
                bridgeFunc(JSON.stringify(params));
            }
            else {
                console.log("%c\u5F53\u524Dapp\u7248\u672C\u4E0D\u652F\u6301: %c" + name + "\u63A5\u53E3", 'color:#A9D5F4;font-size: 14px', 'color:#CD533D;font-size: 14px');
            }
        }
        else {
            console.log("%c\u5F53\u524D\u975EApp\u73AF\u5883%c\u8C03\u7528app\u63A5\u53E3: %c" + name + "%c\uFF0C\u53C2\u6570: %c" + (Array.isArray(data)
                || typeof data === 'object' ? JSON.stringify(data) : data), 'color:#CD533D', 'color:black', 'color:#A9D5F4;font-size: 14px', 'color:black', 'color:#CD533D;font-size: 14px');
        }
    }
};
exports.default = bridgeInterface;
