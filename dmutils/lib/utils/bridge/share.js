"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
var share_1 = require("../wechart/share");
var bridge_1 = require("./bridge");
var env_1 = require("../env");
exports.default = (function (params) {
    var NAME = 'share';
    if (env_1.default.isApp()) {
        bridge_1.buildPromiseFunc(NAME, params);
    }
    else {
        share_1.wechatShare({
            title: params.title || '',
            desc: params.desc || '',
            link: params.url || '',
            imgUrl: params.imageUrl || '',
            cancel: params.cancel,
            success: params.success,
        });
    }
});
