"use strict";
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
Object.defineProperty(exports, "__esModule", { value: true });
var index_1 = require("./../index");
var check_type_1 = require("./check-type");
var parseAll = function (_url) {
    var url = _url || '';
    if (!url && index_1.dmTools.isClient()) {
        url = location.href;
    }
    var result = {
        hostpath: '',
        params: {},
        hash: ''
    };
    try {
        var urlSplit = url.split('?');
        result.hostpath = urlSplit[0] || '';
        var searchSplit = (urlSplit[1] || '').split('#');
        (searchSplit[0] || '').split('&').forEach(function (item) {
            var items = item.split('=');
            if (items[0]) {
                result.params[items[0]] = items[1] || '';
            }
        });
        result.hash = searchSplit[1] || '';
        return result;
    }
    catch (e) {
        console.error(e);
        return result;
    }
};
var parse = function (_url) { return parseAll(_url).params; };
var get = function (key) {
    return parse()[key] || '';
};
var format = function (data, prefix) {
    if (data === void 0) { data = {}; }
    if (prefix === void 0) { prefix = ''; }
    var _a = prefix.split('?'), hostpath = _a[0], search = _a[1];
    var queryString = '';
    if (check_type_1.isObject(data)) {
        var queryParams_1 = {};
        if (search)
            queryParams_1 = parse('?' + search);
        queryParams_1 = __assign({}, queryParams_1, data);
        queryString = Object.keys(queryParams_1).map(function (key) {
            return key + "=" + queryParams_1[key];
        }).join('&');
    }
    else {
        return prefix;
    }
    return hostpath + '?' + queryString;
};
var merge = function (u1, u2, options, searchExclude, isEnforceExclude) {
    if (options === void 0) { options = {}; }
    if (searchExclude === void 0) { searchExclude = []; }
    if (isEnforceExclude === void 0) { isEnforceExclude = false; }
    var url1 = parseAll(u1);
    var url2 = parseAll(u2);
    var result = {
        hostpath: '',
        params: {},
        hash: ''
    };
    result.hostpath = url2.hostpath || url1.hostpath;
    result.hash = url2.hash || url2.hash;
    var objMerge = function (p1, p2) {
        var obj = {};
        Object.keys(p1).forEach(function (key) {
            if (!searchExclude.includes(key)) {
                obj[key] = p1[key] || '';
            }
        });
        Object.keys(p2).forEach(function (key) {
            if (searchExclude.includes(key) && isEnforceExclude)
                return;
            if (obj[key] && !p2[key])
                return;
            obj[key] = p2[key] || '';
        });
        return obj;
    };
    result.params = objMerge(url1.params, __assign({}, url2.params, options));
    var search = Object.keys(result.params).map(function (key) { return key + "=" + result.params[key]; }).join('&');
    return "" + result.hostpath + (search ? "?" + search : '') + (result.hash ? "#" + result.hash : '');
};
var dmUrl = {
    parse: parse,
    parseAll: parseAll,
    format: format,
    get: get,
    merge: merge,
};
exports.default = dmUrl;
