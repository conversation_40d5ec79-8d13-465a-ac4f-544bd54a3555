"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
var env_1 = require("../env");
var adapter_1 = require("../adapter");
var tools_1 = require("../tools");
var GET_WX_SIGN_URL = 'https://gwbk.zhongan.com/dmapi/web-mobile/open-common-WeiXinScreen-getWeiXinSign';
var WX_API_LIST = [
    'checkJsApi',
    'onMenuShareTimeline',
    'onMenuShareAppMessage',
    'onMenuShareQQ',
    'onMenuShareWeibo',
    'updateAppMessageShareData',
    'updateTimelineShareData',
    'hideMenuItems',
    'showMenuItems',
    'hideAllNonBaseMenuItem',
    'showAllNonBaseMenuItem',
    'previewImage',
    'chooseImage',
    'uploadImage',
    'downloadImage',
    'startRecord',
    'stopRecord',
    'onVoiceRecordEnd',
    'playVoice',
    'stopVoice',
    'onVoicePlayEnd',
    'uploadVoice',
    'downloadVoice',
    'addCard',
    'chooseCard',
    'openCard',
    'openLocation',
    'getLocation',
    'chooseWXPay',
];
var wxReady = function (wx) {
    ready = function () {
        return wx;
    };
};
var ready = function () {
    return new Promise(function (resolve, reject) {
        if (!tools_1.default.isClient()) {
            reject();
            return;
        }
        if (env_1.default.isWechat()) {
            if ('wx' in window && wx) {
                adapter_1.fetch().get(GET_WX_SIGN_URL + ("?url=" + encodeURIComponent(location.href))).then(function (res) {
                    var data = res;
                    wx.config({
                        debug: location.search.indexOf('wxdebug=Y') !== -1 ? true : false,
                        appId: data.appid,
                        timestamp: data.timestamp,
                        nonceStr: data.noncestr,
                        signature: data.signature,
                        jsApiList: WX_API_LIST,
                    });
                    wx.ready(function () {
                        resolve(wx);
                        wxReady(wx);
                    });
                }).catch(function (_) {
                    reject('获取微信签名失败');
                });
            }
        }
        else {
            reject('请在【微信】环境调用方法');
        }
    });
};
exports.default = ready;
