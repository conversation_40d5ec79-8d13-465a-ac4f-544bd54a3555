{"name": "@dm/utils", "version": "0.3.50", "description": "直营工具仓库", "main": "lib/index.js", "scripts": {"start": "webpack --config ./scripts/webpack/webpack.dev.conf.js", "dev": "node node_modules/webpack-dev-server/bin/webpack-dev-server.js --config ./scripts/webpack/webpack.dev.conf.js", "build": "rm -rf lib && tsc && tsc -d src/enum.ts && rm -rf src/enum.js && mv src/enum.d.ts lib && cp -r src/types lib"}, "author": "", "license": "ISC", "files": ["lib/**/*"], "husky": {"hooks": {"pre-commit": "lint-staged", "commit-msg": "node scripts/verify-commit-msg.js"}}, "lint-staged": {"*.ts": ["tslint --fix", "git add"]}, "typings": "lib/types/index.d.ts", "devDependencies": {"@babel/core": "^7.1.6", "@babel/plugin-proposal-class-properties": "^7.1.0", "@babel/plugin-transform-runtime": "^7.2.0", "@babel/preset-env": "^7.1.6", "@babel/preset-typescript": "^7.1.0", "@babel/runtime": "^7.2.0", "@commitlint/cli": "^7.2.1", "@commitlint/config-conventional": "^7.1.2", "@types/node": "^10.12.9", "@types/webpack-env": "^1.13.6", "awesome-typescript-loader": "^5.2.1", "babel-loader": "^8.0.4", "babel-minify-webpack-plugin": "^0.3.1", "chalk": "^2.4.1", "clean-webpack-plugin": "^1.0.0", "copy-webpack-plugin": "^4.6.0", "fork-ts-checker-webpack-plugin": "^0.4.15", "html-webpack-plugin": "^3.2.0", "husky": "^1.2.0", "lint-staged": "^8.1.0", "progress-bar-webpack-plugin": "^1.11.0", "ts-loader": "^5.3.2", "tslint": "^5.12.0", "tslint-eslint-rules": "^5.4.0", "typescript": "^3.2.1", "typescript-eslint-parser": "^21.0.1", "uglifyjs-webpack-plugin": "^2.1.1", "webpack": "^4.27.1", "webpack-cli": "^3.1.2", "webpack-dev-server": "^3.1.10", "webpack-merge": "^4.1.4", "webpackbar": "^3.1.3"}}