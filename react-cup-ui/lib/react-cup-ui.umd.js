!function (t, e) { "object" === typeof exports && "object" === typeof module ? module.exports = e() : "function" === typeof define && define.amd ? define([], e) : "object" === typeof exports ? exports["react-cup-ui"] = e() : t["react-cup-ui"] = e() }(window, (function () { return function (t) { var e = {}; function n(r) { if (e[r]) return e[r].exports; var o = e[r] = { i: r, l: !1, exports: {} }; return t[r].call(o.exports, o, o.exports, n), o.l = !0, o.exports } return n.m = t, n.c = e, n.d = function (t, e, r) { n.o(t, e) || Object.defineProperty(t, e, { enumerable: !0, get: r }) }, n.r = function (t) { "undefined" !== typeof Symbol && Symbol.toStringTag && Object.defineProperty(t, Symbol.toStringTag, { value: "Module" }), Object.defineProperty(t, "__esModule", { value: !0 }) }, n.t = function (t, e) { if (1 & e && (t = n(t)), 8 & e) return t; if (4 & e && "object" === typeof t && t && t.__esModule) return t; var r = Object.create(null); if (n.r(r), Object.defineProperty(r, "default", { enumerable: !0, value: t }), 2 & e && "string" != typeof t) for (var o in t) n.d(r, o, function (e) { return t[e] }.bind(null, o)); return r }, n.n = function (t) { var e = t && t.__esModule ? function () { return t.default } : function () { return t }; return n.d(e, "a", e), e }, n.o = function (t, e) { return Object.prototype.hasOwnProperty.call(t, e) }, n.p = "", n(n.s = 2) }([function (t, e) { t.exports = require("react") }, function (t, e, n) { }, function (t, e, n) { "use strict"; function r(t, e) { if (!(t instanceof e)) throw new TypeError("Cannot call a class as a function") } function o(t, e) { for (var n = 0; n < e.length; n++) { var r = e[n]; r.enumerable = r.enumerable || !1, r.configurable = !0, "value" in r && (r.writable = !0), Object.defineProperty(t, r.key, r) } } function c(t, e) { return (c = Object.setPrototypeOf || function (t, e) { return t.__proto__ = e, t })(t, e) } function i(t) { return (i = Object.setPrototypeOf ? Object.getPrototypeOf : function (t) { return t.__proto__ || Object.getPrototypeOf(t) })(t) } function u() { if ("undefined" === typeof Reflect || !Reflect.construct) return !1; if (Reflect.construct.sham) return !1; if ("function" === typeof Proxy) return !0; try { return Date.prototype.toString.call(Reflect.construct(Date, [], (function () { }))), !0 } catch (t) { return !1 } } function a(t) { return (a = "function" === typeof Symbol && "symbol" === typeof Symbol.iterator ? function (t) { return typeof t } : function (t) { return t && "function" === typeof Symbol && t.constructor === Symbol && t !== Symbol.prototype ? "symbol" : typeof t })(t) } function f(t, e) { return !e || "object" !== a(e) && "function" !== typeof e ? function (t) { if (void 0 === t) throw new ReferenceError("this hasn't been initialised - super() hasn't been called"); return t }(t) : e } n.r(e), n.d(e, "UPButton", (function () { return w })); var l = n(0), s = n.n(l), p = (n(1), navigator.userAgent.toLowerCase()); function d(t) { return "[object Function]" === Object.prototype.toString.call(t) } function b(t) { return t ? t.resultParams ? t.resultParams : t.resultString ? t.resultString : void 0 : t } var y = /\(securitywebcache\s([\d\.]+)\)/g.test(p), _ = /\(cordova\s([\d\.]+)\)/g.test(p); function v(t, e, n, r, o) { var c = !1; return _ && window.cordova ? (c = !0, window.cordova.exec(t, e, n, r, o)) : y && window.WebViewJavascriptBridge && (c = !0, window.WebViewJavascriptBridge.callHandler(n, r, o, (function (e) { d(t) && t(b(e)) }), (function (t) { d(e) && e(b(t)) }))), c } var w = function (t) { !function (t, e) { if ("function" !== typeof e && null !== e) throw new TypeError("Super expression must either be null or a function"); t.prototype = Object.create(e && e.prototype, { constructor: { value: t, writable: !0, configurable: !0 } }), e && c(t, e) }(d, t); var e, n, a, l, p = (e = d, function () { var t, n = i(e); if (u()) { var r = i(this).constructor; t = Reflect.construct(n, arguments, r) } else t = n.apply(this, arguments); return f(this, t) }); function d() { var t; r(this, d); for (var e = arguments.length, n = new Array(e), o = 0; o < e; o++)n[o] = arguments[o]; return (t = p.call.apply(p, [this].concat(n))).appletExplicitAuth = function (e, n) { v((function (t) { "function" === typeof e && e(t) }), (function (t) { if ("function" === typeof n) { var e; if (window.cordova) switch (window.cordova.errorRetStatus) { case window.cordova.callbackStatus.INVALID_ACTION: e = { errcode: "c03", errmsg: "INVALID_ACTION_EXCEPTION: \u63d2\u4ef6\u91cc\u9762\u6ca1\u6709\u6b64\u65b9\u6cd5\uff01" }; break; case window.cordova.callbackStatus.CLASS_NOT_FOUND_EXCEPTION: e = { errcode: "c04", errmsg: "CLASS_NOT_FOUND_EXCEPTION: \u6b64\u63d2\u4ef6\u6ca1\u6709\u5b9e\u73b0\uff01" }; break; case window.cordova.callbackStatus.ILLEGAL_ACCESS_EXCEPTION: e = { errcode: "c02", errmsg: "ILLEGAL_ACCESS_EXCEPTION: \u65e0\u6743\u9650\u8bbf\u95ee\u6b64\u63d2\u4ef6\uff01" } }n(e || t) } }), "UPWebSdk", "appletExplicitAuth", [{ scope: t.props.scope }]) || setTimeout((function () { t._count++, t._count > t._timeout / 20 ? (console.warn("\u8bf7\u786e\u5b9a\u662f\u5426\u8fd0\u884c\u5728\u4e91\u95ea\u4ed8APP\u4e2d,\u4e14\u6210\u529f\u52a0\u8f7d\u4e86upsdk.js"), n({ errcode: "__ENV__10001", errmsg: "\u68c0\u6d4b\u5230\u672a\u5728\u4e91\u95ea\u4ed8APP\u4e2d\u8fd0\u884c\u6216\u672a\u6210\u529f\u52a0\u8f7dupsdk.js" })) : t.appletExplicitAuth(e, n) }), 20) }, t.handleClick = function (e) { if (!t.btnDisable) { e.persist(); var n = t.props, r = n.timeout, o = n.onClick; "function" === typeof o ? r && isNaN(parseInt(r)) ? o(e, { errcode: "__ENV__10002", errmsg: "\u68c0\u6d4b\u5230timeout\u503c\u975e\u6cd5" }) : (t._count = 0, t._timeout = r || 2e3, t.btnDisable = !0, t.appletExplicitAuth((function (n) { t.btnDisable = !1, o(e, null, n) }), (function (n) { t.btnDisable = !1, o(e, n) }))) : console.warn("\u8bf7\u68c0\u67e5\u662f\u5426\u5904\u7406\u4e86click\u4e8b\u4ef6") } }, t } return n = d, (a = [{ key: "render", value: function () { return s.a.createElement("button", { className: "up-button ".concat(this.props.className), style: this.props.style, onClick: this.handleClick }, this.props.children) } }]) && o(n.prototype, a), l && o(n, l), d }(s.a.Component); e.default = { version: "0.1.0" } }]) }));