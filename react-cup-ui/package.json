{"name": "react-cup-ui", "version": "0.1.0", "description": "cup applet ui component for react", "main": "lib/react-cup-ui.umd.js", "files": ["src", "lib"], "private": true, "dependencies": {"@testing-library/jest-dom": "^4.2.4", "@testing-library/react": "^9.3.2", "@testing-library/user-event": "^7.1.2", "prop-types": "^15.7.2", "react": "^16.13.1", "react-dom": "^16.13.1", "react-scripts": "3.4.1"}, "scripts": {"start": "react-app-rewired start --examples", "build": "react-app-rewired build --examples", "eject": "react-scripts eject", "lib": "cross-env GENERATE_SOURCEMAP=false react-app-rewired build --library"}, "eslintConfig": {"extends": "react-app"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"cross-env": "^7.0.2", "customize-cra": "^1.0.0", "postcss-px2rem": "^0.3.0", "react-app-rewired": "^2.1.6", "remove-webpack-plugin": "^1.2.2"}}