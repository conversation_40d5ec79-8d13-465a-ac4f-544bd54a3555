/*
 * @Author: sen.lv <EMAIL>
 * @Date: 2022-06-08 18:53:25
 * @LastEditTime: 2023-03-30 10:57:43
 * @LastEditors: <EMAIL> <EMAIL>
 * @FilePath: /za-asclepius-patient-h5/server/env/dev.js
 * @Description:
 */
module.exports = {
  PORT: 8080,
  ENV: 'dev',
  API: {
    api: {
      host: 'za-asclepius-patient-bops.test.za-doctor.net', // 测试
      // host: 'https://online-tst.za-doctor.com/api/api', // 本地
    },
    share: {
      host: 'wechat.zhongan.com', // 分享用
    },
    jssdksign: {
      host: 'rubik.zhongan.com', // 微信jsSdk初始化
    },
    // 直营渠道接口
    gwbkApi: {
      host: 'mgw-daily.zhongan.com/dmapi/za-health-activity',
    },
    ilogApi: {
      host: 'qhebvsiu.lc-cn-n1-shared.com',
    },
    WEBSOCKET_API: {
      host: 'wss://nireus-tst.za-doctor.com/ws',
      // host: 'ws://za-nireus.test.za-tech.net/ws',
      port: '',
    },

  },
  ZAAPP_CONFIG: {
    key: 'f5e76ec5ff65e4b1154f02868fe42022', // 直营APP获取授权信息key
    env: 'test',
  },
  BESTPAY_CONFIG: { // 翼支付APP 获取授权信息key
    key: '3178035938228650', // **********
    env: 'test',
  },
  publicPath: '/hospital/assets',
};
