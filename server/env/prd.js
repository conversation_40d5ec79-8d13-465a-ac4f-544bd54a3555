/*
 * @Description: 头部注释
 * @Autor: hou
 * @Date: 2022-11-03 11:03:19
 * @LastEditors: hou
 * @LastEditTime: 2023-02-27 15:58:26
 * @FilePath: /za-asclepius-patient-h5/server/env/prd.js
 */
module.exports = {
  PORT: 8080,
  ENV: 'prd',
  API: {
    api: {
      // host: 'za-asclepius-patient-bops.prd.za-tech.net', //请求域名
      host: 'za-asclepius-patient-bops.prd.za-doctor.net',
    },
    share: {
      host: 'wechat.zhongan.com', //分享用
    },
    jssdksign: {
      host: 'rubik.zhongan.com', //微信jsSdk初始化
    },
    //直营渠道接口
    gwbkApi: {
      host: 'gwbk.zhongan.com/dmapi/za-health-activity',
    },
    ilogApi: {
      host: 'qhebvsiu.lc-cn-n1-shared.com',
    },
    WEBSOCKET_API: {
      host: 'wss://nireus.za-doctor.com/ws', //ws域名
      // host: 'wss://za-nireus.za-doctor.net/ws',
      port: '',
    },
  },
  ZAAPP_CONFIG: {
    key: '221010c4d5ff937326c2c54298d2882c', //直营APP获取授权信息key
    env: 'prod'
  },
  BESTPAY_CONFIG: { //翼支付APP 获取授权信息key
    key: '3178035938228650',
    env: 'prod'
  },
  publicPath: 'https://static.za-doctor.com/za-asclepius-patient-h5/static',
};
