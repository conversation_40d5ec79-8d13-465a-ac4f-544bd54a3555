/*
 * @Description: 头部注释
 * @Autor: hou
 * @Date: 2022-11-03 11:03:19
 * @LastEditors: hou
 * @LastEditTime: 2023-02-24 10:42:56
 * @FilePath: /za-asclepius-patient-h5/server/env/pre.js
 */
module.exports = {
  PORT: 8080,
  ENV: 'pre',
  API: {
    api: {
      // host: "za-asclepius-patient-bops.pre.za-tech.net"
      host: 'za-asclepius-patient-bops.pre.za-doctor.net',
    },
    share: {
      host: "wechat.zhongan.com"  //分享用
    },
    jssdksign: {
      host: 'rubik.zhongan.com', //微信jsSdk初始化
    },
    //直营渠道接口
    gwbkApi: {
      host: "gwbk-uat.zhongan.com/dmapi/za-health-activity"
    },
    ilogApi: {
      host: 'qhebvsiu.lc-cn-n1-shared.com',
    },
    WEBSOCKET_API: {
      host: 'wss://nireus-uat.za-doctor.com/ws',
      // host: 'wss://za-nireus.uat.za-doctor.net/ws',
      port: '',
    },

  },
  ZAAPP_CONFIG: {
    key: 'cf559f9b2985fb5ffa6d23783dc08116', //直营APP获取授权信息key
    env: 'uat'
  },
  BESTPAY_CONFIG: { //翼支付APP 获取授权信息key
    key: '**********',
    env: 'uat'
  },
  publicPath: 'https://static.za-doctor.com/za-asclepius-patient-h5-pre/static',
  // publicPath: '/hospital/assets',
};
