/*
 * @Description: 头部注释
 * @Autor: hou
 * @Date: 2022-11-03 11:03:19
 * @LastEditors: hou
 * @LastEditTime: 2023-02-27 15:56:36
 * @FilePath: /za-asclepius-patient-h5/server/env/test.js
 */
module.exports = {
  PORT: 8080,
  ENV: 'test',
  API: {
    api: {
      // host: 'za-asclepius-patient-bops.test.za-tech.net',
      host: 'za-asclepius-patient-bops.test.za-doctor.net', // 测试
    },
    share: {
      host: 'wechat.zhongan.com', // 分享用
    },
    jssdksign: {
      host: 'rubik.zhongan.com', // 微信jsSdk初始化
    },
    // 直营渠道接口
    gwbkApi: {
      host: 'mgw-daily.zhongan.com/dmapi/za-health-activity',
    },
    ilogApi: {
      host: 'qhebvsiu.lc-cn-n1-shared.com',
    },
    WEBSOCKET_API: {
      host: 'wss://nireus-tst.za-doctor.com/ws',
      port: '',
    },
  },
  ZAAPP_CONFIG: {
    key: 'f5e76ec5ff65e4b1154f02868fe42022', // 直营APP获取授权信息key
    env: 'test',
  },
  BESTPAY_CONFIG: { // 翼支付APP 获取授权信息key
    key: '**********',
    env: 'test',
  },
  publicPath: '/hospital/assets',
};
