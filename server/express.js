const compression = require('compression');
const cookieParser = require('cookie-parser');
const path = require('path');
const config = require('./config');

module.exports = function(app) {
  app.set('view engine', 'html');
  app.set('views', config.root + '/views');
  app.engine('.html', require('ejs').__express);
  app.use(compression({
    filter: shouldCompress,
  }));
  app.use(cookieParser());

  // expose params to views
  app.use((req, res, next) => {
    res.locals.env = config.ENV;
    res.locals.publicPath = config.publicPath;
    res.locals.debugTools = ['page-spy', 'eruda'];
    // res.locals.ENVIRONMENT_KEY = `_e_${gitCommitHash}`;
    next();
  });
};

function shouldCompress(req, res) {
  if (req.headers['x-no-compression']) {
    // don't compress responses with this request header
    return false;
  }

  // fallback to standard filter function
  return compression.filter(req, res);
}
