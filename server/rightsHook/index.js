/**
 * vasCode 映射到的实际 route 地址
 */
const rightsMap = {
  // 慢病无忧 老款
  ChronicDisease: '/hospital/drug/drugstore?vasCode=ChronicDisease',
  // 慢病无忧 新款
  chronicmedicinewy: '/hospital/drug/drugstore?vasCode=chronicmedicinewy',
  // 药无忧
  DrugsReimbursement: '/hospital/preauth?partnerCode=SYY&businessType=index&thirdBizType=YWY',
  // 云药箱
  MedicineBox: '/hospital/preauth?partnerCode=SYY&businessType=index&thirdBizType=YYX',
  // 医疗垫付
  advancedPayment: '/hospital/rights/detail/special?defaultType=0',
  // 肿瘤特药
  tumourSpecialMedicine: '/hospital/rights/detail/special?defaultType=1',
  // 重疾绿通
  greenChannel: '/hospital/rights/detail/special?defaultType=2',
  // 质子重离子 
  proton: '/hospital/rights/detail/special?defaultType=3',
  // 术后护理
  nursingConfig: '/hospital/rights/detail/special?defaultType=4',
  // 心理倾诉
  psychologicalConfig: '/hospital/rights/detail/special?defaultType=5',
  // 博鳌特药
  hainanSpecialMedicine: '/hospital/rights/detail/special?defaultType=6',
  // 门诊挂号协助
  registeredAssistance: '/hospital/rights/detail/special?defaultType=7',
  // 检查加急
  urgencyCheck: '/hospital/rights/detail/special?defaultType=8',
  // 就医陪诊
  accompanying: '/hospital/rights/detail/special?defaultType=9',
  // 住院绿通
  hospitalService: '/hospital/rights/detail/special?defaultType=10',
  // 二次诊疗
  secondDiagnosis: '/hospital/rights/detail/special?defaultType=11',
  // 按方开中药
  Chinesemedicine: '/hospital/rights/detail/special?defaultType=12',
  // 肿瘤基因检测
  CancerriskGeneDetection: '/hospital/rights/detail/special?defaultType=13',
  // 宠物防疫
  PetService: '/hospital/rights/detail/special?defaultType=14',
  // 上门居家护理
  HomeCare: '/hospital/rights/detail/special?defaultType=15',
  // 中西医康复
  ChineseandWesternRecovering: '/hospital/rights/detail/special?defaultType=16',
  // 少儿罕见病特药
  childrendiseases: '/hospital/rights/detail/special?defaultType=17',
  // 远程康复评估
  RehabilitationEvaluation: '/hospital/rights/detail/special?defaultType=18',
  // 心理倾诉服务(3次)
  mentalRelease3: '/hospital/rights/detail/special?defaultType=19',
  // 肿瘤特药直付
  tumourSpecialMedicineDirectPay: '/hospital/rights/detail/special?defaultType=21',
  // 用药前基因检测
  preMedicationGeneDetection: '/hospital/rights/detail/special?defaultType=22',
  // 特定疾病急需药品
  urgentMedication: '/hospital/rights/detail/special?defaultType=23',
  // 先进疗法就医协助服务
  advancedMedicalAssistance: '/hospital/rights/detail/special?defaultType=24',
  // 门诊挂号协助
  registeredAssistanceOnce: '/hospital/rights/detail/special?defaultType=25',
  // 先进医疗就医协助服务
  Inpatientcareservices: '/hospital/rights/detail/special?defaultType=26',
  // 重疾专家二诊
  RecurrenceSeconddiagnosis: '/hospital/rights/detail/special?defaultType=27',
  // 重疾直通车
  sdBulter: '/hospital/static/SeriousIllnessGreenChannel',
  // 金选畅学 - 普通
  jinxuanchangxue: '/hospital/static/jinxuanStudy?vasCode=jinxuanchangxue',
  // 金选畅学 - 月付版
  healthymonthlyCourse: '/hospital/static/jinxuanStudy?vasCode=healthymonthlyCourse',
  // 金选畅学 - 季付版
  healthyquarterlyCourse: '/hospital/static/jinxuanStudy?vasCode=healthyquarterlyCourse',
  // 金选畅学 - 三年付版
  healthytrienniallyCourse: '/hospital/static/jinxuanStudy?vasCode=healthytrienniallyCourse',
  // 慢病药无忧
  chronicmedicinewy: '/hospital/drug/drugstore?vasCode=chronicmedicinewy',
  // 美年体检
  healthcheck: '/hospital/static/mntjService',
  // 乐牙齿科
  TeethcleaningLY: '/hospital/myrights/leya',
  // 成人洁牙
  Teethcleaningservice: '/hospital/static/cleanteeth',
  // 厚仆中医
  trachinmedcine: '/hospital/myrights/activeTrach',
  // 肿瘤安康小药箱
  Tumorhealthkit: '/hospital/static/smallMedicineBox?subOrderType=Tumorhealthkit',
  // 乳愈安康复发险--康复营养包
  ruyurehabilitation: '/hospital/static/ruyuAnkang',
  // 应季小药箱
  Seasonalmedbox: '/hospital/static/Seasonalmedbox',
  // 健跑应急服务
  healthyrun: '/hospital/static/physicalGoodsService/healthyrun',
  // HP自测
  HPtesting: '/hospital/static/hptest',
  // 臻享 5 选 1
  zhenxiangservice: '/hospital/static/zhenxiang',
  // 金选 4 选 1
  goldenservice: '/hospital/static/jinxuan',
  // 臻享 plus 5 选 1
  PremiumHealthServices: '/hospital/static/zhenxiang/plus',
  // 防癌早筛优选（4 选 1）
  PreferredCancerScreening: '/hospital/myrights/cancercheckappointOptimized',
  // 防癌早筛（2 选 1）
  CancerScreening: '/hospital/myrights/cancercheckappoint',
  // 健康管理服务（3 选 1）
  'Healthservices-wuyou': '/hospital/static/wuyou',
  // 儿童齿科服务（2 选 1）
  PaediatricDentistry: '/hospital/rights/childteeth',
  // 结节管理 
  noduleservice: '/hospital/myrights/qrcode/noduleservice',
  // 肿瘤安康
  secondarydiagnosis: '/hospital/myrights/qrcode/secondarydiagnosis',
  // 仁济转诊 
  referralservice: '/hospital/inquiryform?isChangeInquiry=Y',
  // 慢病药费直赔之一
  DrugDirectPay: '/hospital/claims/inquiry',
  // 慢病药费直赔之二
  HronicDrugDirectPay: '/hospital/claims/inquiry',
  // 慢病药费直赔之三
  ChronicDrugDirectPay: '/hospital/claims/inquiry',
  // 图文问诊之一
  onlineConsult: '/hospital/inquiryform',
  // 图文问诊之二
  ConsultationandDrugDiscount: '/hospital/inquiryform',
  // 图文问诊之三
  drugDiscount: '/hospital/inquiryform',
  // 图文问诊之四
  onlineFreeInquiry: '/hospital/inquiryform',
  // 图文问诊之五
  graphicConsultation: '/hospital/chatmedicalmanage?fromSelfChannel=Y',
  // 视频问诊之一
  videoConsult: '/hospital/videointro',
  // 视频问诊之二
  OnandOfflineDrug: '/hospital/videointro',
  // 视频问诊之三
  ohClinic: '/hospital/videointro',
  // 体检预约
  checkup: '/hospital/mycheckup',
  // 初日减脂
  Initialreducingfat: '/hospital/static/initialReducingFat',
  // 甲状腺加企微
  payforhealthservice: '/hospital/static/payforhealthservice',
  // 护甲服务(仅支持从保单跳转)
  JWYhealthservice: '/hospital/static/jwyHealthservice',

  // 慢病无忧 弃用
  gjDrugDiscount: '',

  // 拜博齿科检查 (暂时不管)
  Dentalexamination: '',
}
/**
 * 根据给定的URL获取正确的权益履约路由
 * 
 * 该函数从URL中提取vasCode，然后通过映射表获取对应的权益履约路由，
 * 并将原始查询参数附加到新路由上
 * 
 * @param {string} url - 包含vasCode的原始URL
 * @returns {string} 映射后的权益履约路由，如果处理失败则返回错误页面路由
 */
function getRightVasCodeRoute(url) {
  try {
    // 从 url 中解析出 path
    const [path, query = ''] = url.split('?');
    // 从 path 中解析出 vasCode. 去掉第一个斜杠, 然后 split 出 vasCode
    const vasCode = path.slice(1).split('/')[1];
    // 从 vasCode 映射实际的权益履约 route
    const rightsUrl = rightsMap[vasCode];
    if (!rightsUrl) {
      throw new Error(`vasCode: ${vasCode} 不存在映射关系`);
    }
    // 拼接符
    const joinStr = query ? (rightsUrl.includes('?') ? '&' : '?') : '';
    // 拼接 query
    const nextUrl = `${rightsUrl}${joinStr}${query}`;
    return nextUrl;
  } catch (e) {
    console.log('getRightVasCodeRoute error', e);
    return '/hospital/error';
  }
}

module.exports = {
  getRightVasCodeRoute,
}