
var httpProxy = require('http-proxy');
var config = require('./config');
const utils = require('./utils');
const { getRightVasCodeRoute } = require('./rightsHook');


module.exports = function (app) {
  var apiServers = config.API;
  var currentEnv = config.ENV;
  var proxy = httpProxy.createProxyServer();

  proxy.on('error', function(e) {
    // logger.error(e);
  });

  // 服务器部署验证
  app.get('/health', function (req, res) {
    return res.status(200).send('OK');
  });

  if (currentEnv === 'dev') {
    /* dev环境，配置图片访问映射 */
    app.all('/v1/attchment/downloadFile*', (req, res) => {
      const { url } = req;
      console.log('----###--正在图片代理转发-----:', url);
      const target = `http://${apiServers.api.host}`;
      // const target = `${apiServers.api.host}`;
      console.log('attchment:', target + req.url);
      console.log('---------####-正在图片代理转发 , 分割线-------------');
      proxy.web(req, res, {
        target,
        changeOrigin: true,
      });
    });
  }

  app.all('*/api/*', function (req, res) {//接口API代理
    const { url } = req;
    const regExp = /\/api\/(.*?)\//;
    const hostkey = url.match(regExp)[1];
    req.url = url.replace(regExp, '/');
    const target = `http://${apiServers[hostkey].host}`;
    // const target = `${apiServers[hostkey].host}`;

    console.log('--代理---api:', target + req.url);
    console.log('--&& H5 &---当前环境变量:',currentEnv,'，DEPLOY_ENV：',process.env.DEPLOY_ENV,'-----h5端---');
    console.log('------------患者端 API代理 分割线-------------');

    proxy.web(req, res, {
      target: target,
      changeOrigin: true
    });
  });

  app.get('/hospital/pdfViewer', function (req, res, next) {
    utils.setWsHost(req, res);
    utils.setEnv(req, res);
    res.render('pdfViewer');
  });

  // 中间件，处理权益路径映射 /rightsHook/*
  app.get('/rightsHook/*', function (req, res, next) {
    const nextUrl = getRightVasCodeRoute(req.url);

    console.log('nextUrl+++', nextUrl)
    // 如果存在映射路径，则重定向到该路径
    if (nextUrl) {
      res.redirect(nextUrl);
    }
  })

  app.get('/hos*', function (req, res, next) {
    utils.setWsHost(req, res);
    utils.setEnv(req, res);
    res.render('index');
  });
};