const config = require('./config');

const setEnv = function (req, res) {
  if (!req.cookies._e !== config.ENV) {
    res.cookie('_e', config.ENV, { path: '/' });
  }
};

const setWsHost = function (req, res) {
  const { API: { WEBSOCKET_API: { host = '' } = {} } = {}, ZAAPP_CONFIG = {},BESTPAY_CONFIG={} } = config || {};
  if (!req.cookies.env_ws !== host) {
    res.cookie('env_ws', host, { path: '/' });
    res.cookie('appKey', ZAAPP_CONFIG.key, { path: '/' });
    res.cookie('appEnv', ZAAPP_CONFIG.env, { path: '/' });
    res.cookie('bestpayMerchantCode', BESTPAY_CONFIG.key, { path: '/' });

  }
};

module.exports = {
  setEnv,
  setWsHost,
};
