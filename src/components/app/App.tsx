// import * as Sentry from '@sentry/browser';
import React, { useCallback, useEffect, useState } from 'react';
import { useDispatch } from 'react-redux';
import { withRouter } from 'react-router-dom';
import { SlotMention } from 'src/components/common';
import { ThirdLoading } from 'src/components/common';
import { fetch_addWechatResource, set_showAddWechat } from 'src/store/config/action';
import { fetch_department_list } from 'src/store/department/action';
import { fetch_dictionary_list } from 'src/store/dictionary/action';
import { getChannelResourceCode, setChannelResourceCode, setIsBlackUser } from 'src/utils';
import cookies from 'src/utils/cookie';
import { setupGlobalErrorMonitor } from 'src/utils/errorHandle';
import Events from 'src/utils/events';
import { fetchJson } from 'src/utils/fetch';
import { initSeraphMonitorSDK } from 'src/utils/initMonitor';
import initTrack from 'src/utils/initTrack';
import initViewportHeight from 'src/utils/initViewportHeight';
import loadJS from 'src/utils/loadJS';
import { xflowSetLogin } from 'src/utils/pageTrack';
import { safeDecode } from 'src/utils/safeBase64';
import { Deserialize } from 'src/utils/serialization';
import session from 'src/utils/session';
import { THIRD_RESOURCE_APPLET_CODE, isFromAliER } from 'src/utils/staticData';
import storage from 'src/utils/storage';
import { fetchProvinceData } from 'src/utils/strictCode';
import { zaAppScrollCompatible } from 'src/utils/tool';
import validate from 'src/utils/validate';
import { DynamicContentMonitor } from './setupDynamicContentMonitor';
import useUnionPayLogin from './useUnionPayLogin';

initSeraphMonitorSDK();
// 初始化全局错误监控
setupGlobalErrorMonitor();

// 获取省市区
fetchProvinceData();


declare global {
  interface Window {
    /**
     * - 自定义事件不要使用这个字段发送，window._XFLOW_ 不方便统一管理
     * - 使用 pageTrack 工具库
     * - 已废弃，后续统一使用 window.__track__
     */
    _XFLOW_: any;
    __setFontSize__: any;
    eruda: any;
    _zals: any;
    _maEvt: any;
    reactHistory: any;
    wx: any;
    /**
     * - 新版 xflow 实例，来自 [@za/hfe-library-track]
     * - 自定义事件不要使用这个字段发送，window.__track__ 不方便统一管理
     * - 使用 pageTrack 工具库
     */
    __track__: any;
  }
}

declare const XFlow;

declare const WeixinJSBridge: any;

// class DatabaseConnectionError extends Error { }

// 存放不需要渠道的页面，白名单的路由不会卡渠道的校验
const _WHITE_LIST = [
  '/favicon.ico',
  '/hosopt',
  '/hosptial/intro',
  '/hospital/electronicSignature',
  '/hospital/jump-applets',
  '/hospital/open-applets',
  '/hospital/middlePage',
  '/hospital/chat/check',
];
const App = (props) => {
  const dispatch = useDispatch();
  const fetchDictionary = useCallback(() => dispatch(fetch_dictionary_list()), [dispatch]);
  const fetchDepartment = useCallback(() => dispatch(fetch_department_list()), [dispatch]);
  const fetchAddWechatResource = useCallback(() => dispatch(fetch_addWechatResource()), [dispatch]);
  const [isThrough, setIsThrough] = useState(false);
  const isCheck = window.location.pathname.indexOf('/hospital/chat/check') >= 0;

  useEffect(() => {
    console.log('-------------------这里是App层  UseEffect-------------------');
    // 十个月前的服务包活动把手机号缓存在手机里了，现在跑一次清除，后续可以删掉
    storage.remove('za_draw_mobile');
    Events.on(window, 'resize', window.__setFontSize__);
    zaAppScrollCompatible();// 兼容android手机低版本系统在直营webview问题
    const { location: { search = '', pathname = '' } } = props;
    const {
      channelContractNo = '',
      userBlackFlag = '',
      token = '',
      expire = '',
      channelSource = '',
      channelResourceCode = '',
      isReal = '',
      maskActId = '',
      maskusId = '',
      maskusSafeId = '',
      openId = '',
      ownAppletAppid = '',
      debug = '',
      observer,
      appCode = '',
      source = '',
      channelOrigin = '',
      channelCode = '',
    } = Deserialize(search);
    const oldChannelSource = cookies.get('channelSource');

    // 渠道下面增加资源位置的颗粒度，有资源位置的时候优先使用资源位置，再使用渠道
    const oldchannelResourceCode = getChannelResourceCode();
    const whiteList = _WHITE_LIST.filter((str) => pathname.includes(str));
    const jumpToThird = cookies.get('jumpToThird') || '';
    const BROWSER_ENV = cookies.get('_e') || 'prd';
    // 存储小程序传进来的 小程序appid，该字段只有互医自有的小程序才会值
    // 小程序之间webview缓存是共享的，确保第三方小程序内嵌h5时，在免登页清除之前互医小程序内缓存的 ownAppletAppid 字段
    // 如果加了这个会导致商城init时候删除了ownAppletAppid，导致返回问题
    if(['/hospital/init'].includes(pathname.toLocaleLowerCase()) && channelResourceCode !== 'SCCFBD') {
      storage.remove('ownAppletAppid');
    }
    ownAppletAppid && storage.set('ownAppletAppid', ownAppletAppid, 12 * 60);
    // 众安保险小程序设置参数
    if(appCode === 'ZABXMiniApp'){
      storage.set('ownAppletAppid', 'wx5ff1b8e8261f39e1', 12 * 60);
    }
    setPublicOAuth(oldchannelResourceCode);

    if (pathname === '/hospital/home' && !channelResourceCode && !channelSource && jumpToThird !== 'Y') {
      props.history.replace({
        pathname: '/hospital/error',
        search: 'type=no_channelSource',
      });
    }

    cookies.remove('jumpToThird');

    if (!whiteList.length && !oldChannelSource && !channelSource && !oldchannelResourceCode && !channelResourceCode) {
      props.history.replace({
        pathname: '/hospital/error',
        search: 'type=no_channelSource',
      });
    }

    // 如果连接上带了channelResourceCode，先使用channelResourceCode查出channelSource，兼容原channelSource逻辑
    // 此时需要先拿到channelSource再执行相关逻辑
    // 如果链接上带了渠道没带channelResourceCode，则需要清除缓存中的channelResourceCode，使用当前链接上的channelSource
    (async () => {
      if (!isCheck && ((channelResourceCode !== oldchannelResourceCode && channelResourceCode) || (oldChannelSource !== channelSource && channelSource) || token !== '')) {
        console.log('----getAccountInfo----');
        cookies.remove('za_token');
        cookies.remove('za_long_token');
        cookies.remove('openId');
        token && cookies.set('za_token', token, expire || 12 * 60);
        token && cookies.set('za_long_token', token, 12 * 60);
        // 这里校验下token的有效性，如果token无效，则清除该token。避免在进入page组件时，因为存在无效token而进入实名页面跳转登录页，登录成功进入实名页的问题（实际实名过了）
        const res = await fetchJson({
          type: 'GET',
          url: '/api/api/v1/patient/user/getAccountInfo',
          needRedirectLogin: false,
        });
        if (res && res.code === '20002') {
          console.log('token无效,清掉token');
          cookies.remove('za_token');
          cookies.remove('za_long_token');
        }
      }
      openId && cookies.set('openId', openId);
      isCheck && token && cookies.set('za_token', token);
      isReal && cookies.set('isRealAuth', isReal);
      maskusId && cookies.set('maskusId', decodeURIComponent(maskusId));
      maskActId && cookies.set('maskActId', safeDecode(maskActId));
      maskusSafeId && cookies.set('maskusSafeId', safeDecode(maskusSafeId));
      source && session.setSessionStorage('source', source);
      setIsBlackUser(userBlackFlag === '1' ? true : false);


      // 设置全局history变量
      window.reactHistory = props.history;

      // 保存健康险串联标识（每次新会话开始，必定更新）。
      // 有些链路会出现打开新 tab 的场景。比如支付后回调链接，会导致session 丢失
      if (channelOrigin) {
        session.setSessionStorage('channelOrigin', channelOrigin);
      }

      // 众安健康细分渠道，不落库可以放在xflow和六翼的上报
      if (channelCode) {
        session.setSessionStorage('channelCode', channelCode);
      }

      if (channelResourceCode) {
        setChannelResourceCode(channelResourceCode);
        cookies.remove('channelSource');
        isFromAliER(channelResourceCode) && cookies.set('channelContractNo', decodeURIComponent(channelContractNo));
      } else if (channelSource) {
        cookies.set('channelSource', channelSource, 60 * 24 * 9999);

        const res = await fetchJson({
          type: 'GET',
          url: `/api/api/v1/patient/channel/queryChannelResourceBySource?channelSource=${channelSource}`,
          isloading: true,
          needLogin: false,
        });
        if (res && res.code === '0') {
          const { result = {} } = res;
          isFromAliER(result.resourceCode) && cookies.set('channelContractNo', decodeURIComponent(channelContractNo));
          setChannelResourceCode(result.resourceCode);
        }
      }

      specialChannelSkins();
      // 初始化ilog埋点
      const ilog = 'https://xflowcore.zhongan.com/sdk/dist/js/v0.0.1/ilog.js?id=z7oeetwqzs6jwvec&setting=true';
      loadJS(ilog, () => {
        try {
          initTrack();
          xflowSetLogin(maskActId);
        } catch (e) {
          console.log(e);
        }
      });

      setIsThrough(true);
    })();

    const eruda = 'https://cdn-health.zhongan.com/library/eruda/3.4.1/eruda.js';
    // 增加一个线上打开调试模式的方法，便于排查问题)
    ((BROWSER_ENV !== 'prd' && BROWSER_ENV !== 'dev') || debug === '1') && loadJS(eruda, () => {
      window.eruda.init();
    });

    const mutationObserver = '//static.za-doctor.com/common/mutationObserver_v_1.0.js';
    // 增加一个线上打开调试模式的方法，便于排查问题)
    (observer === '1') && loadJS(mutationObserver,() => {});

    // 旧版隐藏分享功能，对接公众号之后就可以去掉了，这个方法有很多兼容性bug，暂时用着
    document.addEventListener('WeixinJSBridgeReady', function onBridgeReady() {
      WeixinJSBridge.call('hideOptionMenu');
    });

    // 请求后端的枚举字段
    fetchDictionary();
    // 请求部门列表
    fetchDepartment();
    // 统一处理请求是否展示加微信
    const thirdResourceApplet = storage.get('thirdResourceApplet');
    if (validate.isFromMiniApplet() && ![THIRD_RESOURCE_APPLET_CODE.ZABX].includes(thirdResourceApplet)) {
      dispatch(set_showAddWechat());
    } else {
      fetchAddWechatResource();
    }
  }, []);

  useEffect(() => {
    window.addEventListener('pageshow', () => {
      ThirdLoading.hide();
    });
    // 处理跳转第三方隐藏loading
    document.addEventListener('visibilitychange', () => {
      ThirdLoading.hide();
    });
    return () => {
      ThirdLoading.hide();
    };
  }, []);
  const specialChannelSkins = () => {
    const currentChannelSource = cookies.get('channelSource');
    const bodyNode = document.querySelector('body');
    if (currentChannelSource == 'CSN26000183' && bodyNode) { // 指定支付宝渠道
      bodyNode.classList && bodyNode.classList.add && bodyNode.classList.add('alipay');
    }
  };
  const setPublicOAuth = (oldchannelResourceCode) => {
    // 互医公众号授权
    const {
      location: { search },
    } = props;
    const { from, oauth, channelResourceCode } = Deserialize(search);
    if (oauth) {
      cookies.set('publicOauth', true);
    }
    const publicOauth = cookies.get('publicOauth');
    const getChannelResourceCode = oldchannelResourceCode || channelResourceCode;
    if (from === 'public' && !publicOauth && getChannelResourceCode) {
      window.location.href = `${window.location.origin}/hospital/oauth?url=${encodeURIComponent(window.location.href)}&channelResouceCode=${getChannelResourceCode}`;
    }
  };

  useUnionPayLogin();

  useEffect(() => {
    const monitor = DynamicContentMonitor.getInstance(5000);

    // 路由变化时启动监控
    monitor.start();

    // 组件卸载时不需要清理，因为我们使用的是单例
    return () => {};
  }, [location.pathname]);

  useEffect(() => {
    initViewportHeight();

    return () => {
      // 应用卸载时重置单例
      DynamicContentMonitor.reset();
    };
  }, []);


  return (
    <div className='app-container'>
      <SlotMention></SlotMention>
      {isThrough && props.children}
    </div>
  );
};

export default withRouter(App);
