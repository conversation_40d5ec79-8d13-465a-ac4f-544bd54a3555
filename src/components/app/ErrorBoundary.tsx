// errorBoundary.tsx
import { Component } from 'react';
import { sendMonitorEvent } from 'src/utils/pageTrack';

interface ErrorBoundaryState {
  hasError: boolean;
  error?: Error;
  info?: React.ErrorInfo;
}

class ErrorBoundary extends Component<{ children: React.ReactNode }, ErrorBoundaryState> {
  constructor(props: { children: React.ReactNode }) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error) {
    // 更新状态以显示错误界面
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    // 捕获组件树中的错误并发送监控事件
    sendMonitorEvent(
      'REACT_COMPONENT_ERROR',
      'error',
      'React组件错误',
      {
        componentName: errorInfo.componentStack?.split('\n')[0] || 'UnknownComponent',
        message: error.message,
        stack: error.stack,
        componentStack: errorInfo.componentStack,
      }
    );
  }

  render() {
    return this.props.children;
  }
}

export default ErrorBoundary;
