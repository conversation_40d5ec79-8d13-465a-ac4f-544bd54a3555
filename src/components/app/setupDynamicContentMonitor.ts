// whiteScreenMonitor.ts

import { sendMonitorEvent } from 'src/utils/pageTrack';

/**
 * 动态内容白屏监控
 *
 * @param timeout 检测超时时间（毫秒）
 */
export const DynamicContentMonitor = (() => {
  let instance: any = null;
  let isFirstLoad = true;
  let isMonitoring = false;
  let observer: MutationObserver | null = null;
  let rafId: number | null = null;
  let timeoutId: number | null = null;
  let delayTimeoutId: number | null = null;
  let retryCount = 0;
  let frameCount = 0;

  const createInstance = (timeout = 3000, initialDelay = 100) => {
    const MAX_RETRIES = 3;
    const RAF_THROTTLE = 5;

    // 开始监控
    const startMonitoring = (isRouteChange = false) => {
      // 如果是首次加载且是路由变化触发，则跳过
      if (isFirstLoad && isRouteChange) {
        return;
      }

      // 如果是首次加载且不是路由变化触发，则标记首次加载已完成
      if (isFirstLoad && !isRouteChange) {
        isFirstLoad = false;
      }

      if (isMonitoring || observer) {
        return;
      }

      // 添加延迟，等待组件渲染
      delayTimeoutId = window.setTimeout(() => {
        isMonitoring = true;
        let contentLoaded = false;

        // 查找动态内容的根节点
        const findDynamicContentRoot = () => {
          try {
            const appContainer = document.querySelector('.app-container');
            if (!appContainer) {
              return null;
            }
            if (!appContainer.children[1]) {
              return null;
            }
            return appContainer.children[1] as HTMLElement;
          } catch (error) {
            return null;
          }
        };

        const root = findDynamicContentRoot();

        if (!root) {
          // 如果未找到根节点，尝试重试几次
          if (retryCount < MAX_RETRIES) {
            retryCount++;
            isMonitoring = false;
            // 递增延迟重试
            delayTimeoutId = window.setTimeout(() => startMonitoring(isRouteChange), initialDelay * retryCount);
            return;
          }

          // 多次重试后仍未找到，才触发白屏事件
          console.log('WHITE_SCREEN---未找到动态内容根节点');
          sendMonitorEvent(
            'WHITE_SCREEN',
            'error',
            '白屏监控',
            {
              message: '未找到动态内容根节点',
              timeout,
              domInfo: {
                childrenCount: 0,
                offsetHeight: 0,
              },
              isRouteChange,
            }
          );
          isMonitoring = false;
          return;
        }

        // 重置重试计数
        retryCount = 0;

        // 检查内容是否已加载的通用函数
        const checkContentLoaded = () => {
          if (contentLoaded) {
            return true;
          }

          if (root && root.children.length > 0 && root.offsetHeight > 0) {
            console.log('DOM 加载完毕');
            contentLoaded = true;
            cleanupMonitoring();
            return true;
          }

          return false;
        };

        // 清理监控资源的函数
        const cleanupMonitoring = () => {
          if (observer) {
            observer.disconnect();
            observer = null;
          }

          if (rafId !== null) {
            cancelAnimationFrame(rafId);
            rafId = null;
          }

          if (timeoutId !== null) {
            clearTimeout(timeoutId);
            timeoutId = null;
          }

          isMonitoring = false;
        };

        // 判断是否为低端设备
        const isLowEndDevice = () => {
          const cores = navigator.hardwareConcurrency || 0;
          return cores <= 4;
        };

        // 使用 MutationObserver 监控 DOM 变化
        observer = new MutationObserver(() => {
          checkContentLoaded();
        });

        // 添加一个备选的监控函数
        const monitorContentWithRAF = () => {
          if (checkContentLoaded()) {
            return;
          }

          // 在低端设备上降低检查频率
          frameCount++;
          const isLowEnd = isLowEndDevice();
          if (isLowEnd && frameCount % RAF_THROTTLE !== 0) {
            rafId = requestAnimationFrame(monitorContentWithRAF);
            return;
          }

          // 继续监控
          rafId = requestAnimationFrame(monitorContentWithRAF);
        };

        // 设置 MutationObserver 配置并开始观察
        const config = { childList: true, subtree: true };
        observer.observe(root, config);

        // 启动 requestAnimationFrame 监控
        rafId = requestAnimationFrame(monitorContentWithRAF);

        // 设置超时处理
        timeoutId = window.setTimeout(() => {
          if (!contentLoaded) {
            console.log('WHITE_SCREEN---动态内容区域未加载');
            sendMonitorEvent(
              'WHITE_SCREEN',
              'error',
              '白屏监控',
              {
                message: '动态内容区域未加载',
                timeout,
                domInfo: {
                  childrenCount: root.children.length,
                  offsetHeight: root.offsetHeight,
                },
                isRouteChange,
              }
            );
          }
          cleanupMonitoring();
        }, timeout);
      }, initialDelay);
    };

    // 清理函数
    const cleanup = () => {
      if (delayTimeoutId !== null) {
        clearTimeout(delayTimeoutId);
        delayTimeoutId = null;
      }

      if (observer) {
        observer.disconnect();
        observer = null;
      }

      if (rafId !== null) {
        cancelAnimationFrame(rafId);
        rafId = null;
      }

      if (timeoutId !== null) {
        clearTimeout(timeoutId);
        timeoutId = null;
      }

      window.removeEventListener('load', loadHandler);
      isMonitoring = false;
      retryCount = 0;
    };

    // 页面加载完成后开始首次监控
    const loadHandler = () => startMonitoring(false);
    window.addEventListener('load', loadHandler);

    // 返回包含启动和清理方法的对象
    return {
      start: () => startMonitoring(true), // 路由变化时调用，传入 true
      cleanup,
    };
  };

  return {
    getInstance: (timeout?: number, initialDelay?: number) => {
      if (!instance) {
        instance = createInstance(timeout, initialDelay);
      }
      return instance;
    },
    reset: () => {
      if (instance) {
        instance.cleanup();
        instance = null;
        isFirstLoad = true;
        isMonitoring = false;
        observer = null;
        rafId = null;
        timeoutId = null;
        delayTimeoutId = null;
        retryCount = 0;
        frameCount = 0;
      }
    },
  };
})();
