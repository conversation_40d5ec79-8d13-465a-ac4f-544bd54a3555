import { useEffect } from 'react';
import cookies from 'src/utils/cookie';
import { fetchJson } from 'src/utils/fetch';
import loadJS from 'src/utils/loadJS';
import { processLoginData } from 'src/utils/serialization';
import validate from 'src/utils/validate';
declare global {
  interface Window {
    upsdk: any;
  }
}

const useUnionPayLogin = () => {

  // const dispatch = useDispatch();
  // const bindDoctor = () => dispatch(bind_doctor());

  useEffect(() => {
    if(validate.isFromUnionPay()){
      cookies.remove('za_token');
      cookies.remove('za_long_token');
      cookies.remove('openId');
      cookies.remove('isRealAuth');

      // 初始化ilog埋点
      const upsdk = 'https://open.95516.com/s/open/js/upsdk.js';
      loadJS(upsdk, () => {
        window.upsdk.pluginReady(() => {
          window.upsdk.setTitleStyle({
            navBackgroundColor: '0x8FFFFFFF',
            backBtnVisible: '1', // 显示返回按钮
            appletStyle: 'black', // 可选，black-黑色主题，white-白色主题。
            appletTitleBarVisible: '1', // 可选，标题栏是否显示。’0’不显示，’ 1’显示，默认显示
            appletTitleGradientOrient: 'top', // 可选，渐变色方向，支持 top、 bottom、left、right
            appletTitleGradientStartColor: '0x8FFFFFFF', // 渐变起始颜色
            appletTitleGradientEndColor: '0x8FFFFFFF', // 渐变结束颜色
          });

          window.upsdk.appletAuth({
            success(data) {
              console.log('------------- 获取到了云闪付的code---------------');
              const { code = '' } = data;
              fetchJson({
                url: '/api/api/v1/patient/user/getThirdOpenId',
                type: 'POST',
                isloading: false,
                data: {
                  channelSource: cookies.get('channelSource') || '',
                  channelResourceCode: cookies.get('channelResourceCode') || '',
                  code,
                },
                success: (res) => {
                  console.log('-------------getThirdOpenId---------------');
                  const { code = '0', result: { openId = '', token = '', backendToken = '' } = {} } = res;
                  cookies.set('union_openId', openId);
                  console.log(res);
                  if (code === '0') {
                    fetchJson({
                      url: '/api/api/v1/patient/user/bindThirdUser',
                      type: 'POST',
                      isloading: false,
                      needLogin: false,
                      data: {
                        channelSource: cookies.get('channelSource') || '',
                        channelResourceCode: cookies.get('channelResourceCode') || '',
                        accessToken: token,
                        backendToken,
                        openId,
                        registerFlag: false,
                      },
                      success: (res) => {
                        console.log('-------------bindthirdUser---------------');
                        console.log(res);
                        const { code = '0', result } = res;
                        if (code === '0') {
                          processLoginData(result);
                          // bindDoctor();
                        }
                      },
                      error: (res) => {
                        console.log(JSON.stringify(res));
                        console.log('-------------bindthirdUser出错---------------');
                      },
                    });
                  }
                },
                error: (res) => {
                  console.log(res);
                  console.log('-------------getThirdOpenId 出错---------------');
                },
              });
            },
            fail(error) {
              console.log(error);
            },
          });
        });
      });
    }

  }, []);

  return null;
};

export default useUnionPayLogin;
