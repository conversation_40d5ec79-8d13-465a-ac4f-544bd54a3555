import React from 'react';
import Empty from './imgs/empty.png';
import './index.scss';

interface IProps {
  invalidImg: string;
  invalidText: string;
}
const DefaultProps = {
  invalidImg: Empty,
  invalidText: '活动已到期，敬请期待下次活动',
};

/**
 * @description 展示无效（各种无效，各种空状态）
 * @param props
 * @returns
 */
const ActivityInvalid = (props: Partial<IProps>) => {
  const { invalidImg, invalidText } = props;
  return (
    <div className='activity-invalid'>
      <img className='activity-invalid__icon' src={invalidImg} alt='Activity Invalid' />
      <div className='activity-invalid__info'>{invalidText}</div>
    </div>
  );
};

ActivityInvalid.defaultProps = DefaultProps;

export default ActivityInvalid;
