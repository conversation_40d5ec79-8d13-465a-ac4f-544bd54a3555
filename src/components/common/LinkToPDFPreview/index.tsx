import React from 'react';
import { withRouter, RouteComponentProps } from 'react-router-dom';
import { validate } from 'src/utils';

const LinkToPDFPreview = withRouter<{ fileName: string } & RouteComponentProps, any>(
  function ({ fileName, history, children }) {
    const linkTo = () => {
      const pdfPrefix = 'https://cdn-qcloud.zhongan.com/a00000/za_hospital/unique/';

      const url = pdfPrefix + fileName;
      if (validate.isIos()) {
        window.location.href = url;
      } else {
        history.push(`/hospital/pdf?url=${encodeURIComponent(url)}`);
      }
    };

    return (
      <div onClick={linkTo} style={{ display: 'inline' }}>
        {children}
      </div>
    );
  }
);
export default LinkToPDFPreview
