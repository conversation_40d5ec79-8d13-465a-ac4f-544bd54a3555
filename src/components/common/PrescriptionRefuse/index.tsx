import React, { useState } from 'react';
import { xflowPushEvent } from 'src/utils/pageTrack';
import { storage } from 'src/utils';
import './index.scss';

import Waring_icon from './images/waring_icon.webp';

/**
 * 处方拒绝组件
 *
 * 当处方被拒绝时显示拒绝原因和返回按钮。
 * 在商城-先方后药流程中，点击返回按钮会跳转到存储在本地的失败URL或默认订单列表页。
 *
 * @param {Object} props - 组件属性
 * @param {string} props.inquiryNo - 问诊No，用于获取对应的跳转链接信息
 * @returns {JSX.Element} 处方拒绝组件
 */
export default function PrescriptionRefuse(props: any) {
  const { inquiryNo, rejectReason } = props;
  const [btnClicked, setBtnClicked] = useState(false);
  // 商城-先方后药流程-透传的跳转链接
  const prescriptionJumpLinkInfo = storage.get(`prescriptionJumpLinkInfo_${inquiryNo}`);

  /**
   * 处理处方跳转链接信息的函数
   * 
   * 尝试解析处方跳转链接信息，并根据解析结果重定向页面。
   * 如果解析成功，将跳转到预授权页面，并携带相关参数；
   * 如果解析失败或出现错误，将默认跳转到我的问诊页面。
   * 
   * @returns {void}
   */
  const handleRefuseJump = () => {
    try {
      const { failUrl } = JSON.parse(prescriptionJumpLinkInfo) || {};
      if (!failUrl) {
        throw new Error('获取商城 failUrl 失败');
      }
      window.location.replace(`/hospital/preauth?partnerCode=HY_MALL&businessType=custom&target=${failUrl}&decode=Y`)
    } catch (error) {
      xflowPushEvent(['click', 'hy_mall_failUrl_error', '获取商城 failUrl 失败', { ZAHLWYY_CLICK_CONTENT:  `${inquiryNo}_获取商城 failUrl 失败`}]);
      window.location.replace('/hospital/myinquiry');
    }
  };

  const btnTouch = (status) => {
    setBtnClicked(status);
  };

  return (
    <div className='prescription-refuse'>
      <div className='prescription-refuse__reason'>
        <img className='prescription-refuse__reason-icon' src={Waring_icon} alt='' />
        {rejectReason}
      </div>
      <div
        className={`prescription-refuse__btn ${btnClicked ? 'active' : ''}`}
        onClick={handleRefuseJump}
        onTouchStart={() => btnTouch(true)}
        onTouchEnd={() => btnTouch(false)}
      >
        点击返回
      </div>
    </div>
  );
}
