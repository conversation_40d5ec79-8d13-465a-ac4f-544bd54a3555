.select-use-city {
  background: #f4f4f4;
  overflow: hidden;

  .search-bar {
    margin: 14px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    height: 32px;
    font-size: 14px;
    border-radius: 16px;
    overflow: hidden;
    background: #fff;

    &__input {
      width: 100%;
      padding: 0 25px;
      text-align: center;
      box-sizing: border-box;
      border: 0;
    }

    &__tip {
      position: absolute;
      display: flex;
      align-items: center;
      justify-content: center;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      font-size: 13px;
      color: #999;
      transition: all;

      &.neend-input {
        width: 18px;
        left: 7px;

        .text {
          display: none;
        }
      }
    }

    .search-icon {
      width: 18px;
      height: 18px;
    }

    .text {
      margin-left: 6px;
    }
  }

  .split-text {
    margin: 8px 0 4px 8px;
    font-size: 12px;
    color: #666;
  }

  .city-cube-wrap {
    display: flex;
    justify-content: flex-start;
    flex-wrap: wrap;
    margin-left: 14px;
    font-size: 14px;
  }

  .city-cube {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 106px;
    height: 36px;
    font-size: 14px;
    margin-bottom: 8px;
    border: 1px solid #f4f4f4;
    border-radius: 4px;
    background: #fff;

    &:not(:nth-child(3n + 1)) {
      margin-left: 8px;
    }

    &.current-cube {
      width: auto;
      min-width: 106px;
      padding: 0 10px;
      box-sizing: border-box;
    }
  }

  .city-list {
    &__items {
      padding-left: 16px;
      background: #fff;

      .item {
        display: flex;
        align-items: center;
        height: 45px;
        font-size: 14px;
        color: #000;
        border-bottom: 1px solid #f4f4f4;
      }
    }
  }

  .spell-list {
    position: fixed;
    top: 50%;
    right: 0;
    width: 30px;
    white-space: nowrap;
    transform: translateY(-50%);
    text-align: right;
    z-index: 99;

    &__item {
      padding: 1px 9px 1px 0;
      font-size: 10px;
      color: #365c95;
      transform: scale(0.8);
      display: block;
    }
  }
}
