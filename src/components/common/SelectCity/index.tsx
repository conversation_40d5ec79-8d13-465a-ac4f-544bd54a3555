import classNames from 'classnames';
import React, { useState, useEffect, useRef, useCallback } from 'react';
import {Icon} from 'zarm';
import './index.scss';
import { useDispatch } from 'react-redux';
import UseDebounceFn from 'src/hooks/useDebounceFn';
import {select_city, select_clinic} from 'src/store/clinic/action';
import { storage } from 'src/utils';
import { fetchJson } from 'src/utils';

interface CityItemModel {
  city: string,
  pinyin: string,
}
interface sortCityItem {
  spell: string;
  citys: CityItemModel[];
}
const FirstSpellMap = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
const hotCityList = ['北京市', '上海市', '杭州市', '深圳市', '广州市'];

const SelectCity = (props) => {
  const [keyword, setKeyword] = useState('');
  const [sortCitys, setSortCitys]: any = useState([]);
  const [needInput, toggleNeedInput] = useState(false);
  const dispatch = useDispatch();
  const originCityData = useRef([] as CityItemModel[]);
  const selectCity = (city) => dispatch(select_city(city));
  const selectClinic = (clinic) => dispatch(select_clinic(clinic));
  const handleFilter = useCallback((val): void => {
    const regxp = new RegExp(`^${val}`, 'i');
    const filterData = originCityData.current.filter(
      (city) => city.city.search(val) === 0 || city.pinyin.search(regxp) === 0
    );
    setSortCitys(formatSpellCityData(filterData));
  }, []);
  const run = UseDebounceFn(handleFilter);

  useEffect(() => {
    fetchJson({
      url: '/api/api/v1/patient/teethCleanService/getAddressList',
      type: 'POST',
    }).then((res) => {
      if (res.code === '0') {
        originCityData.current = res.result;
        setSortCitys(formatSpellCityData(originCityData.current));
      }
    });
  }, []);
  useEffect(() => {
    run(keyword);
  }, [keyword, run]);
  const handleInput = (e) => {
    console.log('value', e.target.value);
    setKeyword(e.target.value);
  };

  const handleSelectCity = (city): void => {
    storage.set('jz_select_city', city);
    selectCity(city);
    selectClinic({});
    window.reactHistory.goBack();
  };
  const formatSpellCityData = (
    cityList: CityItemModel[]
  ): sortCityItem[] => {
    const sortCityMap = {} as { [key: string]: CityItemModel[] };
    FirstSpellMap.split('').forEach((spell) => {
      sortCityMap[spell] = cityList.filter(
        (city) => city.pinyin[0].toUpperCase() === spell
      );
    });

    const filterCityData = Object.keys(sortCityMap)
      .map((key) => ({
        spell: key,
        citys: sortCityMap[key],
      }))
    // 过滤掉字母对应城市为空的数据
      .filter(
        (sortCityItem) => sortCityItem.citys && sortCityItem.citys.length > 0
      );
    return filterCityData;
  };
  const jumpAnchor = (spell) => {
    const element = document.querySelector(`#city-spell-${spell}`);
    element?.scrollIntoView();
  };
  const currentCity = storage.get('jz_select_city');
  return (
    <div className='select-use-city' id='select-use-city'>
      <div id='static-wrap'>
        <div className='search-bar'>
          {
            needInput && (
              <input
                value={keyword}
                onChange={handleInput}
                onBlur={(): void => toggleNeedInput(!!keyword)}
                className='search-bar__input'
                autoFocus
              />
            )
          }
          <div
            className={classNames(
              'search-bar__tip',
              needInput && 'neend-input'
            )}
            onClick={(): void => toggleNeedInput(true)}
          >
            <Icon type='search' />
            <span className='text'>输入城市名、拼音或首字母查询</span>
          </div>
        </div>
        {
          currentCity && (
            <div className='current-location'>
              <div className='split-text'>你所在地区</div>
              <div className='city-cube-wrap'>
                <div
                  className='city-cube current-cube'
                  onClick={(): void => handleSelectCity(currentCity)}
                >
                  {currentCity}
                </div>
              </div>
            </div>
          )
        }

        <div className='hot-location'>
          <div className='split-text' id='city-spell-hot'>
                        热门城市
          </div>
          <div className='city-cube-wrap'>
            {hotCityList.map((city) => (
              <div
                className='city-cube'
                key={city}
                onClick={(): void => handleSelectCity(city)}
              >
                {city}
              </div>
            ))}
          </div>
        </div>

      </div>
      <div className='city-list'>
        {(sortCitys || []).map((sortCityItem) => (
          <div key={sortCityItem.spell}>
            <div
              className='split-text'
              id={`city-spell-${sortCityItem.spell}`}
            >
              {sortCityItem.spell}
            </div>
            <div className='city-list__items'>
              {sortCityItem.citys &&
                                sortCityItem.citys.map((city) => (
                                  <div
                                    key={city.city}
                                    className='item'
                                    onClick={(): void => handleSelectCity(city.city)}
                                  >
                                    {city.city}
                                  </div>
                                ))}
            </div>
          </div>
        ))}
      </div>
      <div className='spell-list'>
        <div
          className='spell-list__item'
          onClick={() => {
            jumpAnchor('hot');
          }}
        >
                    热门
        </div>
        {sortCitys &&
                    sortCitys.map((sortCityItem) => (
                      <div
                        className='spell-list__item'
                        key={sortCityItem.spell}
                        onClick={() => {
                          jumpAnchor(sortCityItem.spell);
                        }}
                      >
                        {sortCityItem.spell}
                      </div>
                    ))}
      </div>
    </div>
  );
};
export default SelectCity;
