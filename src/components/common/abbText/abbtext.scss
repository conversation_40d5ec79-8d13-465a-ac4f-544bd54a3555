@import "src/style/index";

.addtext_component {
  .item_text {
    position: relative;
    font-size: r(13);
    margin: r(4) 0 r(10);

    .slice {
      @include line(5);

      .btn {
        position: absolute;
        display: inline-block;
        width: r(80);
        background: linear-gradient(90deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 1) 30%, rgba(255, 255, 255, 1) 100%);
        right: r(6);
        bottom: r(0);
        color: #11cd89;
        text-align: right;
        padding-right: r(16);
        line-height: r(21);
        font-size: r(13);

        .icon {
          position: absolute;
          right: r(1);
          bottom: r(4);
          display: inline-block;
          width: r(16);
          height: r(10);
          transform: rotate(90deg);
        }
      }
    }

    .normal {
      .btn {
        background: linear-gradient(90deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 1) 50%, rgba(255, 255, 255, 1) 100%);
        color: #11cd89;
        text-align: right;
        padding-right: r(16);
        line-height: r(21);
        font-size: r(13);

        .icon {
          position: absolute;
          right: r(0);
          bottom: r(4);
          display: inline-block;
          width: r(16);
          height: r(10);
          transform: rotate(-90deg);
        }
      }
    }
  }

  p {
    color: #999;
    line-height: r(21);
    font-size: r(13);
  }
}
