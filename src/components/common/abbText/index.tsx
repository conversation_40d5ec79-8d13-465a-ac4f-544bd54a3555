import React, { useState, useCallback, useEffect } from 'react';
import { SvgIcon } from 'src/components/common';
import classnames from 'classnames';
import './abbtext.scss';

import arrow_svg from 'src/svgs/sprite-arrow.svg';

interface AbbTextProps {
  showPart: any;
  prefixCls?: string;
  domChange?: any;
  index?: number | string;
  needSlice?: boolean;
}

const AbbText = (props: AbbTextProps) => {
  const [show, setShow] = useState(false);
  const [height, setHeight] = useState(0);
  const { showPart, prefixCls, domChange, index, needSlice = true } = props;

  const measuredRef = useCallback(node => {
    setTimeout(() => {
      if (node !== null) {
        setHeight(node.getBoundingClientRect().height);
      }
    }, 200);
  }, [show]);

  useEffect(() => {
    typeof domChange === 'function' && domChange(index, height);
  }, [height]);

  return (
    <div ref={measuredRef} className={classnames('addtext_component', prefixCls)}>
      {needSlice ? <div className="item_text">
        {!show ? <div className='slice'>
          {showPart}
          {!show && <div className='btn' onClick={() => setShow(true)}>更多
            <SvgIcon className='icon' src={arrow_svg} />
          </div>}
        </div> : <div className='normal'>
            {showPart}
            <div className='btn' onClick={() => setShow(false)}>收起
            <SvgIcon className='icon' src={arrow_svg} />
            </div>
          </div>}
      </div> : <div className="item_text">{showPart}</div>}
    </div >
  );
}

export default AbbText;