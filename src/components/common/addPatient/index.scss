@import "src/style/index";
$prefixCls: 'addPatient-comp';

.#{$prefixCls} {
  background: #f5f5f5;

  // padding-bottom: r(80);

  &__health {
    min-height: 50vh;
    max-height: 70vh;
    padding-top: r(50);
    box-sizing: border-box;
    background: #fff;
    border-radius: r(8) r(8) 0 0;
    overflow-y: scroll;

    .patient_health_header {
      position: absolute;
      left: 0;
      top: 0;
      width: 100%;
      height: r(50);
      line-height: r(50);
      background: #fff;
      border-bottom: r(1) solid var(--border-disabled);
      font-size: r(16);
      font-weight: bold;
      color: #1e1e1e;
      text-align: center;
      z-index: 1;
      border-radius: r(8) r(8) 0 0;
    }

    .patient_health_close {
      position: absolute;
      right: r(15);
      top: r(15);
      font-size: r(20);
      color: #d8d8d8;
    }

    .patient_health_btn {
      border-radius: 0;
      box-shadow: none;
    }
  }

  &__class {
    // padding: r(15) 0 0 r(15);
    margin: 0 r(20);
    padding: r(15) 0;
    border-top: r(1) solid var(--border-disabled);
    border-radius: 0;

    &.no-border {
      border-top: none;
    }

    .class_title {
      font-weight: bold;
      font-size: r(16);
      // margin-right: r(15);
      // padding-bottom: r(15);
      // border-bottom: r(1) solid var(--border-disabled);
    }

    .has_class {
      @include display-flex;

      padding: r(10) 0 0 0;

      .tag {
        margin: 0 r(15) 0 0;
      }
    }

    .tag_list {
      @include display-flex;

      flex-wrap: wrap;
      margin-top: r(10);

      .tag.active {
        border-color: #fdf4e9;
        background: #fdf4e9;
        color: #ff8c40;
      }
    }

    .tag {
      position: relative;
      min-width: r(60);
      height: r(29);
      padding: r(4) r(10);
      margin: 0 r(15) r(10) 0;
      border-radius: r(4);
      font-size: r(14);
      text-align: center;
      background-color: var(--theme-secondary);
      border-color: transparent;
      color: var(--theme-primary);
      transition-property: background-color, color, border-color;
      transition-duration: 0.3s;

      &.detail {
        border: 1px solid #eee;
        background: #fff;
        color: #999;
      }

      &.active {
        transition-property: background-color, color, border-color;
        transition-duration: 0.3s;
        background: var(--theme-success);
        color: #fff;
      }

      .active_img {
        position: absolute;
        right: 0;
        bottom: 0;
        width: r(17);
        height: r(18);
      }
    }

    .additional_input {
      border-top: r(1) solid var(--border-disabled);

      .title {
        font-size: r(16);
        color: #464646;
        padding: r(11) 0 0 0;
      }

      .input_wrapper {
        .za-input.za-input--textarea {
          padding-top: r(11);
        }

        textarea {
          background: #f8f8f8;
          border-radius: r(4);
          padding: r(11);
          margin-bottom: r(5);
        }
      }
    }
  }

  &__upload {
    padding: r(15) r(15) r(20) r(15);

    .header {
      @include display-flex;
      @include justify-content(space-between);

      border-bottom: r(1) solid var(--border-disabled);
      padding-bottom: r(15);

      .title {
        // font-weight: bold;
        color: #999;
        font-size: r(14);
      }

      .length {
        color: #999;
        font-size: r(14);
      }
    }

    .photo-wrapper {
      // margin-top: r(12);
      @include display-flex;
      @include align-items(center);
      @include justify-content(flex-start);
      @include flex-wrap(wrap);

      padding: r(6) 0;

      .upload-item {
        padding: r(16) r(6) 0 0;
        position: relative;

        .delete-icon {
          position: absolute;
          top: r(8);
          right: r(-2);
          font-size: r(18);
        }

        .upload-photo {
          outline: none;
          width: r(74);
          height: r(74);
          border-radius: r(4);
          background: #f2f2f2;
          position: relative;
          display: inline-block;
          border: r(1) solid rgba(55, 198, 151, 1);
        }
      }

      .upload-button {
        position: relative;
        width: r(74);
        height: r(74);

        .upload-cover {
          width: r(74);
          height: r(74);
          position: absolute;
          left: 0;
          top: 0;
        }

        input[type="file"] {
          position: absolute;
          height: inherit;
          width: inherit;
          left: 0;
          top: 0;
          opacity: 0;
          z-index: 2;
        }
      }
    }
  }

  &.pages-bgcolor {
    // background: #f5f7fe;
    background: linear-gradient(180deg, rgba(255, 255, 255, 1) 0%, #f5f7fe 100%);
    min-height: 100vh;
  }

  &__guide {
    padding: r(15) r(15) r(13);
    line-height: r(22);
    color: rgba(0, 0, 0, 0.6);
    font-size: r(13);

    h3 {
      font-size: r(20);
      color: rgba(0, 0, 0, 1);
      font-weight: 600;
    }
  }

  &__card {
    &.card_component {
      &:after {
        display: none;
      }
    }

    .headers {
      padding: r(17) r(15);
      @include display-flex;
      @include justify-content(space-between);

      h4 {
        font-size: r(16);
        font-weight: 600;
      }

      .extra {
        color: rgba(0, 0, 0, 0.5);
      }
    }

    .tips {
      padding: 0 r(15);
      font-size: r(12);
      color: rgba(0, 0, 0, 0.3);
      margin-top: r(-8);
    }
  }

  &__cell {
    .za-cell__title {
      color: rgba(0, 0, 0, 0.8);
    }

    input[type=text] {
      @include placeholder;

      &::placeholder {
        font-size: r(13);
      }
    }

    &.custom p,
    .za-select--placeholder .za-select__value {
      font-size: r(13);
      color: #cecece;
    }

    &.custom {
      .text {
        color: var(--color-text);
      }
    }

    &.input {
      .za-cell__title.za-cell__title--label {
        width: r(80);
      }
    }

    &.select {
      .za-cell__title.za-cell__title--label {
        width: r(120);
      }
    }

    &.special {
      .za-cell__title.za-cell__title--label {
        width: r(80);
      }
    }

    &.za-cell {
      &::after {
        display: none;
      }

      & + .za-cell {
        &::after {
          left: 0;
          display: block;
          border-color: #f0f0f0;
        }
      }
    }

    &.required {
      .za-cell__title {
        &::before {
          content: "*";
          color: #f00;
          margin-right: 2px;
          line-height: 1.5;
        }
      }
    }

    &.cell-security {
      padding: r(10) 0;

      .za-cell__title {
        padding-top: 3px;
      }
    }
  }

  &__cell-relation {
    margin-left: r(-6);

    .security-button {
      margin-bottom: r(6);
    }

    .button {
      display: inline-block;
      min-width: r(47);
      margin-right: r(6);
      padding: r(3) r(12);
      background: rgba(236, 145, 49, 0.06);
      color: rgba(236, 145, 49, 1);
      font-size: r(12);
      border-radius: r(26);
      border: 1px solid rgba(236, 145, 49, 0.32);
      transition: background-color 0.2s linear;
      margin-bottom: r(6);
    }

    .disabled {
      opacity: 0.6;
    }

    .not-selected {
      background: rgba(0, 0, 0, 0.04);
      color: rgba(0, 0, 0, 0.8);
      border-color: transparent;
      border: 1px solid rgba(0, 0, 0, 0.1);
    }

    .svg {
      vertical-align: -0.25em;
      margin-right: r(5);
    }

    .gender {
      min-width: r(65);
      margin-bottom: 0;
    }
  }

  /* extraInfo */
  &__card {
    margin-top: 0;
    @include borderRadius($color: #F0F0F0, $radius: r(8));

    padding: 1px;

    &-core {
      position: relative;
      z-index: 2;

      &.za-cell {
        &::after {
          left: 0;
          display: block;
          border-color: #f0f0f0;
        }
      }
    }

    .unit {
      width: r(60);
      font-size: r(12);
    }
  }
}
