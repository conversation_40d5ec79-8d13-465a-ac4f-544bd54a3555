import classnames from 'classnames';
import React, { useEffect, useState, useCallback, useImperativeHandle } from 'react';
import { shallowEqual, useSelector, useDispatch } from 'react-redux';
import { FixedButton, StaticToast, SvgIcon, Card } from 'src/components/common';
import { ApplicationState } from 'src/store';
import {
  edit_patient_info,
  edit_patient_bloodsugar,
  fetch_patient_no,
  fetch_patient_detail,
} from 'src/store/patients/action';
import { save_patient, clear_patient_list, clear_patient } from 'src/store/patients/action';
import { fetchJson } from 'src/utils/fetch';
import format from 'src/utils/format';
import { Deserialize, TransformEnumArray } from 'src/utils/serialization';
import { PROVINCE_DATA } from 'src/utils/strictCode';
import validate from 'src/utils/validate';
import { Cell, Input, Select, Picker, DatePicker } from 'zarm';

import './index.scss';

const fetchSelfPatientInfo = () =>
  fetchJson({
    type: 'POST',
    url: '/api/api/v1/patient/patient/list',
    data: {
      patientRelation: 1,
    },
    isloading: true,
  });

const AddPatient = (props, ref) => {
  const { showPatientDistrict = true } = props;
  const [blurName, setBlurName] = useState('');
  // 如果是通过患者信息自动带出来的患者，这个时候证件号码是掩码，此时不校验身份证
  // const [isRelative, setIsRelative] = useState(false);
  // 控制出生日期和性别是否可编辑 当身份证和身份证号码正确时 默认带出 不给选择
  const [IdNumberDisabled, setIdNumberDisabled] = useState(false);
  const [visiblePicker, setOpenPicker] = useState(false);
  const [birthdayVisiblePicker, setBirthdayVisiblePicker] = useState(false);
  // 如果患者来完善本人信息或者编辑本人信息的时候，【关系，姓名，身份证】不可以更改
  const [selfDisabled, setSelfDisabled] = useState(false);
  const [hasSelf, setHasSelf] = useState(false); // 是否有本人

  const editPatientData = useSelector((state: ApplicationState) => state.patients.editPatient || {}, shallowEqual);

  const {
    BLOODSUGARMONITORTIMEENUM = [],
    PATIENTRELATIONT,
    GENDER,
    SOCIALSECURITYTYPE,
    CERTTYPE,
  }: any = useSelector((state: ApplicationState) => {
    const {
      BLOODSUGARMONITORTIMEENUM_OBJ = {},
      CERTTYPE = [],
      PATIENTRELATION = [],
      GENDER = [],
      SOCIALSECURITYTYPE = [],
      BLOODSUGARMONITORTIMEENUM = [],
    } = state.dictionary;
    return {
      PATIENTRELATIONT: TransformEnumArray(PATIENTRELATION, 'resValue', 'resName'),
      GENDER: TransformEnumArray(GENDER, 'resValue', 'resName'),
      SOCIALSECURITYTYPE: TransformEnumArray(SOCIALSECURITYTYPE, 'resValue', 'resName'),
      BLOODSUGARMONITORTIMEENUM: TransformEnumArray(BLOODSUGARMONITORTIMEENUM, 'resValue', 'resName'),
      BLOODSUGARMONITORTIMEENUM_OBJ,
      CERTTYPE: TransformEnumArray(CERTTYPE, 'resCode', 'resName'),
    };
  });

  const {
    patientRelation = '',
    patientName = '',
    patientCertType = '',
    patientCertNo = '',
    patientGender = '',
    patientDistrictCode = '',
    patientSocialSecurityType = '',
    patientWeight = '',
    patientBirthday = '',
    bloodSugarList = [
      {
        bloodSugar: '',
        highBloodPressure: '',
        lowBloodPressure: '',
        heartRate: '',
        rangeTimeType: '',
      },
    ],
    isRelative = false,
  } = editPatientData;

  const {
    rangeTimeType = '',
  } = bloodSugarList[0] || {};
  // dispatch 修改患者信息
  const dispatch = useDispatch();
  const dispatchFetchPatientNo = () => dispatch(fetch_patient_no());
  const dispatchClearPatientList = () => dispatch(clear_patient_list());
  const savePatient = (onSuccess) => dispatch(save_patient(onSuccess));
  const dispatchEditPatientInfo = (key: string, value: any) => dispatch(edit_patient_info(key, value));
  const dispatchEditBloodSugarList = (key: string, value: any) => dispatch(edit_patient_bloodsugar(key, value));
  const fetchPatientDetail = (patientId: string, onSuccess) =>
    dispatch(fetch_patient_detail(patientId, { needSensitive: false }, onSuccess));

  useEffect(() => {
    fetchSelfPatientInfo().then((res) => {
      if (res.code === '0' && res.result) {
        setHasSelf(!!(res.result || []).length);
      }
    });
    dispatch(clear_patient());
    dispatchFetchPatientNo();
    if (patientCertNo === '') {
      dispatchEditPatientInfo('patientCertType', 'I');
      dispatchEditPatientInfo('patientSocialSecurityType', '99');
      dispatchEditPatientInfo('patientDistrictName', '北京市-市辖区-东城区');
      dispatchEditPatientInfo('patientDistrictCode', [110000, 110100, 110101]);
    }
  }, []);

  useEffect(() => {
    if (patientCertType === 'I' && validate.isIdCard(patientCertNo)) {
      const value: any = validate.isIdCard(patientCertNo);
      setIdNumberDisabled(true);
      dispatchEditPatientInfo('patientBirthday', value.birthday);
      dispatchEditPatientInfo('patientGender', value.sex);
      dispatchEditPatientInfo('patientSocialSecurityType', '99');
    } else {
      setIdNumberDisabled(false);
      dispatchEditPatientInfo('patientBirthday', '');
      dispatchEditPatientInfo('patientGender', '');
    }
  }, [patientCertType, patientCertNo, patientRelation]);

  /**
   * 使用 useImperativeHandle 钩子，将 savePatientClick 函数暴露给父组件，
   * 使得父组件可以通过 ref 调用该函数来保存患者信息。
   *
   * @param {React.MutableRefObject<any>} ref - 父组件传递的 ref 对象。
   * @returns {Object} - 暴露给父组件的对象，包含 savePatient 方法。
   */
  useImperativeHandle(ref, () => ({
    // 将 savePatientClick 函数赋值给 savePatient 属性，父组件可以通过 ref 调用该方法来保存患者信息
    savePatient: savePatientClick,
  }));

  const patientInfoChange = useCallback(
    (key: string, value: any, args?: any) => {
      if (IdNumberDisabled && key === 'patientGender') {
        return;
      }
      if (key === 'patientDistrictCode') {
        dispatchEditPatientInfo('patientDistrictName', args);
      }
      dispatchEditPatientInfo(key, value);
    },
    [IdNumberDisabled],
  );

  const changePatientWeight = useCallback(
    (value) => {
      if (/^(\d+|\d+\.\d{0,2})$/.exec(value) || value === '') {
        patientInfoChange('patientWeight', value);
      }
    },
    [patientWeight],
  );

  // 校验当前页面的信息录入
  const validata = (editPatientData: any, isRelative) => {
    const {
      patientNo = '',
      patientRelation = '',
      patientName = '',
      patientCertType = '',
      patientCertNo = '',
      patientCertNoCOVER = '',
      patientDistrictCode = '',
      patientWeight = '',
      patientBirthday = '',
    } = editPatientData;

    const isFIll = isRelative && patientCertNoCOVER === patientCertNo;
    const age = Number(patientBirthday ? format.GetAgeByBirthday(format.date(patientBirthday, 'yyyy-MM-dd')) : '');
    !isFIll && patientInfoChange('relativesId', '');

    if (patientNo === '') {
      StaticToast.warning('添加患者异常，请重新添加患者');
      return false;
    } else if (patientRelation === '') {
      StaticToast.warning('您还未选择患者于您的关系');
      return false;
    } else if (patientName.trim() === '') {
      StaticToast.warning('您还未输入患者的姓名');
      return false;
    } else if (!validate.isUsername(patientName)) {
      StaticToast.warning('您输入的患者姓名格式错误');
      return false;
    } else if (patientCertNo.trim() === '') {
      StaticToast.warning('您还未输入患者的证件号码');
      return false;
    } else if (patientCertType === 'I' && !validate.isIdCard(patientCertNo) && !isFIll) {
      StaticToast.warning('您输入的患者证件号码格式错误');
      return false;
    } else if (patientCertType !== 'I' && !validate.isNumberAndWord(patientCertNo) && !isFIll) {
      StaticToast.warning('您输入的患者证件号码格式错误');
      return false;
    } else if (patientCertType !== 'I' && !patientGender.trim()) {
      StaticToast.warning('您还未选择患者性别');
      return false;
    } else if (patientCertType !== 'I' && !patientBirthday.trim()) {
      StaticToast.warning('您还未输入患者出生日期');
      return false;
    } else if (patientWeight === '' && age >= 0 && age <= 12 && !isFIll) {
      StaticToast.warning('您还未输入患者体重');
      return false;
    } else if (Number(patientWeight) > 1000) {
      StaticToast.warning('患者体重最大为1000KG');
      return false;
    } else if (showPatientDistrict && patientDistrictCode === '') {
      StaticToast.warning('您还未选择患者的所在地区');
      return false;
    }
    return true;
  };

  const savePatientClick = useCallback((onSavedSuccess) => {
    if (validata(editPatientData, isRelative)) {
      savePatient(onSavedSuccess);
    }
  }, [editPatientData, isRelative]);

  const blur = useCallback(() => {
    const OBody = document.documentElement || window || document.body;
    window.scroll({
      top: OBody.scrollTop,
      behavior: 'smooth',
    });
  }, []);

  const nameBlur = useCallback(() => {
    if (patientName === blurName) {
      return;
    }
    fetchJson({
      type: 'POST',
      url: '/api/api/v1/patient/patient/getPatientRelativesCertNo',
      data: { patientName },
      isloading: false,
    }).then((res) => {
      if (res && res.code === '0') {
        const { result: { relativesCertNo = '', relativesId = '' } = {} } = res;
        if (relativesCertNo) {
          patientInfoChange('isRelative', true);
          patientInfoChange('patientCertNo', relativesCertNo);
          patientInfoChange('patientCertNoCOVER', relativesCertNo);
          patientInfoChange('relativesId', relativesId);
        } else {
          patientInfoChange('isRelative', false);
          patientInfoChange('relativesId', '');
        }
      }
    });
  }, [patientName, blurName]);

  const prefixCls = 'addPatient-comp';

  return (
    <div className={`${prefixCls}`}>
      <Card prefixCls={`${prefixCls}__card`}>
        <div className='headers'>
          <h4>添加成员</h4>
          <p className='extra'>必填</p>
        </div>
        <Cell className={`select special ${prefixCls}__cell`} title='关系'>
          <div className={`${prefixCls}__cell-relation`}>
            {PATIENTRELATIONT.map((k, i) => (
              <div
                onClick={() => {
                  if (!selfDisabled && (hasSelf ? k.value != '1' : true)) {
                    patientInfoChange('patientRelation', k.value);
                  }
                }}
                className={classnames('button', {
                  'not-selected': patientRelation != k.value,
                  'disabled': hasSelf ? k.value == 1 || selfDisabled : false,
                })}
                key={`k${i}${k.value}`}
              >
                {k.label}
              </div>
            ))}
          </div>
        </Cell>
        <Cell className={`input ${prefixCls}__cell`} title='姓名'>
          <Input
            type='text'
            placeholder='请输入真实姓名'
            onChange={(value) => {
              patientInfoChange('patientName', value);
            }}
            value={patientName}
            maxLength={15}
            onBlur={() => nameBlur()}
          />
        </Cell>
        <Cell className={`input ${prefixCls}__cell`} title='证件类型' hasArrow>
          <Select
            hasArrow={false}
            dataSource={CERTTYPE}
            placeholder='应监管要求录入'
            onOk={(selected: Array<{ value: string; label: string }> = []) => {
              patientInfoChange('patientCertType', (selected[0] || {}).value);
            }}
            value={patientCertType}
          />
        </Cell>
        <Cell className={`input ${prefixCls}__cell`} title='证件号码'>
          <Input
            placeholder={patientCertType === 'Q' ? '婴幼儿可填写出生日期，格式19990101' : '应监管要求录入证件号码'}
            maxLength={patientCertType === 'I' ? 18 : 30}
            onChange={(value) => {
              patientInfoChange('patientCertNo', value);
            }}
            value={patientCertNo}
            onBlur={() => blur()}
          />
        </Cell>
        {patientCertType && patientCertType !== 'I' && (
          <>
            <Cell className={`input ${prefixCls}__cell`} title='性别'>
              <div className={`${prefixCls}__cell-relation`}>
                {GENDER.map((k, i) => (
                  <div
                    onClick={() => patientInfoChange('patientGender', k.value)}
                    className={classnames('gender button', {
                      'not-selected': patientGender !== k.value,
                      'disabled': IdNumberDisabled,
                    })}
                    key={`k${i}${k.value}`}
                  >
                    <SvgIcon
                      width={15}
                      height={15}
                      className='svg'
                      src={require(`./images/sprite-icon-${k.value == 'M' ? 'boy' : 'girl'}.svg`)}
                    />
                    {k.label}
                  </div>
                ))}
              </div>
            </Cell>
            <Cell
              className={`input ${prefixCls}__cell`}
              title='出生日期'
              hasArrow
              onClick={() => setBirthdayVisiblePicker(true)}
            >
              <div className='za-select__value'>{patientBirthday}</div>
              <DatePicker
                title='出生日期'
                max={new Date()}
                visible={birthdayVisiblePicker}
                value={patientBirthday ? new Date(patientBirthday) : null}
                onOk={(value) => {
                  setBirthdayVisiblePicker(false);
                  patientInfoChange('patientBirthday', format.date(value, 'yyyy-MM-dd'));
                }}
                onCancel={() => setBirthdayVisiblePicker(false)}
              />
            </Cell>
          </>
        )}

        <Cell className={`input ${prefixCls}__cell`} title='体重(kg）'>
          <Input
            type='text'
            placeholder='12岁以下青少年需要录入此字段'
            onChange={(value) => changePatientWeight(value)}
            value={patientWeight}
            maxLength={6}
            onBlur={() => blur()}
          />
        </Cell>
      </Card>
      {
        showPatientDistrict && (
          <Card prefixCls={`${prefixCls}__card`}>
            <Cell className={`select ${prefixCls}__cell`} title='所在地区' hasArrow>
              <Select
                hasArrow={false}
                dataSource={PROVINCE_DATA}
                placeholder='应监管要求录入'
                onOk={(selected: any) =>
                  patientInfoChange(
                    'patientDistrictCode',
                    selected && selected.map((item) => item.value),
                    selected && selected.map((item) => item.label).join('-'),
                  )
                }
                value={patientDistrictCode}
              />
            </Cell>
          </Card>
        )
      }
      <div style={{ display: 'none' }}>
        <Cell className={`select ${prefixCls}__cell`} title='性别'>
          <div className={`${prefixCls}__cell-relation`}>
            {GENDER.map((k, i) => (
              <div
                onClick={() => patientInfoChange('patientGender', k.value)}
                className={classnames('gender button', {
                  'not-selected': patientGender !== k.value,
                  'disabled': IdNumberDisabled,
                })}
                key={`k${i}${k.value}`}
              >
                <SvgIcon
                  width={15}
                  height={15}
                  className='svg'
                  src={require(`./images/sprite-icon-${k.value == 'M' ? 'boy' : 'girl'}.svg`)}
                />
                {k.label}
              </div>
            ))}
          </div>
        </Cell>
        <Cell className={`select ${prefixCls}__cell required cell-security`} title='社保类型'>
          <div className={`${prefixCls}__cell-relation`}>
            {SOCIALSECURITYTYPE.map((k, i) => (
              <div
                onClick={() => patientInfoChange('patientSocialSecurityType', k.value)}
                className={classnames('security-button button', {
                  'not-selected': patientSocialSecurityType !== k.value,
                })}
                key={`k${i}${k.value}`}
              >
                {k.label}
              </div>
            ))}
          </div>
        </Cell>
      </div>
      <Picker
        visible={visiblePicker}
        value={rangeTimeType}
        dataSource={BLOODSUGARMONITORTIMEENUM}
        itemRender={(item: any) => item.label}
        onOk={(selected) => {
          const [item = {}] = selected;
          dispatchEditBloodSugarList('rangeTimeType', item.value || '');
          setOpenPicker(false);
        }}
        onCancel={() => setOpenPicker(false)}
      />
    </div>
  );
};

export default React.forwardRef(AddPatient);
