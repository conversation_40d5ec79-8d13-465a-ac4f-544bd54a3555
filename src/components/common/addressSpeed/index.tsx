import React, { useEffect, useState } from 'react';
import classNames from 'classnames';
import { fetchJson } from 'src/utils/fetch';
import { SvgIcon } from 'src/components/common';
import { Popup } from 'zarm';
import './AddressSpeed.scss';

const prefixCls = 'address--speed';

interface AddressSpeedProps {
  className?: string;
  style?: React.CSSProperties;
  /** 快递地址 */
  addressName: string;
  /** 支持时的提示文案 */
  supportTip?: string;
  /** 不支持时的提示文案 */
  nonsupportTip?: string;
}

AddressSpeed.defaultProps = {
  supportTip: '配送地址支持最快28分钟配送',
  nonsupportTip: '配送地址不支持最快28分钟配送，将使用普通快递',
};

/**
 * 是否支持 {28分钟} 快速配送的 UI，包含提示
 */
function AddressSpeed({
  addressName,
  supportTip,
  nonsupportTip,
  className,
  style,
}: AddressSpeedProps) {
  const [addressSpeedPopup, setAddressSpeedPopup] = useState(false);
  const [addressSpeedFast, setAddressSpeedFast] = useState<boolean | null>(null);

  useEffect(() => {
    if (!addressName) return;

    fetchJson({
      url: `/api/api/v1/patient/video/inquiry/isSupport28MinuteDelivery`,
      type: 'get',
      data: {
        deliveryAddress: addressName,
      },
      success: (res) => {
        const { code, result } = res || {};
        if (code === '200') {
          setAddressSpeedFast(!!result);
        }
      },
    });
  }, [addressName]);

  return (
    <>
      <div
        className={classNames(`${prefixCls}__address-speed`, className)}
        style={style}
        onClick={() => setAddressSpeedPopup(true)}
      >
        {addressSpeedFast != null && (
          <>
            {addressSpeedFast ? (
              <span style={{ color: 'var(--text-base-color)' }}>{supportTip}</span>
            ) : (
              <span style={{ color: '#E64848' }}>{nonsupportTip}</span>
            )}
            <SvgIcon
              className='svg_icon'
              src={require('src/pages/serviceCardList/images/sprite-icon-question.svg')}
            />
          </>
        )}
      </div>
      <Popup
        visible={addressSpeedPopup}
        direction='center'
        width='80%'
      >
        <div className={`${prefixCls}__address-speed-pouup-body`}>
          <h3 className='text-center'>支持最快28分钟左右送达地区</h3>
          <div className='content'>
            北京、上海、广州、深圳、成都、天津、济南、杭州、南京、武汉、东莞、佛山等城市核心地区
          </div>
          <div className='content'>其他地区平均1-3个自然日内送达，部分偏远地区5个自然日内送达</div>
          <footer className='i-know text-center' onClick={() => setAddressSpeedPopup(false)}>
            我知道了
          </footer>
        </div>
      </Popup>
    </>
  );
}

export default AddressSpeed;
