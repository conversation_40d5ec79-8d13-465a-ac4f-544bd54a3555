import classnames from 'classnames';
import React, { useMemo } from 'react';
import { CDN_PREFIX } from 'src/utils/staticData';
import './avatar.scss';


const littleBoy = `${CDN_PREFIX}static/components/avatar/little-boy.png`;
const littleGirl = `${CDN_PREFIX}static/components/avatar/little-girl.png`;
const midMan =`${CDN_PREFIX}static/components/avatar/mid-man.png`;
const midWoman =`${CDN_PREFIX}static/components/avatar/mid-woman.png`;
const oldMan =`${CDN_PREFIX}static/components/avatar/old-man.png`;
const oldWoman =`${CDN_PREFIX}static/components/avatar/old-woman.png`;

// 小男孩（<=16岁）、小女孩（<=16岁）、男人（>=17<=50）、女人（>=17<=50）、老年人（男>50岁）、老年人（女>50岁）

interface AvatarProps {
  age?: number;
  gender?: number | string;
  prefixCls?: string;
  preUrl?: string;
  style?: React.CSSProperties;
  isVip?: boolean;
}

const Avatar = (props: AvatarProps) => {
  const { age, gender, preUrl, prefixCls, style = {}, isVip = false } = props;

  const url = useMemo(() => {
    if (preUrl) {
      return preUrl;
    }
    if (age && gender) {

      if (age <= 16 && age >= 0 && gender === 'M') {
        return littleBoy;
      }
      if (age <= 16 && age >= 0 && gender === 'F') {
        return littleGirl;
      }
      if(age > 16 && age <= 50 && gender === 'M'){
        return midMan;
      }
      if(age > 16 && age <= 50 && gender === 'F'){
        return midWoman;
      }
      if(age > 50 && gender === 'M'){
        return oldMan;
      }
      if(age > 50 && gender === 'F'){
        return oldWoman;
      }
    }
    if (gender && gender === 'F') {
      return midWoman;
    }
    return midMan;
  }, [age, gender, preUrl]);
  return (
    <div style={{ position: 'relative' }}>
      <img style={style} className={classnames('head-component', prefixCls)} src={url} alt='' />
      {
        isVip &&
        <img className='head-component__vip' src={require('src/pages/chatMedicalManage/images/vip.png')} alt='' />
      }
    </div>
  );
};

export default Avatar;
