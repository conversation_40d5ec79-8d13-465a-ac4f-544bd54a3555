import classnames from 'classnames';
import './background.scss';
import React from 'react';

interface BackgroundState {
  singleCube?: boolean
  prefixCls?: string
  hideLeftCube?: boolean
}

const BackgroundComponent = (props: BackgroundState) => {
  const { prefixCls = '' } = props;
  return (
    <div className={classnames('bg_cover_component', prefixCls)}>
    </div>
  )
}

export default BackgroundComponent;

