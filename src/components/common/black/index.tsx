import React from 'react';
import ErrorPage from 'src/pages/init/error';
import { getIsBlackUser } from 'src/utils';


// import './black.scss';

interface CardProps {
  children: any;
  isBlackUser?: boolean;
}
const UserBlack = (props: CardProps) => {
  const { children, isBlackUser: _isBlackUser = false } = props;
  let isBlackUser = _isBlackUser || getIsBlackUser();
  if (!isBlackUser) {
    return (<>
      {children}
    </>)
  }
  return (
    <ErrorPage errorType='black' {...props} />
  );
}

export default UserBlack;