/*
 * @Description: 头部注释
 * @Autor: hou
 * @Date: 2022-11-03 11:03:19
 * @LastEditors: hou
 * @LastEditTime: 2023-09-11 10:19:36
 * @FilePath: /za-asclepius-patient-h5/src/components/common/brand/index.tsx
 */
import React from 'react';
import SvgIcon from '../svg';
import { CDN_PREFIX } from 'src/utils/staticData';

import './brand.scss';

const BrandSlogan = (props) => {
  return (
    <div className={`page-brand__footer`}>
      <SvgIcon type="img" src={`${CDN_PREFIX}applets/common/footer_slogan.png`} />
      <div className='filing' >
        <a className='tabbox' href='https://beian.miit.gov.cn/state/outPortal/loginPortal.action#/Integrated/index' target='_blank'>
        </a>
        <img className="icon" src={`${CDN_PREFIX}applets/common/filing-icon.png`} />
        <img className="text" src={`${CDN_PREFIX}applets/common/filing-text.png`} />
      </div>
    </div>
  );
};

export default BrandSlogan;
