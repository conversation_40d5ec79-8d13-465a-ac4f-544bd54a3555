@import 'src/style/index';

.SelectButton {
  $font-size: r(14);

  &-item {
    display: inline-block;
    //min-width: r(47);
    margin-right: r(10);
    padding: r(5) r(15);
    background: rgba(0, 0, 0, 0.04);
    color: rgba(0, 0, 0, 0.8);
    font-size: $font-size;
    border-radius: r(26);
    border: 1px solid rgba(0, 0, 0, 0.1);
    transition: background-color 0.2s linear;
    margin-bottom: r(10);

    .za-icon--sm {
      font-size: $font-size;
    }
  }

  &-selected {
    background: rgba(236, 145, 49, 0.06);
    color: rgba(236, 145, 49, 1);
    border-color: rgba(236, 145, 49, 0.32);
  }
}
