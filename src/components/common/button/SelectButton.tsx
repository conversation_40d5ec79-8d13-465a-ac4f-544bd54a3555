import classNames from 'classnames';
import React from 'react';
import './SelectButton.scss';

export interface SelectButtonProps {
  className?: string;
  selected?: boolean;
  children?: any;
  onClick?(): void;
}

const prefixCls = 'SelectButton';

function SelectButton(props: SelectButtonProps) {
  const { selected, children, className, ...other } = props;
  return (
    <div
      className={classNames(
        `${prefixCls}-item`,
        {
          [`${prefixCls}-selected`]: selected,
        },
        className
      )}
      {...other}
    >
      {children}
    </div>
  );
}

export default SelectButton;
