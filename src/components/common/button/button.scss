@import "src/style/index";

.fixedbutton_component {
  padding-bottom: r(80);

  .fixed_part {
    position: fixed;
    left: 0;
    bottom: 0;
    width: 100%;
    // height: r(60);
    background: #fff;
    padding: r(10) 0 r(20);
    box-shadow: 0 r(-15) r(19) 0 rgba(0, 0, 0, 0.02);
    border-radius: r(10) r(10) 0 0;
    z-index: 3;
    @include display-flex;
    @include align-items(center);
    // @include iphone-bottom-fixed;

    .next_step_btn {
      margin: 0 auto;
      width: r(345);
      height: r(44);
      line-height: r(44);
      // border-radius: r(4);
      font-size: r(16);
      font-weight: bolder;
      text-align: center;
    }
  }
}
