import React, { ReactNode, useCallback } from 'react';
import classnames from 'classnames';
import { Button } from 'zarm';
import './button.scss';
import { ButtonTheme } from 'zarm/types/button/PropsType';

interface ButtonProps {
  prefixCls?: string;
  buttonClick?: any;
  text?: string | ReactNode;
  disabled?: boolean;
  buttonPrefixCls?: string;
  buttonShape?: any;
  theme?:ButtonTheme
}

const FixedButton = (props: ButtonProps) => {
  const { theme = 'primary',prefixCls, buttonClick, text = '下一步', disabled = false, buttonPrefixCls = '', buttonShape = 'radius' } = props;

  const click = useCallback(() => {
    typeof buttonClick === 'function' && buttonClick();
  }, [buttonClick, disabled]);

  return (
    <div className={classnames('fixedbutton_component', prefixCls)}>
      <div className='fixed_part'>
        <Button
          block
          className={classnames('next_step_btn', buttonPrefixCls)}
          theme={theme}
          onClick={click}
          disabled={disabled}
          shape={buttonShape}
        >
          {text}
        </Button>
      </div>
    </div>
  );
};

export default FixedButton;
