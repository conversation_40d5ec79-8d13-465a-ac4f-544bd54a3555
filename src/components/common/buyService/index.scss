@import 'src/style/index';

.buyService_method_comp {
  width: r(345);
  padding: r(15);
  margin: 0 auto;
  background: #fff;
  border-radius: r(8);
  margin-top: r(10);

  &_wrapper {
    margin-top: r(10);
  }

  &_content {
    padding-top: r(20);
    border-top: 1px solid #d8d8d8;

    &_title {
      font-family: PingFang SC;
      font-size: r(13);
      font-weight: 500;
      line-height: normal;
      font-variation-settings: "opsz" auto;
      color: #3d3d3d;

      &.refound {
        margin-top: r(20);
      }
    }

    &_desc {
      font-family: PingFang SC;
      font-size: r(12);
      font-weight: normal;
      line-height: normal;
      font-variation-settings: "opsz" auto;
      color: #878787;
      margin-top: r(8);
    }
  }

  .d_m_title {
    @include display-flex;
    @include align-items(center);

    font-size: r(15);
    color: #999;

    .icon_title {
      width: r(18);
      margin-right: r(4);
    }
  }

  .delivery_method_tip {
    margin-right: r(1);
    font-size: r(12);
    color: var(--text-base-color);

    .icon_tip {
      margin-right: r(3);
      width: r(12);
      height: r(12);
    }
  }

  .active {
    color: var(--text-base-color);
  }

  .strong {
    font-weight: bold;
  }

  .title_text {
    font-size: r(16);
    color: #1e1e1e;
    font-weight: bold;
    @include flex(1);
  }

  .icon_narrow {
    margin-left: r(3);
    font-size: r(15);
    color: #d8d8d8;
  }

  .d_m_types {
    margin-top: r(15);

    .za-cell {
      width: r(315);
      height: r(40);
      border-radius: r(6);
      margin-top: r(10);
      border: r(1) solid #e6e6e6;
      font-size: r(14);
      color: #333;

      &.d_m_radio_checked {
        font-weight: bold;
        border: r(1) solid var(--text-base-color);

        .d_m_describe {
          color: #1e1e1e;
        }
      }

      .za-cell__inner {
        padding: r(10) r(15);
        min-height: 0;
      }

      .za-radio {
        width: 100%;
      }

      .za-radio__text {
        vertical-align: top;
        margin: 0;
      }

      .za-radio__widget {
        float: right;
      }

      .d_m_describe {
        margin-left: r(5);
        font-size: r(12);
        color: #999;
        font-weight: normal;
      }

      &::after {
        display: none;
      }

      &.active {
        font-weight: bold;
        font-size: r(14);
        border: r(1) solid var(--text-base-color);
        color: #1e1e1e;

        .d_m_describe {
          color: #1e1e1e;
        }
      }
    }
  }
}
