import React, { useCallback, useState, useEffect } from 'react';
import { Radio, Modal } from 'zarm';
import SvgIcon from '../svg';
import './index.scss';

const METHODS = [
  {
    id: '1',
    title: '快递配送',
    describe: '药店药品覆盖广，可选择性强',
  },
  {
    id: '2',
    title: '同城配送',
    describe: '送药快，药房药品受限',
  },
  {
    id: '3',
    title: '到店自取',
    describe: '系统自动匹配最佳药店，节省运费 ',
  },
];

const DeliveryMethodTips = () => {
  const alertTips = useCallback(() => {
    Modal.alert({
      content: (
        <div style={{ textAlign: 'left' }}>
          <p>配送方式是根据您的权益和当前收货地址确定的；</p>
          <p>北京、上海等核心地区支持同城配送；</p>
          <p>其他地区可使用快递，平均1-3个自然日内送达，部分偏远地区5个自然日内送达</p>
        </div>
      ),
      cancelText: '我知道了',
    });
  }, []);
  return (
    <p className='delivery_method_tip' onClick={alertTips}>
      <SvgIcon className='icon_tip' src={require('src/svgs/sprite-icon_tips.svg')} />
      配送说明
    </p>
  );
};

const BuyService = (props) => (
  <div className='buyService_method'>
    <div className={'buyService_method_comp'}>
      <div className='buyService_method_comp_wrapper'>
        <div className='d_m_title'>
          <img className='icon_title' src={require('./images/service.png')} />
          <span className='title_text'>服务流程</span>
        </div>

        <div className='d_m_types' style={{textAlign: 'center'}}>
          <img src={require('./images/liucheng.png')} style={{width: '90%'}} alt='' />
        </div>
      </div>
    </div>
    <div className={'buyService_method_comp'}>
      <div className='buyService_method_comp_wrapper'>
        <div className='d_m_title'>
          <img className='icon_title' src={require('./images/buy.png')} />
          <span className='title_text'>购买须知</span>
        </div>

        <div className='d_m_types'>
          <div className='buyService_method_comp_content'>
            <div className='buyService_method_comp_content_title'>
            1、价格说明
            </div>
            <div className='buyService_method_comp_content_desc'>
            划线价格指产品/服务的零售价或历史销售价，并非原价，仅供参考。未划线价格指产品/服务根据用户账户中含有的优惠券、权益等优惠抵扣后的价格，具体成交价最终以订单结算页价格为准；
            以上价格仅供参考，以实际价格为准。
            </div>
            <div className='buyService_method_comp_content_title refound'>
            2、退款规则
            </div>
            <div className='buyService_method_comp_content_desc'>
            (1)未使用服务:<br></br>
              a)超过1年未使用服务，不予退款。<br></br>
              b)自购买之日起1年内未使用服务，可联系客服进行全额退款。<br></br>
              (2)已使用任一服务，不支持退款。<br></br>
              (3)非本人付费，如他人转赠或企业集采类用户，不支持退款。
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
);

export default BuyService;
