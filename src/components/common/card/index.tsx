import React from 'react';
import classnames from 'classnames';
import './card.scss';

interface CardProps {
  children: any;
  prefixCls?: string;
  style?: React.CSSProperties; 
  onClick?: any;
}


const Card = (props: CardProps) => {
  const { children, style, prefixCls, onClick } = props;
  return (
    <div className={classnames('card_component', prefixCls )} style={style} onClick={onClick}>
      {children}
    </div>
  );
}

export default Card;