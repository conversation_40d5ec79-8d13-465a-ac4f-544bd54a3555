@import 'src/style/index';

.home-case-comp {
  .case_item {
    position: relative;
    width: 93.16%;
    margin: r(20) 3.42% 0;
    padding-bottom: r(28);
    background: #fcfcfc;
    border-radius: r(8);
    border: r(1) solid #f0f0f0;
    height: r(135);
    overflow: visible;
    vertical-align: top;

    &:first-child,
    &.active {
      height: auto;
    }

    .case_question_wrap {
      padding: 0 r(12);
    }

    .case_title {
      margin: r(15) 0 r(10) r(12);
      font-size: r(14);
      font-family: PingFangSC-Semibold, PingFang SC;
      font-weight: bold;
      color: #1e1e1e;
    }

    .case_question {
      position: relative;
      padding-left: r(24);
      font-size: r(13);
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: bold;
      overflow: hidden;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 2;
      white-space: normal;
    }

    .case_icon_question {
      position: absolute;
      width: r(18);
      height: r(18);
      left: 0;
      top: 0;
    }

    .case_guide {
      height: r(32);
      line-height: r(32);
      padding: 0 r(10);
      margin-top: r(10);
      font-size: r(13);
      background: #ebf9f3;
      border-radius: 0 r(4) r(4) r(4);
      color: var(--text-base-color);
    }

    .case_icon_arrow {
      float: right;
      margin-top: r(10);
      width: r(12);
      height: r(12);
      font-size: r(12);

      --arrow-color: var(--text-base-color);
    }

    .case_icon_close {
      position: absolute;
      right: 0;
      top: 0;
      width: r(27);
      height: r(27);
    }
  }

  .za-carousel__pagination {
    bottom: r(6);
  }

  .za-carousel__pagination__item {
    width: r(5) !important;
    height: r(3) !important;
    background: #b2b2b2;
    border-radius: r(2);
    opacity: 0.2;

    &.za-carousel__pagination__item--active {
      width: r(10) !important;
      opacity: 1;
    }
  }
}
