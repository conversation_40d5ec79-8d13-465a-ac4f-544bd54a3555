import React, { useCallback, useEffect, useState } from 'react';
import { SvgIcon } from 'src/components/common';
import { storage } from 'src/utils';
import { Carousel } from 'zarm';
import { CASE } from 'src/pages/caseDetail'
import './case.scss';
import { xflowPushEvent } from 'src/utils/pageTrack';

const EVERY_DAY_ONCE_CASE = `everyDayOnceCase`; // 首页弹框

function everyDayOnceStorage(storageKey) {
  // 凌晨 0 点过期
  const day = new Date();
  day.setDate(day.getDate() + 1);
  day.setHours(0, 0, 0, 0);
  const minutes = (day.getTime() - new Date().getTime()) / 1000 / 60;
  storage.set(storageKey, 1, minutes);
}

const Case = (props: any = {}) => {

  const { hideClose = false } = props;
  const [carouseIndex, setCarouseIndex] = useState(0);
  const [show, setShow] = useState(false);

  const isFromHome = location.href.indexOf('/home') > -1;

  useEffect(() => {
    const everyDayOnce = storage.get(EVERY_DAY_ONCE_CASE);
    if (!everyDayOnce) {
      setShow(true);
    }
  }, []);

  const close = useCallback((e) => {
    e.stopPropagation();
    xflowPushEvent([
      'click',
      'ZAHLWYY_SY',
      '首页',
      { ZAHLWYY_CLICK_CONTENT: `首页_都在问_关闭` },
    ]);
    everyDayOnceStorage(EVERY_DAY_ONCE_CASE)
    setShow(false);
  }, []);

  const toDetail = useCallback((index) => {
    xflowPushEvent([
      'click',
      'ZAHLWYY_SY',
      '首页',
      { ZAHLWYY_CLICK_CONTENT: `${isFromHome ? '首页_都在问_' : '健康金_都在问_'}${CASE[index]?.title}` },
    ]);
    window.reactHistory.push({
      pathname: '/hospital/caseDetail',
      search: `currentCase=${index}`
    })
  }, []);

  return <div style={{ display: show ? 'block' : 'none' }}>
    <Carousel className='home-case-comp' activeIndex={carouseIndex} showPagination autoPlay loop onChange={(activeIndex) => {
      setCarouseIndex(activeIndex!);
    }}>
      {
        (CASE || []).map(({ question }, index) => {
          return <div className={`case_item ${carouseIndex === index ? 'active' : ''}`} key={index} onClick={() => { toDetail(index) }} data-_xflow_exposure='true' id={`${isFromHome ? 'home_case_' : 'healthGold_case_'}${index}`}>
            <p className='case_title'>大家都在问</p>
            {
              !hideClose && <img className='case_icon_close' src={require('src/pages/home/<USER>/case_icon_close.svg')} onClick={close} />
            }
            <div className={`case_question_wrap`} key={+index}>
              <p className='case_question'><img className='case_icon_question' src={require('src/pages/caseDetail/images/q.png')} />{question}</p>
              <p className='case_guide'>
                <span>查看医生诊断建议、用药建议</span>
                <SvgIcon className='case_icon_arrow' src={require('src/svgs/sprite-icon_arrow.svg')} />
              </p>
            </div>
          </div>
        })
      }
    </Carousel>
  </div>
};

export default Case;
