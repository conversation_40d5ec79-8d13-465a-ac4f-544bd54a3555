@import "src/style/index";
$prefixCls: 'ceiling-navs';

.#{$prefixCls} {
  height: r(47.5);

  &__core {
    padding: r(11) r(5);
    overflow: hidden;
    overflow-x: auto;
    transition: all .3s;

    &::-webkit-scrollbar {
      display: none;
    }
  }

  &__body {
    height: r(47.5);
    background-color: #fff;
    position: relative;

    &:after,
    &::before {
      content: "";
      position: absolute;
      top: 0;
      bottom: 0;
      width: r(18);
      background: -webkit-linear-gradient(left, rgba(255, 255, 255, 1) 0%, rgba(255, 255, 255, 0) 100%);
      z-index: 3;
    }

    &::before {
      right: 0;
      background: -webkit-linear-gradient(left, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 1) 100%);
    }

    &::after {
      left: 0;
    }

    &.nav-fixed {
      position: fixed;
      z-index: 10;
      left: 0;
      right: 0;
      top: 0;
    }
  }

  &__scroll {
    @include display-flex;

    white-space: nowrap;
    padding-right: r(14);
    min-width: 100%;
    // float: left;

    p {
      // flex: 1;
      @include flex;

      line-height: r(22);
      padding: r(1.5) r(5.5);
      margin-left: r(14);
      font-size: r(14);
      color: #464646;
      transition: all .4s;
      text-align: center;

      &.active {
        color: #fff;
        border-radius: r(5);
      }
    }
  }

  /* 主题2 */
  &__theme-ghost {
    .#{$prefixCls} {
      &__body {
        border-bottom: 1px solid $border-color;
      }

      &__scroll {
        p {
          color: #666;
        }

        .active {
          font-weight: 600;
          color: #1e1e1e;
          position: relative;

          &::after {
            position: absolute;
            left: 50%;
            transform: translateX(-50%);
            bottom: r(-11);
            content: "";
            width: r(30);
            height: r(2.5);
            background: var(--text-base-color);
            border-radius: 20px;
          }
        }
      }
    }
  }
}
