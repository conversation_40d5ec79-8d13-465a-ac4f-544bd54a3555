/*
 * @description： 通用滚动吸顶导航栏
 */
import React, { useState, useEffect, useRef, useCallback } from 'react';
import classnames from 'classnames';
import throttle from 'src/utils/throttle';
import Events from 'src/utils/events';

import './ceilingNav.scss';

// let body = document.body || document.documentElement;
export interface CeilingNavProps {
  activeBgColor?: string;
  className?: string;
  list: string[];
  restProps?: any;
  theme?: 'primary' | 'ghost';
}
const viewportHeight = screen.height;

const CeilingNav = (props: CeilingNavProps) => {
  const prefixCls = 'ceiling-navs';
  const navRef: any = useRef(null);
  const navScrollRef: any = useRef(null);
  const [isFixed, setFixed] = useState(false);
  const [activeIndex, setAactiveIndex] = useState(0);
  const { list = [], activeBgColor = '', className = '', theme = 'primary' } = props;

  const horizontalScroll = ({ index = 0 }) => {
    let scrollNode = navScrollRef.current;
    scrollNode.scrollTo(index * 30, 0);
  };
  const anchorPoint = () => {
    for (var i = list.length - 1; i >= 0; i--) {
      let allNode = document.querySelectorAll('.anchor-node');
      if (allNode) {
        let curNode: any = allNode[i];
        let { top: itemTop, bottom = 0 } = curNode.getBoundingClientRect();
        if (bottom > 0 && itemTop < viewportHeight / 2.5) {
          if (i !== activeIndex) {
            setAactiveIndex(i);
            throttle(horizontalScroll, 300)({ index: i });
          }
          break;
        }
      }
    }
  };
  const _scroll = useCallback(
    throttle(() => {
      let navNode = navRef.current;
      if (!navNode) {
        return;
      }
      let { top: navTop } = navNode.getBoundingClientRect();
      console.log(navTop);
      if (navTop <= 0) {
        if (!isFixed) {
          setFixed(true);
        }
      } else {
        if (!!isFixed) {
          setFixed(false);
        }
      }
      anchorPoint();
    }, 200),
    [isFixed],
  );

  const tapScrollTo = (index) => {
    let allNode = document.querySelectorAll('.anchor-node');
    if (allNode) {
      let curNode: any = allNode[index];
      // let { top } = curNode.getBoundingClientRect();
      // body.scrollTop = top + (viewportHeight * 0.59);
      window.scroll(0, curNode.offsetTop - viewportHeight * 0.32);
      // console.log(curNode.offsetTop, top, index, "----2--");
    }
  };

  useEffect(() => {
    Events.on(window, 'scroll', _scroll);
    return () => Events.off(window, 'scroll', _scroll);
  }, [activeIndex, isFixed]);

  // console.log(isFixed);
  return (
    <div className={`${prefixCls} ${className} ${prefixCls}__theme-${theme}`} ref={navRef}>
      <nav className={classnames(`${prefixCls}__body`, { 'nav-fixed': !!isFixed })}>
        <div className={`${prefixCls}__core`} ref={navScrollRef}>
          <div className={`${prefixCls}__scroll`}>
            {list.map((k, index) => {
              return (
                <p className={classnames({ active: activeIndex === index })} style={{ backgroundColor: activeIndex === index ? activeBgColor : '' }} onClick={() => tapScrollTo(index)} key={`nav${index}`}>
                  {k}
                </p>
              );
            })}
          </div>
        </div>
      </nav>
    </div>
  );
};

export default CeilingNav;
