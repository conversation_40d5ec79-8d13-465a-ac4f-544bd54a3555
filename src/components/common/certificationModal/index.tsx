
import React, { useEffect, useRef, useState, useCallback } from 'react';
import { StaticToast } from 'src/components/common';
import { fetchJson } from 'src/utils/fetch';
import { Popup, Input, Button } from 'zarm';
import validate from 'src/utils/validate';
import { Link } from 'react-router-dom';
import cookies from 'src/utils/cookie';
import './certificationmodal.scss';

interface CertificationModalProps {
  visible: boolean
  afterClose?: Function
  onSuccess?: Function
  onClose?: Function
}

const val = (data) => {
  const { patientName = '', patientCertNo = '', patientNo = '' } = data;
  if (patientNo === '') {
    StaticToast.warning('实名异常，请重新进入');
    return false;
  } else if (patientName.trim() === '') {
    StaticToast.warning('您还未输入姓名');
    return false;
  } else if (!validate.isUsername(patientName)) {
    StaticToast.warning('您输入的姓名格式错误');
    return false;
  } else if (patientCertNo.trim() === '') {
    StaticToast.warning('您还未输入身份证号码');
    return false;
  } else if (!validate.isIdCard(patientCertNo)) {
    StaticToast.warning('您输入的身份证号码格式错误');
    return false;
  }
  return true
}


const CertificationModal = (props: CertificationModalProps) => {
  const { visible = false, onSuccess, onClose, afterClose } = props;
  const [modalVisible, setModalVisible] = useState(visible);
  const isRealAuth: string = cookies.get('isRealAuth') || 'N';
  /** 
   * 实名表格
   */
  const [form, setForm] = useState({
    patientName: '',
    patientCertNo: '',
    patientCertType: 'I',
    patientNo: '',
  });

  const formRef = useRef({
    patientName: '',
    patientCertNo: '',
    patientCertType: 'I',
    patientNo: ''
  });

  useEffect(() => {
    if (isRealAuth === 'NY') {
      const { pathname, search } = window.location;
      window.reactHistory.replace({
        pathname: '/hospital/certification/select',
        search: `pathname=${encodeURIComponent(pathname)}&jumpSearch=${encodeURIComponent(search)}`
      });
    }
  }, []);

  useEffect(() => {
    const fetchPatientNo = async () => {
      const res = await fetchJson({
        type: "POST",
        url: '/api/api/v1/patient/bizno/getByType',
        data: { bizNoType: 'PATIENT_NO' }
      });

      if (res && res.code === '0') {
        const { result = '' } = res;
        setForm({
          ...form,
          patientNo: result
        })
      }
    }
    fetchPatientNo();
  }, []);

  useEffect(() => {
    formRef.current = form;
  });

  useEffect(() => {
    if (visible !== modalVisible) {
      setModalVisible(visible !== null && visible);
    }
  }, [visible]);

  useEffect(() => {
    if (modalVisible) {
      document.body.style.overflowY = 'hidden';
    } else {
      document.body.style.overflowY = 'visible';
    }
  }, [modalVisible]);

  const submit = useCallback(() => {
    if (val(formRef.current)) {
      const { patientCertNo } = formRef.current;
      const value: any = validate.isIdCard(patientCertNo);
      fetchJson({
        url: '/api/api/v1/patient/patient/save',
        type: 'POST',
        data: {
          patientRelation: 1,
          patientBirthday: value.birthday,
          patientGender: value.sex,
          ...formRef.current,
        },
        isloading: true,
        success: res => {
          if (res && res.code === '0') {
            cookies.set('isRealAuth', 'Y');
            typeof onSuccess === 'function' && onSuccess();
          } else {
            const { message = '授权失败' } = res;
            cookies.set('isRealAuth', 'N');
            StaticToast.warning(message);
          }
        },
        error: (e) => { }
      });
    }
  }, [formRef]);

  const popupRef: any = useRef(null);
  const prefixCls = 'certification_modal';

  return <React.Fragment>
    <div className={prefixCls} ref={popupRef}></div>
    <Popup
      visible={modalVisible}
      onMaskClick={() => {
        setModalVisible(false);
        typeof onClose === 'function' && onClose();
      }}
      afterClose={() => { typeof afterClose === 'function' && afterClose(); }}
      mountContainer={popupRef.current}
      destroy={true}
    >
      {isRealAuth === 'N' && <div className='wrapper'>
        <p className='title'>实名认证绑定权益</p>
        <Input className='form_input' placeholder='请输入姓名' value={form.patientName} maxLength={10} onChange={value => setForm(prevForm => ({
          ...prevForm,
          patientName: value.trim()
        }))} />
        <Input className='form_input' placeholder='请输入身份证号码' value={form.patientCertNo} maxLength={18} onChange={value => setForm(prevForm => ({
          ...prevForm,
          patientCertNo: value.trim()
        }))} />
        <Button className='submit' theme='primary' onClick={() => submit()} block>确定</Button>
        <p className='know'>点击确定，即代表您已同意<Link to={{ pathname: '/hospital/agreement' }} style={{ color: '#309EEB' }} onClick={() => { document.body.style.overflowY = 'visible'; }}>《用户信息授权协议》</Link></p>
      </div>}
    </Popup>
  </React.Fragment>
}

export default CertificationModal;

