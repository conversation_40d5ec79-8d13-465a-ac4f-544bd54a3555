@import 'src/style/index';

.footer_input_wrap {
  padding: r(15);
  @include display-flex;
  @include align-items(flex-end);

  .chat_input_wrap {
    margin: 0 r(14);
    padding: r(11) r(4) r(11) r(14);
    border-radius: r(4);
    transition: all 0.2s linear;
    background: rgba(0, 0, 0, 0.03);

    @include flex(1);
  }

  .chat_input {
    display: block;
    padding: 0 r(10) 0 0;
    width: 100%;
    max-height: r(110);
    min-height: r(23);
    background: transparent;
    line-height: 1.5;
    font-size: r(15);
    word-break: break-all;
    outline: none;
    border: none;
    resize: none;
    overflow: auto;
    -webkit-appearance: none;
    transition: all 0.2s linear;

    &::-webkit-scrollbar {
      display: none;
    }
  }

  .btn_send_msg {
    border: none;
    background: transparent;
    padding: 0;
    width: r(36);
    height: r(36);
    margin-bottom: r(4);
    position: relative;

    &:active {
      opacity: 0.6;
    }

    .za-icon {
      color: #969696;
      font-size: r(36);
    }

    .input_image {
      display: none;
    }

    .input_alipay {
      position: absolute;
      left: 0;
      right: 0;
      top: 0;
      bottom: 0;
      z-index: 2;
    }
  }

  .btn_chat {
    width: r(36);
    height: r(36);
  }
}
