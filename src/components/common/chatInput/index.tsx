import React, { useEffect, useCallback, useRef } from 'react';
import { notifyAlipayArouseChooseImage } from 'src/pages/prescriptionDetail/utils';
import { THIRD_PLATFORM_RESOURCECODE } from 'src/utils/staticData';
import throttle from 'src/utils/throttle';
import validate from 'src/utils/validate';
import CompatibleFileInput from '../fileInput';
import SvgIcon from '../svg';

import './index.scss';

const emptyFunction = () => {};

const ChatInput = (props) => {
  const {
    businessNo='', chatSendMsg, className = '', handleSumbit, sendVal, handlePicture,
    handleChange = emptyFunction,
    handleFocus = emptyFunction, handleBlur = emptyFunction,
    platformCode = '', popWrapperEl = null,
    placeholder='请输入',
    disabled = false,
  } = props;

  const textareaRef: any = useRef(null);
  useEffect(() => {
    if (validate.isAlipayApplet()) {
      my &&
        (my.onMessage = (e: any) => {
          const { data = {} } = e;
          console.log(e, '小程序传的消息');
          changeFMUpload(data);
        });
    }

    return () => {
      handleBlur && handleBlur();
    };
  }, []);
  useEffect(() => {
    const input = textareaRef.current;
    input.style.height = 'auto';
    const actualHeight = Math.max(input.scrollHeight, 23);
    input.style.height = `${actualHeight}px`;
  }, [sendVal]);

  useEffect(() => {
    touchInputBlur();
  }, [popWrapperEl, textareaRef]);

  const touchInputBlur = () => {
    if (popWrapperEl && popWrapperEl.current && textareaRef && textareaRef.current) {
      const watchTouch = () => {
        textareaRef.current && textareaRef.current.blur && textareaRef.current.blur();
      };
      popWrapperEl.current.ontouchstart = () => {
        throttle(watchTouch(), 800);
      };
    }
  };

  const changeFMUpload = useCallback(
    (item) => {
      chatSendMsg &&
        chatSendMsg(
          {
            link: item.attachmentDownloadUrl,
            localLink: item.localLink,
          },
          4,
        );
    },
    [chatSendMsg, businessNo],
  );
  const monitorChange = typeof handlePicture === 'function' ? handlePicture : changeFMUpload;

  const onSubmit = useCallback(
    (e) => {
      e.preventDefault();

      // 验证通过，使用清理并编码后的内容
      handleSumbit && handleSumbit(sendVal);
    },
    [sendVal],
  );

  const isAlipayArouseChoosePlatformCode =[THIRD_PLATFORM_RESOURCECODE.MBJK,THIRD_PLATFORM_RESOURCECODE.KYUSHU_POP,THIRD_PLATFORM_RESOURCECODE.ZAHY].includes(platformCode);
  return (
    <form onSubmit={onSubmit} className={`footer_input_wrap ${className}`} action='#'>
      {businessNo&&<label className='btn_send_msg' htmlFor='photoNode'>
        <SvgIcon className='btn_chat' type='img' src={require('src/pages/chatMedicalManage/images/icon_add.png')} />
        {isAlipayArouseChoosePlatformCode && validate.isAlipayApplet() ? (
          <p className='input_alipay' onClick={() => notifyAlipayArouseChooseImage({ businessNo, attachmentType: 'inquiryIMFile' })}></p>
        ) : (
          <CompatibleFileInput id='photoNode' className='input_image' businessNo={businessNo} attachmentType='inquiryIMFile' onUploadSuccess={monitorChange} platformCode={platformCode} />
        )}
      </label>}
      <div className='chat_input_wrap'>
        <textarea
          ref={textareaRef}
          placeholder={placeholder} rows={1}
          disabled={disabled}
          className='chat_input'
          value={sendVal}
          onChange={(e) => {
            handleChange(e);
          }} onFocus={handleFocus} onBlur={handleBlur} />
      </div>
      <button className='btn_send_msg' type='submit' onMouseDown={(e) => e.preventDefault()}>
        <SvgIcon className='btn_chat' type='img' src={require('src/pages/chatMedicalManage/images/icon_send.svg')} />
      </button>
    </form>
  );
};

export default ChatInput;
