@import 'src/style/index';

.comp_check_inquiry {
  @include display-flex;
  @include align-items(center);

  .check_inquiry_text {
    @include flex(1);
  }

  &.check_inquiry_home {
    margin: r(-5) 0 r(10);
    padding: 0 r(11) 0 r(10);
    height: r(40);
    background: linear-gradient(270deg, #7ddcd2 0%, #52c8b0 100%);
    border-radius: r(8);
    font-size: r(14);
    color: #fff;
    font-weight: bold;

    &.alipay {
      margin: r(10) 0 0;
      padding: r(6) r(11)  r(6) r(10);
    }

    .btn_continue {
      width: r(78);
      height: r(28);
      padding: 0;
      border: none;
      font-size: r(14);
      font-weight: bold;
      color: #6acfba;
    }

    .check_inquiry_img {
      width: r(35);
      height: r(40);
    }
  }

  &.check_inquiry_inquiryForm {
    height: r(40);
    padding: 0 r(15);
    color: #ec9131;
    font-size: r(14);
    background: #fff7cb;

    .check_inquiry_icon {
      font-size: r(15);
      color: #ec9131;
      margin-right: r(5);
    }

    .check_inquiry_arrow {
      width: r(12);
      height: r(12);
      font-size: r(12);

      --arrow-color: #ec9131;
    }
  }
}

.za-modal__dialog {
  .bar-icon {
    display: block;
    margin: 0 auto;
    width: r(54);
    height: r(54);
  }

  .pop-h1 {
    font-size: r(12);
    color: #666;
    text-align: center;
    line-height: r(20);
    margin-top: r(5);
    padding-bottom: r(8);
  }

  .pop_con {
    font-size: r(14);
    color: #666;
    text-align: center;
    font-weight: 400;
    line-height: r(20);
  }
}
