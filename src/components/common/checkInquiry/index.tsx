import React, { useCallback, useEffect, useState, forwardRef, useImperativeHandle } from 'react';
import { cookies, fetchJson } from 'src/utils';
import { jumpBeforeFetchThirdInquiryAbility } from 'src/utils/auth';
import { getInquiryOrMentalConsultListUrl } from 'src/utils/tool';
import { Button, Icon, Modal } from 'zarm';
import SvgIcon from '../svg';
import './index.scss';

const CheckInquiry = forwardRef((props: any, ref) => {
  const [inquiryList, setInquiryList]: any = useState([]);
  const { position = '', create = {}, currentTab, className = '', onEmit = () => {}, direct = false } = props;

  const { hasInquiry, onGoingInquiry = [] } = create;

  const showPatientInfo = currentTab == 1 && hasInquiry;

  const inquiryNum = showPatientInfo ? onGoingInquiry.length : inquiryList.length;

  const firstInquiry = showPatientInfo ? onGoingInquiry[0] : inquiryList[0];

  useEffect(() => {
    console.log('----position-----', position);
    const ZA_TOKEN = cookies.get('za_token') || '';
    if (ZA_TOKEN) {
      checkHasInquiry();
    }
  }, [position]);

  // 检查问诊单
  const checkHasInquiry = useCallback(() => new Promise((resolve) => {
    fetchJson({
      type: 'POST',
      url: '/api/api/v1/patient/inquiry/list',
      data: {
        inquiryStatusList: [1, 2, 3, 7, 8, 10, 11],
        inquiryTypes: ['I', 'V'],
        excludeInquiryTimingTypeList: ['drug_replenish', 'mentalConsult'],
      },
      success: (res) => {
        const { code = '', result = [] } = res;
        if (code === '0') {
          setInquiryList(result);
          onEmit && onEmit(result);
          resolve(result);
        } else {
          resolve([]);
        }
      },
      error: () => {
        resolve([]);
      },
    });
  }), []);

  // 暴露方法给父组件
  useImperativeHandle(ref, () => ({
    checkHasInquiry,
  }));

  useEffect(() => {
    if(direct && inquiryList.length) {
      toContinue(null, true);
    }
  }, [inquiryList, direct]);

  const toContinue = useCallback((e, needReplace = false) => {
    e && e.stopPropagation();
    const { inquiryStatus, inquiryTimingType = '' } = firstInquiry;
    if (inquiryNum > 1 || inquiryStatus === 1) {
      window.reactHistory.replace(getInquiryOrMentalConsultListUrl(inquiryTimingType));
    } else {
      jumpBeforeFetchThirdInquiryAbility({
        data: firstInquiry,
        needInquiryDeatil: false,
        position: 'checkInquiry',
        needReplace,
        notAbilityCallback: () => {
          Modal.alert({
            title: (
              <p className='image'>
                <SvgIcon className='bar-icon' src={require('src/svgs/sprite-icon_pay_fail.svg')} />
              </p>
            ),
            content: (
              <div className='pop_con'>
                <p className='pop-h1'>接通失败</p>当前无空闲医生，请稍后再试
              </div>
            ),
            cancelText: '我知道了',
          });
        },
      });

    }
  }, [firstInquiry, inquiryNum]);

  if (!inquiryNum || direct) {
    return null;
  }

  return <div className={`comp_check_inquiry check_inquiry_${position} ${className}`} onClick={toContinue}>
    {
      position === 'home' && <React.Fragment>
        <img className='check_inquiry_img' src={require('src/images/icon_check_inquiry.png')} />
        <p className='check_inquiry_text'>您有{inquiryNum}个未完成的问诊单</p>
        <Button className='btn_continue' shape='round' onClick={toContinue}>前往查看</Button>
      </React.Fragment>
    }
    {
      position === 'inquiryForm' && <React.Fragment>
        <Icon className='check_inquiry_icon' type='info-round' theme='primary' size='sm' />
        <p className='check_inquiry_text'>{showPatientInfo ? '该用户' : '您'}有{inquiryNum}个未完成的问诊单</p>
        <SvgIcon className='check_inquiry_arrow' src={require('src/svgs/sprite-icon_arrow.svg')} />
      </React.Fragment>
    }
  </div>;
});

export default CheckInquiry;
