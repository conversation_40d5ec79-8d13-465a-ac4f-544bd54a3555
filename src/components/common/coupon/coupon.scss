@import "src/style/index";

.coupon_component {
  .base {
    position: relative;
    width: r(345);
    margin: r(10) auto 0;
    background: #fff;
    border-radius: r(5);
    overflow: hidden;
    z-index: 2;

    .coupon-order {
      background: #fcfcfc;
      padding: 0 r(15);
      line-height: r(27);
      color: #999;
      font-size: r(12);
    }

    .coupon-body {
      padding: 0 r(15);
    }

    .info {
      @include display-flex;
      @include align-items(center);
      @include justify-content(space-between);

      padding: r(15) 0 r(13);
      border-bottom: 1px dashed rgba(236, 236, 236, .6);
      position: relative;

      &:after,
      &::before {
        content: "";
        position: absolute;
        bottom: r(-7.5);
        width: r(15);
        height: r(15);
        border-radius: 50%;
        background-color: #f5f5f5;
      }

      &:before {
        right: r(-22.5);
      }

      &:after {
        left: r(-22.5);
      }

      .num {
        color: #ff7240;
        text-align: center;
        font-size: r(12);
        min-width: r(80);

        .discount {
          font-family: PingFangSC-Medium, PingFang SC;
          font-weight: bold;
          font-size: r(16);

          span {
            font-size: r(28);
            font-family: PingFangSC-Semibold, PingFang SC;
          }

          &.free {
            font-size: r(26);

            span {
              font-size: r(26);
            }
          }
        }

        .min_consume {
          color: #999;
        }

        .unlimited {
          margin-top: r(5);
          display: inline-block;
          background-color: rgba(255, 114, 64, 0.09);
          border-radius: r(3);
          font-size: r(12);
          padding: r(1) r(5);
          color: #ff7240;
        }
      }

      .info-detail {
        padding-left: r(12);
        font-size: r(12);
        line-height: r(23);
        @include flex;

        .expire-soon {
          color: #e64848;
        }

        .attached-msg {
          color: #999;
        }
      }

      .name {
        color: #1e1e1e;
        font-size: r(17);
        font-weight: 600;
        line-height: 24px;
        word-break: break-all;
        @include line(2);
      }
    }

    .sprite-icon--img {
      position: absolute;
      top: r(10);
      right: 0;
      width: r(67.5);
      height: r(67.5);
      z-index: -1;
    }

    .coupon-footer {
      @include display-flex;
      @include align-items(center);
      @include justify-content(space-between);

      color: #909090;
      padding: r(10) 0;
      font-size: r(12);

      .use {
        background: #ff7240;
        border: 0;
        font-weight: bold;
        padding: 0 15px;
        font-size: 13px;
        font-family: PingFangSC-Medium, PingFang SC;
      }
    }
  }

  .detail .fold-btn,
  .coupon-footer .fold {
    font-size: r(12);
    color: #999;
    @include display-flex;
    @include align-items(center);

    &::after {
      content: "";
      position: relative;
      top: -2px;
      margin-left: 5px;
      display: inline-block;
      width: 9px;
      height: 9px;
      border-width: 1.8px 1.8px 0 0;
      border-style: solid;
      border-color: #d6d6d6 #d6d6d6 transparent transparent;
      transform: rotate(135deg);
      transition: all 0.3s ease-in-out;
    }

    &.pack-up {
      &::after {
        top: 2px;
        transform: rotate(-45deg);
      }
    }
  }

  .detail {
    position: relative;
    width: r(345);
    transition: height 0.3s ease-in-out;
    color: #909090;
    font-size: r(12);
    z-index: 1;
    overflow: hidden;
    @include display-flex;
    @include justify-content(center);

    flex-direction: column;

    & > div {
      padding: r(2) r(15);
      word-break: break-all;

      span {
        color: #ff5050;
      }
    }

    &.unused {
      margin-top: -9px;
      overflow: visible;

      .ellipsis {
        padding-bottom: 10px;
      }
    }

    &.fold {
      height: 0;
    }

    &.min-height {
      min-height: 49px;
      padding: r(10) r(23) r(10) 0;

      &.fold {
        .ellipsis {
          padding-right: r(10);
          @include line(1);
        }
      }

      .fold-btn {
        position: absolute;
        right: r(13);
        top: r(15);
        padding: 5px 7px;
      }
    }
  }

  &.disabled {
    .info {
      .num,
      .use {
        opacity: .79;
      }

      .name {
        opacity: .7;
      }
    }

    .coupon-footer {
      color: #9b9b9b;
    }

    .detail > div > span {
      color: #9b9b9b;
    }
  }
}
