import classnames from 'classnames';
import React, { useCallback, useState } from 'react';
// import { ApplicationState } from 'src/store';
// import { useSelector } from 'react-redux';
import { SvgIcon } from 'src/components/common';
import format from 'src/utils/format';
import { Button } from 'zarm';
import './coupon.scss';

import coupon2 from './images/coupon-2.svg';
import coupon3 from './images/coupon-3.svg';
import coupon4 from './images/coupon-4.svg';

interface CouponProps {
  prefixCls?: string;
  renderData: any;
  couponClick?: any;
  usable?: boolean;
  isShowCouponStatusIcon?: boolean;
}

const CouponOrder = ({ relatedOrderNo }) => (<div className='coupon-order'>订单号：{relatedOrderNo}</div>);
const couponIcon = {
  2: coupon2,
  3: coupon3,
  4: coupon4,
  5: coupon3,
};
const Coupon = (props: CouponProps) => {
  const [isFold, setIsFold] = useState(true);
  const { prefixCls, renderData, couponClick, usable = true, isShowCouponStatusIcon = false } = props;
  const { couponName, couponType, couponUseScope, couponAmount, minConsumeAmount, discount, couponEffectiveTime = '', couponExpiryTime = '', couponStatus, couponDesc = '', relatedOrderNo = '', couponSubtitle = '', releaseNum = '' } = renderData;
  const disabled = couponStatus !== 1 || !usable;

  const click = useCallback(() => {
    typeof couponClick === 'function' && couponClick(renderData);
  }, [couponClick]);

  // const detailFold = (couponStatus != 1 && !isFold) || (couponStatus == 1 && !isFold);
  return (
    <div className={classnames('coupon_component', prefixCls, { disabled })}>
      <div className={classnames('base', { fold: isFold })}>
        {couponStatus == 2 && <CouponOrder relatedOrderNo={relatedOrderNo} />}
        <div className='coupon-body'>
          <div className='info'>
            <div className='num'>
              {couponType === 1 && <p className='discount'>¥<span>{couponAmount}</span></p>}
              {couponType === 2 && <p className='discount'><span>{discount}</span>折</p>}
              {couponType === 3 && <p className='discount free'><span>免</span>费</p>}
              <p className='min_consume'>满¥{minConsumeAmount}使用</p>
              {releaseNum === 0 && <p className='min_consume coupon-type'><span className='unlimited'>不限次</span></p>}
            </div>
            <div className='info-detail'>
              <div className='name'>{couponUseScope === 'express' ? '运费券 | ' : ''}{couponName}</div>
              <p className='attached-msg'>{couponSubtitle}</p>
              <p className='expire-date'>{format.date(couponEffectiveTime, 'yyyy-MM-dd')} 至 {format.date(couponExpiryTime, 'yyyy-MM-dd')}有效</p>
            </div>
            {couponStatus != 1 && isShowCouponStatusIcon && <SvgIcon type='img' src={couponIcon[couponStatus || '3']} />}
          </div>
          {couponStatus == 1 && <div className='coupon-footer' >
            <div className={classnames('fold', { 'pack-up': !isFold })} onClick={() => setIsFold(!isFold)}>使用说明</div>
            {!disabled && <Button theme='primary' size='xs' className='use' onClick={click} shape='round'>立即使用</Button>}
          </div>}
        </div>
        <div className={classnames('detail', { 'fold': isFold, 'min-height': couponStatus != 1, 'unused': couponStatus == 1 && !isFold })}>
          {couponStatus != 1 && <div className={classnames('fold-btn', { 'pack-up': !isFold })} onClick={() => setIsFold(!isFold)}></div>}
          <div className='ellipsis'>{couponStatus != 1 && '使用说明：'}{couponDesc}</div>
        </div>
      </div>
    </div>
  );
};

export default Coupon;
