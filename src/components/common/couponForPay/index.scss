@import 'src/style/index';
$prefixCls: 'coupon-page';

.#{$prefixCls} {
  padding: 0 r(15);
  background-color: #f5f5f5;

  .coupon-title {
    margin: r(15) 0 r(10) 0;
    color: #333;
    font-size: r(14);
    font-weight: bold;

    .coupon-number {
      margin-left: r(5);
      font-size: r(12);
      color: #666;
    }
  }

  .coupon-list {
    margin-bottom: r(27);
  }

  &_vas {
    &_item {
      margin-bottom: r(10);
      padding: r(16) r(15) 0;
      background: #fff;
      border-radius: r(7);
      position: relative;

      .free-header {
        @include display-flex;
        @include align-items(center);
        @include justify-content(space-between);

        .disabled-tips {
          color: #ff5050;
          font-size: 12px;

          .za-icon {
            transform: scale(.9);
            margin-right: 3px;
            vertical-align: -3px;
          }
        }
      }

      .free-tag {
        display: inline-block;
        height: r(19);
        line-height: r(19);
        padding: 0 r(6);
        text-align: center;
        font-size: r(11);
        font-weight: bold;
        color: #ff7240;
        border: 1px solid #ff7240;
        border-radius: r(2);
      }

      .vas-info {
        padding: r(12) 0 0;

        &-head {
          @include display-flex;
          @include align-items(center);
          @include justify-content(space-between);
        }

        .name {
          flex: 1 1 auto;
          font-size: r(16);
          font-weight: bold;
          color: #1e1e1e;
        }

        &-tips {
          line-height: r(20);
          margin-top: r(10);
          font-size: r(13);
          color: #666;
        }

        &-detail {
          position: relative;
          @include display-flex;
          @include justify-content(space-between);

          padding: r(10) 0;
          border-top: 1px solid #ececec;
          margin-top: r(16);

          .icon {
            position: absolute;
            right: r(0);
            top: r(10);
            color: #e6e6e6;
            width: r(16);
            height: r(16);
            transform: rotate(90deg);

            &.isUnfold {
              transform: rotate(270deg);
            }
          }

          .policy-unfold {
            font-size: r(13);
            color: #999;
            line-height: r(18.5);
          }

          .line {
            line-height: r(21);

            .title {
              font-size: r(13);
              color: #999;
              width: r(80);
              display: inline-block;
            }

            .value {
              font-size: r(13);
              color: #666;
            }
          }
        }
      }
    }
  }
}
