import React, { useCallback, useState } from 'react';
import classnames from 'classnames';
import { Radio } from 'zarm';
import format from 'src/utils/format';
import { CouponType, couponUseScope } from 'src/store/coupon/type';
import './index.scss';

export function getCouponName(coupon) {
  const { couponName, couponType, couponAmount, minConsumeAmount, discount } = coupon;
  switch (couponType) {
    case 1:
      return (
        (!!Number(minConsumeAmount) ? `满${minConsumeAmount}` : '') +
        `减${couponAmount} | ${couponName}`
      );
    case 2:
      return (
        (!!Number(minConsumeAmount) ? `满${minConsumeAmount}打` : '') +
        `${discount}折券 | ${couponName}`
      );
    case 3:
      return (!!Number(minConsumeAmount) ? `满${minConsumeAmount}` : '') + `抵用券 | ${couponName}`;
    default:
      return '';
  }
}

const couponUseScopeName: { [k in couponUseScope]?: string } = {
  videoInquiry: '视频问诊',
  inquiry: '图文问诊',
  drugs: '购药',
};

const prefixCls = 'coupon-page';

export type CouponProps = {
  coupons: Array<CouponType>;
  userCouponId?: number;
  onClick(coupon: CouponType): void;
};

export default function Coupon(props: CouponProps) {
  //优惠券展开的列表
  const [couponUnfoldList, setCouponUnfoldList] = useState<any[]>([]);

  //某一个优惠券展开详情
  const setCouponUnfoldClick = useCallback(
    (isUnfold, couponId) => {
      setCouponUnfoldList((old) => {
        if (isUnfold) {
          return old.filter((i) => i != couponId);
        } else {
          const _new = JSON.parse(JSON.stringify(old));
          _new.push(couponId);
          return _new;
        }
      });
    },
    [couponUnfoldList]
  );

  const { coupons, userCouponId, onClick } = props;

  const ableCouple: any = [];
  const disabledCouple: any = [];
  coupons && coupons.forEach((coupon) => {
    console.log('couponStatus',coupon.couponStatus,coupon)
    if (coupon.couponStatus === 1) {
      ableCouple.push(coupon);
    } else {
      disabledCouple.push(coupon);
    }
  });
  const renderCouponItem = ({
    couponEffectiveTime,
    couponExpiryTime,
    couponDesc,
    couponUseScope,
    isUnfold,
    id,
    coupon,
    disable
  }) => {
    console.log('id',id,userCouponId)
    return (
      <div className={`${prefixCls}_vas_item`} key={+id}>
        <div className='free-tag'>{couponUseScopeName[couponUseScope]}优惠券</div>
        <div className='vas-info'>
          <div className='vas-info-head' onClick={() => onClick(coupon)}>
            <div className='name'>{getCouponName(coupon)}</div>
            <Radio checked={id == userCouponId} disabled={disable} />
          </div>
          <div className='vas-info-detail'>
            {!isUnfold ? (
              <div className='policy-unfold' onClick={() => setCouponUnfoldClick(isUnfold, id)}>
                有效期至 {format.date(couponExpiryTime, 'yyyy-MM-dd')}
              </div>
            ) : (
              <div>
                <div className='line'>
                  <span className='title'>有效期：</span>
                  <span className='value'>
                    {format.date(couponEffectiveTime, 'yyyy-MM-dd')}至
                    {format.date(couponExpiryTime, 'yyyy-MM-dd')}
                  </span>
                </div>
                <div className='line'>
                  <span className='title'>使用说明：</span>
                  <span className='value'>{couponDesc}</span>
                </div>
              </div>
            )}
            <div />
            <img
              className={classnames('icon', { isUnfold: isUnfold })}
              src={require('./images/arrow-right.svg')}
              onClick={() => setCouponUnfoldClick(isUnfold, id)}
              alt=''
            />
          </div>
        </div>
      </div>
    )
  }
  const renderCouponList = (couponType, disable) => {
    return (
      <div className="coupon-list">
        {couponType.map((coupon) => {
          const {
            couponEffectiveTime = '',
            couponExpiryTime = '',
            id = '',
            couponDesc = '',
            couponUseScope,
          } = coupon;
          const isUnfold = couponUnfoldList.includes(id);
          return renderCouponItem({
            couponEffectiveTime,
            couponExpiryTime,
            couponDesc,
            couponUseScope,
            isUnfold,
            id,
            coupon,
            disable
          })
        })}
      </div>
    )
  }
  return (
    <div className="coupon-page">
      <div className="coupon-title">可用优惠券<span className="coupon-number">{ableCouple.length}张</span></div>
      {
        renderCouponList(ableCouple, false)
      }
     
      <div className="coupon-title">不可用优惠券<span className="coupon-number">{disabledCouple.length}张</span></div>
      {
        renderCouponList(disabledCouple, true)
      }
     
     

    </div>
  );
}
