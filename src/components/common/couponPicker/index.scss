@import 'src/style/index';

.coupon-picker {
  &_trigger {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: #fff;
    padding: r(15);
    margin-top: r(10);

    &-info {
      font-size: r(15);
      font-weight: 400;
      color: #333;
      line-height: r(21);
    }

    &-select {
      display: flex;
      align-items: center;

      &::after {
        content: '';
        position: relative;
        margin-left: 11px;
        right: 0;
        width: 9px;
        height: 9px;
        border-width: 1.8px 1.8px 0 0;
        border-style: solid;
        border-color: #d6d6d6 #d6d6d6 transparent transparent;
        transform: rotate(45deg);
      }
    }

    &-none {
      font-size: r(15);
      font-weight: 400;
      color: #999;
    }

    &-tip {
      color: #fff;
      padding: r(5) r(10);
      background: #ff5050;
      border-radius: 13px;
    }

    &-cut {
      font-size: r(13);
      font-weight: 600;
      color: #ff5050;

      &_space {
        padding-right: r(4);
      }
    }
  }

  &_popup {
    height: r(550);
    background: #fff;
    border-radius: 8px 8px 0 0;
    position: relative;

    &-header {
      display: flex;
      justify-content: center;
      align-items: center;
      height: r(50);
      font-size: r(16);
      font-weight: 600;
      color: #1e1e1e;
      border-bottom: 1px solid rgba($color: #e6e6e6, $alpha: 0.6);
    }

    &-body {
      padding: r(15) r(15) r(84);

      &_title {
        font-size: r(14);
        margin-bottom: r(10);
        font-weight: 400;
        color: #999;
      }
    }

    &-list {
      max-height: r(414);
      overflow-y: scroll;
    }

    &-footer {
      display: flex;
      justify-content: space-between;
      align-items: center;
      position: absolute;
      bottom: 0;
      left: 0;
      right: 0;
      padding: r(10) r(15) r(20);
      border-top: 1px solid rgba($color: #e6e6e6, $alpha: 0.6);
      background: #fff;

      &_confirm {
        display: flex;
        justify-content: center;
        align-items: center;
        width: r(150);
        height: r(44);
        background: #00bc70;
        border-radius: r(22);
        font-size: r(17);
        font-weight: 600;
        color: #fff;
      }
    }
  }
}
