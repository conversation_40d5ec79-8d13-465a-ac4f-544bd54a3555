import React, { useState } from 'react';
import Coupon from 'src/pages/video/components/Coupon';
import { CouponType } from 'src/store/coupon/type';
import { Popup } from 'zarm';
import IntegerPointPrice from '../integerPointPrice';
import './index.scss';

const PreClassName = 'coupon-picker';

export type CouponTypeCalc = CouponType & { coupon: CouponType };
interface Props {
  onChange: (value: CouponTypeCalc | undefined) => void;
  couponList: CouponTypeCalc[];
  value: CouponTypeCalc | undefined;
  discountFee: number | string
  disablePicker?: boolean; // 禁止重新选择优惠
  disableCoupon?: boolean; // 禁止使用任何优惠
}
export default function CouponSelectPopup(props: Props): JSX.Element {
  const { onChange, couponList, value, discountFee, disablePicker, disableCoupon } = props;
  const [popupVisible, setPopupVisible] = useState(false);
  const [selectedCoupon, setSelectedCoupon] = useState<CouponTypeCalc>();

  const onPopupOpen = () => {
    if (couponList.length > 0 && !disablePicker && !disableCoupon) {
      setPopupVisible(true);
    }
  };

  const onPopupClose = () => {
    setPopupVisible(false);
  };

  const onSelect = (coupon: CouponTypeCalc) => {
    setSelectedCoupon(selectedCoupon && coupon.id === selectedCoupon.id ? undefined : coupon);
  };

  return (
    <div className={PreClassName}>
      <div className={`${PreClassName}_trigger`} onClick={() => onPopupOpen()}>
        <div className={`${PreClassName}_trigger-info`}>权益/优惠券</div>
        {couponList.length > 0 && !disableCoupon ? (
          <div className={`${PreClassName}_trigger-select`}>
            {!selectedCoupon || !value ? (
              <div className={`${PreClassName}_trigger-tip`}>优惠券待使用</div>
            ) : (
              <div className={`${PreClassName}_trigger-cut`}>
                <span className={`${PreClassName}_trigger-cut_space`}>减</span>
                {selectedCoupon && <IntegerPointPrice value={discountFee} color='#FF5050' integerSize={14} pointSize={10} unitSize={10}></IntegerPointPrice>}
              </div>
            )}
          </div>
        ) : (
          <div className={`${PreClassName}_trigger-none`}>暂无优惠</div>
        )}
      </div>
      <Popup visible={popupVisible} direction='bottom' destroy={false} onMaskClick={onPopupClose}>
        <div className={`${PreClassName}_popup`}>
          <div className={`${PreClassName}_popup-header`}>我的权益优惠</div>
          <div className={`${PreClassName}_popup-body`}>
            <div className={`${PreClassName}_popup-list`}>
              <div className={`${PreClassName}_popup-body_title`}>我拥有的权益优惠</div>
              {<Coupon coupons={couponList.map((coupon) => ({ ...coupon, ...coupon.coupon, id: coupon.id }))} userCouponId={selectedCoupon?.id} onClick={onSelect}></Coupon>}
            </div>
          </div>
          <div className={`${PreClassName}_popup-footer`}>
            <div className=''>已选{selectedCoupon ? 1 : 0}张</div>
            <div
              className={`${PreClassName}_popup-footer_confirm`}
              onClick={() => {
                onChange(selectedCoupon);
                onPopupClose();
              }}
            >
              确认
            </div>
          </div>
        </div>
      </Popup>
    </div>
  );
}
