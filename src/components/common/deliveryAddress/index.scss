@import 'src/style/index';

.delivery_address_comp {
  width: r(345);
  padding: r(15);
  margin: 0 auto;
  background: #fff;
  border-radius: r(8);

  .d_a_title {
    @include display-flex;
    @include align-items(center);

    font-size: r(15);
    color: #999;

    .icon_title {
      width: r(18);
      margin-right: r(4);
    }
  }

  .active {
    color: var(--text-base-color);
  }

  .strong {
    font-weight: bold;
  }

  .title_text {
    font-size: r(16);
    color: #1e1e1e;
    font-weight: bold;
    @include flex(1);
  }

  .icon_narrow {
    margin-left: r(3);
    font-size: r(15);
    color: #d8d8d8;
  }

  .address_wrap {
    margin-top: r(12);
    overflow: hidden;

    .add-button {
      margin-top: r(6);
    }

    .address_info {
      position: relative;
      padding-right: r(50);

      p:first-of-type {
        font-size: r(15);
        color: #333;
        padding: r(15) 0 r(6);
      }

      p:last-of-type {
        font-size: r(13);
        color: #666;
      }

      .icon_narrow {
        position: absolute;
        right: r(0);
        top: 50%;
        transform: translateY(-50%);
        margin-top: r(8);
        margin-left: r(3);
        font-size: r(15);
        color: #d8d8d8;
      }
    }
  }
}
