import React from 'react';
import { DoubtFillinAddress } from 'src/pages/video/components/addressSpeed';
import { Icon, Button } from 'zarm';
import './index.scss';

const DeliveryAddress = (props) => {
  const { className = '', deliveryAddressId = '', address = {}, toMyAddress } = props;

  return <div className={`delivery_address_comp ${className}`} >
    <div className='d_a_title'>
      <img className='icon_title' src={require('src/pages/inquiryForm/images/icon_address.svg')} />
      <span className='title_text'>药品收货信息</span>
      <DoubtFillinAddress />
    </div>
    <div className={`address_wrap`}>
      {deliveryAddressId ? (
        <div className="address_info" onClick={toMyAddress}>
          <Icon className="icon_narrow" type="arrow-right" />
          <p>
            {address.districtName ? address.districtName.split('-').join('') : null}
            {address.contactUserAddress}
          </p>
          <p>
            {address.contactUserName} {address.contactUserPhone}
          </p>
        </div>
      ) : (
        <Button className="add-button" theme="primary" ghost block shape="round" onClick={toMyAddress}>
          添加收货地址
        </Button>
      )}
    </div>
  </div>
}

export default DeliveryAddress;