import React, { useCallback, useState, useEffect } from 'react';
import { Radio, Modal } from 'zarm';
import SvgIcon from '../svg';
import './index.scss';

const METHODS = [
  {
    id: '1',
    title: '快递配送',
    describe: '药店药品覆盖广，可选择性强',
  },
  {
    id: '2',
    title: '同城配送',
    describe: '送药快，药房药品受限',
  },
  {
    id: '3',
    title: '到店自取',
    describe: '系统自动匹配最佳药店，节省运费 ',
  },
];

const DeliveryMethodTips = () => {
  const alertTips = useCallback(() => {
    Modal.alert({
      content: (
        <div style={{ textAlign: 'left' }}>
          <p>配送方式是根据您的权益和当前收货地址确定的；</p>
          <p>北京、上海等核心地区支持同城配送；</p>
          <p>其他地区可使用快递，平均1-3个自然日内送达，部分偏远地区5个自然日内送达</p>
        </div>
      ),
      cancelText: '我知道了',
    });
  }, []);
  return (
    <p className="delivery_method_tip" onClick={alertTips}>
      <SvgIcon className="icon_tip" src={require('src/svgs/sprite-icon_tips.svg')} />
      配送说明
    </p>
  );
};

const DeliveryMethod = (props) => {
  const { className, deliveryWay = [], checkedDeliveryWay = '', deliveryClick } = props;
  const [methods, setMethods]: any = useState([]);

  useEffect(() => {
    if (deliveryWay.length) {
      const methods = METHODS.filter((item) => {
        return deliveryWay.includes(item.id);
      });
      setMethods(methods);
    }
  }, [deliveryWay]);

  return (
    <div className={`delivery_method_comp ${className}`}>
      <div className="d_m_title">
        <img className="icon_title" src={require('src/images/icon_delivery_method.png')} />
        <span className="title_text">配送方式</span>
        <DeliveryMethodTips />
      </div>
      <div className="d_m_types">
        <Radio.Group
          type="cell"
          value={checkedDeliveryWay}
          onChange={(value) => {
            deliveryClick && deliveryClick(value);
          }}
        >
          {methods.map((item) => {
            return (
              <Radio value={item.id} className={`d_m_radio_${checkedDeliveryWay == item.id ? 'checked' : ''}`} key={`d_m_${item.id}`}>
                <span>
                  {item.title}
                  <span className="d_m_describe">{item.describe}</span>
                </span>
              </Radio>
            );
          })}
        </Radio.Group>
      </div>
    </div>
  );
};

export default DeliveryMethod;
