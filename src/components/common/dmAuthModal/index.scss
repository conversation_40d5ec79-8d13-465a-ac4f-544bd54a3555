@import 'src/style/index';

.dm-auth-modal {
  position: relative;

  // .za-mask {
  //   background: rgba(0, 0, 0, 0.2);
  // }

  .za-popup {
    background: #fff;
    border-radius: r(16) r(16) 0 0;
    overflow: hidden;
  }

  .modal_close {
    position: absolute;
    right: r(16);
    top: r(16);
    width: r(18);
    height: r(18);
    z-index: 1;
  }

  .header {
    position: relative;
    height: r(174);
    padding-top: r(35);
    text-align: center;
    font-size: r(14);
    background: #f6f6f6;

    .auth_company {
      @include display-flex;
      @include align-items(center);
      @include justify-content(center);
    }

    .icon_logo {
      width: r(50);
      height: r(50);
      border-radius: 50%;
    }

    .icon_tranfor {
      width: r(17);
      height: r(17);
      margin: 0 r(15);
    }

    .describe {
      margin: r(16) r(33) 0;
      font-size: r(16);
      color: #333;
      text-align: center;
    }

    .text_red {
      color: #c93b2b;
    }
  }

  .main {
    margin-top: r(28);
    padding-bottom: r(5);

    .auth_item {
      position: relative;
      padding: 0 r(24) 0 r(42);
      margin-top: r(8);
      font-size: r(13);
      color: #909090;

      &:first-child {
        margin-top: 0;
      }

      &::before {
        position: absolute;
        content: '';
        top: r(8);
        left: r(33);
        width: r(3);
        height: r(3);
        border-radius: 50%;
        background: #cbcbcb;
      }
    }

    .text_stong {
      color: var(--text-base-color);
    }
  }

  .footer {
    padding: r(10) r(15);
    font-size: r(15);
    color: #999;
    text-align: center;

    &.agree_footer {
      position: absolute;
      width: 100%;
      left: 0;
      bottom: 0;
      background: #fff;
      border-top: r(1) solid #f5f5f5;
    }

    .za-button {
      height: r(52);
      line-height: r(52);
    }

    .from_auth {
      margin: 0 auto;
      width: r(319);
      height: r(54);
      line-height: r(54);
      font-weight: bold;
      font-size: r(19);
      color: #fff;
    }

    .btn_refuse {
      margin-top: r(10);
    }
  }

  .agree_modal_title {
    position: relative;
    height: r(50);
    line-height: r(50);
    padding-left: r(24);
    font-size: r(16);
    font-weight: bold;
    color: var(--text-base-color);
    border-bottom: r(1) solid #f5f5f5;

    &::after {
      position: absolute;
      content: '';
      width: r(32);
      height: r(3);
      left: r(56);
      bottom: 0;
      background: var(--text-base-color);
      border-radius: 3px;
    }
  }

  .agree_content {
    max-height: r(350);
    overflow-y: scroll;
    box-sizing: border-box;
    padding: r(20) r(24) r(86);
    font-size: r(13);
    color: #464646;
    line-height: r(24);

    &::-webkit-scrollbar {
      display: none;
    }

    .agree_title {
      margin-bottom: r(10);
      font-weight: bold;
      font-size: r(16);
      color: #464646;
    }
  }
}
