import React, { useCallback } from 'react';
import ReactDOM from 'react-dom';
import { cookies, fetchJson, getChannelResourceCode } from 'src/utils';
import { Button, Popup } from 'zarm';
import { fetch_channel_config } from 'src/store/config/action';
import { dmBridge } from '@dm/utils';

import './index.scss';

const dmLogin = (props) => {
  const { partnerCode, channelResourceCode, resolve } = props;
  const fetchChannelConfig = (options: any, success: any) => window.globalStore.dispatch(fetch_channel_config(options, success));
  // fetchChannelConfig({
  //   channelResourceCode: channelResourceCode || getChannelResourceCode(),
  //   openId: 'fdbb1f023834d1c6dfa8c7373129ab446e13ebdb120db15eb0b78dbc9468f992',
  // }, (res) => {
  //   const { code = '' } = res;
  //   if (code === '0') {
  //     fetchJson({
  //       url: '/api/api/v1/patient/partner/user/auth/saveUserAuth',
  //       type: 'POST',
  //       data: {
  //         thirdPlatformCode: partnerCode,
  //       },
  //       isloading: true,
  //     });
  //     DMAuthModal.hide();
  //     DMAuthModal.hideAgreement();
  //     resolve && resolve({});
  //   }
  // });
  // return;
  dmBridge.login('').then(() => {
    dmBridge.getZAToken().then(IBridgeResult => {
      const { data: { token = '' } = {} } = IBridgeResult;
      fetchChannelConfig({
        channelResourceCode: channelResourceCode || getChannelResourceCode(),
        openId: token,
      }, (res) => {
        const { code = '' } = res;
        if (code === '0') {
          fetchJson({
            url: '/api/api/v1/patient/partner/user/auth/saveUserAuth',
            type: 'POST',
            data: {
              thirdPlatformCode: partnerCode,
            },
            isloading: true,
          });
          DMAuthModal.hide();
          DMAuthModal.hideAgreement();
          resolve && resolve({});
        }
      });
    });
  });
}


const AuthButton = (props) => {
  const { btnText, className } = props;
  const saveAuth = useCallback(() => {
    dmLogin(props)
  }, [props]);
  return <Button theme='primary' shape="round" className={`btn_auth ${className}`} block onClick={saveAuth}>{btnText}</Button>
}

const AuthModal = (props) => {

  const { info, reject } = props;
  const refuseAuth = useCallback(() => {
    DMAuthModal.hide();
    reject && reject();
  }, []);

  const openAgreement = useCallback(() => {
    DMAuthModal.showAgreement(props);
  }, []);

  return (
    <Popup className='dm-auth-modal' visible={true}>
      <img src={require('src/images/icon_dm_close.png')} className='modal_close' onClick={refuseAuth} />
      <div className='header'>
        <div className='auth_company'>
          <img className='icon_logo' src={info.partnerImage} />
          <img className='icon_tranfor' src={require('src/images/icon_dm_transfor.png')} />
          <img className='icon_logo' src={info.ourImage} />
        </div>
        <p className='describe'><span className='text_red'>众安互联网医院</span>与众安保险携手打造保险和线上医疗相结合的健康管理平台</p>
      </div>
      <div className='main'>
        {((info.extraInfo || '').split(';') || []).map((item, i) => {
          return (
            item && <p className='auth_item' key={`item${i}`}>{item}</p>
          );
        })}
        <p className='auth_item' key={`item_agreemen`}>我已阅读并同意<span className="text_stong" onClick={openAgreement}>《用户授权书》</span></p>
      </div>
      <div className="footer">
        <AuthButton className="from_auth" btnText="确认授权" {...props} />
        <p className="btn_refuse" onClick={refuseAuth}>暂不授权</p>
      </div>
    </Popup>
  )
}

const AgreementModal = (props) => {

  const closeAgreement = useCallback(() => {
    DMAuthModal.hideAgreement();
  }, []);

  return <Popup className='dm-auth-modal' visible={true}>
    <img src={require('src/images/icon_dm_close.png')} className='modal_close' onClick={closeAgreement} />
    <div className='agree_modal_title'>用户授权协议</div>
    <div className='agree_content'>
      <p className='agree_title'>服务告知及个人信息授权书</p>
      <p>您通过众安在线财产保险股份有限公司(下简称“众安保险”)负责运营的众安保险APP（以下简称“众安保险平台”）向众安互联网医院（以下简称“众安互医”）办理健康相关服务（包括但不限于在线问诊服务）时，请仔细阅读以下条款。您的使用、登录等行为即视为您已阅读并同意本协议条款的约束。</p>
      <p>一、基于本授权协议，众安保险提供之服务内容如下：</p>
      <p>1．您充分理解并同意，众安保险仅向您提供众安互医服务入口，众安保险并非健康业务的提供方，您的实际交易行为与众安保险无关，您如需办理相关个人业务的需与众安互医另行签署相关协议。</p>
      <p>2．您同意并授权众安保险在您通过众安保险平台提供的入口进入众安互医页面并勾选确认本协议后即可将您在众安保险平台预留的相关个人信息（即：手机号）传输给众安互医使用，以便众安互医提供健康相关服务。以上授权期限自您授权之日起至您与众安互医间权利义务全部结清之日及法律法规及监管规定要求保留信息最少日期届满之日止。</p>
      <p>3. 您确认众安保险的上述行为并未违反您与众安保险之间的保密义务，且众安保险应免予承担任何违约责任或赔偿责任。</p>
      <p>4. 您确认并同意，对于您所接受使用的众安互医提供的健康服务，由相应众安互医向您提供客服服务。您在申请并使用健康业务过程中，与众安互医或因上述业务发生的任何纠纷及争议，应按照国家相关法律以及您与众安互医的相关协议处理，众安保险对此不承担任何责任。众安保险不对众安互医所提供的服务质量及内容承担保证责任。</p>
      <p>二、其他</p>
      <p>1、本告知书未尽事宜请以众安<a href='https://static.zhongan.com/website/sign/upload/privacyStatement/privacyStatement2.pdf' style={{ fontWeight: 'bold' }}>《个人信息保护政策》</a>及众安互医相关服务协议为准。</p>
      <p>2、众安保险仅承担本服务协议明确约定的直接责任。</p>
      <p>3、本服务协议条款中的部分条款被司法机关认定为违法，那么这些条款并不影响其他条款的有效性。</p>
    </div>
    <div className="footer agree_footer">
      <AuthButton btnText="我已逐页阅读上述内容并确认授权" {...props} />
    </div>
  </Popup>
}

const DMAuthModal: any = {
  check: (props) => {
    return new Promise((resolve, reject) => {
      // fetchJson({
      //   url: '/api/api/v1/patient/partner/user/auth/checkUserAuthByToken',
      //   data: {
      //     thirdPlatformCode: props.partnerCode,
      //     openId: 'fdbb1f023834d1c6dfa8c7373129ab446e13ebdb120db15eb0b78dbc9468f992',
      //     option: {
      //       needAttachment: true,
      //     },
      //   },
      //   type: 'POST',
      //   success: (res) => {
      //     const { isUserAuth = '', attchmentList = [] } = res.result || {};
      //     const result: any = res.result || {};
      //     attchmentList.map((item) => {
      //       result[item.attachmentType] = item.attachmentDownloadUrl;
      //     });
      //     // result.authNotice = (result.attchmentList || []).filter((item) => {
      //     //   return item.attachmentType === 'authNotice';
      //     // });
      //     if (isUserAuth === 'N') {
      //       DMAuthModal.show({ ...props, info: result, resolve, reject });
      //     } else {
      //       dmLogin({ ...props, info: res.result, resolve, reject })
      //     }
      //   }
      // });
      // return;

      dmBridge.login('').then(() => {
        dmBridge.getZAToken().then(IBridgeResult => {
          const { data: { token = '' } = {} } = IBridgeResult;
          fetchJson({
            url: '/api/api/v1/patient/partner/user/auth/checkUserAuthByToken',
            data: {
              thirdPlatformCode: props.partnerCode,
              openId: token,
              option: {
                needAttachment: true,
              },
            },
            needLogin: false,
            type: 'POST',
            success: (res) => {
              const { isUserAuth = '', attchmentList = [] } = res.result || {};
              const result: any = res.result || {};
              attchmentList.map((item) => {
                result[item.attachmentType] = item.attachmentDownloadUrl;
              });
              // result.authNotice = (result.attchmentList || []).filter((item) => {
              //   return item.attachmentType === 'authNotice';
              // });
              if (isUserAuth === 'N') {
                cookies.remove('za_token');
                cookies.remove('za_long_token');
                DMAuthModal.show({ ...props, info: result, resolve, reject });
              } else {
                dmLogin({ ...props, info: res.result, resolve, reject })
              }
            }
          });
        });
      });
    })
  },
  show: (props) => {
    if (!DMAuthModal.container) {
      DMAuthModal.container = document.createElement('div');
    }
    ReactDOM.render(<AuthModal {...props} />, DMAuthModal.container);
  },
  showAgreement: (props) => {
    if (!DMAuthModal.agreeContainer) {
      DMAuthModal.agreeContainer = document.createElement('div');
    }
    ReactDOM.render(<AgreementModal {...props} />, DMAuthModal.agreeContainer);
  },
  hide: () => {
    DMAuthModal.container && ReactDOM.render(<React.Fragment />, DMAuthModal.container);
  },
  hideAgreement: () => {
    DMAuthModal.agreeContainer && ReactDOM.render(<React.Fragment />, DMAuthModal.agreeContainer);
  },
  queueList: [],
}


export default DMAuthModal;