import React from 'react';
import classnames from 'classnames';
import './dotinit.scss';

interface DotInitProps {
  length?: number
  color?: string
  className?: string
}

const DotInit = (props: DotInitProps) => {
  const { className = '', color = "#b2b2b2", length = 3 } = props;
  const prefixCls = 'dot_component';
  const arr = new Array(length).fill('');
  
  return (
    <div className={classnames(className, prefixCls)}>
      <ul className={`${prefixCls}_row`}>
        {arr.map((NULL, index) => <li key={+index} className={`${prefixCls}_row_dot`} style={{ animationDelay: `${index * 0.4}s`, backgroundColor: color }}></li>)}
      </ul>
    </div>
  )
}

export default DotInit;