@import "src/style/index";

.drug {
  @include display-flex;

  padding: r(15) 0 0 r(12);

  &:last-of-type {
    .info-price {
      border-bottom: 0;
    }
  }

  .photo {
    width: r(44);
    height: r(44);
    border-radius: r(8);
  }

  .info-price {
    display: flex;
    justify-content: space-between;
    border-bottom: 1px solid #e6e6e6;
    @media (-webkit-min-device-pixel-ratio: 2) {
      border-bottom: 0.5px solid #e6e6e6;
    }

    padding-bottom: r(15);
    margin-left: r(10);
    width: 100%;
  }

  .subname-sum {
    font-size: r(12);
    margin-right: r(12);

    .p {
      color: #1e1e1e;
      font-weight: bold;
      font-size: r(12);
      margin-bottom: r(5);

      .integer {
        font-size: r(14);
      }
    }

    .n {
      font-weight: bold;
      font-size: r(13);
      color: #9b9b9b;
      text-align: right;
    }
  }

  .name-price {
    margin-right: r(15);
    font-size: r(15);

    .sub_name {
      @include line(2);

      font-size: r(12);
      color: #999;
      margin-bottom: r(6);
    }

    .name {
      @include line(2);

      color: #1e1e1e;
      margin-bottom: r(5);
      font-size: r(14);
    }
  }

  .drug-type {
    border: 1px solid #00a864;
    border-radius: 4px;
    padding: r(1) r(4);
    font-size: r(11);
    color: #00a864;
  }
}
