import React from 'react';
import './index.scss';
import { SvgIcon } from 'src/components/common';
import { THIRD_PLATFORM_RESOURCECODE } from 'src/utils/staticData';
import { sjxyxName } from './static';

interface DrugItemType {
  good: any,
  orderDetail?: any,
  page?: string
  isFromShop?: boolean
  isChinese?: boolean
}
const ORDER = 'order';


export const renderPrice = (price: Number = 0, isChinese = false) => {
  if (!price) {
    return;
  }
  const p = String(price).split('.');
  const _price = price.toFixed(isChinese && typeof p[1] === 'string' && p[1].length > 2 ? 4 : 2);
  const [integer, point] = _price.split('.');
  return <React.Fragment>
    <span className='sign'>¥</span><span className='integer'>{integer}</span>
    <span className='point'>.{point}</span>
  </React.Fragment>;
};

export const renderPriceHasZero = (price: Number = 0, isChinese = false) => {
  const p = String(price).split('.');
  const _price = price.toFixed(isChinese && typeof p[1] === 'string' && p[1].length > 2 ? 4 : 2);
  const [integer, point] = _price.split('.');
  return <React.Fragment>
    <span className='sign'>¥</span><span className='integer'>{integer}</span>
    <span className='point'>.{point}</span>
  </React.Fragment>;
};


const DrugItem = (props: DrugItemType) => {
  const { page = ORDER } = props;
  const { isFromShop = false, isChinese } = props;
  const {
    goodsEntry: {
      drugsPictureList = [],
      attachmentList: [attachmentItem = {}] = [],
      attachmentList = [],
      drugName = '',
      drugCommonName = '',
      drugType: goodsEntryDrugType = '',
      drugsPictureUrl = '',
      drugSpecifications = '',
      drugPackingUnit = '',
    } = {},
    goodsPrice = '',
    goodsRealPrice = '',
    goodsNum = '',
    goodsDetailNum,
    drugType, goodsName,
    goodsPicture = [],
    goodsDrugSpecifications, goodsDrugPackingUnit, ext,
  } = props.good;
  const [attachmentItemFirst = {}] = drugsPictureList.length ? drugsPictureList : goodsPicture;
  const { businessEntry: { appointmentSchedule = {} } = {}, platformCode = '', orderType, subOrderTypeAlias, subOrderType } = props.orderDetail || {};
  const isPhysiotherapy = orderType === 'physiotherapy';
  const isHealthCheckOrDentistry = ['healthCheck', 'dentalServices'].includes(orderType);  // 体检商品类型或者拜博齿科

  const { attachment = {} } = attachmentItemFirst;
  let thumbnail = (attachment.attachmentDownloadUrl && `${attachment.attachmentDownloadUrl}`) || '';
  if (platformCode === THIRD_PLATFORM_RESOURCECODE.LHYW) {
    thumbnail = attachment.thirdAttachmentDownloadUrl || '';
  } else if (isFromShop && ext) {
    // 商城的解析
    thumbnail = JSON.parse(ext).goodsPictureUrl;
  } else if (
    platformCode === THIRD_PLATFORM_RESOURCECODE.SYY_DRUGSTROE ||
    platformCode === THIRD_PLATFORM_RESOURCECODE.SYY_MB ||
    platformCode === THIRD_PLATFORM_RESOURCECODE.SYY_XB ||
    isPhysiotherapy ||
    isHealthCheckOrDentistry
  )  {
    // 如果是上药云慢病，后端这边不重新做嵌套层级了
    thumbnail = drugsPictureUrl;
  } else if(isChinese){
    thumbnail = require('src/images/default-chinese.png');
  } else if(orderType === 'productServpack') {
    thumbnail = attachmentList.filter((item) => item.attachmentType === 'commodityListPicture')[0]?.attachmentDownloadUrl;
  } else if(platformCode === THIRD_PLATFORM_RESOURCECODE.RJ) {
    thumbnail = 'https://static.za-doctor.com/common/renji-goods.png';
  } else if(orderType === 'serviceOrder') {
    thumbnail = require('src/images/icon_servpack.png');
  }

  const renderSubName = () => {
    if (isPhysiotherapy || isFromShop || isHealthCheckOrDentistry) {
      return null;
    }
    // const THIRD_CODE = [THIRD_PLATFORM_RESOURCECODE.LHYW, THIRD_PLATFORM_RESOURCECODE.SYY_DRUGSTROE, THIRD_PLATFORM_RESOURCECODE.SYY_MB, THIRD_PLATFORM_RESOURCECODE.SYY_XB];
    const orderPackingUnit = page === ORDER ? drugPackingUnit : goodsDrugPackingUnit;
    const newDrugSpecifications = page === ORDER ? drugSpecifications : goodsDrugSpecifications;

    return (newDrugSpecifications && <div className='sub_name'>规格 {`${newDrugSpecifications}${orderPackingUnit && '/'}${orderPackingUnit}`}</div>);
  };

  const renderDesc = () => {
    if (isPhysiotherapy) {
      return <span className='sub_name'>
        {appointmentSchedule.enableAppointStartTime}&nbsp;&nbsp;{appointmentSchedule.serviceDuration}分钟
      </span>;
    }
    return (
      (drugType || goodsEntryDrugType) && <span className='drug-type'>{drugType || goodsEntryDrugType}</span>
    );
  };
  const renderName = () => {
    if(subOrderType === 'PremiumHealthServices_sjxyx') {
      return sjxyxName[subOrderTypeAlias] || '四季小药箱';
    }
    if (isFromShop || (page === ORDER)) {
      return goodsName;
    } else {
      return drugName || drugCommonName || goodsName;
    }
  };


  return (
    <div className='drug'>
      {thumbnail ? <SvgIcon className='photo' type='img' src={thumbnail} /> : <SvgIcon className='photo' src={require('src/svgs/sprite-icon-drug.svg')} />}
      <div className='info-price'>
        <div className='name-price'>
          <div className='name'>{renderName()}</div>
          {renderSubName()}
          {renderDesc()}
        </div>
        <div className='subname-sum'>
          <div className='p'>{renderPrice(goodsPrice || goodsRealPrice, isChinese)}</div>
          {
            !!(goodsDetailNum || goodsNum) && <div className='n'>×{isChinese ? goodsDetailNum : goodsNum}</div>
          }
        </div>

      </div>
    </div>
  );
};
export default DrugItem;
