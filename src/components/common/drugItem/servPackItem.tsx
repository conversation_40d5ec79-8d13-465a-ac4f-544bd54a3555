/*
 * @Author: <EMAIL> <EMAIL>
 * @Date: 2022-06-20 15:32:23
 * @LastEditTime: 2022-11-25 13:56:24
 * @LastEditors: <EMAIL> <EMAIL>
 * @FilePath: /za-asclepius-patient-h5/src/components/common/drugItem/servPackItem.tsx
 * @Description:
 */
import React from 'react';
import { SvgIcon } from 'src/components/common';
import format from 'src/utils/format';
import { renderPrice } from './index';
import './index.scss';
import { sjxyxName } from './static';

interface ServPackType {
  good: any,
  order: any,
  orderStatus: any,
  isExistsExprss: any,
  toCheckExpress: any,
}

const ServPackItem = (props: ServPackType) => {
  const {
    good: { goodsName, goodsPrice, goodsRealPrice, goodsNum, goodsEntry = {} } = {},
    order: { orderType, subOrderType, subOrderTypeAlias },
  } = props;
  const { effectiveTime = '', expireTime = '' } = goodsEntry || {};
  const timeRange = effectiveTime && expireTime ? `${format.date(effectiveTime, 'yyyy.MM.dd')}-${format.date(expireTime, 'yyyy.MM.dd')}` : '';

  const img = {
    signDetection: require('src/images/checkBody.png'),
    skinTextureTest: require('src/images/checkBody.png'),
  }[orderType] || require('src/images/icon_servpack.png');

  const renderName = () => {
    if(subOrderType === 'PremiumHealthServices_sjxyx') {
      return sjxyxName[subOrderTypeAlias] || '四季小药箱';
    } else {
      return goodsName;
    }
  };

  return (
    <div className='drug'>
      <SvgIcon className='photo' type='img' src={img} />
      <div className='info-price'>
        <div className='name-price'>
          <div className='name'>{renderName()}</div>
          <div className='sub_name'>{timeRange ? `服务有效期：${timeRange}` : ''}</div>
        </div>
        <div className='subname-sum'>
          <div className='p'>{renderPrice(goodsPrice || goodsRealPrice)}</div>
          {!!goodsNum && <div className='n'>×{goodsNum}</div>}
        </div>
      </div>
    </div>
  );
};
export default ServPackItem;
