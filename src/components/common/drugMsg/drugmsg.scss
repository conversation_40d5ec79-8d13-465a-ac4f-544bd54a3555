@import 'src/style/index';

.comp_drug_msg {
  width: r(252);
  padding: 0 r(14);
  background: linear-gradient(360deg, rgba(255, 255, 255, 0.5) 0%, #e5f6ef 100%);
  box-shadow: 0 r(4) r(16) 0 rgba(0, 0, 0, 0.03);
  font-size: r(14);
  border-radius: 0 r(8) r(8) r(8);

  .title {
    margin-bottom: r(13);
    height: r(45);
    line-height: r(45);
    white-space: nowrap;
    color: rgba(0, 0, 0, 0.8);
    border-bottom: 1px solid rgba(0, 0, 0, 0.08);
  }

  .drug_index {
    margin: r(10) 0 r(7);
    color: #999;
  }

  .drug_item {
    font-size: r(12);
    font-weight: bold;
    color: #999;

    @include display-flex;
  }

  .drug_info {
    @include flex(1);
  }

  .drug_name {
    line-height: 1.35;
    font-size: r(14);
    color: #333;
  }

  .drug_icon {
    width: r(52);
    height: r(52);
    margin-right: r(14);
    background: #3b3b3b;
    border-radius: r(8);
    border: 1px solid #e6e6e6;
  }

  .drug_num {
    margin-left: r(5);
  }

  .btn_wrap {
    padding: r(14) 0;

    .za-button {
      font-size: r(15);
      font-weight: bold;
    }
  }
}

.comp_drug_not_support {
  padding: r(14);
}

.comp_drug_usage {
  width: r(252);
  padding: 0 r(14) r(14);
  box-shadow: 0 r(4) r(16) 0 rgba(0, 0, 0, 0.03);
  font-size: r(14);
  color: rgba(0, 0, 0, 0.8);
  background: #fff;
  border-radius: 0 r(8) r(8) r(8);
  position: relative;

  .title {
    margin-bottom: r(13);
    height: r(45);
    line-height: r(45);
    font-weight: bold;
    white-space: nowrap;
    border-bottom: 1px solid rgba(0, 0, 0, 0.08);
  }

  .drug_usage {
    word-break: break-all;

    &.fold {
      @include line($num: 3);
    }
  }

  .drug_usage_hide {
    position: absolute;
    word-break: break-all;
    z-index: -1;
    left: r(14);
    right: r(14);
    top: 0;
    opacity: 0;
  }

  .btn_toggle_fold {
    margin-top: r(10);
    text-align: center;
    font-size: r(14);
    color: var(--theme-primary);

    .icon_arrow {
      width: r(12);
      height: r(12);
      margin-left: r(5);
      font-size: r(8);
      transform: rotate(-90deg);
      transition: all 0.2s;

      --arrow-color: var(--theme-primary);
    }

    &.fold {
      .icon_arrow {
        transform: rotate(90deg);
      }
    }
  }
}
