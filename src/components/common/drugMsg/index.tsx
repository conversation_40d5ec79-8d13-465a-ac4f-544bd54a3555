import React, { useCallback, useEffect, useLayoutEffect, useRef, useState } from 'react';
import { shallowEqual, useDispatch, useSelector } from 'react-redux';
import { ApplicationState } from 'src/store';
import { FETCH_USER_CHANNEL } from 'src/store/channel/action-types';
import { DEPLOY__ENV } from 'src/utils/env';
import { Deserialize, Serialize } from 'src/utils/serialization';
import { THIRD_PLATFORM_RESOURCECODE } from 'src/utils/staticData';
import { Button } from 'zarm';
import SvgIcon from '../svg';
import './drugmsg.scss'

const channelPoint = DEPLOY__ENV({
  'tst': 'tOp00C46',
  'uat': 'x7nhxTMM',
  'prd': 'PhGKZvlh',
});

const appletParams = ['token', 'expire', 'channelResourceCode', 'channelSource', 'isReal', 'maskActId', 'maskusId', 'userId', 'openId', 'userBlackFlag', 'ownAppletAppid'];
export const handleUrlSearchs = () => {
  let searchParam: any = Deserialize(location.search);
  for (let key in searchParam) {
    if (appletParams.includes(key)) {
      delete searchParam[key]
    }
  }
  console.log(searchParam);
  return Serialize(searchParam);
}

const DrugMsg = (props) => {
  const { msg_id, replyContent: { content = '' } } = props;
  const drugList = JSON.parse(content);
  const len = drugList.length;

  const { applicationId, resourceName, applicationIds } = useSelector((state: ApplicationState) => {
    const { applicationId, resourceName } = state.userChannel;
    const { PUSHCOMMODITYCHANNEL = [] } = state.dictionary;
    const applicationIds = JSON.parse(PUSHCOMMODITYCHANNEL[0]?.resValue || '[]');
    return {
      applicationId,
      resourceName,
      applicationIds
    };
  }, shallowEqual);

  const dispatch = useDispatch();
  useEffect(() => {
    if (!resourceName) {
      dispatch({ type: FETCH_USER_CHANNEL });
    }
  }, [resourceName])

  const picList = drugList.filter((item) => {
    return !!item.drugsPictureUrl;
  });

  const noPic = !picList.length;

  const goToMall = useCallback(() => {
    const search = handleUrlSearchs();
    const backUrl = encodeURIComponent(`${location.origin}${location.pathname}${search ? '?' + search : ''}`);
    const param = {
      partnerCode: THIRD_PLATFORM_RESOURCECODE.ZA_HAOYAOSHI,
      businessType: 'commodityRecommend',
      channelPoint: channelPoint,
      bizNo: drugList[0].commodityId,
      relatedBizNo: drugList[0].recommendNo,
      backUrl,
    };
    window.reactHistory.push({
      pathname: '/hospital/preauth',
      search: Serialize(param),
    });
  }, [drugList]);

  if (!applicationIds.includes(applicationId)) {
    return <p className='comp_drug_not_support'>该入口暂不支持的消息类型</p>
  }

  return <div className='comp_drug_msg'>
    <p className='title'>根据您的症状，推荐使用以下药品：</p>
    {
      drugList.map((item, index) => {
        return <div key={`${msg_id}_${item.commodityCode}`}>
          {len > 1 && <p className='drug_index'>药品{index + 1}</p>}
          <div className='drug_item'>
            {
              !noPic && <img className='drug_icon' src={item.drugsPictureUrl} />
            }
            <div className='drug_info'>
              <p className='drug_name'>{item.commodityName}</p>
              {
                !noPic && <p>x1</p>
              }
            </div>
            {noPic && <p className='drug_num'>x1</p>}
          </div>
        </div>
      })
    }
    <div className='btn_wrap'><Button block size='sm' theme='primary' shape='round' onClick={goToMall}>查看并购买</Button></div>
  </div>
}

const DrugUsage = (props) => {
  const { msg_id, replyContent: { content = '', title = '' } } = props;
  const [fold, setFold] = useState(true);
  const [showMore, setShowMore] = useState(false);
  const drugList = JSON.parse(content);

  const realList = drugList.filter((item) => {
    return !!item.drugUsageDosage;
  });

  const ellipseRef: any = useRef(null);

  const noEllipseRef: any = useRef(null);

  // if (!realList.length) {
  //   return null;
  // }

  useLayoutEffect(() => {
    if (realList.length) {
      if (noEllipseRef.current.clientHeight > ellipseRef.current.clientHeight) {
        setShowMore(true);
      } else {
        setShowMore(false);
      }
    }
  }, []);

  const toggleFold = useCallback(() => {
    setFold(!fold);
  }, [fold]);

  return <div className='comp_drug_usage'>
    <p className='title'>{title}</p>
    <div className={`drug_usage ${fold ? 'fold' : ''}`} ref={ellipseRef}>
      {
        realList.map((item) => {
          return <p key={`${msg_id}_${item.commodityCode}`}>
            {item.commodityName}：{item.drugUsageDosage}
          </p>
        })
      }
    </div>
    <div className={`drug_usage_hide`} ref={noEllipseRef}>
      {
        realList.map((item, index) => {
          return <p key={`${msg_id}_${item.commodityCode}_hide`}>
            {item.commodityName}：{item.drugUsageDosage}
          </p>
        })
      }
    </div>
    {
      showMore && <div className={`btn_toggle_fold ${fold ? 'fold' : ''}`} onClick={toggleFold}>
        <span>{fold ? '展开' : '收起'}</span>
        <SvgIcon className='icon_arrow' src={require('src/svgs/sprite-arrow-right.svg')} />
      </div>
    }
  </div>
}


export default {
  drugMsg: (props) => <DrugMsg {...props} />,
  drugUsage: (props) => <DrugUsage {...props} />,
}
