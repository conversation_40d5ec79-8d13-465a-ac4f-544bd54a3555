import React, { useCallback } from 'react';
import { Link } from 'react-router-dom';
import { SvgIcon } from 'src/components/common';
import { isFromCAINIAO, isFromAliNOCAINIAO } from 'src/utils/staticData';
import validate from 'src/utils/validate';
import { Button } from 'zarm';
import './emptycover.scss';

import noSvg from './images/video-empty.svg';
import { Deserialize } from 'src/utils';
import { isFromAliER } from 'src/utils/staticData';

interface EmptyProps {
  config: {
    text: string,
    icon: string
  },
  type: 'text' | 'video' | 'card' | 'static' | 'nodata' | 'safeguard'
  position?: string;
}

const EmptyCover = (props: EmptyProps) => {
  const { config: { text, icon }, type, position = '' } = props;
  const { search = '' } = location;
  const { policyNo, patientId } = Deserialize(search);
  const isClaim = ((position === 'myInquiry' || position === 'myOrder') && policyNo) || isFromAliER();
  const noVideoPrefixCls = 'no-video-empty-component';
  const prefixCls = 'empty-component';

  const toChat = useCallback(() => {
    if (isClaim) {
      window.reactHistory.push({
        pathname: '/hospital/inquiryform',
        search: `policyNo=${policyNo}&patientId=${patientId}`,
      });
    } else {
      window.reactHistory.push({
        pathname: '/hospital/chatmedicalmanage',
      });
    }
  }, [isClaim, policyNo, patientId]);

  if (type === 'video' && !validate.isFromMiniApplet()) {
    return (
      <div className={noVideoPrefixCls}>
        <SvgIcon type='img' width={80} height={80} src={noSvg} />
        {!validate.isAlipayApplet() ? (<p className='text'>本平台的视频问诊单列表功能暂未完成<br />您可请前往众安互联网医院小程序体验</p>) : (<p className='text'>视频问诊功能建设中</p>)}
      </div>
    );
  } else if (type === 'card') {
    return (
      <div className={`${prefixCls} ${prefixCls}__${type}`}>
        <SvgIcon type='img' src={icon} />
        <p className='text'>{text}</p>
      </div>
    );
  } else if (type === 'video') {
    return (
      <div className={prefixCls}>
        <SvgIcon type='img' src={icon} />
        <p className='text'>{text}</p>
        <Link to={{ pathname: '/hospital/videointro' }}>
          <Button theme='primary' block className='add_btn'>立即问诊</Button>
        </Link>
      </div>
    );
  } else if (type === 'static') {
    return (
      <div className={prefixCls}>
        <SvgIcon type='img' src={icon} />
        <p className='text'>{text}</p>
      </div>
    );
  } else if (type === 'nodata') {
    return (
      <div className={`${prefixCls} ${prefixCls}_${type}`}>
        <SvgIcon type='img' src={icon} />
        <p className='text'>{text}</p>
        {
          (!isFromCAINIAO() && !isFromAliNOCAINIAO()) && (
            <div>
              <Button theme='primary' block className='add_btn' onClick={toChat}>{isClaim ? '立即问诊' : '咨询医生'}</Button>
            </div>
          )
        }
      </div>
    );
  } else if(type === 'safeguard') {
    return (
      <div className={`${prefixCls} ${prefixCls}_nodata`}>
        <SvgIcon type='img' src={icon} />
        <p className='text'>{text}</p>
        <div>
          <Button theme='primary' block className='add_btn' onClick={() =>
            window.location.href = validate.isAlipayApplet() ?
              'https://webchat-sh.clink.cn/chat.html?accessId=1936f9be-d24f-46cf-b0d1-09d34b8bef9d&language=zh_CN'
              : 'https://webchat-sh.clink.cn/chat.html?accessId=1936f9be-d24f-46cf-b0d1-09d34b8bef9d&language=zh_CN'
          }>联系客服</Button>
        </div>
      </div>
    );
  } else {
    return (
      <div className={prefixCls}>
        <SvgIcon type='img' src={icon} />
        <p className='text'>{text}</p>
        <div>
          <Button theme='primary' block className='add_btn' onClick={toChat}>立即问诊</Button>
        </div>
      </div>
    );
  }
};

export default EmptyCover;
