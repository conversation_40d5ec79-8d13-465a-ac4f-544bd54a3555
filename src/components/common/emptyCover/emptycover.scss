@import "src/style/index";
$NoVideoPrefixCls: 'no-video-empty-component';
$prefixCls: 'empty-component';

.#{$prefixCls} {
  @include flex(1);
  @include display-flex;
  @include align-items(center);

  flex-direction: column;
  padding-top: r(80);

  img {
    display: block;
    // width: r(120);
    height: r(80);
  }

  .text {
    margin: r(40) 0;
    color: #9b9b9b;
    font-size: r(15);
  }

  .add_btn {
    width: r(250);
    height: r(44);
    text-align: center;
    line-height: r(44);
    border-radius: r(4);
  }

  &__card,
  &_nodata {
    padding-top: r(20);

    img {
      height: r(115);
    }

    .text {
      color: #666;
      margin: 0 0 r(20);
      font-size: r(15);
    }

    .add_btn {
      width: r(100);
      height: r(36);
      line-height: r(36);
      border-radius: r(18);
      font-size: r(15);
      font-weight: bold;
    }
  }

  &_nodata {
    padding-top: r(80);

    .text {
      margin: r(10) 0 r(20);
      font-size: r(15);
      line-height: 1em;
      color: rgba(0, 0, 0, 0.8);
    }
  }
}

.#{$NoVideoPrefixCls} {
  min-height: 50vh;
  padding: r(88) 0 r(20);
  text-align: center;
  font-size: r(15);
  color: #9b9b9b;
  line-height: r(24.5);

  .sprite-icon-components {
    margin-bottom: r(44);
  }
}
