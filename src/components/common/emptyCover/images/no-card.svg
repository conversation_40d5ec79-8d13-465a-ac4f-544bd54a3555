<?xml version="1.0" encoding="UTF-8"?>
<svg width="231px" height="231px" viewBox="0 0 231 231" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>服务卡缺省图</title>
    <defs>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-1">
            <stop stop-color="#E1F2EC" offset="0%"></stop>
            <stop stop-color="#E1F1EC" offset="100%"></stop>
        </linearGradient>
        <filter x="0.0%" y="0.0%" width="100.0%" height="100.0%" filterUnits="objectBoundingBox" id="filter-2">
            <feGaussianBlur stdDeviation="0" in="SourceGraphic"></feGaussianBlur>
        </filter>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-3">
            <stop stop-color="#E1F2EC" offset="0%"></stop>
            <stop stop-color="#E1F1EC" offset="100%"></stop>
        </linearGradient>
        <filter x="0.0%" y="0.0%" width="100.0%" height="100.0%" filterUnits="objectBoundingBox" id="filter-4">
            <feGaussianBlur stdDeviation="0" in="SourceGraphic"></feGaussianBlur>
        </filter>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-5">
            <stop stop-color="#E1F2EC" offset="0%"></stop>
            <stop stop-color="#E1F1EC" offset="100%"></stop>
        </linearGradient>
        <filter x="54.5%" y="0.0%" width="45.5%" height="96.4%" filterUnits="objectBoundingBox" id="filter-6">
            <feGaussianBlur stdDeviation="0" in="SourceGraphic"></feGaussianBlur>
        </filter>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-7">
            <stop stop-color="#E1F2EC" offset="0%"></stop>
            <stop stop-color="#E1F1EC" offset="100%"></stop>
        </linearGradient>
        <filter x="0.0%" y="0.0%" width="100.0%" height="100.0%" filterUnits="objectBoundingBox" id="filter-8">
            <feGaussianBlur stdDeviation="0" in="SourceGraphic"></feGaussianBlur>
        </filter>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-9">
            <stop stop-color="#E1F2EC" offset="0%"></stop>
            <stop stop-color="#E1F1EC" offset="100%"></stop>
        </linearGradient>
        <filter x="0.0%" y="0.0%" width="100.0%" height="100.0%" filterUnits="objectBoundingBox" id="filter-10">
            <feGaussianBlur stdDeviation="0" in="SourceGraphic"></feGaussianBlur>
        </filter>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-11">
            <stop stop-color="#0DD081" offset="0%"></stop>
            <stop stop-color="#00A864" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-12">
            <stop stop-color="#BDE6D6" offset="0%"></stop>
            <stop stop-color="#83CDAF" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-13">
            <stop stop-color="#0DD081" offset="0%"></stop>
            <stop stop-color="#00A864" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-14">
            <stop stop-color="#0DD081" offset="0%"></stop>
            <stop stop-color="#00A864" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="-8.32416373%" y1="32.6216382%" x2="78.2364107%" y2="69.2025392%" id="linearGradient-15">
            <stop stop-color="#BFF0DC" offset="0%"></stop>
            <stop stop-color="#8BD1B4" offset="100%"></stop>
        </linearGradient>
    </defs>
    <g id="0916" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="1012-服务卡空态" transform="translate(-260.000000, -375.000000)">
            <g id="编组-4" transform="translate(260.000000, 375.000000)">
                <rect id="区域" stroke="#979797" fill="#D8D8D8" opacity="0" x="0.5" y="0.5" width="230" height="230"></rect>
                <ellipse id="椭圆形" fill="url(#linearGradient-1)" filter="url(#filter-2)" cx="115" cy="180" rx="103" ry="24"></ellipse>
                <path d="M20,111 L32,111 L32,122 L44,122 L44,178 L20,178 L20,111 Z" id="形状结合" fill="url(#linearGradient-3)" opacity="0.560849144" filter="url(#filter-4)"></path>
                <path d="M224.859375,72 L242.859375,72 L242.859,88 L259.859375,88 L259.859375,180 L224.859375,180 L224.859375,72 Z" id="形状结合备份" fill="url(#linearGradient-5)" opacity="0.560849144" filter="url(#filter-6)" transform="translate(221.429688, 128.000000) scale(-1, 1) translate(-221.429688, -128.000000) "></path>
                <path d="M183,22 C188.430757,22 193.01892,24.555365 194.498759,28.0612033 C199.310515,28.4590485 203,30.7425498 203,33.5 C203,36.5375661 198.522847,39 193,39 C191.155698,39 189.428007,38.7253986 187.944715,38.2464785 C186.437126,38.7307119 184.76296,39 183,39 C176.372583,39 171,35.1944204 171,30.5 C171,25.8055796 176.372583,22 183,22 Z" id="形状结合" fill="url(#linearGradient-7)" opacity="0.597771" filter="url(#filter-8)"></path>
                <path d="M27,60 C32.4307569,60 37.0189198,62.555365 38.4987589,66.0612033 C43.3105148,66.4590485 47,68.7425498 47,71.5 C47,74.5375661 42.5228475,77 37,77 C35.1556976,77 33.4280071,76.7253986 31.9447154,76.2464785 C30.4371265,76.7307119 28.7629601,77 27,77 C24.1264478,77 21.4887999,76.2845647 19.4227045,75.0914447 C18.2318875,76.2598802 16.4680908,77 14.5,77 C10.9101491,77 8,74.5375661 8,71.5 C8,68.4624339 10.9101491,66 14.5,66 C14.8263501,66 15.1470828,66.0203507 15.4605224,66.0596341 L15.499973,66.0642094 C16.9784578,62.5568259 21.567691,60 27,60 Z" id="形状结合" fill="url(#linearGradient-9)" opacity="0.842633929" filter="url(#filter-10)"></path>
                <g id="编组-2" transform="translate(44.000000, 36.000000)">
                    <path d="M39,10 L127,10 C129.761424,10 132,12.2385763 132,15 L132,84 C132,86.7614237 129.761424,89 127,89 L39,89 C36.2385763,89 34,86.7614237 34,84 L34,15 C34,12.2385763 36.2385763,10 39,10 Z" id="Rectangle-7" fill="url(#linearGradient-11)" opacity="0.142522"></path>
                    <path d="M15,0 L115,0 C117.761424,-5.07265313e-16 120,2.23857625 120,5 L120,74 C120,76.7614237 117.761424,79 115,79 L15,79 C12.2385763,79 10,76.7614237 10,74 L10,5 C10,2.23857625 12.2385763,5.07265313e-16 15,0 Z" id="Rectangle-7" fill="url(#linearGradient-12)"></path>
                    <path d="M22,8 L108,8 C110.209139,8 112,9.790861 112,12 L112,98 C112,100.209139 110.209139,102 108,102 L22,102 C19.790861,102 18,100.209139 18,98 L18,12 C18,9.790861 19.790861,8 22,8 Z" id="Rectangle-8" fill="#FFFFFF" opacity="0.947473"></path>
                    <path d="M31,40 L99,40 C100.656854,40 102,41.3431458 102,43 C102,44.6568542 100.656854,46 99,46 L31,46 C29.3431458,46 28,44.6568542 28,43 C28,41.3431458 29.3431458,40 31,40 Z" id="Rectangle-8" fill="url(#linearGradient-13)" opacity="0.142522"></path>
                    <path d="M31,24 L81,24 C82.6568542,24 84,25.3431458 84,27 C84,28.6568542 82.6568542,30 81,30 L31,30 C29.3431458,30 28,28.6568542 28,27 C28,25.3431458 29.3431458,24 31,24 Z" id="Rectangle-8" fill="url(#linearGradient-14)" opacity="0.142522"></path>
                    <path d="M5,62 L48.8715045,62 C51.5347651,62 54.0233973,63.3253496 55.5097402,65.5352685 L63.2656687,77.0669096 L63.2656687,77.0669096 L136.92309,75.9337727 C139.684187,75.8912963 141.956932,78.0951738 141.999408,80.8562708 C141.999803,80.8819057 142,80.9075432 142,80.9331811 L142,145 C142,147.761424 139.761424,150 137,150 L5,150 C2.23857625,150 3.38176876e-16,147.761424 0,145 L0,67 C-3.38176876e-16,64.2385763 2.23857625,62 5,62 Z" id="Rectangle" fill="url(#linearGradient-15)"></path>
                    <g id="编组" transform="translate(53.000000, 95.000000)" fill="#FFFFFF">
                        <path d="M3,15 L33,15 C34.6568542,15 36,16.3431458 36,18 C36,19.6568542 34.6568542,21 33,21 L3,21 C1.34314575,21 2.02906125e-16,19.6568542 0,18 C-2.02906125e-16,16.3431458 1.34314575,15 3,15 Z" id="Rectangle-8备份"></path>
                        <path d="M3,15 L33,15 C34.6568542,15 36,16.3431458 36,18 C36,19.6568542 34.6568542,21 33,21 L3,21 C1.34314575,21 2.02906125e-16,19.6568542 0,18 C-2.02906125e-16,16.3431458 1.34314575,15 3,15 Z" id="Rectangle-8备份" transform="translate(18.000000, 18.000000) rotate(90.000000) translate(-18.000000, -18.000000) "></path>
                    </g>
                </g>
            </g>
        </g>
    </g>
</svg>