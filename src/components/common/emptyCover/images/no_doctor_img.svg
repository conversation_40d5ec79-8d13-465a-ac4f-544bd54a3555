<?xml version="1.0" encoding="UTF-8"?>
<svg width="230px" height="230px" viewBox="0 0 230 230" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>空态 图 未搜索到</title>
    <defs>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-1">
            <stop stop-color="#D3EDE4" stop-opacity="0.837713068" offset="0%"></stop>
            <stop stop-color="#E1F1EC" stop-opacity="0.242870411" offset="100%"></stop>
        </linearGradient>
        <filter x="0.0%" y="0.0%" width="100.0%" height="100.0%" filterUnits="objectBoundingBox" id="filter-2">
            <feGaussianBlur stdDeviation="0" in="SourceGraphic"></feGaussianBlur>
        </filter>
        <linearGradient x1="17.1006944%" y1="29.4275848%" x2="50%" y2="54.8164752%" id="linearGradient-3">
            <stop stop-color="#74B99C" stop-opacity="0.171328671" offset="0%"></stop>
            <stop stop-color="#ACDDCC" stop-opacity="0" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="17.1006944%" y1="41.038743%" x2="50%" y2="52.0980362%" id="linearGradient-4">
            <stop stop-color="#74B99C" stop-opacity="0.171328671" offset="0%"></stop>
            <stop stop-color="#ACDDCC" stop-opacity="0" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="48.3471752%" y1="100%" x2="58.42702%" y2="0%" id="linearGradient-5">
            <stop stop-color="#F5FAF9" stop-opacity="0" offset="0%"></stop>
            <stop stop-color="#CEEEE4" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="45.7975253%" y1="100%" x2="71.4265533%" y2="0%" id="linearGradient-6">
            <stop stop-color="#F5FAF9" stop-opacity="0" offset="0%"></stop>
            <stop stop-color="#E7F4F0" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="47.0606845%" y1="100%" x2="64.9862651%" y2="0%" id="linearGradient-7">
            <stop stop-color="#F5FAF9" stop-opacity="0" offset="0%"></stop>
            <stop stop-color="#CEEEE4" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="47.3717982%" y1="100%" x2="63.4000343%" y2="0%" id="linearGradient-8">
            <stop stop-color="#F5FAF9" stop-opacity="0" offset="0%"></stop>
            <stop stop-color="#E7F4F0" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-9">
            <stop stop-color="#E1F2EC" offset="0%"></stop>
            <stop stop-color="#E1F1EC" offset="100%"></stop>
        </linearGradient>
        <filter x="0.0%" y="0.0%" width="100.0%" height="100.0%" filterUnits="objectBoundingBox" id="filter-10">
            <feGaussianBlur stdDeviation="0" in="SourceGraphic"></feGaussianBlur>
        </filter>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-11">
            <stop stop-color="#E1F2EC" offset="0%"></stop>
            <stop stop-color="#E1F1EC" offset="100%"></stop>
        </linearGradient>
        <filter x="0.0%" y="0.0%" width="100.0%" height="100.0%" filterUnits="objectBoundingBox" id="filter-12">
            <feGaussianBlur stdDeviation="0" in="SourceGraphic"></feGaussianBlur>
        </filter>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-13">
            <stop stop-color="#E1F2EC" offset="0%"></stop>
            <stop stop-color="#E1F1EC" offset="100%"></stop>
        </linearGradient>
        <filter x="0.0%" y="0.0%" width="100.0%" height="100.0%" filterUnits="objectBoundingBox" id="filter-14">
            <feGaussianBlur stdDeviation="0" in="SourceGraphic"></feGaussianBlur>
        </filter>
        <linearGradient x1="68.9306973%" y1="13.1014385%" x2="22.6505417%" y2="100%" id="linearGradient-15">
            <stop stop-color="#76C3A3" offset="0%"></stop>
            <stop stop-color="#AFE2CD" offset="50.6953413%"></stop>
            <stop stop-color="#D6F5E8" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="66.1555041%" y1="13.1678427%" x2="24.9588441%" y2="100%" id="linearGradient-16">
            <stop stop-color="#76C3A3" offset="0%"></stop>
            <stop stop-color="#AFE2CD" offset="40.7172857%"></stop>
            <stop stop-color="#D6F5E8" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="11.1894248%" x2="50%" y2="100%" id="linearGradient-17">
            <stop stop-color="#76C3A3" offset="0%"></stop>
            <stop stop-color="#AFE2CD" offset="31.3657371%"></stop>
            <stop stop-color="#D6F5E8" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="52.1568908%" y1="61.9705426%" x2="50%" y2="76.1656858%" id="linearGradient-18">
            <stop stop-color="#76C3A3" offset="0%"></stop>
            <stop stop-color="#BFF0DC" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="68.9951894%" y1="46.1404825%" x2="78.6500609%" y2="54.1157506%" id="linearGradient-19">
            <stop stop-color="#BFF0DC" offset="0%"></stop>
            <stop stop-color="#76C3A3" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="22.4259531%" x2="69.8218075%" y2="68.3857629%" id="linearGradient-20">
            <stop stop-color="#DEF7ED" offset="0%"></stop>
            <stop stop-color="#8BD1B4" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="67.2176096%" y1="90.9941883%" x2="29.6738131%" y2="7.33526626%" id="linearGradient-21">
            <stop stop-color="#BDF1DC" offset="0%"></stop>
            <stop stop-color="#DCFFF1" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="14.1723289%" y1="4.74983378%" x2="67.3452094%" y2="100%" id="linearGradient-22">
            <stop stop-color="#7BCAA9" offset="0%"></stop>
            <stop stop-color="#B2EBD4" offset="100%"></stop>
        </linearGradient>
        <ellipse id="path-23" cx="27.1602414" cy="34.653619" rx="27.1602414" ry="34.653619"></ellipse>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-25">
            <stop stop-color="#FFFFFF" stop-opacity="0.542012675" offset="0%"></stop>
            <stop stop-color="#FFFFFF" stop-opacity="0" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="0%" x2="52.8145984%" y2="76.1644103%" id="linearGradient-26">
            <stop stop-color="#FFFFFF" stop-opacity="0.460008741" offset="0%"></stop>
            <stop stop-color="#FFFFFF" stop-opacity="0" offset="100%"></stop>
        </linearGradient>
    </defs>
    <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="暂无医生" transform="translate(-260.000000, -376.000000)">
            <g id="页面模板-/-空态-/-图+主副文案+按钮" transform="translate(0.000000, 176.000000)">
                <g id="编组-26" transform="translate(260.000000, 200.000000)">
                    <rect id="区域" stroke="#979797" fill="#D8D8D8" opacity="0" x="0.5" y="0.5" width="229" height="229"></rect>
                    <ellipse id="椭圆形" fill="url(#linearGradient-1)" filter="url(#filter-2)" cx="115" cy="179" rx="103" ry="24"></ellipse>
                    <path d="M64,181 L64.2856088,181 C67.9738693,181 71.5412587,182.315191 74.3468842,184.709294 L96.5048014,203.617146 C98.5050316,205.323987 98.7428701,208.329163 97.0360284,210.329393 C96.1314493,211.38946 94.8078497,212 93.4142911,212 L88.4023497,212 C83.4380884,212 78.7741807,209.622205 75.858197,205.604627 L61.5243717,185.855801 C60.5320116,184.488549 60.8359213,182.575706 62.203173,181.583346 C62.7255667,181.204189 63.3545119,181 64,181 Z" id="矩形" fill="url(#linearGradient-3)"></path>
                    <path d="M110,186 L110.285609,186 C113.973869,186 117.541259,187.315191 120.346884,189.709294 L142.504801,208.617146 C144.505032,210.323987 144.74287,213.329163 143.036028,215.329393 C142.131449,216.38946 140.80785,217 139.414291,217 L134.40235,217 C129.438088,217 124.774181,214.622205 121.858197,210.604627 L107.524372,190.855801 C106.532012,189.488549 106.835921,187.575706 108.203173,186.583346 C108.725567,186.204189 109.354512,186 110,186 Z" id="矩形" fill="url(#linearGradient-3)"></path>
                    <path d="M152,179 L154.060874,179 C156.645873,179 159.189824,179.646513 161.461172,180.880691 L188.981245,195.834219 C190.573913,196.699624 191.163476,198.692285 190.298071,200.284953 C189.723749,201.341919 188.617214,202 187.414291,202 L179.97425,202 C176.431966,202 172.996548,200.786688 170.239951,198.562066 L150.669187,182.768116 C149.758441,182.033128 149.615961,180.698997 150.350949,179.788251 C150.753239,179.289761 151.35943,179 152,179 Z" id="矩形" fill="url(#linearGradient-4)"></path>
                    <g id="编组-23" transform="translate(14.266884, 86.873598)">
                        <path d="M37.8385847,2.27373675e-13 C9.10160549,58.1649969 -3.2668841,87.2474953 0.733115902,87.2474953 C4.7331159,87.2474953 14.6419739,86.3334328 30.45969,84.5053078 L37.8385847,2.27373675e-13 Z" id="路径-2" fill="url(#linearGradient-5)"></path>
                        <path d="M71.9267225,-2.27373675e-13 C36.5230767,58.1649969 20.8212537,87.2474953 24.8212537,87.2474953 C28.8212537,87.2474953 48.7301118,86.3334328 84.5478278,84.5053078 L71.9267225,-2.27373675e-13 Z" id="路径-2" fill="url(#linearGradient-6)" transform="translate(54.380000, 43.623748) scale(-1, 1) translate(-54.380000, -43.623748) "></path>
                        <path d="M137.838585,30 C109.101605,88.1649969 96.7331159,117.247495 100.733116,117.247495 C104.733116,117.247495 121.308641,116.333433 150.45969,114.505308 L137.838585,30 Z" id="路径-2" fill="url(#linearGradient-7)"></path>
                        <path d="M184.547828,30 C149.144182,88.1649969 133.442359,117.247495 137.442359,117.247495 C141.442359,117.247495 154.68455,116.333433 177.168933,114.505308 L184.547828,30 Z" id="路径-2" fill="url(#linearGradient-8)" transform="translate(160.690553, 73.623748) scale(-1, 1) translate(-160.690553, -73.623748) "></path>
                    </g>
                    <path d="M34,38 C39.4307569,38 44.0189198,40.555365 45.4987589,44.0612033 C50.3105148,44.4590485 54,46.7425498 54,49.5 C54,52.5375661 49.5228475,55 44,55 C42.1556976,55 40.4280071,54.7253986 38.9447154,54.2464785 C37.4371265,54.7307119 35.7629601,55 34,55 C31.1264478,55 28.4887999,54.2845647 26.4227045,53.0914447 C25.2318875,54.2598802 23.4680908,55 21.5,55 C17.9101491,55 15,52.5375661 15,49.5 C15,46.4624339 17.9101491,44 21.5,44 C21.8263501,44 22.1470828,44.0203507 22.4605224,44.0596341 L22.499973,44.0642094 C23.9784578,40.5568259 28.567691,38 34,38 Z" id="形状结合" fill="url(#linearGradient-9)" opacity="0.568450218" filter="url(#filter-10)"></path>
                    <g id="编组-6" transform="translate(191.500000, 133.000000) scale(-1, 1) translate(-191.500000, -133.000000) translate(177.000000, 87.000000)" opacity="0.560849144">
                        <rect id="矩形" fill="url(#linearGradient-11)" filter="url(#filter-12)" x="0" y="14" width="29" height="78"></rect>
                        <rect id="矩形" fill="url(#linearGradient-13)" filter="url(#filter-14)" x="0" y="0" width="15" height="70"></rect>
                    </g>
                    <g id="编组-22" transform="translate(21.728941, 19.958904)">
                        <path d="M78.9199031,101.041096 L83.8126904,101.041096 C85.4695446,101.041096 86.8126904,102.384241 86.8126904,104.041096 C86.8126904,104.610599 86.6505873,105.168338 86.3453288,105.64912 L50.1548527,162.64912 C49.6044814,163.515955 48.6490107,164.041096 47.6222142,164.041096 L42.7294269,164.041096 C41.0725726,164.041096 39.7294269,162.69795 39.7294269,161.041096 C39.7294269,160.471592 39.8915299,159.913853 40.1967884,159.433071 L76.3872646,102.433071 C76.9376359,101.566236 77.8931066,101.041096 78.9199031,101.041096 Z" id="矩形" fill="url(#linearGradient-15)"></path>
                        <path d="M129.974338,101.041096 L134.987134,101.041096 C136.643988,101.041096 137.987134,102.384241 137.987134,104.041096 C137.987134,104.582408 137.840672,105.113636 137.563272,105.578468 L104.143917,161.578468 C103.602593,162.485551 102.624108,163.041096 101.567779,163.041096 L96.5549832,163.041096 C94.898129,163.041096 93.5549832,161.69795 93.5549832,160.041096 C93.5549832,159.499783 93.7014456,158.968555 93.9788455,158.503723 L127.3982,102.503723 C127.939524,101.59664 128.918009,101.041096 129.974338,101.041096 Z" id="矩形" fill="url(#linearGradient-16)" transform="translate(115.771059, 132.041096) scale(-1, 1) translate(-115.771059, -132.041096) "></path>
                        <path d="M87.2710586,101.041096 L93.2710586,101.041096 C94.9279129,101.041096 96.2710586,102.384241 96.2710586,104.041096 L96.2710586,167.041096 C96.2710586,168.69795 94.9279129,170.041096 93.2710586,170.041096 L87.2710586,170.041096 C85.6142044,170.041096 84.2710586,168.69795 84.2710586,167.041096 L84.2710586,104.041096 C84.2710586,102.384241 85.6142044,101.041096 87.2710586,101.041096 Z" id="矩形" fill="url(#linearGradient-17)"></path>
                        <rect id="矩形" fill="url(#linearGradient-18)" transform="translate(91.771059, 90.041096) rotate(4.000000) translate(-91.771059, -90.041096) " x="68.2710586" y="73.0410955" width="47" height="34" rx="14"></rect>
                        <g id="编组-21" transform="translate(84.599641, 58.083137) rotate(-345.000000) translate(-84.599641, -58.083137) translate(7.599641, 18.583137)">
                            <path d="M137.465913,20.7931948 L137.466311,20.7987534 C137.597022,20.7977392 137.728118,20.7985695 137.859582,20.8012643 C146.773689,20.983986 154,29.5190061 154,39.8647653 C154,50.2105245 146.773689,58.7455447 137.859582,58.9282664 C137.728118,58.9309611 137.597022,58.9317915 137.466311,58.9307773 L137.465913,58.9363358 L58.7321647,60.550223 C40.9039508,60.9156664 26.4513279,51.6544715 26.4513279,39.8647653 C26.4513279,28.0750591 40.9039508,18.8138643 58.7321647,19.1793077 L137.465913,20.7931948 Z" id="形状结合" fill="url(#linearGradient-19)"></path>
                            <path d="M32.9718365,-3.54022159e-13 L120.815275,7.32072176 C120.961554,7.3335651 121.107818,7.34014736 121.254492,7.34988467 C131.199983,8.00671345 139.262397,22.3831284 139.262397,39.4666217 C139.262397,56.5501149 131.199983,70.9265299 121.254492,71.5833586 C121.107818,71.5930959 120.961554,71.5996782 120.815719,71.6031367 L32.9718365,78.9332433" id="形状结合" fill="url(#linearGradient-20)"></path>
                            <path d="M31.4040291,0.0667566875 C48.7479955,0.0667566875 62.8080582,17.7365651 62.8080582,39.5333783 C62.8080582,61.3301916 48.7479955,79 31.4040291,79 C14.0600628,79 0,61.3301916 0,39.5333783 C0,17.7365651 14.0600628,0.0667566875 31.4040291,0.0667566875 Z M31.887168,4.87975933 C16.9446739,4.87975933 4.83138909,20.394713 4.83138909,39.5333783 C4.83138909,58.6720437 16.9446739,74.1869974 31.887168,74.1869974 C46.8296621,74.1869974 58.9429469,58.6720437 58.9429469,39.5333783 C58.9429469,20.394713 46.8296621,4.87975933 31.887168,4.87975933 Z" id="形状结合" fill="url(#linearGradient-21)"></path>
                            <g id="矩形" transform="translate(4.243788, 4.879759)">
                                <mask id="mask-24" fill="white">
                                    <use xlink:href="#path-23"></use>
                                </mask>
                                <g id="蒙版">
                                    <use fill="url(#linearGradient-22)" xlink:href="#path-23"></use>
                                    <use fill-opacity="0.19416521" fill="#FFFFFF" xlink:href="#path-23"></use>
                                </g>
                                <polygon fill="url(#linearGradient-25)" mask="url(#mask-24)" transform="translate(15.081769, 32.728418) rotate(-26.000000) translate(-15.081769, -32.728418) " points="8.37494193 2.43361351 13.0139178 -0.937706551 21.7885954 63.0232224 17.1496195 66.3945425"></polygon>
                                <polygon fill="url(#linearGradient-26)" mask="url(#mask-24)" transform="translate(36.348447, 20.845394) rotate(-26.000000) translate(-36.348447, -20.845394) " points="15.6663019 -9.47050029 48.2559141 -12.7996413 57.0305917 51.1612877 24.4409795 54.4904287"></polygon>
                            </g>
                        </g>
                    </g>
                    <g id="四角星1" transform="translate(170.000000, 23.000000)" fill-rule="nonzero">
                        <rect id="矩形" fill="#000000" opacity="0" x="0" y="0" width="29" height="29"></rect>
                        <polygon id="路径" fill="#E7F4F0" points="29 15 20.1209104 9.87908962 15 1 9.87908962 9.87908962 1 15 9.87908962 20.1209104 15 29 20.1209104 20.1209104"></polygon>
                    </g>
                </g>
            </g>
        </g>
    </g>
</svg>