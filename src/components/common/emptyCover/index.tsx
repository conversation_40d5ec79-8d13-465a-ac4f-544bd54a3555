
import React from 'react';
import no_data_svg from 'src/svgs/no-inquiry-prescription.svg';
import no_order_svg from 'src/svgs/system-jumping.svg';
import EmptyCover from './emptyCover';
import no_checkup_img from './images/checkup-empty.svg';
import no_card_svg from './images/no-card.svg';
import no_doctor_img from './images/no_doctor_img.svg';

const emptyConfig = {
  inquiry: {
    text: '暂无数据',
    icon: no_data_svg,
  },
  prescription: {
    text: '暂无数据',
    icon: no_data_svg,
  },
  order: {
    text: '暂时没有购药记录',
    icon: no_order_svg,
  },
  servcard: {
    text: '暂无记录',
    icon: no_card_svg,
  },
  checkup: {
    text: '您没有体检订单',
    icon: no_checkup_img,
  },
  hospital: {
    text: '暂无查询结果',
    icon: no_data_svg,
  },
  doctor: {
    text: '该诊室当前暂无医生接诊',
    icon: no_doctor_img,
  },
  safeguard: {
    text: '系统维护中...',
    icon: no_doctor_img,
  },
};

export default {
  inquiryTextEmpty: () => <EmptyCover config={emptyConfig.inquiry} type='nodata' position='myInquiry'/>,
  inquiryVideoEmpty: () => <EmptyCover config={emptyConfig.inquiry} type='video' />,
  prescriptionTextEmpty: () => <EmptyCover config={emptyConfig.prescription} type='nodata' />,
  prescriptionVideoEmpty: () => <EmptyCover config={emptyConfig.prescription} type='video' />,
  orderTextEmpty: () => <EmptyCover config={emptyConfig.order} type='text' position='myOrder'/>,
  orderVideoEmpty: () => <EmptyCover config={emptyConfig.order} type='video' />,
  cardEmpty: ({propsConfig={}}) => <EmptyCover config={{...emptyConfig.servcard,...propsConfig}} type='card' />,
  doctorEmpty: ({propsConfig={}}) => <EmptyCover config={{...emptyConfig.doctor,...propsConfig}} type='card' />,
  checkupEmpty: () => <EmptyCover config={emptyConfig.checkup} type='static' />,
  hospitalEmpty: () => <EmptyCover config={emptyConfig.hospital} type='card' />,
  safeguard: () => <EmptyCover config={emptyConfig.safeguard} type='safeguard'/>,
};
