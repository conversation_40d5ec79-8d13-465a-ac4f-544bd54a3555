@import "src/style/index";

.evaluate_component {
  position: relative;
  padding: r(10) 0;
  border-bottom: r(1) solid var(--border-disabled);

  &:last-child {
    border: none;
  }

  .head {
    @include display-flex;
    @include justify-content(space-between);

    .base_info {
      @include display-flex;
      @include align-items(center);

      .name {
        color: var(--color-text);
        font-size: r(14);
        // font-weight: bold;
        padding-right: r(7);
      }

      .tag {
        background: #f0f0f0;
        margin-left: r(7);
        border-radius: r(3);
        padding: 0 r(6);
        height: r(22);
        line-height: r(22);
        color: #999;
      }
    }

    .time {
      color: #9b9b9b;
      font-size: r(13);
    }
  }

  .evaluate_tags {
    font-size: r(12);
    color: #909090;
    margin: r(9) 0;

    span {
      padding: 0 r(2);
    }
  }

  .evaluate_desc {
    color: var(--color-text);
    font-size: r(14);
  }
}
