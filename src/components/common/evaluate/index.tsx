import { Star } from 'src/components/common';
import { ApplicationState } from 'src/store';
import { useSelector } from 'react-redux';
import format from 'src/utils/format';
import classnames from 'classnames';
import React from 'react';
import './evaluate.scss';

interface EvaluateProps {
  renderData: any;
  prefixCls?: string;
  TAG?: any;
}

const Evaluate = (props: EvaluateProps) => {

  const { INQUIRYTYPE_OBJ }: any = useSelector((state: ApplicationState) => {
    return {
      INQUIRYTYPE_OBJ: state.dictionary.INQUIRYTYPE_OBJ
    }
  });

  const { prefixCls, TAG, renderData: { inquiry, totalScore, evaluateTags, evaluateDesc, evaluateTime } } = props;
  const tags = JSON.parse(evaluateTags);
  return (
    <div className={classnames('evaluate_component', prefixCls)}>
      <div className="head">
        <div className='base_info'>
          <p className='name'>{inquiry && inquiry.patientName || ''}</p>
          <Star value={totalScore} />
          <p className='tag'>{inquiry && INQUIRYTYPE_OBJ[inquiry.inquiryType] || ''}问诊</p>
        </div>
        <p className='time'>{format.date(evaluateTime, 'yyyy-MM-dd')}</p>
      </div>
      <p className='evaluate_tags'>
        {tags.map((c, index) => {
          if (index >= 1) { return ` | ${TAG[c]}` }
          else { return `${TAG[c]}` }
        })}
      </p>
      <p className='evaluate_desc'></p>
      {evaluateDesc}
    </div>
  );
}

export default Evaluate;