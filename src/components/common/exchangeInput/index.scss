@import "src/style/index";

$prefixCls: 'exchange-comp';

.#{$prefixCls} {
  margin-top: r(15);
  @include display-flex;

  &-input {
    background-color: #fff;
    margin-right: r(10);
    line-height: 36px;
    border-radius: 3px;
    padding: 0 15px;
    border: 1px solid #e6e6e6;
    @include flex;

    .za-input__clear {
      right: 12px;
      color: #d2d2d2;
    }
  }

  &-btn {
    padding-top: 2px;
    padding-bottom: 2px;
    font-weight: 600;
    border: none;
    background-color: #ff7240;
    color: #fff;

    &.za-button--disabled {
      color: #666;
      background-color: #e8e8e8;
    }
  }

  &-bind {
    &__modal {
      .za-popup {
        width: 85% !important;
        border-radius: r(8);
      }
    }

    &__patient {
      margin-bottom: r(5);

      &-title {
        font-size: r(14);
        color: #333;
        margin-bottom: r(5);
        font-weight: 500;

        span {
          color: #ff5050;
        }
      }

      &-list {
        display: flex;
        align-items: center;
        flex-wrap: wrap;

        button {
          font-size: r(12);
          padding: 0 r(10);
          height: r(28);
          margin-right: r(10);
          margin-bottom: r(10);
        }
      }

      .zarm-v3-loading {
        margin-top: r(-10);
        margin-right: r(10);
      }
    }

    &__qrcode {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;

      &-img {
        margin: r(5) auto;
        width: r(150);
      }

      &-text {
        max-width: r(250);
        font-size: r(13);
        text-align: center;
      }
    }

    &__footer {
      margin-top: r(15);
      display: flex;
      justify-content: center;

      button {
        margin-right: r(10);

        &:last-child {
          margin-right: 0;
        }
      }
    }
  }

  &-add {
    &__modal {
      .za-popup {
        width: 85% !important;
        border-radius: r(8);
      }

      .za-modal__body {
        padding: 0;
      }

      .addPatient-comp {
        padding: r(10) 0;
      }
    }

    &__footer {
      padding: r(10) 0;
      display: flex;
      justify-content: center;

      button {
        margin-right: r(10);
        width: r(80);

        &:last-child {
          margin-right: 0;
        }
      }
    }
  }
}
