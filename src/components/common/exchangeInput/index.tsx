import React, { useState, useCallback, useEffect } from 'react';
import { StaticToast } from 'src/components/common';
import { fetch_patients_list, save_default_patient_relation } from 'src/store/patients/action';
import { fetchJson } from 'src/utils/fetch';
import throttle from 'src/utils/throttle';
import { Input, Button, Modal } from 'zarm';
import { Loading } from 'zarm-v3';
import './index.scss';
import AddPatient from '../addPatient';
import { useDispatch } from 'react-redux';

const prefixCls = 'exchange-comp';

interface IProps {
  onExchangeSuccess?: () => void;
}

interface ICardInfo {
  authName: string;
  addWechat: string;
  attachmentDomain: {
    attachmentDownloadUrl: string;
  };
}

interface IPatientInfo {
  id: number;
  patientName: string;
  patientNo: string;
  patientRelation: number;
}

interface IAddPatientRef {
  savePatient: (callback: () => void) => void;
}

interface IExchangeParams {
  patientId: number;
  patientRelation: number;
}

const ExchangeInput = (props: IProps) => {
  const [cardInstancePwd, setCardInstancePwd] = useState('');
  const [cardInfo, setCardInfo] = useState<ICardInfo>();
  const [bindModalVisible, setBindModalVisible] = useState(false);
  const [addModalVisible, setAddModalVisible] = useState(false);
  const [patientList, setPatientList] = useState<IPatientInfo[]>([]);
  const [selectedPatient, setSelectedPatient] = useState<IPatientInfo>();
  const [isLoadingPatient, setIsLoadingPatient] = useState(true);
  const addPatientRef = React.useRef<IAddPatientRef>();
  const compRef = React.useRef<HTMLDivElement>();

  const dispatch = useDispatch();
  const saveDefaultPationRelation = (data) => dispatch(save_default_patient_relation(data));

  const getCardInfo = async () => fetchJson({
    type: 'GET',
    url: `/api/api/v1/patient/card/instance/detail/bypwd?cardInstancePwd=${cardInstancePwd.trim()}`,
    isloading: true,
    needToast: false,
  });

  const exchangeCard = (params: Partial<IExchangeParams> = {}) => {
    fetchJson({
      type: 'POST',
      url: '/api/api/v1/patient/card/exchange',
      data: { cardInstancePwd:cardInstancePwd.trim(), exchangeCardCategory: '3', ...params },
      isloading: true,
      needToast: false,
    }).then((res) => {
      if (res && res.code === '0') {
        StaticToast.success('兑换成功', 5000);
        setCardInstancePwd('');
        props.onExchangeSuccess && props.onExchangeSuccess();

        setBindModalVisible(false);

        // 保存默认家属关系，用于一会儿加载完本人和家属权益后，能自动切换到这里的tab
        if (params.patientRelation) {
          saveDefaultPationRelation(params.patientRelation);
        }
      } else {
        StaticToast.error(res.message || '服务异常', 5000);
      }
    });
  };

  const handleExchangeCard = () => {
    if (!selectedPatient) {
      StaticToast.error('请先选择使用人!', 3000);
      return;
    }

    exchangeCard({ patientId: selectedPatient.id, patientRelation: selectedPatient.patientRelation });
  };

  const getPatientInfo = async () => fetchJson({
    type: 'POST',
    url: '/api/api/v1/patient/patient/list',
    isloading: false,
    needToast: false,
  });

  const fetchPatientsList = (onSuccess?) => dispatch(fetch_patients_list({}, onSuccess));

  const onChangeAddPatientModal = (status: boolean) => {
    setAddModalVisible(status);
    setBindModalVisible(!status);
  };

  const exchangeClick = throttle(
    useCallback(async () => {
      if (!cardInstancePwd) {
        StaticToast.warning('您输入的兑换码');
        return;
      }
      try {
        const cardInfoRes = await getCardInfo();

        if (cardInfoRes && cardInfoRes.code === '0' && cardInfoRes.result) {
          const { cardDomain } = cardInfoRes.result;
          const { authName, addWechat }  = cardDomain || {};
          setCardInfo(cardDomain);

          if (authName === 'Y' || addWechat === 'Y') {
            setBindModalVisible(true);
            return;
          }

          exchangeCard();
        } else {
          StaticToast.error('兑换码有误，查询失败');
        }

      } catch (e) {
        StaticToast.error(e.message || '服务异常', 5000);
      }
    }, [cardInstancePwd]),
    1000,
  );

  useEffect(() => {
    if (cardInfo && cardInfo.authName === 'Y' && bindModalVisible) {
      setIsLoadingPatient(true);
      fetchPatientsList((patientsList) => {
        let _patientsList = [...patientsList];
        // 处理患者信息列表，对列表中的每个元素进行映射操作
        // 如果患者的关系标识为 1，将其姓名修改为 '本人'，否则保持不变
        _patientsList = _patientsList
          .map((item) => item.patientRelation === 1 ? { ...item, patientName: '本人' } : item)
          // 对处理后的患者信息列表进行排序
          // 将关系标识为 1 的患者排在列表前面，其他患者排在后面
          .sort((a, b) => a.patientRelation === 1 ? -1 : 1);
        // 更新患者列表状态，若 _patientsList 存在则使用其值，否则使用空数组

        setPatientList(_patientsList || []);
        setIsLoadingPatient(false);
      });
    }

    if (!bindModalVisible) {
      setPatientList([]);
      setSelectedPatient(undefined);
    }
  }, [bindModalVisible, cardInfo]);

  const onCardInstancePwdChange = (e) => {
    setCardInstancePwd(e);
  };

  return (
    <header className={`${prefixCls}`} ref={(e) => compRef.current = (e as HTMLDivElement)}>
      <Input
        placeholder='请输入兑换码/券码'
        value={cardInstancePwd}
        onChange={onCardInstancePwdChange}
        className={`${prefixCls}-input`}
      />
      <Button size='sm' disabled={!cardInstancePwd.length} className={`${prefixCls}-btn`} onClick={exchangeClick}>
        立即兑换
      </Button>

      <Modal visible={bindModalVisible} className={`${prefixCls}-bind__modal`} mountContainer={compRef.current}>
        {
          cardInfo && (
            <div>
              {
                cardInfo.authName === 'Y' && (
                  <div className={`${prefixCls}-bind__patient`}>
                    <p className={`${prefixCls}-bind__patient-title`}>请先绑定卡券权益使用人<span>*</span>:</p>
                    <div className={`${prefixCls}-bind__patient-list`}>
                      {
                        !isLoadingPatient ? (
                          <>
                            {
                              patientList.map((item, index) => (
                                <Button
                                  theme={item.patientNo === selectedPatient?.patientNo ? 'primary' : 'default'}
                                  size='sm'
                                  onClick={() => setSelectedPatient(item)}
                                >
                                  {item.patientName}
                                </Button>
                              ))
                            }
                          </>
                        )
                          : (
                            <Loading />
                          )
                      }
                      <Button theme='default' size='sm' onClick={() => onChangeAddPatientModal(true)}>+新增</Button>
                    </div>
                  </div>
                )
              }
              {
                cardInfo.attachmentDomain?.attachmentDownloadUrl && (
                  <div className={`${prefixCls}-bind__qrcode`}>
                    <p className={`${prefixCls}-bind__qrcode-text`}>卡券服务使用过程中，如有问题可添加微信咨询服务人员</p>
                    <img src={cardInfo.attachmentDomain?.attachmentDownloadUrl} className={`${prefixCls}-bind__qrcode-img`} />
                    <p className={`${prefixCls}-bind__qrcode-text`}>长按识别添加微信或截图保存在微信中扫码/识别添加微信</p>
                  </div>
                )
              }

              <div className={`${prefixCls}-bind__footer`}>
                <Button size='sm' onClick={() => setBindModalVisible(false)}>取消</Button>
                <Button size='sm' theme='primary' onClick={handleExchangeCard}>确定绑定并兑换</Button>
              </div>
            </div>
          )
        }
      </Modal>
      {/* 添加成员 */}
      <Modal visible={addModalVisible} className={`${prefixCls}-add__modal`} mountContainer={compRef.current}>
        <div>
          <AddPatient
            showPatientDistrict={false}
            ref={(e) => addPatientRef.current = e as IAddPatientRef}
          />
          <div className={`${prefixCls}-add__footer`}>
            <Button size='sm' onClick={() => onChangeAddPatientModal(false)}>取消</Button>
            <Button size='sm' theme='primary' onClick={() => addPatientRef.current?.savePatient(() => onChangeAddPatientModal(false))}>确定</Button>
          </div>
        </div>
      </Modal>
    </header>
  );
};
export default ExchangeInput;
