/*
 * @description： 通用滚动吸顶导航栏
 */
import classnames from 'classnames';
import React, { useCallback } from 'react';
import { fetchJson, pushEvent } from 'src/utils';
import { THIRD_PLATFORM_RESOURCECODE } from 'src/utils/staticData';
import validate from 'src/utils/validate';
import StaticToast from '../toast';
export interface FileInputProps {
  className?: string;
  id?: string;
  name?: string;

  // 每张图上传成功后的回调
  onUploadSuccess: Function;

  // 上传前的验证
  onUploadBeforeValidate?: Function;

  // 最大上传张数
  maxAttachmentTotal?: number;

  // 已上传张图
  hasAttachmentTotal?: number;

  // 图片大小限制
  maxSize?: number;

  // 业务编号
  businessNo: string;

  // 业务类型
  attachmentType: string;
  // 业务场景
  platformCode?: 'ZA' | 'UMP';
}
const MAXSIZE = 20 * 1024 * 1024; // 图片最大 20M
const CompatibleFileInput = (props: FileInputProps) => {
  const { businessNo = '', attachmentType = '', className = '', name = 'file[]', id = 'upload_file', onUploadSuccess, maxAttachmentTotal = 9, hasAttachmentTotal = 0, maxSize = MAXSIZE, platformCode = '', onUploadBeforeValidate } = props;
  const recursionUploadImg = (files: any[] = [], usableTotal, index) => {
    if (index >= files.length) {
      return;
    }
    const itmeFile = files[index] || {};
    const maxTips = files.length ? `第${index + 1}张` : '';
    index++;
    if (itmeFile.size > maxSize) {
      StaticToast.warning(`${maxTips}图片超过大小限制`);
      recursionUploadImg(files, usableTotal, index);
      pushEvent({
        eventTag: 'uploadFileChange_fail',
        text: `${maxTips}图片超过大小限制${itmeFile.size}，businessNo: ${businessNo}, attachmentType: ${attachmentType}, platformCode: ${platformCode}`,
      });
      return;
    }
    if (!/png|jpg|jpeg/.test(itmeFile.type)) {
      StaticToast.warning('只支持上传png，jpg和jpeg格式的图片');
      recursionUploadImg(files, usableTotal, index);
      pushEvent({
        eventTag: 'uploadFileChange_fail',
        text: ` ${maxTips}图片格式不正确${itmeFile.type}，businessNo: ${businessNo}, attachmentType: ${attachmentType}, platformCode: ${platformCode}`,
      });
      return;
    }
    const formData = new FormData();
    formData.append('files', itmeFile);
    formData.append('businessNo', businessNo);
    formData.append('attachmentType', attachmentType);
    fetchJson({
      url: '/api/api/v1/attchment/upload',
      type: 'UPLOAD',
      isloading: true,
      data: formData,
      success: (res) => {
        const { code = '0', result: [image = {}] = [] } = res;
        if (code === '0') {
          onUploadSuccess({ ...image, localLink: window.URL.createObjectURL(itmeFile) });
          pushEvent({
            eventTag: 'uploadFileChange_success',
            text: `${maxTips}图片上传成功，businessNo: ${businessNo}, attachmentType: ${attachmentType}, platformCode: ${platformCode}`,
          });
        }
        if(index < usableTotal) {
          recursionUploadImg(files, usableTotal, index);
        }
      },
    });
  };
  // 上传附件
  const uploadFileChange = useCallback(
    (e) => {
      e.preventDefault(); // 阻止默认行为
      const fileList = (e.target && e.target.files) || [];
      pushEvent({
        eventTag: 'uploadFileChange_start',
        text: `开始上传图片，businessNo: ${businessNo}, attachmentType: ${attachmentType}, platformCode: ${platformCode}，共${fileList.length}张图片`,
      });
      if (typeof onUploadBeforeValidate == 'function' && !onUploadBeforeValidate()) {
        return;
      }
      // 第三方的不走自有问诊逻辑
      if ([THIRD_PLATFORM_RESOURCECODE.MBJK, THIRD_PLATFORM_RESOURCECODE.KYUSHU_POP].includes(platformCode)) {
        onUploadSuccess(e);
        return;
      }
      const recursionFileList: any = [];
      for (let i = 0; i < fileList.length; i++) {
        recursionFileList.push(fileList[i]);
      }
      const usableTotal = maxAttachmentTotal - hasAttachmentTotal;
      usableTotal > 0 && recursionUploadImg(recursionFileList, usableTotal, 0);
      e.target.value = '';
    },
    [maxAttachmentTotal, hasAttachmentTotal, businessNo, attachmentType, onUploadSuccess, platformCode],
  );
  return <input type='file' name={name} accept='image/*' multiple={maxAttachmentTotal > 1} id={id} className={classnames('upload-input', { [`${className}`]: !!className })} onChange={(e) => uploadFileChange(e)} />;
};

export default CompatibleFileInput;
