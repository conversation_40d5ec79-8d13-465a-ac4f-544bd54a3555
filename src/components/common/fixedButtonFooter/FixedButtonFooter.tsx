import classNames from 'classnames';
import React from 'react';
import { Button } from 'zarm';
import './FixedButtonFooter.scss';

export interface FixedButtonFooterProps {
  className?: string;
  style?: React.CSSProperties;
  onClick(): void;
  onClick(e: React.FormEvent): void;
  /* 按钮左侧的内容，可为组件 or 其他 */
  extal?: any;
  /* 组件上半部分内容，必须组件 */
  header?: React.ReactNode | void;
  buttonText: any;
}

export default function FixedButtonFooter({
  className,
  extal,
  header,
  buttonText,
  onClick,
  style = {},
}: FixedButtonFooterProps) {
  return (
    <footer className={classNames('FixedButtonFooter-container', className)} style={style}>
      {header}
      <div className='body'>
        <div className='extal'>{extal}</div>
        <Button theme='primary' shape='round' onClick={onClick}>
          {buttonText}
        </Button>
      </div>
    </footer>
  );
}
