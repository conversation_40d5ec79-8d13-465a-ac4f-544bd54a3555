import React from 'react';
import { onAdClick } from 'src/utils/ad';
import { Carousel } from 'zarm';
import './index.scss';

interface AdState {
    /** 广告图片地址 */
    attachmentDomain: {
        attachmentDownloadUrl: string
    };
    /** 广告类型 */
    adType: number;
    /** 广告跳转地址 */
    adRequestUrl: string;
    /** 广告名称 */
    adName: string;
    targetAppId?: string,
    extraData?: string
}

const FooterBanner = props => {
    const { bannerList } = props;
    const sliceBannerList = bannerList.slice(0,5); //只显示前5个
    const bannerClick = (item) => {
        const { adRequestUrl = '', adType = '', adName = '', appId = '', extraData, adPosition, adNo } = item;
        onAdClick({
            adRequestUrl,
            adType,
            adName,
            adPosition,
            adNo,
            appId,
            extraData
        })
    }
    if( sliceBannerList.length === 0 ){
        return null
    }
    return (
        <div className='footer-banner-comp'>
            {
                sliceBannerList.length > 1 && (
                    <Carousel autoPlay={true} loop={true}>
                        {sliceBannerList.map((item: AdState, index) => (
                            <div className="carousel__item__pic" key={+index} onClick={() => bannerClick(item)}>
                                <img className="carousel_item_img" src={item.attachmentDomain.attachmentDownloadUrl} alt="" draggable={false} />
                            </div>
                        ))}
                    </Carousel>
                )
            }
            {
                sliceBannerList.length === 1 && (
                    <img src={sliceBannerList[0].attachmentDomain.attachmentDownloadUrl} alt="" onClick={() => bannerClick(sliceBannerList[0])} />
                )
            }
        </div>
    )
}
export default FooterBanner