import React from 'react';
import './index.scss';

const formatPrice = (props: { className?: string | undefined; price: any }) => {
  const price = (Number(props.price) || 0).toFixed(2).toString();
  const [integer, point] = price.split('.');
  return <span className={props.className}>
    <span className='sign'>¥</span><span className='integer'>{integer}</span>
    <span className='point'>.{point}</span>
  </span>;
};
export default formatPrice;
