@import 'src/style/index';

.gj_modal_wrap {
  text-align: center;

  .za-popup {
    width: r(300) !important;
    border-radius: r(8);
  }

  .gj_modal_title {
    font-size: r(17);
    font-weight: bold;
    font-family: PingFangSC-Medium, PingFang SC;
    line-height: 1.5;
    color: #333;
  }

  .za-modal__header__close {
    top: r(7);
    right: r(7);
    font-size: r(20);
  }

  .za-modal__body {
    padding: 0 r(15) r(18);
  }

  .gj_modal__desc {
    margin-top: r(10);
    font-size: r(14);
    color: rgba(0, 0, 0, 0.6);
  }

  .za-modal__footer {
    padding: 0;
    height: r(45);
    line-height: r(40);
    border-top: r(1) solid #f5f5f5;
  }

  .gj_modal_btn {
    font-size: r(16);
    color: var(--text-base-color);
    @include flex;
  }
}
