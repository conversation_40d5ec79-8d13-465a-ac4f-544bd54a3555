
import React from 'react';
import ReactDOM from 'react-dom';
import { dmEnv } from '@dm/utils';
import { zaappEnv } from 'src/utils/env';
import { Modal } from 'zarm';
import './gjmodal.scss'
import format from 'src/utils/format';

const GJModal: any = (props) => {
  const { visible, gjData = {} } = props;

  const handleClose = () => {
    const { url = '', isInsured } = gjData;
    if (url) {
      if (dmEnv.isApp()) {
        location.href = `zaapp://zai.wxprogram?params={"userName":"gh_b915b88f3aa9","path":"/pages/index","type":"${zaappEnv}"}`
      }
    } else {
      if (!isInsured) {
        //todo:投保链接，本期上线暂不提供
      }
    }
    GJModal.hide();
  }

  let { effectiveDate = '' } = gjData;

  effectiveDate = effectiveDate ? format.date(effectiveDate, 'yyyy/MM/dd') : effectiveDate;

  return (
    <Modal
      maskClosable
      closable
      className="gj_modal_wrap"
      visible={visible}
      onCancel={()=>{
        GJModal.hide();
      }}
      title={
        gjData && gjData.url ?
          <div className="gj_modal_title">
            <p>本平台药品商城开发中</p>
            <p>可前往众安互医小程序使用</p>
          </div> :
          <p className="gj_modal_title">商城暂不能进入</p>
      }
      footer={
        <p className="gj_modal_btn"
          onClick={handleClose}
        >{gjData && gjData.isInsured ? '我知道了' : '我知道了'}</p>
      }
    >
      <div className="gj_modal_content">
        {
          gjData && gjData.url
            ? ''
            : gjData.isInsured
              ? <div className="gj_modal__desc">
                <p>您投保的{gjData.productName}还未过等待期</p>
                <p>{effectiveDate} 可进入商城购药</p>
              </div>
              : <div className="gj_modal__desc">
                <p>现仅限有{gjData.productName}购药权益的用户进入</p>
              </div>
        }
        {/* <p className="gj_modal_btn">{gjData && gjData.isInsured ? '我知道了' : '获取购药权益'}</p> */}
      </div>
    </Modal>
  );
}

GJModal.show = (data = {}) => {
  if (!GJModal.gjDiv) {
    GJModal.gjDiv = document.createElement('div');
  }
  if (GJModal.gjDiv) {
    ReactDOM.render(
      <GJModal visible={true} gjData={data} />,
      GJModal.gjDiv,
    );
  }
};

GJModal.hide = () => {
  if (GJModal.gjDiv) {
    ReactDOM.render(
      <GJModal visible={false} />,
      GJModal.gjDiv,
    );
  }
};

export default GJModal;
