import React, { useEffect, useState, useRef } from 'react';
import { Drag } from 'zarm';
import { Link } from 'react-router-dom'
import IconPersonal from 'src/images/icon_personal.png';
import './gotoPersonalCenter.scss';

let currentPoint = [0, 0];



const GotoPersonalCenter = props => {

    const containerRef = props.containerRef;
    const boxRef: any = useRef(null)
    const [point, setPoint] = useState([0, 0]);

    useEffect(() => {
        if (boxRef.current && containerRef.current) {
            const { width, height } = boxRef.current.getBoundingClientRect();
            const {
                width: containerWidth,
                height: containerHeight,
            } = containerRef.current.getBoundingClientRect();
            console.log('containerHeight', containerRef, containerHeight, height)
            currentPoint[0] = Math.round(containerWidth - width + 10);
            currentPoint[1] = Math.round(document.documentElement.clientHeight - height - 210);
            setPoint(currentPoint);

            document.body.style.overflow = 'hidden';


        }
        return () => {
            document.body.style.overflow = 'auto';
        };

    }, []);

    const onDragStart = (event, dragState) => {
        console.log('onDragStart', dragState);
    };


    const onDragEnd = (event, dragState) => {
        console.log('onDragEnd', dragState);

        currentPoint = point;
    };
    const onDragMove = (event, dragState) => {
        if (boxRef.current && containerRef.current) {
            const { height } = boxRef.current.getBoundingClientRect();
            const {
                height: containerHeight,
            } = containerRef.current.getBoundingClientRect();
            //只能在右边上下滑动
            // let newX = currentPoint[0] + dragState.offsetX;
            let newY = currentPoint[1] + dragState.offsetY;

            // if (newX < 0) {
            //     newX = 0;
            // }
            // if (newX > containerWidth - width) {
            //     newX = containerWidth - width;
            // }
            if (newY < 0) {
                newY = 0;
            }
            if (newY > containerHeight - height) {
                newY = containerHeight - height;
            }

            setPoint([currentPoint[0], newY]);
        }
        return true;


    };

    return (

        <Drag
            onDragStart={onDragStart}
            onDragMove={onDragMove}
            onDragEnd={onDragEnd}

        >
            <Link to={{ pathname: '/hospital/personalcenter' }}>
                <div ref={boxRef} style={{
                    display: 'inline-block',
                    transform: `translate3d(${point[0]}px, ${point[1]}px, 0)`,
                }}
                >
                    <div className="personal-center">
                        <div className="personal-wrap">
                            <div className="personal-content">
                                <img className="icon-personal" src={IconPersonal} alt="" />
                                <div className="icon-text">个人中心</div>
                            </div>
                        </div>
                     
                    </div>
                </div>

            </Link>


        </Drag>
    );
}
export default GotoPersonalCenter