import React, { useCallback } from 'react'

const ImgWithDefault = (props) => {

  const { className, src, onClick = () => { } } = props || {};

  const notFound = useCallback((e) => {
    const img = e.target;
    img.src = require('src/images/img_not_found.png');
    img.οnerrοr = null;  //控制不要一直跳动
    img.style = "border: none;";
    if (img.parentNode) {
      img.parentNode.style = "background: transparent";
    }
  }, []);

  return (
    <img className={className} src={src} onClick={onClick} onError={notFound} />
  )
};

export default ImgWithDefault;