import React from 'react';
import classnames from 'classnames';
import './inputNumber.scss';

interface InputNumberProps {
  className?: string;
  placeholder?: string;
  type: 'tel' | 'number';
  maxLength?: number;
  value?: string | number;
  defaultValue?: string | number;
  onChange?: (value?: string | number) => void;
  onFocus?: (value?: string | number) => void;
  onBlur?: (value?: string | number) => void;
};

const InputNumber = (props: InputNumberProps) => {
  const { className, type = 'tel', onBlur, onFocus, onChange, ...other } = props;
  const cls = classnames('za-input za-input--number', className);
  const focus = (e) => {
    const { value } = e.target;
    if (typeof onFocus === 'function') {
      onFocus(value);
    }
  }
  const blur = (e) => {
    const { value } = e.target;
    if (typeof onBlur === 'function') {
      onBlur(value);
    }
  }
  return (
    <div className={cls} >
      <input type={type} onChange={(e) => onChange && onChange(e.target.value)}
        onBlur={blur} onFocus={focus} {...other} />
    </div>
  )
}


export default InputNumber;

