import React from 'react';
import './otherInfo.scss';
import { useSelector } from 'react-redux';
import { SelectButton } from 'src/components/common';
import { ALLERGY_DRUG, ALLFAMILY_ILLNESS } from 'src/pages/patients/editPatient/patientHealth';
import { ApplicationState } from 'src/store';
import format from 'src/utils/format';
import { Radio, Input } from 'zarm';
import classnames from 'classnames';

const prefix = 'inquiry-other';
const InquiryOtherInfo = (props) => {
  const { inquiryExtraInfo = {}, editorHandler } = props;
  const { YESORNO = [] } = useSelector((state: ApplicationState) =>
    // const { inquiryExtraInfo = '{}' } = state.inquiry.create;
    ({
      // inquiryExtraInfo: toJsonParse(inquiryExtraInfo),
      YESORNO: (state.dictionary.YESORNO || []).map((k) => ({ ...k, resCode: k.resCode == 'Y' ? '有' : '无' })),
    })
  );
  const { drugAllergic = {}, patientFamilyHistory = {} } = inquiryExtraInfo;
  const setEditorContent = (value) => {
    // if (inquiryType == 'I') {
    //   editorHandler && editorHandler({
    //     type: 'edit', key: 'inquiryExtraInfo', value
    //   });
    // } else {
    //   editorHandler && editorHandler('inquiryExtraInfo', value);
    // }
    editorHandler && editorHandler('inquiryExtraInfo', value);
  };
  const radioOnChange = (key, value) => {
    let extra = {
      [`${key}`]: value,
    };

    if (key == 'drugAllergic' || key == 'patientFamilyHistory') {
      let item = {};
      if (value === '无') {
        item = {
          tags: [],
          additional: '',
        };
      }
      extra = {
        [key]: {
          value,
          ...item,
        },
      };
    }
    setEditorContent(format.merge(inquiryExtraInfo, extra));
  };
  const selectTag = (key, value) => {
    let tags = [];
    if (key === 'drugAllergic') {
      if ((drugAllergic.tags || []).includes(value)) {
        tags = drugAllergic.tags.filter((k) => k != value);
      } else {
        tags = drugAllergic.tags.concat(value);
      }
    } else if (key === 'patientFamilyHistory') {
      if ((patientFamilyHistory.tags || []).includes(value)) {
        tags = patientFamilyHistory.tags.filter((k) => k != value);
      } else {
        tags = patientFamilyHistory.tags.concat(value);
      }
    }
    setEditorContent(format.merge(inquiryExtraInfo, { [key]: { tags } }));
  };
  const additionalSupplement = (key, additional) => {
    setEditorContent(format.merge(inquiryExtraInfo, { [key]: { additional } }));
  };
  return (

    <div className={`${prefix}__wrapper`}>
      <div className={`${prefix}__title`}>
        <p>其他问题</p>
        <p className='tip'>必填</p>
      </div>
      <div className={`${prefix}__cell`}>
        <p className='cell-desc'>您是否存在肝肾功能异常</p>
        <Radio.Group value={inquiryExtraInfo.liverSick} onChange={(value) => radioOnChange('liverSick', value)}>
          {YESORNO.map((k) => (<Radio key={`${k.resCode}-liverSick`} value={k.resCode}>{k.resName}</Radio>))}
        </Radio.Group>
      </div>
      <div className={classnames(`${prefix}__cell`, { 'not-border': drugAllergic.value == '有' })}>
        <p>{drugAllergic.class}</p>
        <Radio.Group value={drugAllergic.value} onChange={(value) => radioOnChange('drugAllergic', value)}>
          {YESORNO.map((k) => (<Radio key={`${k.resCode}-drugAllergic`} value={k.resCode}>{k.resName}</Radio>))}
        </Radio.Group>
      </div>
      {drugAllergic.value == '有' && <div className={`${prefix}__more`}>
        <div className={`${prefix}__tags`}>
          {ALLERGY_DRUG.map((k, i) => <SelectButton onClick={() => selectTag('drugAllergic', k)} selected={(drugAllergic.tags || []).includes(k)} key={`drug${i}`}>{k}</SelectButton>)}
        </div>
        <div className={`${prefix}__issues`}>
          <Input
            type='text'
            rows={4}
            autoHeight
            onChange={(value) => additionalSupplement('drugAllergic', value)}
            maxLength={200}
            placeholder={drugAllergic.placeholder}
            value={drugAllergic.additional}
          />
        </div>
      </div>}
      <div className={classnames(`${prefix}__cell not-border`)}>
        <p>{patientFamilyHistory.healthClass}</p>
        <Radio.Group value={patientFamilyHistory.value} onChange={(value) => radioOnChange('patientFamilyHistory', value)}>
          {YESORNO.map((k) => (<Radio key={`${k.resCode}-patientFamilyHistory`} value={k.resCode}>{k.resName}</Radio>))}
        </Radio.Group>
      </div>
      {patientFamilyHistory.value == '有' && <div className={`${prefix}__more no-border`}>
        <div className={`${prefix}__tags`}>
          {ALLFAMILY_ILLNESS.map((k, i) => <SelectButton onClick={() => selectTag('patientFamilyHistory', k)} selected={(patientFamilyHistory.tags || []).includes(k)} key={`patientFamilyHistory${i}`}>{k}</SelectButton>)}
        </div>
        <div className={`${prefix}__issues`}>
          <Input
            type='text'
            rows={4}
            autoHeight
            onChange={(value) => additionalSupplement('patientFamilyHistory', value)}
            maxLength={200}
            placeholder={patientFamilyHistory.placeholder}
            value={patientFamilyHistory.additional}
          />
        </div>
      </div>}
    </div>
  );
};

export default InquiryOtherInfo;
