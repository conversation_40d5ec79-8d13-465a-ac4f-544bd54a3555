@import "src/style/index";
$prefixCls: 'inquiry-other';

.#{$prefixCls} {
  &__wrapper {
    margin: r(10) auto;
    background: #fff;
    width: r(345);
    padding: r(15) r(15) 0;
    border-radius: r(7.5);
  }

  &__title {
    font-weight: bold;
    font-size: r(16);
    margin-bottom: r(10);
    @include display-flex;
    @include align-items(center);
    @include justify-content(space-between);

    .tip {
      font-weight: normal;
      font-size: r(13);
      color: #b2b2b2;
    }
  }

  &__cell {
    position: relative;
    line-height: r(50);
    padding-bottom: r(1);
    font-size: r(15);
    color: #333;
    @include display-flex;
    @include align-items(center);
    @include justify-content(space-between);

    &:after {
      position: absolute;
      bottom: 0;
      left: 0;
      right: 0;
      content: "";
      height: 1px;
      @include borderBottom($color: $border-color);
    }

    .cell-desc {
      @include flex;
    }

    &.not-border {
      // margin-bottom: r(-13);

      &:after {
        display: none;
      }
    }

    .za-radio {
      color: #666;
    }

    .za-radio--checked {
      color: var(--text-base-color);
      font-weight: 600;
    }
  }

  // &__tags {
  //   padding-top: r(25);
  // }

  &__issues {
    margin-top: r(10);
    border-radius: r(8);
    padding: r(15);
    background-color: rgba(248, 248, 248, 1);
  }

  &__more {
    padding-bottom: r(15);
    border-bottom: 1px solid $border-color;

    &.no-border {
      border-bottom: none;
    }
  }
}
