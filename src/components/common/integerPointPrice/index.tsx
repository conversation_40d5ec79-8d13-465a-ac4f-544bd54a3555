import React from 'react';
import { rem } from 'src/utils/tool';

interface Props {
  value: number | string;
  color: string;
  integerSize: number;
  pointAmount?: number;
  pointSize: number;
  unitSize: number;
}
const PrefixCls = 'integer-point-price';

export default function IntegerPointPrice(props: Props): JSX.Element {
  const { value = 0, color, integerSize, pointSize, unitSize, pointAmount = 2 } = props;
  const [integer, point] = `${(+value).toFixed(pointAmount)}`.split('.');

  return (
    <span className={PrefixCls} style={{ color }}>
      <span style={{ fontSize: rem(unitSize) }}>¥</span>
      <span style={{ fontSize: rem(integerSize) }}>{integer}</span>
      <span style={{ fontSize: rem(pointSize) }}>.{point}</span>
    </span>
  );
}
