import React, { useState } from 'react';
import { SvgIcon, InvoicePopup } from 'src/components/common';

const InvoiceForm = (props) => {
  const { isSupportClaims = false } = props;
  const [visible, setVisible] = useState(false);
  const [value, setValue] = useState({
    invoiceTitleType: '0',
    mobile: '',
    email: '',
    invoiceTitle: '',
    taxpayerIdentityNo: '',
  });
  const toCheckInvoice = () => {
    setVisible(true);
  };
  const onEditSuccess = (value) => {
    console.log('value',value);
    props.invoiceOnChange && props.invoiceOnChange(value);
    setValue(value);
    setVisible(false);
  };
  const onEditClose = () => {
    setVisible(false);
  };
  return (
    <div>
      {value.invoiceTitleType !== '0' ? (
        <div className="cell cell-ticket">
          <div className="label">发票</div>
          <div className="cell-value" onClick={toCheckInvoice}>
            电子普通({value.invoiceTitleType === '1' ? '个人' : '单位'})
            <SvgIcon className="icon-arrow" src={require('src/svgs/sprite-icon_arrow.svg')} />
          </div>
        </div>
      ) : (
        <div className="cell cell-ticket">
          <div className="label">发票</div>
          {isSupportClaims ? (
            <p className="goto-ticket ticket-tips">购药可享受理赔，不支持个人申请发票</p>
          ) : (
            <div className="goto-ticket" onClick={toCheckInvoice}>
              未开具发票
              <SvgIcon className="icon-arrow" src={require('src/svgs/sprite-icon_arrow.svg')} />
            </div>
          )}
        </div>
      )}
      <InvoicePopup value={value} onSuccess={onEditSuccess} visible={visible} onClose={onEditClose} />
    </div>
  );
};
export default InvoiceForm;
