@import "src/style/index";

.invoice-popup {
  background-color: #fbfbfb;

  .title-box {
    position: relative;
    height: r(50);
    display: flex;
    align-items: center;
    justify-content: center;
    border-bottom: 1px solid #d8d8d8;
    background-color: #fff;
    @media (-webkit-min-device-pixel-ratio: 2) {
      border-bottom: 0.5px solid #d8d8d8;
    }

    .icon-close {
      position: absolute;
      top: r(16);
      right: r(15);
      width: r(16);
      height: r(16);
    }

    .title {
      color: #1e1e1e;
      font-size: r(16);
      font-weight: bold;
    }
  }

  .cont {
    padding: r(10) r(15);
    color: #1e1e1e;
    font-size: r(14);
    font-weight: bold;
  }

  .form-cont {
    background-color: #fff;

    .form-item {
      padding: r(15);
      display: flex;
      align-items: center;

      .label {
        width: r(95);
        min-width: r(95);
        max-width: r(95);
        font-size: r(15);
        margin-right: r(25);
      }
    }
  }

  .radio-group {
    display: flex;

    .radio-item {
      padding: r(5) r(13);
      margin-right: r(10);
      color: #333;
      font-size: r(12);
      border-radius: 26px;
      border: 1px solid rgba(0, 0, 0, 0.1);

      &.active {
        color: #ec9131;
        background: rgba(236, 145, 49, 0.06);
        border: 1px solid rgba(236, 145, 49, 0.32);
      }
    }
  }

  .button-group {
    background-color: #fff;
    padding: r(10) r(15) r(25);
    border-top: 1px solid #e6e6e6;
    @media (-webkit-min-device-pixel-ratio: 2) {
      border-top: 0.5px solid #e6e6e6;
    }
  }

  .tips {
    color: #b2b2b2;
    font-size: r(12);
    padding: r(10) r(15) r(20) r(15);
    background-color: #fff;
  }
}
