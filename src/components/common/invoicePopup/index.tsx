import React, { useEffect, useState } from 'react';
import { Popup, Input, Button } from 'zarm';
import validate from 'src/utils/validate';

import './index.scss';
import StaticToast from '../toast';

type popupType = {
    value: any,
    orderId?: string,
    visible: boolean,
    onClose: any,
    onSuccess: any

}

const InvoicePopup = ({value, orderId, visible, onClose, onSuccess }: popupType) => {
    const [type, setType] = useState('1');
    const [mobile, setMobile] = useState('')
    const [email, setEmail] = useState('');
    const [title, setTitle] = useState('')
    const [taxpayerIdentityNo, setTaxpayerIdentityNo]= useState('')
    const typeOnChange = value => {
        console.log('type',type);
        setType(value);
    }
    const mobileonChange = (value) => {
        setMobile(value);
    }
    const onConfirm = () => {
        console.log('mobile', mobile, !validate.isMobile(mobile))
        if(title.trim() === ''){
            StaticToast.warning('您输入抬头格式有误');
            return ;
        }
        if(type === '2' && validate.isEmpty(taxpayerIdentityNo)){
            StaticToast.warning('请输入纳税人识别号');
            return ;
        }
        if(mobile === '' || !validate.isMobile(mobile)){
            StaticToast.warning('您输入手机号格式有误');
            return ;
        }
        if(email === '' || !validate.isEmail(email)){
            StaticToast.warning('您输入邮箱格式有误');
            return ;
        }
        if(type === '0'){
            StaticToast.warning('请选择开票抬头类型');
            return ;
        }


        if(type === '1'){
            onSuccess && onSuccess({
                email: email,
                mobile: mobile,
                invoiceTitleType: type,
                invoiceTitle: title,
                orderId: orderId
            })
        }else{
            onSuccess && onSuccess({
                email: email,
                mobile: mobile,
                invoiceTitleType: type,
                invoiceTitle: title,
                taxpayerIdentityNo: taxpayerIdentityNo,
                orderId: orderId
            })

        }

    }
    useEffect(() => {
        console.log('value',value)
        if(value.invoiceTitle){
            setType(value.invoiceTitleType);
            setMobile(value.mobile);
            setTitle(value.invoiceTitle);
            setEmail(value.email);
            if(value.invoiceTitleType === '2'){
                setTaxpayerIdentityNo(value.taxpayerIdentityNo)
            }
        }
    },[value])
    const titleonChange = value => {
        setTitle(value)
    }
    const mailonChange = (value) => {
        setEmail(value)
    }
    const taxpayerIdentityNoonChange = (value) => {
        setTaxpayerIdentityNo(value)
    }
    return (
        <Popup visible={visible} destroy onMaskClick={onClose}>
            <div className="invoice-popup">
                <div className="title-box">
                    <div className="title">发票</div>
                    <img className="icon-close" onClick={onClose} src={require('./images/icon-close.png')} />
                </div>
                <div className="cont">发票类型：电子普通发票</div>
                <div className="form-cont">
                    <div className="form-item">
                        <div className="label">发票抬头</div>
                        <div className="radio-group">
                            <div className={type === '1' ? 'radio-item active' : 'radio-item'} onClick={() => typeOnChange('1')}>个人</div>
                            <div className={type === '2' ? "radio-item active" : 'radio-item'} onClick={() => typeOnChange('2')}>单位</div>
                        </div>
                    </div>
                    {
                        type === '2' ?(
                            <div>
                                <div className="form-item">
                                    <div className="label">单位名称</div>
                                    <Input value={title} onChange={titleonChange} placeholder="请填写单位名称" />
                                </div>
                                <div className="form-item">
                                    <div className="label">纳税人识别号</div>
                                    <Input value={taxpayerIdentityNo} onChange={taxpayerIdentityNoonChange} placeholder="请填写纳税人识别号" />
                                </div>
                            </div>

                        ): (
                            <div className="form-item">
                                <div className="label">个人姓名</div>
                                <Input value={title} onChange={titleonChange} placeholder="请填写个人姓名" />
                            </div>
                        )
                    }
                </div>
                <div className="cont">收票人信息</div>
                <div className="form-cont">
                    <div className="form-item">
                        <div className="label">收票人手机</div>
                        <Input value={mobile} maxLength={11} onChange={mobileonChange} placeholder="用于接收电子发票短信" />
                    </div>
                </div>
                <div className="form-cont">
                    <div className="form-item">
                        <div className="label">收票人邮箱</div>
                        <Input type="email" value={email} onChange={mailonChange} placeholder="用于接收电子发票" />
                    </div>
                </div>
                <div className="tips">提示：发票开具后不支持重开，如有问题请及时联系客服处理</div>
                <div className="button-group">
                    <Button shape="round" block theme="primary" className="btn-confirm" onClick={onConfirm}>确认</Button>
                </div>
            </div>

        </Popup>
    )
}
export default InvoicePopup