import { _Data_To_Obj } from 'src/utils/staticData';
import React, { useState, useRef } from 'react';
import classnames from 'classnames';
import { Popup } from 'zarm';
import './menu.scss';

interface MenuProps {
  menu: Array<{ label: string, value: string | number }>;
  valueOnChange?: (value: string) => void;
  prefixCls?: string;
  value?: string | number;
}

const Menu = (props: MenuProps) => {
  const { menu, valueOnChange, prefixCls } = props;
  const menuObj = _Data_To_Obj(menu);
  const menuRef: any = useRef(null)
  const defaultSelected = menu[0].value;
  const [select, setSelect] = useState(defaultSelected);
  const [active, setActive] = useState(false);

  const menuItemClick = (item) => {
    setSelect(item.value);
    setActive(!active);
    typeof valueOnChange === 'function' && valueOnChange(item);
  }

  return (
    <div className={`menu_component ${prefixCls ? prefixCls : ''}`}>
      <div className={classnames('menu', { 'active': active })} onClick={() => setActive(!active)}>
        <p>{menuObj[select]}</p>
        <p className='arrow' />
      </div>
      <div className='menu_ref' ref={menuRef}></div>
      <Popup className='menu_popup' visible={active} onMaskClick={() => setActive(false)} direction="top" mountContainer={() => menuRef.current}>
        <div className='menu_list'>
          {menu.map(item => {
            return (
              <div className={classnames('menu_item', { 'active': select === item.value })} key={`menu_item_${item.value}`} onClick={() => menuItemClick(item)}>
                <p>{item.label}</p>
              </div>
            );
          })}
        </div>
      </Popup>
    </div>
  );
}

export default Menu;