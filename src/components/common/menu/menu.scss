@import "src/style/index";

.menu_component {
  position: relative;
  width: 100%;
  // height: r(50);
  .menu {
    @include display-flex;
    @include align-items(center);
    @include justify-content(center);

    position: relative;
    width: inherit;
    height: r(50);
    line-height: r(50);
    font-size: r(16);
    font-weight: bold;
    color: var(--color-text);
    z-index: 52;
    background: #fff;

    .arrow {
      margin-left: r(6);
      height: r(10);
      width: r(10);
      border-top: r(2) solid #d8d8d8;
      border-right: r(2) solid #d8d8d8;
      transform: translateY(r(-2.5)) rotate(135deg);
      transition: transform .3s linear;
    }

    &.active {
      color: var(--theme-primary);
      @include borderBottom($color: #E6E6E6);

      .arrow {
        transform: translateY(r(2.5)) rotate(-45deg);
      }
    }
  }
}

.menu_ref {
  .za-mask {
    z-index: 50;
  }

  .za-mask,
  .za-popup--top {
    top: 50px;
  }

  .menu_popup {
    z-index: 51;

    .menu_list {
      width: 100%;
      background: #fff;
      position: relative;
      padding-top: r(50);

      .menu_item {
        height: r(45.5);
        line-height: r(45.5);
        padding: 0 r(15);
        width: 100%;
        font-size: r(16);
        border-bottom: r(1) solid var(--border-disabled);
        color: var(--color-text);

        &.active {
          color: var(--theme-primary);
        }
      }
    }
  }
}

/* 列表的公共 tabs样式 */
.my-common__tabs {
  background-color: #fff;
  position: relative;
  z-index: 58;
  font-weight: bold;

  .active {
    color: var(--theme-primary);
  }

  .za-tabs__header,
  ul,
  li {
    height: r(50);
    line-height: r(50);
  }
}
