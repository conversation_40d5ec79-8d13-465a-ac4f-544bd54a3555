import React from 'react';
import { SvgIcon } from 'src/components/common';
import './index.scss';

const STATUSMAP = {
  1: '待骑手接单',
  2: '骑手已接单',
  3: '骑手已到达',
  4: '骑手已取货',
  5: '骑手已送达',
  6: '配送单已取消',
  7: '配送异常',
};

const o2oExpress = (props) => {
  const { o2oInfo = {} } = props;
  const { riderName, riderPhone, expressStatus } = o2oInfo || {};
  return <div className='o2o_express_copm'>
    <div className='o2o_express_cell title'>
      <span>配送信息</span>
      <span className='active'>{STATUSMAP[expressStatus]}</span>
    </div>
    <div className='o2o_express_cell'>
      <span className='label'>期望时间</span>
      <span>立即配送</span>
    </div>
    {
      riderName && <div className='o2o_express_cell'>
        <span className='label'>配送骑手</span>
        <span>{riderName}</span>
      </div>
    }

    {
      riderPhone && <a className='rider_phone' href={`tel:${riderPhone}`}><SvgIcon className='icon_phone' src={require('src/svgs/sprite-icon_phone.svg')}/>致电骑手</a>
    }
  </div>;
};

export default o2oExpress;
