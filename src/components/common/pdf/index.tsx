import PropTypes from 'prop-types';
import React, { useEffect } from 'react';
import './pdf.scss';


const PdfComponent = ({ src, containerId }) => {


  useEffect(() => {
    import('pdfjs-dist/es5/build/pdf').then((pdfjsLib) => {
      import('pdfjs-dist/es5/build/pdf.worker.entry').then((pdfjsWorker) => {
        pdfjsLib && (pdfjsLib.GlobalWorkerOptions.workerSrc = pdfjsWorker);
        fetchPdf(pdfjsLib);
      });
    });

  }, [src, containerId]);

  const fetchPdf = async (pdfjsLib) => {
    const loadingTask = pdfjsLib.getDocument(src);
    const pdf = await loadingTask.promise;
    const firstPageNumber = 1;
    const container = document.getElementById(containerId)!;
    for (let i = firstPageNumber; i <= pdf._pdfInfo.numPages; i++) {
      const id = `${src}-${i}`;
      const canvas = document.createElement('canvas');
      const page = await pdf.getPage(i);
      const scale = 1.5;
      const viewport = page.getViewport({ scale });
      const context = canvas.getContext('2d');
      canvas.height = viewport.height || 300;
      canvas.width = viewport.width || 400;
      canvas.id = id;
      canvas.className = 'canvas';
      container.appendChild(canvas);

      const renderContext = {
        canvasContext: context,
        viewport,
      };

      const renderTask = page.render(renderContext);
      await renderTask.promise;
    }
  };

  return (
    <div id={containerId}>

    </div>
  );
};

PdfComponent.propTypes = {
  src: PropTypes.string,
};


export default PdfComponent;
