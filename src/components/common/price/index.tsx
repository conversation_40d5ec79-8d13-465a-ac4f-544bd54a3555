import React from 'react';
import './index.scss';

interface GoodsPriceProps {
  price: number;
  originalPrice?: number;
  className?: string;
}

function GoodsPrice({ price = 0, originalPrice, className }: GoodsPriceProps) {
  const priceSource = Number(price).toFixed(2).split('.');
  return (
    <div className={`DrugPrice ${className}`}>
      <span className='DrugPrice__real'>
        <span className='DrugPrice__rmb'>¥</span>
        <span className='DrugPrice__value'>{priceSource[0]}</span>
        <span className='DrugPrice__float'>.{priceSource[1]}</span>
      </span>
      {originalPrice || originalPrice === 0
        ? (
          <>
            <span className='DrugPrice__original'>¥{Number(originalPrice).toFixed(2)}</span>
          </>
        )
        : (
          ''
        )}
    </div>
  );
}

export default GoodsPrice;
