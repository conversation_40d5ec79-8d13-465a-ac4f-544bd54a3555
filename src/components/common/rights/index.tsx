import React, { useCallback } from 'react';
import { RIGHTS_OBJ } from 'src/pages/home/<USER>';
import SvgIcon from '../svg';
import './rights.scss';

const prefix = 'comp_rights';

const Rights = (props) => {

  const { prefixCls, list = [] } = props;

  const toConsultOrRights = useCallback((param) => {
    //这里用vscode区分是权益还是咨询
    if (param.vasCode) {
      const _right = RIGHTS_OBJ[param.vasCode];
      const { authType = 'real', defaultType = '' } = _right;
      // window._XFLOW_.pushEvent(['click', 'ZAHLWYY_SY', '首页', { ZAHLWYY_CLICK_CONTENT: text }]);
      window.reactHistory.push({
        pathname: authType === 'real' ? '/hospital/rights/detail' : '/hospital/rights/detail/special',
        search: `defaultType=${defaultType}`
      })
    } else {
      window.reactHistory.push({
        pathname: '/hospital/rights/detail/special',
        search: `consultType=${param.defaultType}`
      })
    }
  }, []);

  return (
    <div className={`${prefix} ${prefixCls}`}>
      {
        list.map((item, index) => {
          const { title, num, img, vasName, isSelected, vasCode, totalCount } = item;
          return <div className={`${prefix}__item ${num || isSelected ? '' : 'no_rights'}`} style={{ backgroundImage: `url(${img ? img : require('src/pages/home/<USER>/rights/' + vasCode + '.png')})` }} key={`rights${index}`} onClick={()=>toConsultOrRights(item)}>
            <p className={`${prefix}__name`}>{title || vasName}</p>
            <div>{num ? num : isSelected ? totalCount > 0 ? '可使用' : '不限次' : <div className={`${prefix}__noright`}><p>未获得</p><SvgIcon type='img' className="icon_right_narrow" src={require('src/pages/home/<USER>/icon_arrow.svg')} /></div>}</div>
          </div>
        })
      }
    </div>
  )
}

export default Rights;