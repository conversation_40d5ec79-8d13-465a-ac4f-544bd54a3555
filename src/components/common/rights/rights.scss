@import "src/style/index";
$prefixCls: 'comp_rights';

.#{$prefixCls} {
  @include display-flex;

  &__item {
    width: r(94);
    height: r(55);
    padding: r(12) 0 0 r(8);
    min-width: r(94);
    margin: 0 r(10) r(10) 0;
    background: #f0f9f6;
    border-radius: r(6);
    font-size: r(11);
    color: #666;
    line-height: 2.4;
    background-size: 100% 100%;

    &.no_rights {
      filter: grayscale(100%);

      .comp_rights__name {
        color: #999;
      }
    }
  }

  &__name {
    color: #333;
    font-weight: bold;
    font-size: r(13);
    line-height: 1em;
  }

  &__noright {
    margin-top: r(8);
    font-size: r(11);
    color: rgba(102, 102, 102, 0.5);
    line-height: 1;
    @include display-flex;
    @include align-items(center);

    .icon_right_narrow {
      display: block;
      width: r(12);
      height: r(12);
      margin-left: r(1);
    }
  }
}
