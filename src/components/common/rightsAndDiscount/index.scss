@import 'src/style/index';

.right_discount_comp {
  width: r(345);
  padding: r(15);
  margin: 0 auto;
  background: #fff;
  border-radius: r(8);

  .r_d_title {
    @include display-flex;
    @include align-items(center);

    font-size: r(15);
    color: #999;

    .icon_title {
      width: r(18);
      margin-right: r(4);
    }
  }

  .doubt-tips {
    color: var(--text-base-color);
    font-size: 12px;
    margin-left: 5px;
    font-weight: normal;

    .icon_tip {
      width: r(12);
      height: r(12);
      margin-right: r(3);
    }
  }

  .active {
    color: var(--text-base-color);
  }

  .strong {
    font-weight: bold;
  }

  .r_d_title_text {
    font-size: r(16);
    color: #1e1e1e;
    font-weight: bold;
    @include flex(1);
  }

  .icon_narrow {
    margin-left: r(3);
    font-size: r(15);
    color: #d8d8d8;
  }

  .r_d_select {
    width: r(315);
    padding: r(14) r(15);
    margin-top: r(12);
    box-sizing: border-box;
    font-size: r(15);
    background: #f8fffc;
    border: r(1) solid var(--text-base-color);
    border-radius: r(6);
    @include display-flex;
    @include align-items(center);
    @include justify-content(space-between);

    .strong {
      padding: 0 r(4);
    }
  }
}

.right_discount_modal {
  background-color: #fff;
  border-radius: r(8) r(8) 0 0;
  padding-bottom: r(0);

  .orange {
    color: #ff7240;
  }

  .header {
    position: relative;
    padding: r(17) 0;
    font-size: r(16);
    line-height: r(16);
    text-align: center;

    .icon {
      position: absolute;
      top: r(15);
      right: r(15);
      bottom: 0;
      font-size: r(18);
      color: #e6e6e6;
    }
  }

  .r_d_modal_container {
    border-top: 1px solid #e6e6e6;
    padding: r(10) r(15);
    min-height: r(253);
    max-height: r(305);
    overflow-y: auto;

    .vas_title {
      margin: r(15) 0;
      font-weight: bold;

      &:first-child {
        margin-top: r(5);
      }
    }

    .vas-item {
      margin-bottom: r(10);
      padding: r(16) r(15) 0;
      background: #fafafa;
      border-radius: r(7);
      position: relative;

      .free-tag {
        display: inline-block;
        height: r(19);
        line-height: r(19);
        padding: 0 r(6);
        text-align: center;
        font-size: r(11);
        font-weight: bold;
        color: #ff7240;
        border: 1px solid #ff7240;
        border-radius: r(2);
      }

      .vas-info {
        padding: r(12) 0 0;

        &-head {
          @include display-flex;
          @include align-items(center);
          @include justify-content(space-between);
        }

        .name {
          flex: 1 1 auto;
          font-size: r(16);
          font-weight: bold;
          color: #1e1e1e;
        }

        &-tips {
          line-height: r(20);
          margin-top: r(10);
          font-size: r(13);
          color: #666;
        }

        &-detail {
          position: relative;
          @include display-flex;
          @include justify-content(space-between);

          padding: r(10) 0;
          border-top: 1px solid #ececec;
          margin-top: r(16);

          .icon {
            position: absolute;
            right: r(0);
            top: r(10);
            font-size: r(18);
            font-weight: bold;
            color: #e6e6e6;

            // transition: all 1s linear;
            &.isUnfold {
              transform: rotate(180deg);
            }
          }

          .policy-unfold {
            font-size: r(13);
            color: #999;
            line-height: r(18.5);
          }

          .line {
            .title {
              font-size: r(13);
              color: #999;
              line-height: r(18.5);
              width: r(78);
            }

            .value {
              font-size: r(13);
              color: #666;
              line-height: r(18.5);
            }
          }
        }
      }
    }
  }

  .r_d_modal_footer {
    border-top: 1px solid #e6e6e6;
    height: r(74);
    padding: 0 r(15) r(10);
    width: 100%;
    @include display-flex;
    @include align-items(center);
    @include justify-content(space-between);

    .btn_submit {
      width: r(150);
    }
  }
}
