import React, { useState, useCallback, ReactNode } from 'react';
import { useOverflowHidden } from 'src/components/hooks';
import { Icon, Modal } from 'zarm';
import RightsAndDiscountPopup from './popup';
import './index.scss';
import SvgIcon from '../svg';

interface RightsAndDiscountProps {
  className?: string;
  patientName?: string;
  checkedVas?: any;
  vasList?: any[];
  vasClick?: (vas: any) => void;
  ableCouple?: any[];
  checkedCoupon?: any;
  couponClick?: (coupon: any) => void;
  children?: ReactNode;
  disabled?: boolean;
}

const RightsAndDiscount: React.FC<RightsAndDiscountProps> = (props) => {
  const {
    className = '',
    patientName,
    checkedVas = {},
    vasList = [],
    vasClick,
    ableCouple = [],
    checkedCoupon = {},
    couponClick,
    children,
    disabled,
  } = props;

  const [vasDiscountPop, setVasDiscountPop] = useState(false);

  useOverflowHidden(vasDiscountPop);

  const hasNoChoice = !ableCouple.length && !vasList.length;

  const couponId = checkedCoupon.id;
  const couponName = couponId ? getCouponName(checkedCoupon || {}) : '';
  const { id: vasId, rightsBasicInfoDomain = {} } = checkedVas;
  const vasName = rightsBasicInfoDomain.rightsName;

  const click = useCallback(() => {
    if (hasNoChoice || disabled) {
      return;
    }
    setVasDiscountPop(true);
  }, [hasNoChoice]);

  const alertSupportMinute = useCallback((e) => {
    e.stopPropagation();
    Modal.alert({
      content: <p className='danger_inquiry_alert_contents'>请根据所需确定使用的权益优惠</p>,
      cancelText: '我知道了',
    });
  }, []);

  // 如果使用自定义内容，直接返回子节点和弹窗
  if (children) {
    return (
      <>
        <div onClick={click}>
          {children}
        </div>
        <RightsAndDiscountPopup
          visible={vasDiscountPop}
          patientName={patientName}
          vasList={vasList}
          checkedVas={checkedVas}
          vasClick={vasClick}
          ableCouple={ableCouple}
          checkedCoupon={checkedCoupon}
          couponClick={couponClick}
          onClose={() => {
            setVasDiscountPop(false);
          }}
        />
      </>
    );
  }

  // 否则使用原有的组件结构
  return (
    <div className={`right_discount_comp ${className}`} onClick={click}>
      <div className='r_d_title'>
        <img className='icon_title' src={require('src/svgs/icon_rights_discount.svg')} />
        <span className='r_d_title_text'>权益优惠</span>
        {
          hasNoChoice ?
            <span>暂无可用</span> :
            !couponId && !vasId ?
              <span className='active'>优惠待使用</span> : null
        }
        {
          (!hasNoChoice && !couponId && !vasId) && <Icon className='icon_narrow' type='arrow-right' />
        }
        {
          (couponId || vasId) && <p className={'doubt-tips'} onClick={alertSupportMinute}>
            <SvgIcon className='icon_tip' src={require('src/svgs/sprite-icon_tips.svg')} />
            请根据所需确定使用的权益优惠
          </p>
        }
      </div>
      {
        couponId && <div className='r_d_select'>
          <p>已选<span className='active strong'>{couponName}</span>优惠</p>
          <Icon className='icon_narrow' type='arrow-right' />
        </div>
      }
      {
        vasId && <div className='r_d_select'>
          <p>已选<span className='active strong'>{vasName}</span>权益</p>
          <Icon className='icon_narrow' type='arrow-right' />
        </div>
      }
      <RightsAndDiscountPopup
        visible={vasDiscountPop}
        patientName={patientName}
        vasList={vasList}
        checkedVas={checkedVas}
        vasClick={vasClick}
        ableCouple={ableCouple}
        checkedCoupon={checkedCoupon}
        couponClick={couponClick}
        onClose={() => {
          setVasDiscountPop(false);
        }}
      />
    </div>
  );
};

// 辅助函数
const getCouponName = (coupon: any) => {
  if (!coupon) {
    return '';
  }
  const { couponAmount, couponName } = coupon;
  return couponName || `${couponAmount}元优惠券`;
};

export default RightsAndDiscount;
