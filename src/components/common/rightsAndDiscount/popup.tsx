import classnames from 'classnames';
import React, { useState, useCallback, useEffect } from 'react';
import Coupon from 'src/pages/video/components/Coupon';
import format from 'src/utils/format';
import { Icon, Popup, Radio, Button } from 'zarm';
// import { getName } from './utils';
import './index.scss';
import { pushEvent } from 'src/utils';

const VAS = 'vas';
const COUPON = 'coupon';

export const lineFeedContent = (content: any = '') => {
  if (typeof content !== 'string') {
    return content;
  }
  return (content.split(/(\t|\n|\r|↵)/)).map((k, i) => ((k || '').trim() && <p className='value' key={'content-' + String(i)}>{i == 0 && <span className='title'>使用说明：</span>}{k}</p >));
};

const RigthsAndDiscountPopup = (props) => {
  const { onClose, visible, patientName, checkedVas = {}, vasList = [], ableCouple = [], checkedCoupon = {}, couponClick, vasClick } = props;

  const [vasUnfoldList, setVasUnfoldList]: [any, any] = useState([]);

  const [type, setType] = useState('');
  const [currentVas, setCurrentVas]: any = useState({});
  const [currentCoupon, setCurrentCoupon]: any = useState({});

  useEffect(() => {
    pushEvent({eventTag: 'euity_address'});
  }, []);

  useEffect(() => {
    if (visible) {
      setCurrentVas(checkedVas);
      setCurrentCoupon(checkedCoupon);
    } else {
      setCurrentVas({});
      setCurrentCoupon({});
    }
    // console.log('setCurrent');
  }, [checkedVas, checkedCoupon, visible]);

  // 某一个权益展开详情
  const setVasUnfoldClick = useCallback(
    (id) => {
      const index = vasUnfoldList.indexOf(id);
      if (index > -1) {
        const list = [...vasUnfoldList];
        list.splice(index, 1);
        setVasUnfoldList(list);
      } else {
        setVasUnfoldList([...vasUnfoldList, id]);
      }
    },
    [vasUnfoldList],
  );

  const selectCoupon = useCallback((item) => {
    setType(COUPON);
    if (currentCoupon.id === item.id) {
      setCurrentCoupon({});
    } else {
      setCurrentCoupon(item);
    }
    setCurrentVas({});
  }, [currentCoupon]);

  const selectVas = useCallback((item) => {
    if (item.userRightsStatus != 1) {
      return;
    }
    setType(VAS);
    if (currentVas.id === item.id) {
      setCurrentVas({});
    } else {
      setCurrentVas(item);
    }
    setCurrentCoupon({});

  }, [currentVas]);

  const submitClick = useCallback(() => {
    pushEvent({eventTag: 'euity_choose'});
    if (type === VAS) {
      vasClick(currentVas);
    } else if (type === COUPON) {
      couponClick(currentCoupon);
    }
    onClose && onClose();
  }, [type, currentVas, currentCoupon]);

  return <Popup visible={visible} onMaskClick={onClose} direction='bottom'>
    <div className={'right_discount_modal'}>
      <header className='header'>
        {patientName}拥有的权益优惠
        <Icon className='icon' type='wrong' onClick={onClose} />
      </header>
      <div className={'r_d_modal_container'}>
        {!!vasList.length && (
          <>
            <p className='vas_title'>{patientName}拥有的问诊权益</p>
            {
              vasList.map((policy) => {
                // if (!policy.isPolicyRights) {
                const { rightsEffectTime, rightsExpiryTime, id, rightsBasicInfoDomain = {} } = policy;
                const isUnfold = vasUnfoldList.includes(id);
                const effectDate = format.date((rightsEffectTime || '').replace(/-/g, '/'), 'yyyy.MM.dd');
                const expireDate = format.date((rightsExpiryTime || '').replace(/-/g, '/'), 'yyyy.MM.dd');
                // const rightsShowDesc = lineFeedContent(rightsBasicInfoDomain.rightsShowDesc);
                return <div className='vas-item' key={policy.id}>
                  <div className='free-tag'>问诊权益</div>
                  <div className='vas-info'>
                    <div className='vas-info-head' onClick={() => selectVas(policy)}>
                      <p className='name'>{rightsBasicInfoDomain.rightsName}</p>
                      {<Radio checked={policy.id === currentVas.id} disabled={policy.userRightsStatus != 1} />}
                    </div>
                    <div className='vas-info-tips'></div>
                    <div className='vas-info-detail'>
                      {!isUnfold ? (
                        <div className='policy-unfold' onClick={() => setVasUnfoldClick(id)}>
                          权益有效期至 {expireDate ? expireDate : '无限期'}
                        </div>
                      ) : (
                        <div>
                          <div className='line'>
                            <span className='title'>权益有效期：</span>
                            <span className='value'>{effectDate && expireDate ? `${effectDate} 至 ${expireDate}` : '无限期'}</span>
                          </div>
                          {
                            rightsBasicInfoDomain.rightsDesc && <div className='line'>
                              <span className='title'>使用说明：</span>
                              <span className='value'>{rightsBasicInfoDomain.rightsDesc}</span>
                            </div>
                          }
                          <div className='line'>
                            <span className='title'>权益来源：</span>
                            <span className='value'>{policy.rightsResource}</span>
                          </div>
                        </div>
                      )}
                      <Icon className={classnames('icon', { isUnfold })} type='arrow-bottom' size='md' onClick={() => setVasUnfoldClick(id)} />
                    </div>
                  </div>
                </div>;

                // }
                // const { policyNo: _policyNo = '', payFrequency: _payFrequency, isInstallment: _isInstallment = false, policyVasInfos: _policyVasInfos = [], policyProduct: { packageName: _packageName = '' } = {}, policyInsurant: { name: _name = '' } = {} } = policy;
                // return _policyVasInfos.map((vas, vasIndex) => {
                //   const { effectDate: _effectDate = '', expireDate: _expireDate = '', vasRightsList = [] } = vas;
                //   const [{ patientPackageId: _patientPackageId = '' } = {}] = vasRightsList;
                //   const vasInfo = getName(vas);
                //   const effectDate = format.date(_effectDate.replace(/-/g, '/'), 'yyyy.MM.dd');
                //   const expireDate = format.date(_expireDate.replace(/-/g, '/'), 'yyyy.MM.dd');
                //   const isUnfold = vasUnfoldList.includes(_policyNo + vasIndex);
                //   const isPendingPay = (_payFrequency === '4' || _payFrequency === '6') && _isInstallment === false;
                //   const expire = new Date(_expireDate).getTime() < Date.now();

                //   return (
                //     <div className="vas-item" key={_policyNo + vasIndex}>
                //       <div className="free-tag">免费问诊权益</div>
                //       <div className="vas-info">
                //         <div className="vas-info-head" onClick={() => selectVas(policy)}>
                //           <p className="name">{vasInfo.name}</p>
                //           {!expire && !isPendingPay && <Radio checked={!!_patientPackageId && _patientPackageId === currentVas.id} disabled={policy.userRightsStatus != 1} />}
                //         </div>
                //         <div className="vas-info-tips">{vasInfo.drugsTips}</div>
                //         <div className="vas-info-detail">
                //           {!isUnfold ? (
                //             <div className="policy-unfold" onClick={() => setVasUnfoldClick(_policyNo + vasIndex)}>
                //               权益有效期至 {expireDate}
                //             </div>
                //           ) : (
                //             <div>
                //               <div className="line">
                //                 <span className="title">权益来源：</span>
                //                 <span className="value">{_packageName}</span>
                //               </div>
                //               <div className="line">
                //                 <span className="title">受益人：</span>
                //                 <span className="value">{_name}</span>
                //               </div>
                //               <div className="line">
                //                 <span className="title">权益有效期：</span>
                //                 <span className="value">{effectDate} 至 {expireDate}</span>
                //               </div>
                //             </div>
                //           )}
                //           <Icon className={classnames('icon', { isUnfold: isUnfold })} type="arrow-bottom" size="md" onClick={() => setVasUnfoldClick(_policyNo + vasIndex)} />
                //         </div>
                //       </div>
                //     </div>
                //   );
                // });
              })
            }
          </>
        )}
        {
          !!ableCouple.length && <>
            <p className='vas_title'>{patientName}拥有的问诊优惠</p>
            <Coupon coupons={ableCouple} userCouponId={currentCoupon.id} onClick={selectCoupon} />
          </>
        }
      </div>
      <div className='r_d_modal_footer'>
        <p>已选{currentVas.id || currentCoupon.id ? 1 : 0}张</p>
        <Button theme='primary' shape='round' className='btn_submit' onClick={submitClick}>确定</Button>
      </div>
    </div>
  </Popup>;
};

export default RigthsAndDiscountPopup;
