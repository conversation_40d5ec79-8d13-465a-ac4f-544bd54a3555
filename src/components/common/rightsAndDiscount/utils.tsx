import { reverseArray } from 'src/utils';
import React from 'react';

type vasRight = {
  businessType: string;
  patientPackageId: string;
  patientPackageType: string;
  yearMaxCount: number;
  discount: number;
  monthMaxCount: number;
  currentMonthUsedCount: number;
};

function monthDrugsTips(count, maxCount) {
  return (
    <React.Fragment key={count + maxCount}>
      本月还剩<span className='orange'>{count}次</span>优惠，本年可用
      <span className='orange'>{maxCount}次</span>
    </React.Fragment>
  );
}

export function getName(right) {
  let name: string = '';
  let drugsTips: any[] = [];
  const vasRightsList = (right.vasRightsList || []) as vasRight[];
  for (let r of reverseArray(vasRightsList)) {
    const {
      businessType,
      yearMaxCount,
      discount,
      monthMaxCount,
      currentMonthUsedCount,
    } = r;

    if (businessType === 'inquiry') {
      if (name) name += ' | ';
      if (yearMaxCount == -1) {
        name += '不限次0元问诊';
      } else {
        name += yearMaxCount + '次0元问诊';
      }
    }
    if (businessType === 'drugs') {
      if (name) name += ' | ';
      if (yearMaxCount > 0) {
        name += yearMaxCount + '次';
      }
      if (discount > 0) {
        name += parseInt((discount as number) / 10 + '', 10) + '折购药';
      }

      if (monthMaxCount - currentMonthUsedCount >= 0) {
        drugsTips.push(monthDrugsTips(monthMaxCount - currentMonthUsedCount, yearMaxCount));
      }
    }
  }

  return {
    name,
    drugsTips,
  };
}
