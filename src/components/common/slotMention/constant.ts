/**
 * @description url中referrer所有枚举
 */
export enum ReferrerEnum {
  /**
   * @description 健康险小程序618
   */
  health618 = 'health618'
}

/**
 * @description 健康险618活动中带有【返回主会场】按钮的路径
 */
export const Paths_618_Enter = [
  '/hospital/physicalExamination', // 体检套餐
  '/hospital/static/channelServPack', // 医院家庭医生普惠版
  '/hospital/familydoctor/intro', // 199家庭医生
  '/hospital/static/hpdetection', // 众安无“幽”
  '/hospital/static/hpdetection/paysuccess', // 众安无“幽” 支付完成页
  '/hospital/static/hpdetection/paySuccess', // 众安无“幽” 支付完成页
  '/hospital/physicalExamination/paysuccess', // 体检套餐 支付完成页
  '/hospital/static/oneservpack/paysuccess', // 支付完成页
  '/hospital/static/commonSuccess/consult', // 支付完成页
  '/hospital/familydoctor/rights'
]

/**
 * @description 互医小程序跳板页路径
 */
export const AppletMiddleBridgePagePath = '/pages/common/middleBridgePage'