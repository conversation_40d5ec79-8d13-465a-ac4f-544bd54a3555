/* Toast.css */
.toast-container {
  display: flex;
  align-items: center;
  animation: toast-in 0.3s ease-out;
  pointer-events: auto;
  max-width: 80vw;
}

.toast-message {
  padding: 12px;
  border-radius: 6px;
  background: rgba(0, 0, 0, 0.8);
  color: #fff;
  text-align: center;
  font-size: 13px;
}

@keyframes toast-in {
  from {
    opacity: 0;
    transform: translate(-50%, -70%);
  }

  to {
    opacity: 1;
    transform: translate(-50%, -50%);
  }
}
