// Toast.tsx
import React, { useEffect, useState, useRef } from 'react';
import ReactDOM from 'react-dom';
import './index.scss';

interface ToastProps {
  message: string;
  type?: 'success' | 'error' | 'warning' | 'info';
  duration?: number;
  onClose?: () => void;
}

// Toast组件
const Toast: React.FC<ToastProps> = ({ message, type = 'info', duration = 3000, onClose }) => {
  const toastRef = useRef<HTMLDivElement>(null);

  // 计算并更新位置
  const updatePosition = () => {
    if (!toastRef.current) {
      return;
    }

    // 获取当前可视区域高度（考虑键盘弹出的情况）
    const viewportHeight = window.visualViewport ? window.visualViewport.height : window.innerHeight;
    const viewportWidth = window.visualViewport ? window.visualViewport.width : window.innerWidth;

    // 计算垂直偏移量（考虑键盘弹出后的位置）
    const offsetY = window.visualViewport ? window.visualViewport.offsetTop : 0;

    // 更新Toast位置
    toastRef.current.style.top = `${offsetY + viewportHeight / 2}px`;
    toastRef.current.style.left = `${viewportWidth / 2}px`;
  };

  useEffect(() => {
    // 初始化位置
    updatePosition();

    // 使用visualViewport API监听视口变化（iOS键盘弹出时更准确）
    if (window.visualViewport) {
      window.visualViewport.addEventListener('resize', updatePosition);
      window.visualViewport.addEventListener('scroll', updatePosition);
    } else {
      // 兼容不支持visualViewport的浏览器
      window.addEventListener('resize', updatePosition);
    }

    // iOS特有事件
    document.addEventListener('focusin', updatePosition);
    document.addEventListener('focusout', updatePosition);

    const timer = setTimeout(() => {
      onClose?.();
    }, duration);

    return () => {
      clearTimeout(timer);
      if (window.visualViewport) {
        window.visualViewport.removeEventListener('resize', updatePosition);
        window.visualViewport.removeEventListener('scroll', updatePosition);
      } else {
        window.removeEventListener('resize', updatePosition);
      }
      document.removeEventListener('focusin', updatePosition);
      document.removeEventListener('focusout', updatePosition);
    };
  }, [duration, onClose]);

  return (
    <div
      ref={toastRef}
      className={`toast-container ${type}`}
      style={{
        position: 'absolute',
        transform: 'translate(-50%, -50%)',
        zIndex: 10000,
      }}
    >
      <div className='toast-content'>
        <span className='toast-message'>{message}</span>
      </div>
    </div>
  );
};

// Toast管理器
class ToastManager {
  private static container: HTMLDivElement | null = null;
  private static toastId = 0;
  private static toasts: Array<{ id: number; element: React.ReactElement }> = [];

  private static createContainer() {
    if (!this.container) {
      this.container = document.createElement('div');
      this.container.className = 'toast-manager';
      this.container.style.position = 'fixed';
      this.container.style.top = '0';
      this.container.style.left = '0';
      this.container.style.width = '100%';
      this.container.style.height = '100%';
      this.container.style.pointerEvents = 'none';
      this.container.style.zIndex = '9999';
      document.body.appendChild(this.container);
    }
    return this.container;
  }

  private static renderToasts() {
    if (!this.container) {
      return;
    }

    ReactDOM.render(
      <React.Fragment>
        {this.toasts.map(({ id, element }) => (
          <React.Fragment key={id}>{element}</React.Fragment>
        ))}
      </React.Fragment>,
      this.container
    );
  }

  static show(options: ToastProps) {
    const id = ++this.toastId;
    const container = this.createContainer();

    const handleClose = () => {
      this.toasts = this.toasts.filter((toast) => toast.id !== id);
      this.renderToasts();

      if (this.toasts.length === 0 && container.parentNode) {
        ReactDOM.unmountComponentAtNode(container);
      }
    };

    const toastElement = (
      <Toast
        {...options}
        onClose={handleClose}
      />
    );

    this.toasts.push({ id, element: toastElement });
    this.renderToasts();

    return id;
  }

  static success(message: string, options?: Omit<ToastProps, 'message' | 'type'>) {
    return this.show({ ...options, message, type: 'success' });
  }

  static error(message: string, options?: Omit<ToastProps, 'message' | 'type'>) {
    return this.show({ ...options, message, type: 'error' });
  }

  static warning(message: string, options?: Omit<ToastProps, 'message' | 'type'>) {
    return this.show({ ...options, message, type: 'warning' });
  }

  static info(message: string, options?: Omit<ToastProps, 'message' | 'type'>) {
    return this.show({ ...options, message, type: 'info' });
  }
}

export default ToastManager;
