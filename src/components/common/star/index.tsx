import React from 'react';
import classnames from 'classnames';
import './star.scss';

interface StarProps {
  starClick?: any;
  value: number;
  prefixCls?: string;
}

const stars = [
  { value: 1 },
  { value: 2 },
  { value: 3 },
  { value: 4 },
  { value: 5 }
]

const Star = (props: StarProps) => {
  const { prefixCls, value, starClick } = props;
  
  const click = (index) => {
    typeof starClick === 'function' && starClick(index + 1);
  };

  return (
    <div className={classnames('star_component', prefixCls)}>
      {stars.map((item, index) => {
        return (
          value > index ? <img className='star_item' key={`star_key_${item.value}`} onClick={() => click(index)} src={require('src/images/icon_star_active.png')}/> : <img className='star_item' key={`star_key_${item.value}`} onClick={() => click(index)} src={require('src/images/icon_star.png')}/>
        )
      })}
    </div>
  );
}

export default Star;