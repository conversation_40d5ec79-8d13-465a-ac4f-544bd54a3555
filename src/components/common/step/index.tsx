import React from 'react';
import classnames from 'classnames';
import './step.scss';

interface StepProps {
  current: number,
  list?: string[],
}

const Step = (props: StepProps) => {
  const defaultList = ['基本信息', '个人情况', '健康信息', '健康档案'];
  const { list = defaultList, current } = props;

  return (
    <div className="steps-compoment">
      {list.map((k: any, v: number) => {
        let index = v + 1;
        return (
          <section key={`key-step-${index}`} className={classnames('steps-compoment-item', { 'steps-compoment-finish': index <= current, 'steps-compoment-current': index == current })}>
            <p className="steps-compoment-item-icon"></p>
            <p className="steps-compoment-item-text">{k}</p>
          </section>
        )
      })}
    </div>
  )
}
export default Step;