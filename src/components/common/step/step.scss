@import "src/style/index";

.steps-compoment {
  @include display-flex;

  background: #fff;
  padding: r(22);
  z-index: 0;
  position: relative;

  &-item {
    @include flex;

    position: relative;
    color: #909090;

    &-icon {
      width: r(13);
      height: r(13);
      margin: 0 auto;
      position: relative;
      z-index: 2;

      &:after {
        content: " ";
        background-color: #e6e6e6;
        width: r(9);
        height: r(9);
        border-radius: 50%;
        position: absolute;
        left: 50%;
        top: 50%;
        transform: translate(-50%, -50%);
      }
    }

    &-text {
      line-height: r(20);
      font-size: r(13);
      text-align: center;
      margin-top: r(9);
    }

    &:not(:first-child) {
      &::after {
        content: "";
        position: absolute;
        right: 50%;
        width: 100%;
        height: r(2);
        top: r(6.2);
        background-color: #e6e6e6;
        z-index: 1;
      }
    }
  }

  &-finish {
    &:not(:first-child) {
      &::after {
        background-color: var(--theme-primary);
      }
    }

    .steps-compoment-item-icon {
      &:after {
        background-color: var(--theme-primary);
      }
    }

    color: var(--theme-primary);
  }

  &-current {
    .steps-compoment-item-icon {
      &:after {
        background-color: #fff;
        width: r(13);
        height: r(13);
        border: r(3) solid var(--theme-primary);
        box-shadow: 0 2px 3px 0 rgba(140, 230, 200, 1);
      }
    }
  }
}
