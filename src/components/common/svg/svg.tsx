import classnames from 'classnames';
import React, { ReactNode } from 'react';

import PlaceholderSvg from './images/sprite-placeholder.svg';

export interface PureSvgProps {
  className?: string;
  type?: 'svg' | 'img';
  src?: ReactNode;
  width?: string | number;
  height?: string | number;
  restProps?: any;
}
const PureSvgIcon = (props: PureSvgProps) => {
  const { className, type = 'svg', src = PlaceholderSvg, ...restProps } = props;
  const cls = classnames('sprite-icon-components', { [`${className}`]: !!className, [`sprite-icon--${type}`]: !!type });
  if (type === 'img') {
    return <img src={src} className={cls} {...restProps} />;
  }
  return (
    <svg className={cls} {...restProps} fill='currentColor' crossOrigin='anonymous'>
      <use className='svg-icon-use' href={src} />
    </svg>
  );
};


export default PureSvgIcon;

