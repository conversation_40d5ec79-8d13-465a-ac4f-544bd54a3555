/*
 * @authors :<PERSON>
 * @description：首页 / 我的页 底部
 */

import React, { useCallback, useLayoutEffect } from 'react';
import { SvgIcon } from 'src/components/common';
import { cookies, pushEvent, validate } from 'src/utils';
import { TabBar } from 'zarm';
// import { DEPLOY__ENV } from 'src/utils/env';

import './tabBars.scss';
import { isFromSF } from 'src/utils/staticData';

interface tabItemTypes {
  key: string;
  title: string;
  icon: string | undefined;
  link: any;
  appletLink?: string;
  search?: string;
  activeIcon: string;
  activeLink: any;
}
const { Item } = TabBar;
// const channelPoint = DEPLOY__ENV({
//   'tst': 'Jk4QLN7T',
//   'uat': 'D2dSNTW5',
//   'prd': '2Y0hcqGQ',
// })
let tabBarList: tabItemTypes[] = [
  {
    title: '首页',
    key: 'home',
    link: '/hospital/home',
    appletLink: '/pages/index',
    icon: require('./images/sprite-tab-home.svg'),
    activeIcon: require('./images/sprite-tab-active-home.svg'),
    activeLink: ['/hospital/home'],
  },
  // {
  //   title: '商城',
  //   key: 'mall',
  //   link: '/hospital/preauth',
  //   search: 'partnerCode=ZA_HAOYAOSHI&businessType=index&channelPoint=' + channelPoint,
  //   icon: require('./images/sprite-tab-mall.svg'),
  //   activeIcon: require('./images/sprite-tab-active-mall.svg'),
  //   activeLink: [],
  // },
  {
    title: '健康管理',
    key: 'familydoctor',
    link: '/hospital/healthManage',
    appletLink: '/pages/healthManage',
    icon: require('./images/sprite-tab-health.svg'),
    activeIcon: require('./images/sprite-tab-active-health.svg'),
    activeLink: ['/hospital/healthManage'],
    // activeLink: ['/hospital/familydoctor/pre', '/hospital/familydoctor/intro', '/hospital/familydoctor/rights', '/hospital/static/hpdetection'],
  },
  {
    title: '个人中心',
    key: 'me',
    link: '/hospital/personalcenter',
    appletLink: '/pages/personalcenter',
    icon: require('./images/sprite-tab-personal.svg'),
    activeIcon: require('./images/sprite-tab-active-personal.svg'),
    activeLink: ['/hospital/personalcenter'],
  },
];


const TabBars = (props) => {
  const {
    location: { pathname = '/hosptial' },
    source,
  } = props;
  useLayoutEffect(() => {
    // 支付宝不能展示 医药商城
    if ((validate.isFromUnionPay() || validate.isAlipayApplet()) && tabBarList.length > 3) {
      tabBarList = tabBarList.filter((k) => k.key !== 'mall');
    }
    // 支付宝不能展示
    if (validate.isAlipayApplet()) {
      tabBarList = tabBarList.filter((k) => k.key !== 'familydoctor');
    }
  }, []);
  if(source === 'sf' || isFromSF()) {
    tabBarList = [
      {
        title: '首页',
        key: 'home',
        link: '/hospital/static/sfhome',
        icon: require('./images/sprite-tab-home.svg'),
        activeIcon: require('./images/sprite-tab-active-home.svg'),
        activeLink: ['/hospital/static/sfhome'],
      },
      {
        title: '个人中心',
        key: 'me',
        link: '/hospital/personalcenter',
        icon: require('./images/sprite-tab-personal.svg'),
        activeIcon: require('./images/sprite-tab-active-personal.svg'),
        activeLink: ['/hospital/personalcenter'],
      },
    ];
  }

  const activeKey = tabBarList.filter((item) => item.activeLink.includes(pathname))[0].key;
  const onChange = useCallback((value) => {
    if (value !== activeKey || !tabBarList.some((item) => pathname === item.link)) {
      const item = tabBarList.filter((item) => item.key === value)[0];
      const { appletLink = '', link = '', title = '' } = item;
      let {search = ''} = item;
      const channelResourceCode = cookies.get('channelResourceCode') || '';
      if(channelResourceCode){
        search += `${search.includes('?') ? '&' : '?'}channelResourceCode=${channelResourceCode}`;
      }
      pushEvent({ eventTag: 'ZAHLWYY_SY', text: '首页', attrs: { ZAHLWYY_CLICK_CONTENT: `首页_${title}` } });
      if (validate.isFromOwnMiniApplet() && value !== 'service' && appletLink) { // 这里不知道干嘛的，建议以后删掉
        wx.miniProgram.reLaunch({
          url: appletLink,
        });
      } else {
        window.reactHistory.push({
          pathname: link,
          search,
        });
      }
    }
  }, [pathname]);

  return (
    <div className='custom-tabbar-container'>
      <TabBar visible activeKey={activeKey} onChange={(value) => onChange(value)}>
        {tabBarList.map((item) => (
          <Item
            key={item.key}
            itemKey={item.key}
            title={item.title}
            icon={
              activeKey == item.key ? (
                <SvgIcon
                  className='bar-icon'
                  src={item.activeIcon}
                />
              ) : (
                <SvgIcon
                  className='bar-icon'
                  src={item.icon}
                />
              )
            }
          />
        ))}
      </TabBar>
    </div>
  );
};

export default TabBars;
