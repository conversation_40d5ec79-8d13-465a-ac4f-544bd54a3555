@import "src/style/index";

.custom-tabbar-container {
  position: relative;
  z-index: 999;

  &::after {
    content: "";
    position: relative;
    height: 50px;
    display: block;
    z-index: -10;
  }

  color: rgba($color: #000, $alpha: 0.4);

  .bar-icon {
    font-family: PingFangSC-Medium, PingFang SC;
    transition: color .3s;
    height: r(24);
    width: r(24);

    &.active {
      color: var(--theme-primary);

      .svg-icon-use {
        fill: var(--theme-primary);
      }
    }
  }

  .za-tab-bar {
    height: r(50);
    // border-radius: r(10) r(10) 0 0;
    box-shadow: 0 r(-5) r(16) 0 rgba(0, 0, 0, 0.04);
    @include iphone-bottom-fixed();

    .za-tab-bar__icon {
      width: auto;
      height: auto;
    }

    &--active {
      .za-tab-bar__title {
        font-weight: 600;
      }
    }

    &__title {
      // margin-top: r(3);
      font-size: r(10);
    }
  }
}
