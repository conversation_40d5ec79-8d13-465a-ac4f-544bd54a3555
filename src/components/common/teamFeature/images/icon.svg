<?xml version="1.0" encoding="UTF-8"?>
<svg width="105px" height="32px" viewBox="0 0 105 32" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>金牌医生</title>
    <defs>
        <linearGradient x1="0%" y1="50%" x2="100%" y2="50%" id="linearGradient-1">
            <stop stop-color="#E5C798" offset="0%"></stop>
            <stop stop-color="#BC916E" offset="100%"></stop>
        </linearGradient>
        <rect id="path-2" x="0" y="0" width="105" height="32" rx="16"></rect>
        <filter x="-1.4%" y="-4.7%" width="102.9%" height="109.4%" filterUnits="objectBoundingBox" id="filter-4">
            <feGaussianBlur stdDeviation="0.5" in="SourceAlpha" result="shadowBlurInner1"></feGaussianBlur>
            <feOffset dx="0" dy="-2" in="shadowBlurInner1" result="shadowOffsetInner1"></feOffset>
            <feComposite in="shadowOffsetInner1" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner1"></feComposite>
            <feColorMatrix values="0 0 0 0 1   0 0 0 0 1   0 0 0 0 1  0 0 0 0.149612107 0" type="matrix" in="shadowInnerInner1"></feColorMatrix>
        </filter>
        <linearGradient x1="74.3318257%" y1="59.6931673%" x2="100%" y2="21.0898931%" id="linearGradient-5">
            <stop stop-color="#FFFFFF" stop-opacity="0" offset="0%"></stop>
            <stop stop-color="#FFFFFF" stop-opacity="0.739920236" offset="100%"></stop>
        </linearGradient>
        <path d="M30.24,24.66 L30.493212,22.94 L23.173212,22.94 L23.8621841,18.26 L30.0421841,18.26 L30.2953961,16.54 L24.1153961,16.54 L24.4216059,14.46 L28.7216059,14.46 L28.8982654,13.26 C29.8715274,13.7133333 30.9649928,14.12 32.1786616,14.48 L32.4171519,12.86 C30.6396287,12.0733333 29.2583832,11.2666667 28.2734153,10.44 C27.2049285,9.54666667 26.2937365,8.4 25.5398392,7 L22.8398392,7 C21.6737365,8.4 20.4249285,9.54666667 19.0934153,10.44 C17.8650498,11.2666667 16.2462954,12.0733333 14.2371519,12.86 L13.9986616,14.48 C15.3908814,14.08 16.6250641,13.6666667 17.7012097,13.24 L17.5216059,14.46 L21.7816059,14.46 L21.4753961,16.54 L15.3153961,16.54 L15.0621841,18.26 L21.2221841,18.26 L20.533212,22.94 L13.253212,22.94 L13,24.66 L30.24,24.66 Z M18.7918736,12.76 C20.4790541,11.9866667 22.1543254,10.8866667 23.8176872,9.46 C25.0362882,10.8733333 26.3876837,11.9733333 27.8718736,12.76 L18.7918736,12.76 Z M27.2850963,22.18 C27.9389793,21.18 28.4016952,20.12 28.6732441,19 L26.3932441,19 C26.1083618,20.12 25.6856459,21.18 25.1250963,22.18 L27.2850963,22.18 Z M18.7850963,22.18 C18.502127,21.1133333 18.3915096,20.0533333 18.4532441,19 L16.1732441,19 C16.1150285,20.12 16.2656459,21.18 16.6250963,22.18 L18.7850963,22.18 Z M46.8916702,25.26 L47.4304818,21.6 L51.2104818,21.6 L51.4489721,19.98 L47.6689721,19.98 L47.9987366,17.74 L45.7387366,17.74 L45.4089721,19.98 L41.5489721,19.98 C43.7302997,19.02 45.2449607,18.06 46.092955,17.1 L48.572955,17.1 C49.4662883,17.1 50.1280834,16.9066667 50.5583404,16.52 C51.0038936,16.12 51.297334,15.44 51.4386616,14.48 L52.3101819,8.56 L47.5501819,8.56 C47.7478835,8.21333333 47.9489363,7.75333333 48.1533403,7.18 L45.7533403,7.18 C45.5681583,7.71333333 45.3737722,8.17333333 45.1701819,8.56 L42.4501819,8.56 L41.192955,17.1 L43.772955,17.1 C43.187773,17.6333333 42.3682762,18.1733333 41.3144646,18.72 L41.1289721,19.98 L39.9889721,19.98 L39.7504818,21.6 L45.1704818,21.6 L44.6316702,25.26 L46.8916702,25.26 Z M32.3169486,25.36 C33.5625732,24.5066667 34.4570307,23.5933333 35.0003212,22.62 C35.5781299,21.5933333 36.0152319,20.0733333 36.3116273,18.06 L36.4176231,17.34 L37.8776231,17.34 L36.7381692,25.08 L38.6581692,25.08 L40.0243361,15.8 L36.6443361,15.8 L36.9623232,13.64 L41.0223232,13.64 L41.2549249,12.06 L40.4349249,12.06 L41.1180084,7.42 L39.1980084,7.42 L38.5149249,12.06 L37.1949249,12.06 L37.8073446,7.9 L35.8073446,7.9 L34.2174089,18.7 C34.0348608,19.94 33.8162741,20.9266667 33.5616488,21.66 C33.3301713,22.3266667 33.0016381,22.9733333 32.5760492,23.6 L32.3169486,25.36 Z M45.8267022,11.98 L43.8467022,11.98 L44.1205245,10.12 L46.1005245,10.12 L45.8267022,11.98 Z M49.8867022,11.98 L47.9067022,11.98 L48.1805245,10.12 L50.1605245,10.12 L49.8867022,11.98 Z M47.0826123,15.54 C47.3230906,14.9933333 47.5175124,14.3066667 47.6658778,13.48 L49.6658778,13.48 L49.5363275,14.36 C49.4715524,14.8 49.3397394,15.1066667 49.1408886,15.28 C48.9420377,15.4533333 48.6359457,15.54 48.2226123,15.54 L47.0826123,15.54 Z M43.3226123,15.54 L43.6258778,13.48 L45.6058778,13.48 C45.4594753,14.2933333 45.2450534,14.98 44.9626123,15.54 L43.3226123,15.54 Z M70.5,24.66 L70.7384903,23.04 L57.7184903,23.04 C57.2118237,23.04 56.8675268,22.9333333 56.6855996,22.72 C56.5036724,22.5066667 56.4490221,22.1533333 56.5216488,21.66 L58.3117986,9.5 L72.5117986,9.5 L72.7502889,7.88 L56.0502889,7.88 L54.0039828,21.78 C53.8508779,22.82 53.9619379,23.56 54.3371627,24 C54.7123876,24.44 55.42,24.66 56.46,24.66 L70.5,24.66 Z M70.6141434,20.76 C69.4463633,20.36 68.5464346,19.9066667 67.9143576,19.4 C67.2442433,18.88 66.7025909,18.1666667 66.2894004,17.26 L71.0094004,17.26 L71.2390577,15.7 L66.1590577,15.7 C66.1979086,15.5266667 66.2448143,15.2533333 66.2997751,14.88 L66.5500427,13.18 L70.8100427,13.18 L71.0397001,11.62 L62.8797001,11.62 C63.0581797,11.3133333 63.280214,10.8466667 63.5458029,10.22 L61.3058029,10.22 C61.0241041,11.0466667 60.673608,11.7066667 60.2543147,12.2 C59.8483546,12.6933333 59.3151534,13.1 58.6547108,13.42 L58.475107,14.64 C59.8363275,14.36 60.9013061,13.8733333 61.6700427,13.18 L64.1700427,13.18 L63.9050534,14.98 C63.8559814,15.3133333 63.8139828,15.5533333 63.7790577,15.7 L58.2190577,15.7 L57.9894004,17.26 L63.2294004,17.26 C62.818294,18.06 62.178187,18.74 61.3090792,19.3 C60.4705638,19.8333333 59.290289,20.3333333 57.7682548,20.8 L57.562152,22.2 C61.0967773,21.4666667 63.4720485,20.3666667 64.6879657,18.9 C65.2329336,19.7266667 65.9424375,20.3866667 66.8164775,20.88 C67.6411099,21.3466667 68.8363347,21.7866667 70.402152,22.2 L70.6141434,20.76 Z M90.7406103,24.52 L90.9908779,22.82 L83.7708779,22.82 L84.5216809,17.72 L90.2416809,17.72 L90.4948929,16 L84.7748929,16 L85.4403104,11.48 L91.7803104,11.48 L92.0335223,9.76 L85.6935223,9.76 L86.0821733,7.12 L83.3621733,7.12 L82.9735223,9.76 L80.0935223,9.76 C80.4144074,9.12 80.7145145,8.44 80.9938435,7.72 L78.3738435,7.72 C77.9508885,9.05333333 77.4415059,10.1133333 76.8456958,10.9 C76.2518486,11.6733333 75.4029085,12.4133333 74.2988757,13.12 L74.08394,14.58 C76.2373803,13.9 77.8695038,12.8666667 78.9803104,11.48 L82.7203104,11.48 L82.0548929,16 L75.8148929,16 L75.5616809,17.72 L81.8016809,17.72 L81.0508779,22.82 L73.1508779,22.82 L72.9006103,24.52 L90.7406103,24.52 Z" id="path-6"></path>
        <filter x="-0.6%" y="-2.7%" width="101.3%" height="110.9%" filterUnits="objectBoundingBox" id="filter-7">
            <feOffset dx="0" dy="1" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feColorMatrix values="0 0 0 0 0.702473958   0 0 0 0 0.510311952   0 0 0 0 0.34560166  0 0 0 1 0" type="matrix" in="shadowOffsetOuter1"></feColorMatrix>
        </filter>
    </defs>
    <g id="页面-2" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="绑定医管家" transform="translate(-429.000000, -574.000000)">
            <g id="编组" transform="translate(0.000000, 319.000000)">
                <g id="编组-20" transform="translate(30.000000, 219.000000)">
                    <g id="编组-5">
                        <g id="编组-4" transform="translate(149.000000, 30.000000)">
                            <g id="编组-17">
                                <g id="金牌医师" transform="translate(250.000000, 6.000000)">
                                    <mask id="mask-3" fill="white">
                                        <use xlink:href="#path-2"></use>
                                    </mask>
                                    <g id="矩形">
                                        <use fill="url(#linearGradient-1)" fill-rule="evenodd" xlink:href="#path-2"></use>
                                        <use fill="black" fill-opacity="1" filter="url(#filter-4)" xlink:href="#path-2"></use>
                                    </g>
                                    <ellipse id="椭圆形" fill="url(#linearGradient-5)" mask="url(#mask-3)" cx="22.9166667" cy="-6.73684211" rx="42.0833333" ry="32"></ellipse>
                                    <g id="金牌医生" fill-rule="nonzero" mask="url(#mask-3)">
                                        <use fill="black" fill-opacity="1" filter="url(#filter-7)" xlink:href="#path-6"></use>
                                        <use fill-opacity="0.6" fill="#FFFFFF" xlink:href="#path-6"></use>
                                        <use fill="#FFFFFF" xlink:href="#path-6"></use>
                                    </g>
                                </g>
                            </g>
                        </g>
                    </g>
                </g>
            </g>
        </g>
    </g>
</svg>