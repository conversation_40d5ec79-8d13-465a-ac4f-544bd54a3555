<?xml version="1.0" encoding="UTF-8"?>
<svg width="105px" height="32px" viewBox="0 0 105 32" version="1.1" xmlns="http://www.w3.org/2000/svg"
    xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>金牌医生</title>
    <defs>
        <linearGradient x1="0%" y1="50%" x2="100%" y2="50%" id="linearGradient-1">
            <stop stop-color="#E5C798" offset="0%"></stop>
            <stop stop-color="#BC916E" offset="100%"></stop>
        </linearGradient>
        <rect id="path-2" x="0" y="0" width="105" height="32" rx="16"></rect>
        <filter x="-1.4%" y="-4.7%" width="102.9%" height="109.4%" filterUnits="objectBoundingBox" id="filter-4">
            <feGaussianBlur stdDeviation="0.5" in="SourceAlpha" result="shadowBlurInner1"></feGaussianBlur>
            <feOffset dx="0" dy="-2" in="shadowBlurInner1" result="shadowOffsetInner1"></feOffset>
            <feComposite in="shadowOffsetInner1" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1"
                result="shadowInnerInner1"></feComposite>
            <feColorMatrix values="0 0 0 0 1   0 0 0 0 1   0 0 0 0 1  0 0 0 0.149612107 0" type="matrix"
                in="shadowInnerInner1"></feColorMatrix>
        </filter>
        <linearGradient x1="74.3318257%" y1="59.6931673%" x2="100%" y2="21.0898931%" id="linearGradient-5">
            <stop stop-color="#FFFFFF" stop-opacity="0" offset="0%"></stop>
            <stop stop-color="#FFFFFF" stop-opacity="0.739920236" offset="100%"></stop>
        </linearGradient>
        <path
            d="M31.19092,24.1717895 L31.3160379,22.4336842 L23.9960379,22.4336842 L24.3364747,17.7044211 L30.5164747,17.7044211 L30.6415925,15.9663158 L24.4615925,15.9663158 L24.6128978,13.8644211 L28.9128978,13.8644211 L29.0001893,12.6517895 C30.0072125,13.1098947 31.1309637,13.5208421 32.3714429,13.8846316 L32.4892864,12.2475789 C30.6531775,11.4526316 29.2118568,10.6374737 28.1653243,9.80210526 C27.0303079,8.89936842 26.0337198,7.74063158 25.1755599,6.32589474 L22.4755599,6.32589474 C21.4137198,7.74063158 20.2503079,8.89936842 18.9853243,9.80210526 C17.8185235,10.6374737 16.2598442,11.4526316 14.3092864,12.2475789 L14.1914429,13.8846316 C15.5538734,13.4804211 16.7572738,13.0627368 17.8016441,12.6315789 L17.7128978,13.8644211 L21.9728978,13.8644211 L21.8215925,15.9663158 L15.6615925,15.9663158 L15.5364747,17.7044211 L21.6964747,17.7044211 L21.3560379,22.4336842 L14.0760379,22.4336842 L13.95092,24.1717895 L31.19092,24.1717895 Z M18.8565607,12.1465263 C20.4861486,11.3650526 22.0794991,10.2534737 23.6366123,8.81178947 C24.960469,10.24 26.3937851,11.3515789 27.9365607,12.1465263 L18.8565607,12.1465263 Z M28.0513225,21.6656842 C28.630732,20.6551579 29.0145062,19.584 29.2026449,18.4522105 L26.9226449,18.4522105 C26.7211729,19.584 26.3773987,20.6551579 25.8913225,21.6656842 L28.0513225,21.6656842 Z M19.5513225,21.6656842 C19.1889149,20.5877895 18.9993557,19.5166316 18.9826449,18.4522105 L16.7026449,18.4522105 C16.7278395,19.584 16.9573987,20.6551579 17.3913225,21.6656842 L19.5513225,21.6656842 Z M47.8872743,24.7781053 L48.1535134,21.0795789 L51.9335134,21.0795789 L52.0513569,19.4425263 L48.2713569,19.4425263 L48.434301,17.1789474 L46.174301,17.1789474 L46.0113569,19.4425263 L42.1513569,19.4425263 C44.2611901,18.4724211 45.7043566,17.5023158 46.4808565,16.5322105 L48.9608565,16.5322105 C49.8541898,16.5322105 50.5015868,16.3368421 50.9030474,15.9461053 C51.3188112,15.5418947 51.5616097,14.8547368 51.6314429,13.8846316 L52.062081,7.90231579 L47.302081,7.90231579 C47.4739652,7.552 47.6407602,7.08715789 47.8024662,6.50778947 L45.4024662,6.50778947 C45.2570033,7.04673684 45.0968749,7.51157895 44.922081,7.90231579 L42.202081,7.90231579 L41.5808565,16.5322105 L44.1608565,16.5322105 C43.6153936,17.0711579 42.8361124,17.6168421 41.8230129,18.1692632 L41.7313569,19.4425263 L40.5913569,19.4425263 L40.4735134,21.0795789 L45.8935134,21.0795789 L45.6272743,24.7781053 L47.8872743,24.7781053 Z M33.32,24.8791579 C34.502074,24.0168421 35.3285125,23.0938947 35.7993156,22.1103158 C36.300665,21.0728421 36.6245675,19.5368421 36.7710233,17.5023158 L36.8233982,16.7747368 L38.2833982,16.7747368 L37.720368,24.5962105 L39.640368,24.5962105 L40.3154223,15.2185263 L36.9354223,15.2185263 L37.0925469,13.0357895 L41.1525469,13.0357895 L41.2674808,11.4391579 L40.4474808,11.4391579 L40.7850079,6.75031579 L38.8650079,6.75031579 L38.5274808,11.4391579 L37.2074808,11.4391579 L37.5100913,7.23536842 L35.5100913,7.23536842 L34.7244678,18.1490526 C34.6342666,19.4021053 34.4891603,20.3991579 34.2891488,21.1402105 C34.1073202,21.8138947 33.8269464,22.4673684 33.4480275,23.1006316 L33.32,24.8791579 Z M45.8333002,11.3583158 L43.8533002,11.3583158 L43.988602,9.47873684 L45.968602,9.47873684 L45.8333002,11.3583158 Z M49.8933002,11.3583158 L47.9133002,11.3583158 L48.048602,9.47873684 L50.028602,9.47873684 L49.8933002,11.3583158 Z M47.3543354,14.9557895 C47.5541015,14.4033684 47.697385,13.7094737 47.7841858,12.8741053 L49.7841858,12.8741053 L49.7201721,13.7633684 C49.6881652,14.208 49.5791907,14.5178947 49.3932486,14.6930526 C49.2073065,14.8682105 48.9076687,14.9557895 48.4943354,14.9557895 L47.3543354,14.9557895 Z M43.5943354,14.9557895 L43.7441858,12.8741053 L45.7241858,12.8741053 C45.6383549,13.696 45.4750714,14.3898947 45.2343354,14.9557895 L43.5943354,14.9557895 Z M71.45092,24.1717895 L71.5687636,22.5347368 L58.5487636,22.5347368 C58.0420969,22.5347368 57.6898561,22.4269474 57.4920413,22.2113684 C57.2942264,21.9957895 57.2132623,21.6387368 57.2491488,21.1402105 L58.1337026,8.85221053 L72.3337026,8.85221053 L72.4515461,7.21515789 L55.7515461,7.21515789 L54.7404196,21.2614737 C54.664767,22.3124211 54.8309372,23.0602105 55.2389304,23.5048421 C55.6469235,23.9494737 56.37092,24.1717895 57.41092,24.1717895 L71.45092,24.1717895 Z M71.2746174,20.2307368 C70.0770479,19.8265263 69.143358,19.3684211 68.4735478,18.8564211 C67.7647074,18.3309474 67.1699307,17.6101053 66.6892176,16.6938947 L71.4092176,16.6938947 L71.5226965,15.1174737 L66.4426965,15.1174737 C66.4686387,14.9423158 66.4951884,14.6661053 66.5223457,14.2888421 L66.6460087,12.5709474 L70.9060087,12.5709474 L71.0194876,10.9945263 L62.8594876,10.9945263 C63.0151288,10.6846316 63.2024088,10.2130526 63.4213277,9.57978947 L61.1813277,9.57978947 C60.9611936,10.4151579 60.6598499,11.0821053 60.2772967,11.5806316 C59.9080769,12.0791579 59.4051615,12.4901053 58.7685504,12.8134737 L58.679804,14.0463158 C60.0201721,13.7633684 61.0489069,13.2715789 61.7660087,12.5709474 L64.2660087,12.5709474 L64.1350714,14.3898947 C64.1108238,14.7267368 64.0866988,14.9692632 64.0626965,15.1174737 L58.5026965,15.1174737 L58.3892176,16.6938947 L63.6292176,16.6938947 C63.2776899,17.5023158 62.6882248,18.1894737 61.8608221,18.7553684 C61.0620258,19.2943158 59.9189877,19.7995789 58.4317077,20.2711579 L58.3298676,21.6858947 C61.8098791,20.9448421 64.1032296,19.8332632 65.2099192,18.3511579 C65.8164517,19.1865263 66.5751081,19.8534737 67.4858883,20.352 C68.3452749,20.8235789 69.573268,21.2682105 71.1698676,21.6858947 L71.2746174,20.2307368 Z M75.22,24.8791579 C76.9564999,23.9090526 78.184031,22.8783158 78.9025933,21.7869474 C79.6201858,20.7090526 80.0316022,19.2538947 80.1368427,17.4214737 L80.9195565,6.54821053 L78.7795565,6.54821053 L77.953197,18.0277895 C77.8726948,19.1461053 77.6529229,20.0690526 77.2938814,20.7966316 C76.9195667,21.5511579 76.268039,22.3595789 75.3392984,23.2218947 L75.22,24.8791579 Z M87.12,24.8791579 L87.9798212,12.9347368 L89.7998212,12.9347368 L89.3138986,19.6850526 C89.2925607,19.9814737 89.2170422,20.1970526 89.0873431,20.3317895 C88.9576441,20.4665263 88.7527945,20.5338947 88.4727945,20.5338947 L88.0727945,20.5338947 L87.9593156,22.1103158 L88.9193156,22.1103158 C89.7459823,22.1103158 90.3457426,21.9284211 90.7185968,21.5646316 C91.0914509,21.2008421 91.30843,20.5945263 91.369534,19.7456842 L91.9776648,11.2976842 L88.0976648,11.2976842 L88.2635186,8.99368421 L92.8035186,8.99368421 L92.9213621,7.35663158 L81.7413621,7.35663158 L81.6235186,8.99368421 L86.0235186,8.99368421 L85.8576648,11.2976842 L81.9576648,11.2976842 L81.1734962,22.1911579 L83.2334962,22.1911579 L83.8998212,12.9347368 L85.7398212,12.9347368 L84.88,24.8791579 L87.12,24.8791579 Z M76.4022597,19.8467368 L77.2460775,8.12463158 L75.2460775,8.12463158 L74.4022597,19.8467368 L76.4022597,19.8467368 Z"
            id="path-6"></path>
        <filter x="-0.6%" y="-2.7%" width="101.3%" height="110.8%" filterUnits="objectBoundingBox" id="filter-7">
            <feOffset dx="0" dy="1" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feColorMatrix values="0 0 0 0 0.702473958   0 0 0 0 0.510311952   0 0 0 0 0.34560166  0 0 0 1 0"
                type="matrix" in="shadowOffsetOuter1"></feColorMatrix>
        </filter>
    </defs>
    <g id="页面-2" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="绑定医管家" transform="translate(-429.000000, -574.000000)">
            <g id="编组" transform="translate(0.000000, 319.000000)">
                <g id="编组-20" transform="translate(30.000000, 219.000000)">
                    <g id="编组-5">
                        <g id="编组-4" transform="translate(149.000000, 30.000000)">
                            <g id="编组-17">
                                <g id="金牌医师" transform="translate(250.000000, 6.000000)">
                                    <mask id="mask-3" fill="white">
                                        <use xlink:href="#path-2"></use>
                                    </mask>
                                    <g id="矩形">
                                        <use fill="url(#linearGradient-1)" fill-rule="evenodd" xlink:href="#path-2">
                                        </use>
                                        <use fill="black" fill-opacity="1" filter="url(#filter-4)" xlink:href="#path-2">
                                        </use>
                                    </g>
                                    <ellipse id="椭圆形" fill="url(#linearGradient-5)" mask="url(#mask-3)" cx="22.9166667"
                                        cy="-6.73684211" rx="42.0833333" ry="32"></ellipse>
                                    <g fill-rule="nonzero" mask="url(#mask-3)">
                                        <use fill="black" fill-opacity="1" filter="url(#filter-7)" xlink:href="#path-6">
                                        </use>
                                        <use fill="#FFFFFF" xlink:href="#path-6"></use>
                                    </g>
                                </g>
                            </g>
                        </g>
                    </g>
                </g>
            </g>
        </g>
    </g>
</svg>