import React from 'react';
import { shallowEqual, useSelector } from 'react-redux';
import { ApplicationState } from 'src/store';
import { Popup, Icon } from 'zarm';
import Avatar from '../avatar';
import SvgIcon from '../svg';
import './team.scss';

interface TeamProps {
  direction?: 'bottom' | 'top';
  visible: boolean;
  afterClose: () => void;
  data?: object;
}

const featureList = [
  {
    title: '医术精湛',
    desc: '医生源自知名三甲医院，具备多年临床诊治经验',
  },
  {
    title: '经验丰富',
    desc: '根据需求开具专属服务处方，制定就医路径',
  },
  {
    title: '热情亲切',
    desc: '服务周到，患者满意度高',
  },
];
const prefixCls: string = 'team-components';
const TeamFeature = (props: TeamProps) => {
  let { direction = 'bottom', afterClose, visible } = props;
  let close = () => afterClose && afterClose();

  const { staffName = '', headPortrait = 'https://cdn-qcloud.zhongan.com/a00000/za-asclepius/applets/default_avatar.png' } = useSelector((state: ApplicationState) => {
    return state.chat.doctorInfo
  }, shallowEqual);

  return (
    <Popup visible={visible} direction={direction} afterClose={() => close()}>
      <div className={`${prefixCls} ${prefixCls}__${direction}`}>
        <div className={`${prefixCls}__scroll__${direction}`}>
          {direction == 'bottom' && (
            <header className={`${prefixCls}__header`}>
              <SvgIcon type="img" className="svg_title" src={require('src/svgs/modal_title.svg')} />
              {/* <h3 className="title">已为您绑定专属家庭医生</h3> */}
              <p>根据您所拥有的医疗服务，从20+个医生团队中匹配而出</p>
            </header>
          )}
          {direction == 'bottom' && (
            <div className={`${prefixCls}__close`} onClick={() => close()} >
              <SvgIcon type="img" className="svg_close" src={require('src/svgs/modal_close.svg')} />
            </div>
          )}
          <div className={`${prefixCls}__body`}>
            <section className={`${prefixCls}__core`}>
              <div className="team-intro">
                <Avatar
                  prefixCls="doctor-avatar"
                  preUrl={headPortrait}
                />
                <div className="team-info">
                  <div className="team-info__hd">
                    {staffName}医生团队
                    <SvgIcon
                      className="icon"
                      type="img"
                      width={52.5}
                      height={16}
                      src={require(`./images/icon.svg`)}
                    />
                  </div>
                  <p className="team-info__aptitude">领衔专家 | 全科金牌医师 | 从医平均5年以上</p>
                  <p className="team-info__aptitude">
                    <Icon type="time" theme="primary" size="lg" />
                    服务时间 06:00-22:00
                  </p>
                </div>
              </div>
              <div className="team-mean">
                <div className="flex-row">
                  <p className="label">
                    <strong>3</strong>秒
                  </p>
                  <p className="value">平均响应时间</p>
                </div>
                <div className="flex-row praise">
                  <p className="label">
                    <strong>99</strong>%
                  </p>
                  <p className="value">平均好评率</p>
                </div>
              </div>
            </section>
          </div>
          <section className={`${prefixCls}__feature`}>
            <h4 className="title">
              {' '}
              <i></i>团队特色 <i></i>
            </h4>
            <ul className={`${prefixCls}__feature-list ${direction}`}>
              {featureList.map((item, index) => (
                <li className="feature-row" key={`ine${index}`}>
                  <span className="num">0{index + 1}</span>
                  <span className="num shadow"></span>
                  {
                    direction == 'bottom' && <SvgIcon type="img" className="svg_num" src={require(`src/svgs/team_feature_0${index + 1}.svg`)} />
                  }
                  <h6>{item.title}</h6>
                  <p>{item.desc}</p>
                </li>
              ))}
            </ul>
          </section>
        </div>
        {direction !== 'bottom' &&
          <p className={`${prefixCls}__arrow`} onClick={close}>
            <Icon type="arrow-top" />
          </p>
        }
      </div>
    </Popup>
  );
};

export default TeamFeature;
