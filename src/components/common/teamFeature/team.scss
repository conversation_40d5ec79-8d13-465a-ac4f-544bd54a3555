@import "src/style/index";
$prefixCls: 'team-components';

.#{$prefixCls} {
  background: #fcfaf8;

  &__scroll__top {
    max-height: 70vh;
    overflow: hidden;
    overflow-y: auto;
  }

  &__header {
    // padding: r(13) 0 r(16);
    // text-align: center;
    // font-size: r(13);
    // line-height: r(22);
    // color: #909090;
    // @include borderBottom($color: #E6E6E6);

    // h3 {
    //   font-weight: 600;
    //   color: #000;
    // }
    position: relative;
    width: 100%;
    height: r(200);
    padding-top: r(38);
    background: linear-gradient(129deg, rgba(237, 209, 168, 0.79) 0%, rgba(230, 196, 150, 0.79) 100%);
    box-shadow: 0 2px 1px 0 #fff;
    border-radius: 0 0 r(16) r(16);
    margin-bottom: r(-100);
    z-index: 0;
    font-size: r(13);
    color: #66472d;
    text-align: center;

    .title {
      color: #66472d;
      font-size: r(23);
      font-weight: bold;
      margin-bottom: r(6);
    }

    .svg_title {
      width: r(253);
      height: r(30);
      margin-bottom: r(8);
    }
  }

  &__close {
    position: absolute;
    right: r(16);
    top: r(16);

    .svg_close {
      width: r(18);
      height: r(18);
    }
  }

  &__core {
    position: relative;
    z-index: 1;
    margin: r(17) r(15) 0;
    background: linear-gradient(135deg, #454e68 0%, #41465b 100%);
    // @include images($url:'');
    background-image: url(./images/team-bg.png);
    background-repeat: no-repeat;
    background-size: 100% auto;
    border-radius: r(8);
    padding: r(5);

    .team-intro {
      padding: r(15) r(11) r(7);
      @include display-flex;
    }

    .doctor-avatar {
      width: r(46);
      height: r(46);
      border: 3px solid rgba(244, 225, 207, .14);
    }

    .team-info {
      margin-left: r(13);

      &__hd {
        font-size: r(17);
        color: #fff;
        line-height: r(24);
        margin-bottom: r(5);
        font-weight: bold;
        @include display-flex;
        @include align-items(center);

        .icon {
          margin-left: 5px;
        }
      }

      &__aptitude {
        font-size: r(12);
        line-height: r(21);
        color: rgba(255, 255, 255, 0.6);
        @include display-flex;
        @include align-items(center);

        .za-icon {
          margin: -1px r(2) 0 0;
          color: rgba(204, 169, 135, 1);
          font-size: 14px;
        }
      }
    }

    .team-mean {
      min-height: r(67.5);
      background-image: url(./images/trait-bg.png);
      background-repeat: no-repeat;
      background-size: 100% auto;
      padding: r(13.5) r(2);
      @include display-flex;

      .flex-row {
        padding-left: r(65);
        color: rgba(210, 156, 95, 1);
        font-size: r(12);
        background-image: url(./images/icon-lightning.png);
        background-repeat: no-repeat;
        background-size: r(34);
        background-position: r(18) 50%;
        @include flex;

        & + .flex-row {
          position: relative;

          &:after {
            position: absolute;
            left: 0;
            top: 4px;
            bottom: 4px;
            content: "";
            width: 1px;
            background: rgba(86, 44, 22, .17);
          }
        }

        &.praise {
          background-image: url(./images/icon-praise.png);
        }

        .label {
          line-height: r(22);
          color: rgba(76, 46, 22, 1);

          strong {
            font-size: r(20);
          }
        }
      }
    }
  }

  &__feature {
    overflow: hidden;

    .title {
      padding: r(16) 0;
      text-align: center;
      font-size: r(17);
      font-weight: bold;
      color: rgba(170, 106, 0, 1);
      @include display-flex;
      @include align-items(center);
      @include justify-content(center);

      i {
        display: inline-block;
        height: r(9);
        border-color: rgba(170, 106, 0, .38);
        border-width: 1px 0;
        border-style: solid;
        width: r(55);
        position: relative;
        transform: scaleY(0.5);

        &::after {
          content: "";
          position: absolute;
          top: 50%;
          right: r(-8);
          height: 1px;
          width: r(55);
          background-color: rgba(170, 106, 0, .38);
        }
      }

      i:first-child {
        margin-right: r(23);
      }

      i:last-child {
        margin-left: r(23);
      }
    }

    &-list {
      padding: 0 r(25) r(30);

      .feature-row {
        // background: linear-gradient(90deg, #534a42 0%, #3c3530 100%) #fff;
        background: #fff;
        box-shadow: 0 r(1.5) r(6) 0 rgba(0, 0, 0, 0.1);
        border-radius: r(8);
        padding: r(8.5) r(8) r(8.5) r(39);
        position: relative;
        margin-bottom: r(10);
        line-height: r(22);
        color: #909090;
        font-size: r(13);

        .num {
          // width: 36px;
          height: 22px;
          position: absolute;
          left: -9.5px;
          top: 7px;
          background: linear-gradient(135deg, #f0c99a 0%, #d3a26a 100%);
          border-radius: 14px 14px 0;
          color: #fff;
          font-size: 16px;
          text-align: center;
          font-weight: bold;
          z-index: 1;

          &.shadow {
            background: linear-gradient(227deg, #dcb36f 0%, #bf8734 100%);
            opacity: 0.41;
            left: -5px;
            z-index: 0;
          }
        }

        h6 {
          line-height: r(24);
          font-size: r(17);
          font-weight: 600;
          color: #000;
          margin-bottom: r(3);
        }
      }

      &.bottom {
        padding: r(4) r(10) r(20);
        @include display-flex();
        @include justify-content(space-around);

        .feature-row {
          width: r(109);
          height: r(128);
          padding: r(0) r(10) 0;
          font-size: r(12);

          .num {
            display: none;
          }

          .svg_num {
            width: auto;
            height: r(30);
          }

          h6 {
            font-size: r(15);
            margin: r(-4) 0 r(2);
          }
        }
      }
    }
  }

  &__footer {
    background: #fff;
    box-shadow: 0 -15px 18.5px 0 rgba(0, 0, 0, 0.02);
    border-radius: 10px 10px 0 0;
    padding: r(11) r(30);
  }

  &__arrow {
    padding: r(10) 0;
    text-align: center;
    font-size: r(15);
    color: rgba(188, 188, 188, 1);
  }

  &__top {
    // background: linear-gradient(0deg, #fff 0%, #edcda8 49%, #e6c496 100%);

    background: linear-gradient(360deg, #fff 0%, #f8f3ef 100%);
    border-radius: 0 0 16px 16px;

    .#{$prefixCls}__body {
      padding-top: r(35);
      background-image: url(./images/v-bg.png);
      background-repeat: no-repeat;
      background-size: 100% auto;
      background-position: 0 0;
    }
  }

  &__bottom {
    overflow: hidden;
    // background: linear-gradient(0deg, #fff 0%, #edcda8 49%, #e6c496 100%);
    border-radius: r(16) r(16) 0 0;
  }
}
