import React from 'react';
import ReactDOM from 'react-dom';
import { Popup, ActivityIndicator } from 'zarm';

const prefixCls = 'za-loading';
const Loading: any = (props) => {
  return (
    <Popup
      direction="center"
      maskType="transparent"
      width="70%"
      visible={props.visible}
    >
      <div className={prefixCls}>
        <div className={`${prefixCls}__container`}>{<ActivityIndicator type="spinner" size="lg" />}</div>
      </div>
    </Popup>
  );
}

Loading.show = () => {
  if (!Loading.zarmLoading) {
    Loading.zarmLoading = document.createElement('div');
  }
  if (Loading.zarmLoading) {
    ReactDOM.render(
      <Loading visible={true}/>,
      Loading.zarmLoading,
    );
  }
};

Loading.hide = () => {
  if (Loading.zarmLoading) {
    ReactDOM.render(
      <Loading visible={false} />,
      Loading.zarmLoading,
    );
  }
};

export default Loading;
