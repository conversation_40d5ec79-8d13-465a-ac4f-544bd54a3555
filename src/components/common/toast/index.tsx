
import React from 'react';
import { Toast } from 'zarm';

const StaticToast = {
  default: (children?: any, stayTime?: number, mask?: boolean, afterClose?: any) => {
    Toast.show(stayTime ? {
      content: children,
      stayTime,
      mask,
      afterClose,
    } : children);
  },
  success: (text?: string, stayTime?: number, mask?: boolean) => {
    StaticToast.default(<div className='custom'>
      {/* <Icon className="custom-icon" type="right-round-fill" /> */}
      <div className='custom-text'>
        {text}
      </div>
    </div>, stayTime || 2000, mask);
  },
  warning: (text?: string, stayTime?: number, mask?: boolean) => {
    StaticToast.default(<div className='custom'>
      {/* <Icon className="custom-icon" type="warning-round-fill" /> */}
      <div className='custom-text'>
        {text}
      </div>
    </div>, stayTime || 2000, mask);
  },
  error: (text?: string, stayTime?: number, mask?: boolean) => {
    StaticToast.default(<div className='custom'>
      {/* <Icon className="custom-icon" type="wrong-round-fill" /> */}
      <div className='custom-text'>
        {text}
      </div>
    </div>, stayTime || 2000, mask);
  },
};

export default StaticToast;
