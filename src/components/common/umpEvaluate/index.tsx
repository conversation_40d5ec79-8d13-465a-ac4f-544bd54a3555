import React from 'react';
import { SvgIcon } from 'src/components/common';
import { Input, Button } from 'zarm';

import './umpevaluate.scss';

export const scoreList = [
  {
    title: '不满意',
    key: 'unsatisfied',
    value: 1,
    icon: require('src/images/icon_unsatisfied.png'),
    iconActive: require('src/images/icon_unsatisfied_active.png'),
  },
  {
    title: '一般',
    key: 'soso',
    value: 2,
    icon: require('src/images/icon_soso.png'),
    iconActive: require('src/images/icon_soso_active.png'),
  },
  {
    title: '还不错',
    key: 'satisfied',
    value: 3,
    icon: require('src/images/icon_notbad.png'),
    iconActive: require('src/images/icon_notbad_active.png'),
  },
  {
    title: '很满意',
    key: 'great',
    value: 4,
    icon: require('src/images/icon_satisfied.png'),
    iconActive: require('src/images/icon_satisfied_active.png'),
  },
  {
    title: '非常满意',
    key: 'excellent',
    value: 5,
    icon: require('src/images/icon_excellent.png'),
    iconActive: require('src/images/icon_excellent_active.png'),
  },
];

const umpEvaluateComp = (props) => {
  const { totalScore, totalScoreClick, tags, evaluateTags, tagsClick, evaluateDesc, setEvaluateDesc, submitFlag, submit, isFromCheck } = props;

  // 键盘收缩页面回弹
  const windowScrollBottom = () => {
    window.scroll({
      top: 0,
      behavior: 'smooth',
    });
  };

  if (isFromCheck) {
    return (
      <div className='ump_evaluate_comp'>
        <p className='evaluate_title'>您对本次问诊医生满意吗？</p>
        <div className='evaluate_option check'>
          {scoreList.map(({ title, key, icon, iconActive, value }) => {
            if (totalScore == value) {
              return (
                <div className='option' key={key} onClick={() => totalScoreClick(value)}>
                  <p>{title}</p>
                  <SvgIcon className='icon' type='img' src={value == totalScore ? iconActive : icon} />
                </div>
              );
            }
            return null;
          })}
        </div>
        <div className='evaluate_content_wrap'>
          {evaluateTags && (
            <div className='tag_wrap check'>
              {((evaluateTags || '').split(',') || []).map((item, index) => (
                item && (
                  <p className={'tag active'} key={`tag${index}`}>
                    {item}
                  </p>
                )
              ))}
            </div>
          )}
          {evaluateDesc && <div className='evaluate_desc'>{evaluateDesc}</div>}
        </div>
      </div>
    );
  }

  return (
    <div className='ump_evaluate_comp'>
      <p className='evaluate_title'>您对本次问诊医生满意吗？</p>
      <div className='evaluate_option'>
        {scoreList.map(({ title, key, icon, iconActive, value }) => (
          <div className='option' key={key} onClick={() => totalScoreClick(value)}>
            <p>{title}</p>
            <SvgIcon className='icon' type='img' src={value === totalScore ? iconActive : icon} />
          </div>
        ))}
      </div>
      {
        <div className='evaluate_content_wrap'>
          <div className='tag_wrap'>
            {(tags || []).map((item, index) => (
              <p className={`tag ${evaluateTags.includes(item.id) ? 'active' : ''}`} key={`tag${index}`} onClick={() => tagsClick(item.id)}>
                {item.name || item.tagName}
              </p>
            ))}
          </div>
          <div className='evaluate_input_wrap'>
            <Input className='evaluate_input' type='text' rows={3} maxLength={200} placeholder='您的建议就是我们努力的方向' onChange={(value) => setEvaluateDesc(value.trim())} value={evaluateDesc} disabled={submitFlag} onBlur={windowScrollBottom} />
            <div className='evaluate_input_length'>
              <span className='evaluate_length'>{evaluateDesc.length}</span>/200
            </div>
          </div>
          <Button block className='evaluate_btn' theme='primary' disabled={!totalScore || submitFlag} onClick={submit}>
            提交
          </Button>
        </div>
      }
    </div>
  );
};

export default umpEvaluateComp;
