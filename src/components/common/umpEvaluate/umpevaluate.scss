@import "src/style/index";

.ump_evaluate_comp {
  box-sizing: border-box;
  margin: r(15);
  padding: r(16) r(15) r(20);
  background: #fff;
  padding-bottom: r(18);
  box-shadow: 0 r(9) r(16) 0 rgba(0, 0, 0, 0.04);
  border-radius: r(8);

  .evaluate_title {
    font-size: r(16);
    font-family: PingFangSC-Semibold, PingFang SC;
    font-weight: 600;
    color: #1e1e1e;
  }

  .evaluate_option {
    margin: r(18) r(-5) r(5) 0;
    @include display-flex;
    @include justify-content(space-between);

    &.check {
      margin-right: 0;
      @include justify-content(center);
    }

    .option {
      font-size: r(12);
      text-align: center;

      .active {
        color: #ec9131;
      }
    }

    .icon {
      margin-top: r(5);
      width: r(34);
      height: r(34);
    }
  }

  .tag_wrap {
    margin: r(25) r(-10) r(15) 0;

    &.check {
      text-align: center;
    }

    .tag {
      display: inline-block;
      padding: 0 r(13);
      height: r(26);
      margin: 0 r(10) r(10) 0;
      line-height: r(26);
      background: rgba(0, 0, 0, 0.04);
      border-radius: r(13);
      border: r(1) solid rgba(0, 0, 0, 0.1);
      font-size: r(12);
      color: #333;
      text-align: center;

      &.active {
        color: #ec9131;
        background: rgba(236, 145, 49, 0.06);
        border: 1px solid rgba(236, 145, 49, 0.32);
      }
    }
  }

  .evaluate_desc {
    margin-top: r(10);
    font-size: r(13);
    color: #666;
    padding: r(15);
    word-break: break-all;
    background: #f8f8f8;
    border-radius: r(8);
  }

  .evaluate_content_wrap {
    margin-top: r(24);
  }

  .evaluate_input {
    margin: r(-5) auto r(15);
    border-radius: r(8);
    padding: r(15) r(15) r(28);
    font-size: r(13);
    background: rgba(0, 0, 0, 0.02);
    line-height: 1.6;

    // .za-input {
    //   @include placeholder;
    // }
  }

  .evaluate_input_wrap {
    position: relative;

    .evaluate_input_length {
      position: absolute;
      bottom: r(13);
      right: r(15);
      font-size: r(13);
      color: rgba(0, 0, 0, 0.3);
    }

    .evaluate_length {
      color: rgba(0, 0, 0, 0.8);
    }
  }

  .evaluate_btn {
    height: r(44);
    border-radius: r(22);
    color: #fff;
    font-weight: bold;
    border: none;

    &.za-button--disabled {
      background: rgba(0, 0, 0, 0.3);
      opacity: 1;
    }

    &.submited {
      background: var(--button-submit-color);
    }
  }
}
