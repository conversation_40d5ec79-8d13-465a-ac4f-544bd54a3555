import React from 'react';
import SvgIcon from '../svg';
import './uniqueSign.scss';

interface StarProps {
  className?: string;
}

const UniqueSign = (props: StarProps) => {
  let { className = '' } = props;
  const prefixCls = 'inquiry-uniquesign';
  return (
    <span className={`${prefixCls} ${className}`}><SvgIcon width="18" height="18" src={require('src/svgs/sprite-unique-icon.svg')} />门诊险权益</span>
  );
}

export default UniqueSign;