import { useState, useLayoutEffect } from 'react';
import { fetchJson } from 'src/utils';
// import { useSelector } from 'react-redux';
// import { ApplicationState } from 'src/store';

export default function useChatAtWork() {
  /** 咨询医生当前是否处于工作时间段 */
  const [chatAtWork, setChatAtWork] = useState(false);

  useLayoutEffect(() => {
    const isHasDoctor = async () => {
      const res = await fetchJson({
        type: 'POST',
        url: '/api/api/v1/patient/doctor/assignInquiryDoctor',
        isloading: false,
        data: {
          option: {
            isQueryPriority: true,
            isExcludeButler: true,
            isQueryMedicalStaffService: true,
          },
        },
      });
      const { code = '' } = res;
      if (code === '0') {
        const { result: { id = '' } = {} } = res;
        console.log('这里设置了是否有医生在线' + !!id);
        setChatAtWork(!!id)
      }
    }

    isHasDoctor();
  }, []);

  return chatAtWork;
}