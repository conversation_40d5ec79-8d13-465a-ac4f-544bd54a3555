import { fetchJson } from 'src/utils/fetch';
import cookies from 'src/utils/cookie';
import { dmEnv } from '@dm/utils';
import { useState, useCallback } from 'react';

/*
async function useImmediateChatAtWorks(options = {}, useCalculate = false) {
  let result:any = {
    chatAtWorkStatus: false,
    preferenceCalculateInfo: {
      order: {},
    },
  };
  try {
    //** 用户token
    const ZA_TOKEN = cookies.get('za_token') || '';
    const { pathname = '', search = '' } = location;

    if (!ZA_TOKEN && !dmEnv.isApp()) {
      window.reactHistory.push({
        pathname: '/hospital/login',
        search: `pathname=${encodeURIComponent(pathname)}&jumpSearch=${encodeURIComponent(search)}`
      })
      return Promise.reject();
    }
    // 查询用户是否在线，及患者试算*
    const fetchOption = {
      type: "POST",
      url: `/api/api/v1/patient/${useCalculate ? 'preference/calculate' : 'doctor/list'}`,
      needLoading: false,
      data: useCalculate
        ? {
            accountId: cookies.get(`accountId`),
            orderType: 'inquiry',
            ...options,
          }
        : {
            ...options,
            isInquiryNot: 'N',
            staffAttributeList: [1, 2],
          },
    }

    const res = await fetchJson(fetchOption);

    if (res && res.code === '0') {

      const { result: { id = '' } = {}, result: list = [] } = res;

      result.chatAtWorkStatus = useCalculate ? !!id : !!list.length;
      result.preferenceCalculateInfo = res.result || {};
      return Promise.resolve(result);
    }
    return Promise.reject();
  } catch (error) {
    return Promise.reject(error);
  }
}*/

function useImmediateChatAtWorks() {
  let [chatAtWorkData, setChatAtWorkData] = useState<any>({
    chatAtWorkStatus: false,
    doctorList: [],
    preferenceCalculateInfo: {},
  });

  const getDoctorAtWork = useCallback(
    async (options = {}) => {
      let result = chatAtWorkData;
      const ZA_TOKEN = cookies.get('za_token') || '';
      const { pathname = '', search = '' } = location;
      try {
        if (!ZA_TOKEN && !dmEnv.isApp()) {
          window.reactHistory.push({
            pathname: '/hospital/login',
            search: `pathname=${encodeURIComponent(pathname)}&jumpSearch=${encodeURIComponent(search)}`,
          });
          return Promise.reject();
        }
        const res = await fetchJson({
          needLoading: false,
          type: 'POST',
          url: `/api/api/v1/patient/doctor/list`,
          data: {
            ...options,
            isInquiryNot: 'N',
            staffAttributeList: [1, 2],
          },
        });
        if (res.code === '0') {
          const { result: list = [] } = res;
          result = {
            chatAtWorkStatus: !!list.length,
            doctorList: list,
          };
          setChatAtWorkData(result);
        }
        return Promise.resolve(result);
      } catch (error) {
        return Promise.resolve(result);
      }
    },
    [chatAtWorkData],
  );
  const getUnifiedCalculate = useCallback(
    async (options) => {
      let resultData = chatAtWorkData;
      const ZA_TOKEN = cookies.get('za_token') || '';
      const { pathname = '', search = '' } = location;
      try {
        if (!ZA_TOKEN && !dmEnv.isApp()) {
          window.reactHistory.push({
            pathname: '/hospital/login',
            search: `pathname=${encodeURIComponent(pathname)}&jumpSearch=${encodeURIComponent(search)}`,
          });
          return Promise.reject();
        }
        const res = await fetchJson({
          needLoading: false,
          type: 'POST',
          url: `/api/api/v1/patient/preference/calculate`,
          data: {
            accountId: cookies.get(`accountId`),
            orderType: 'inquiry',
            inquiryType: 'I',
            returnValidPreference: true,
            ...options,
          },
        });
        if (res.code === '0') {
          const { result = {} } = res;
          resultData = {
            chatAtWorkStatus: true,
            preferenceCalculateInfo: result,
          };
          setChatAtWorkData(resultData);
        }
        return Promise.resolve(resultData);
      } catch (error) {
        return Promise.resolve(resultData);
      }
    },
    [chatAtWorkData],
  );
  return {
    getDoctorAtWork,
    getUnifiedCalculate,
    chatAtWorkData,
  };
  // return fetchChatAtWork();
}

export default useImmediateChatAtWorks;
