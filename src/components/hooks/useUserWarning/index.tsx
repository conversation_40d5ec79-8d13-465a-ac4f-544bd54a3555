import { useState, useEffect } from 'react';
import { fetchJson, setIsBlackUser } from 'src/utils';
import cookies from 'src/utils/cookie';
// import { useSelector } from 'react-redux';
// import { ApplicationState } from 'src/store';

export default function useUserWarning() {
  /**
   *  用户的数据状状态
   *  unfinishedInquiry： 有未结束的问诊单
   *  unfinishedOrder： 有未结束的购药订单
   *  unusedPrescription： 有未结束的处方单
   *  userBlackFlag： 改用户是否是黑名单用户
   */
  const [userWaringStatus, setWaringStatus] = useState({
    unfinishedInquiry: false,
    unfinishedOrder: false,
    unusedPrescription: false,
    userBlackFlag: false,
    couponSum: 0,
    rightsSum: 0,
    unPaidSum: 0,
  });

  useEffect(() => {
    try {
      if (!cookies.get('za_token')) {
        return;
      }
      (async () => {
        const res = await fetchJson({
          url: '/api/api/v1/patient/user/userStatistics',
          type: 'GET',
          isloading: false,
        });
        if(res){
          const { code = '', result: {
            userBlackFlag = false,
            unusedPrescription = false,
            unfinishedInquiry = false,
            unfinishedOrder = false,
            unPaidSum = 0,
          } = {} } = res;
          if (code === '0') {
            setWaringStatus((prev) => ({ ...prev, ...{
              unusedPrescription,
              unfinishedInquiry,
              unfinishedOrder,
              unPaidSum,
            } }));
            setIsBlackUser(userBlackFlag);
          }
        }
      })();
      (async () => {
        const res = await fetchJson({
          url: '/api/api/v1/patient/user/userRightsCount',
          type: 'GET',
          isloading: false,
        });
        if(res){
          const { code = '', result: {
            rightsSum = 0,
            couponSum = 0,
          } = {} } = res;
          if (code === '0') {
            setWaringStatus((prev) => ({ ...prev, ...{
              rightsSum,
              couponSum,
            } }));
          }
        }
      })();
    } catch (error) {

    }
  }, []);

  return userWaringStatus;
}
