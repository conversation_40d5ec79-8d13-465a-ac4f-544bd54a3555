import { dmBridge, dmEnv } from '@dm/utils';
import { useEffect } from 'react';

export const pinnedNavColor = {
  fontColor: '#000',//	字体颜色
  backgroundColor: '#fff',//	背景颜色
  returnButtonColor: '#000',//
};

export const defalutNavPageMap = {
  'search': {
    fontColor: '#fff',//	字体颜色
    backgroundColor: '#0DA86F',//	背景颜色
    returnButtonColor: '#fff',//
  },
  'rank': {
    fontColor: '#000',//	字体颜色
    backgroundColor: '#FFE5CA',//	背景颜色
    returnButtonColor: '#000',//
  }
}

export const shareUrlMap = {
  'search': 'static/hospitalsearch',
  'rank': 'static/hospitalrank'
}

// const za_app_share = (page) => {
//   const channelSource = cookies.get('channelSource');
//   const url = `${location.origin}/hospital/${shareUrlMap[page]}?channelSource=${channelSource}`;
//   console.log('---fenxiang-----');
//   dmBridge
//     .showShareView({
//       shareType: '2,3' as any,
//       dialogTitle: '',
//       dialogDesc: '',
//       dataType: '1',
//       shareUrl: url,
//     })
//     .then((res) => {
//       dmBridge.share({
//         shareType: res.data.shareType,
//         url: url,
//         title: '家庭医管家 - 不限次健康咨询',
//         imageUrl: 'https://cdn-qcloud.zhongan.com/a00000/za-asclepius/index_share.png',
//         miniProgramId: '' as any,
//         miniProgramPath: '' as any,
//         miniProgramType: '' as any,
//         shareMinProgramType: '' as any,
//       });
//     });
// }

// const zaappSharePage = `hospital_zashare`;

export default function (ispinned: boolean, page) {

  useEffect(() => {
    if (!dmEnv.isApp()) {
      return
    }
    // dmBridge.setNavigationBarRightButton({
    //   buttonIcon: `${CDN_PREFIX}icon/icon_share_black.png`,
    //   buttonName: '',
    //   buttonListener: zaappSharePage,
    // }, (res) => {
    //   console.log('-----effect----share', res);
    //   za_app_share(page);
    // });

    dmBridge.changeWebviewControlSpring('0' as any);
    return () => {
      dmBridge.changeWebviewControlSpring('1' as any);
    }
  }, [])

  useEffect(() => {
    if (!dmEnv.isApp()) {
      return
    }
    const defaultNav = defalutNavPageMap[page] || {};
    dmBridge.setNavigationBarColor(ispinned ? pinnedNavColor : defaultNav);

    // if (page == 'search') {
    //   dmBridge.setNavigationBarRightButton({
    //     buttonIcon: `${CDN_PREFIX}icon/icon_share${ispinned ? '_black' : ''}.png`,
    //     buttonName: '',
    //     buttonListener: zaappSharePage,
    //   }, (res) => {
    //     console.log('----ispinnedeffect-----share', res);
    //     za_app_share(page);
    //   });
    // }

    return () => {
    };
  }, [ispinned]);
}
