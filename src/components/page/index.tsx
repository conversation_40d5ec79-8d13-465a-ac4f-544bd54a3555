import React, { useEffect, useState } from 'react';
import { getChannelResourceCode } from 'src/utils';
import bridge from 'src/utils/bridge';
import cookies from 'src/utils/cookie';
import { fetchJson } from 'src/utils/fetch';
import { unionPayProcess } from 'src/utils/unionpayProcess';
import validate from 'src/utils/validate';
// import { DMAuthModal, StaticLoading } from '../common';

// import { dmEnv } from '@dm/utils';
const jumpQueryStr = (pathname, search, jumpSearchKey = 'search') => {
  const channelResourceCode = getChannelResourceCode() ? `&channelResourceCode=${getChannelResourceCode()}` : '';
  return `pathname=${encodeURIComponent(pathname)}&${jumpSearchKey}=${encodeURIComponent(search)}${channelResourceCode}`;
};
const Pages = (props) => {
  const { singleConfig: { title = '', auth = false, realAuth = false } = {} } = props;
  const token: string = cookies.get('za_token') || '';
  const long_token: string = cookies.get('za_long_token') || '';
  const isRealAuth: string = cookies.get('isRealAuth') || 'N';
  const [isThrough, setIsThrough] = useState((!(auth && !token)) || (realAuth && token && isRealAuth === 'Y'));

  useEffect(() => {
    console.log('-------------------这里是Page层  UseEffect-------------------');
    const refreshToken = async () => {
      const res = await fetchJson({
        type: 'GET',
        url: '/api/api/v1/patient/user/refreshToken',
        data: { token: long_token },
        isloading: true,
        headers: { token: long_token },
      });
      if (res && res.code === '0') {
        const { result = '' } = res;
        result && cookies.set('za_token', result, 90);
        result && cookies.set('za_long_token', result, 12 * 60);
        setIsThrough(true);
      }
    };

    const { pathname, search } = window.location;
    const h5Searchs =jumpQueryStr(pathname,search,'jumpSearch');
    // token过期 重新刷新token
    if (auth && !token && long_token) {
      refreshToken();
      return;
    }
    // 没有token H5渠道/小程序渠道去相应的登录页
    if (auth && !token && !long_token) {
      const queryStr = jumpQueryStr(pathname,search);// `pathname=${encodeURIComponent(pathname)}&search=${encodeURIComponent(search)}${channelResourceCode}`;
      console.log('-------------------这里是Page层  UseEffect 去相应的登录页面-------------------');
      if (validate.isAlipayApplet()) {
        props.history.goBack();
        my && my.navigateTo({
          url: `/pages/login/index?${queryStr}`,
        });
      } else if (validate.isFromOwnMiniApplet()) { // 自有小程序内
        wx.miniProgram.redirectTo({
          url: `/pages/login?${queryStr}`,
        });
      } else if (validate.isFromUnionPay()) {
        // props.history.replace({
        //   pathname: '/hospital/unionauth',
        //   search: `pathname=${encodeURIComponent(pathname)}&jumpSearch=${encodeURIComponent(search)}`
        // });
        unionPayProcess(pathname, search, () => setIsThrough(true));
      } else {
        props.history.replace({
          pathname: '/hospital/login',
          search: h5Searchs,// `pathname=${encodeURIComponent(pathname)}&jumpSearch=${encodeURIComponent(search)}`
        });
      }
      return;
    }

    if (realAuth && isRealAuth === 'NY') {
      console.log('-------------------这里是Page层  UseEffect 这个实名异常了-------------------');
      props.history.replace({
        pathname: '/hospital/certification/select',
        search: h5Searchs,// `pathname=${encodeURIComponent(pathname)}&jumpSearch=${encodeURIComponent(search)}`
      });
      return;
    }

    // 需要实名 去实名认证页面  TIPS: 实名判断的逻辑要在token后面
    if (realAuth && isRealAuth !== 'Y') {

      console.log('-------------------这里是Page层  UseEffect 这个要去实名-------------------');
      props.history.replace({
        pathname: '/hospital/certification',
        search: h5Searchs,//  `pathname=${encodeURIComponent(pathname)}&jumpSearch=${encodeURIComponent(search)}`
      });
      return;
    }
    // 标题获取添加逻辑
    bridge.setTitle(typeof title === 'function' ? title() : title);
  }, []);

  return (
    <>
      {isThrough && props.children}
    </>
  );
};

export default Pages;
