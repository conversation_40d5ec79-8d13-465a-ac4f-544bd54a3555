/*
 * File: /Users/<USER>/Desktop/za-docter/za-asclepius-patient-h5/src/hooks/couponCalculate.ts
 * Project: /Users/<USER>/Desktop/za-docter/za-asclepius-patient-h5
 * Created Date: Tuesday May 10th 2022, 9:57:56 am
 * Author: yuchao (<EMAIL>>)
 * -----
 * Last Modified: Tuesday, 10th May 2022 9:58:38 am
 * Modified By: yuchao (<EMAIL>>)
 * -----
 * Copyright (c) 2022 Jie Zhong
 * Description 优惠/权益 金额试算
 */

import { useEffect, useState } from 'react';
import { CouponTypeCalc } from 'src/components/common/couponPicker';
import { cookies, fetchJson } from 'src/utils';

interface Props {
  goodsInfo: {
    goodsId: string;
    relatedBizNo: string;
    orderType: string;
    goodsRealPrice: number;
    goodsPrice: number;
    goodsCode?: string;
    goodsNum?: number;
    goodsName?: string;
  };
  selectedCoupon: CouponTypeCalc | undefined;
}

interface OrderPreferenceItem {}

interface Returns {
  couponList: CouponTypeCalc[];
  discountFee: number | string;
  orderRealAmount: number | string;
  orderAmount: number | string;
  orderPreferenceList: OrderPreferenceItem[];
  orderGoods: any[];
  couponId?: string;
  finish: boolean;
  containActual: boolean;
  expressFee?: number;
}
/**
 * @description 金额试算
 */
export default function UseCouponCalculate(props: Props): Returns {
  const { goodsInfo, selectedCoupon } = props;
  const [discountFee, setDiscountFee] = useState<number | string>(0);
  const [orderRealAmount, setOrderRealAmount] = useState<number | string>(0);
  const [orderAmount, setOrderAmount] = useState<number | string>(0);
  const [couponList, setCouponList] = useState<CouponTypeCalc[]>([]);
  const [orderPreferenceList, setOrderPreferenceList] = useState<OrderPreferenceItem[]>([]);
  const [orderGoods, setOrderGoods] = useState([]);
  const [couponId, setCouponId] = useState('');
  const [finish, setFinish] = useState(false);
  const [containActual, setContainActual] = useState(false);
  const [expressFee, setExpressFee] = useState(0);

  /**
   * @description 优惠金额试算
   * @param tmpCoupon
   */
  const calculatePrice = (): void => {
    if (goodsInfo && goodsInfo.goodsId) {
      const { goodsRealPrice, goodsId, goodsPrice, goodsCode, goodsNum = 1, goodsName = '' } = goodsInfo;
      const goodsItem: any = {
        goodsRealPrice,
        goodsPrice,
        goodsNum,
      };
      if(goodsCode){
        goodsItem.goodsCode = goodsCode; // 新的服务商品一一对应
        goodsItem.goodsId = goodsId;
      }else{
        goodsItem.goodsCode = goodsId; // 这里原来服务包里用是这样是传参的，不去动它
      }
      if(goodsName){
        goodsItem.goodsName = goodsName;
      }

      const data: any = {
        orderGoods: [goodsItem],
        relatedBizNo: goodsInfo.relatedBizNo,
        orderType: goodsInfo.orderType,
        /**
         * 当用户选择优惠券后，该字段需要传值
         * - isPolicyRights：是否是保单权益，默认false
         * - preferenceObjectType：优惠券类型 （1、优惠券 2、服务包
         * - userPreferenceObjectId：用户优惠Id
         */
        orderPreferenceList: selectedCoupon ? [{ isPolicyRights: false, preferenceObjectType: 1, userPreferenceObjectId: selectedCoupon.id }] : [],
        returnValidPreference: true,
        accountId: cookies.get('za_accountId'),
        userId: cookies.get('za_userId'),
        applicationId: 2,
        channelResourceCode: cookies.get('channelResourceCode'),
      };

      fetchJson({
        url: '/api/api/v1/patient/preference/calculate',
        type: 'POST',
        data: {
          ...data,
        },
        isloading: true,
      }).then((res) => {
        if(res.code === '0'){
          const { result } = res;
          const { order: { orderAmount = 0, orderRealAmount = 0, expressRealFee = 0, expressFee = 0, orderPreferenceList = [], orderGoods = [], couponId = '' } = {}, couponList, containActual } = result;
          // eslint-disable-next-line @typescript-eslint/restrict-plus-operands
          const discountFee = orderAmount - orderRealAmount + expressRealFee;
          setCouponList(couponList);
          // 在没有选择优惠券情况下，使用原价，无折扣逻辑
          setDiscountFee(selectedCoupon ? discountFee : 0);
          setOrderRealAmount(selectedCoupon ? orderRealAmount : orderAmount);
          setOrderAmount(orderAmount);
          setOrderPreferenceList(orderPreferenceList);
          setOrderGoods(orderGoods);
          setCouponId(couponId);
          setContainActual(containActual);
          setExpressFee(expressRealFee || expressFee);
          setFinish(true);
        }
      });
    } else {
      setDiscountFee(0);
      return;
    }
  };

  useEffect(() => {
    calculatePrice();
  }, [goodsInfo, selectedCoupon]);

  return {
    discountFee, orderRealAmount, couponList, orderAmount,
    orderPreferenceList, orderGoods, couponId, finish, containActual, expressFee};
}
