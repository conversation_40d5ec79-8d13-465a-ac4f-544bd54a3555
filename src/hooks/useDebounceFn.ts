/**
 * 防抖函数. 用户持续操作时，不执行方法。等到用户停下来一段时间后，开始执行方法
 * 常用于输入时需要执行某些方法，
 */

import { useRef, useCallback } from 'react';

interface Options {
  wait: number;
}
const defaultOptions = {
  wait: 200,
};

export default function useDebounceFn(
  fn: Function,
  options: Options = defaultOptions
): Function {
  const timer = useRef(0);
  const { wait } = options;
  const run = useCallback(
    (...args): void => {
      clearTimeout(timer.current);
      timer.current = setTimeout(() => fn(...args), wait) as any;
    },
    [fn, wait]
  );

  return run;
}
