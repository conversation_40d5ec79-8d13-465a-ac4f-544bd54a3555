import { useEffect, useRef } from 'react';
import { validate } from 'src/utils';

/**
 * 处理iOS设备上输入框获取焦点时的视图调整
 * 
 * 该钩子函数解决了iOS设备上键盘弹出时可能遮挡输入框的问题。
 * 当输入框获得焦点时，会检查输入框是否在可视区域内，
 * 如果不在，则自动滚动页面使输入框位于视口中央。
 * 
 * @param inputRefs - 需要处理焦点事件的输入框元素引用数组
 * @returns void
 */
export const useIOSInputScroll = (inputRefs: Array<HTMLInputElement> | undefined) => {

  useEffect(() => {
    if (!validate.isIos()) return;

    const handleFocus = (event: FocusEvent) => {
      setTimeout(() => {
        const target = event.target as HTMLInputElement;
        
        if (!target) return;
  
        // 使用 visualViewport API 获取实际可视区域高度
        const viewportHeight = window.visualViewport?.height || window.innerHeight;
        // 获取输入框相对于视口的位置
        const inputRect = target.getBoundingClientRect();
  
        // 检查输入框是否在可视区域内
        const isInputVisible = 
          inputRect.top >= 0 && 
          inputRect.bottom <= viewportHeight;
  
        // 如果输入框不在可视区域内，需要滚动
        if (!isInputVisible) {
          // 计算需要滚动的位置
          const scrollPosition = window.scrollY + inputRect.top - (viewportHeight / 2);
          
          // 使用平滑滚动
          window.scrollTo({
            top: scrollPosition,
            behavior: 'smooth'
          });
        }
      }, 500)
    };

    if (!inputRefs) return;

    // 为每个输入框添加焦点事件监听
    inputRefs.forEach(inputRef => {
      const input = inputRef;
      if (input) {
        input.addEventListener('focus', handleFocus);
      }
    });

    // 清理事件监听
    return () => {
      inputRefs.forEach(inputRef => {
        const input = inputRef;
        if (input) {
          input.removeEventListener('focus', handleFocus);
        }
      });
    };
  }, [inputRefs]);
};