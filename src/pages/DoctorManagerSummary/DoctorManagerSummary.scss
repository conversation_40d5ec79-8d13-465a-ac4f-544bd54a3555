@import "src/style/index";

.doctor-manager-summary-container {
  padding-bottom: 6.5rem;

  img {
    width: 100%;
  }

  .doctor-manager-summary {
    position: relative;

    .video {
      position: absolute;
      left: r(25);
      right: r(25);
      bottom: r(25);
      padding-top: 50.25%;
    }
  }

  .doctor-manager-hospital {
    padding: 0 r(10);
    //background: #e8fcf6;
  }

  .agreement {
    //display: flex;
    padding: r(25) r(10) 0 r(40);
    color: #333;
    font-size: r(12);

    .za-checkbox {
      margin-left: r(-27);
      font-size: r(12);

      .za-checkbox__widget {
        padding-top: 0.1rem;
      }
    }

    .za-checkbox__inner {
      border-radius: 50%;
    }
  }
}

.doctor-manager-consult-fixed {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  padding: r(10) r(15) r(20);
  background-color: white;
  box-shadow: 0 r(-15) r(13) 0 rgba(0, 0, 0, 0.02);

  .btn {
    height: auto;
    padding: r(11) 0;
    text-align: center;
    //color: white;
    font-size: r(16);
    font-weight: bold;
    font-family: PingFangSC-Semibold, PingFang SC;
    //background: var(--theme-primary);
    border-radius: r(22);
  }
}
