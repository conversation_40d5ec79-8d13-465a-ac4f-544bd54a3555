import React, { useEffect, useState } from 'react';
import { RouterProps } from 'react-router';
import { Button, Checkbox, Toast } from 'zarm';
import { CDN_PREFIX } from 'src/utils/staticData';
import { ModelAgreement } from './Agreement';
import { pushEvent } from '../../utils';
import './DoctorManagerSummary.scss';
import { shallowEqual, useDispatch, useSelector } from 'react-redux';
import { FETCH_USER_CHANNEL } from '../../store/channel/action-types';
import { ApplicationState } from '../../store';
import { channelRightNames } from '../home/<USER>';

interface props extends RouterProps {}

export default function ({ history }: props) {
  const [isAgree, setIsAgree] = useState<boolean>(true);
  const dispatch = useDispatch();

  const channelRight = useSelector(
    (state: ApplicationState) => state.userChannel.channelRight,
    shallowEqual
  );

  useEffect(() => {
    dispatch({ type: FETCH_USER_CHANNEL });
  }, []);

  const hasVideo: boolean = false;

  const toggleAgree = () => {
    setIsAgree(!isAgree);
  };
  const summaryImg = `${CDN_PREFIX}static/doctorManagerSummary/<EMAIL>`;
  const summaryNoVideoImg = `${CDN_PREFIX}static/doctorManagerSummary/<EMAIL>`;
  const dingZhiHuaImg = `${CDN_PREFIX}static/doctorManagerSummary/dingzhihua.jpg`;
  const consultImg = `${CDN_PREFIX}static/doctorManagerSummary/<EMAIL>`;
  const whyImg =  `${CDN_PREFIX}static/doctorManagerSummary/<EMAIL>`;
  const proImg =  `${CDN_PREFIX}static/doctorManagerSummary/<EMAIL>`;
  const summaryBg = hasVideo ? summaryImg : summaryNoVideoImg;

  const go = () => {
    pushEvent({
      eventTag: 'ZAHLWYY_YLDFJSY',
      text: '众安健康管家',
      attrs: { ZAHLWYY_CLICK_CONTENT: '医管家介绍页_咨询健康管家' },
    });

    if (isAgree) {
      history.push({
        pathname: '/hospital/chatmedicalmanage',
        // search: `rightsname=医管家`
      });
    } else {
      Toast.show('请阅读并同意知情同意书');
    }
  };

  return (
    <div className='doctor-manager-summary-container'>
      <div className='doctor-manager-summary'>
        <img src={summaryBg} alt='' />
        <div className='video' />
      </div>
      <div className='doctor-manager-why'>
        <img src={whyImg} loading='lazy' alt='' />
      </div>
      <div className='doctor-manager-pro'>
        <img src={proImg} loading='lazy' alt='' />
      </div>
      <div className='doctor-manager-dingzhihua'>
        <img src={dingZhiHuaImg} loading='lazy' alt='' />
      </div>
      <div className='doctor-manager-consult'>
        <img src={consultImg} loading='lazy' alt='' />
      </div>
      <div className='doctor-manager-hospital'>
        <img src={require('./assets/images/hospital.png')} loading='lazy' alt='' />
      </div>
      <div className='agreement'>
        <Checkbox id='agreement' checked={isAgree} onChange={toggleAgree}>
          我已仔细阅读
        </Checkbox>
        <ModelAgreement>
          <span style={{ color: 'var(--text-base-color)' }}>《互联网诊疗风险告知及知情同意书》</span>
        </ModelAgreement>
        <label htmlFor='agreement'>，继续咨询即表示我已知悉相关规则与风险并同意相关条款。</label>
      </div>
      <div className='doctor-manager-consult-fixed'>
        <div style={{ position: 'relative' }}>
          <Button className='btn' block theme='primary' onClick={go}>
            马上咨询医管家
          </Button>
          {channelRight != null && (
            <div className='free-consult-tip'>{channelRightNames[channelRight]}</div>
          )}
        </div>
      </div>
    </div>
  );
}
