@import "src/style/index";

.lottery-draw-for-new-user {
  &__container {
    width: 100%;
    position: relative;
  }

  &__fixed {
    position: absolute;
    top: r(5);
    right: 0;
  }

  &__rules {
    width: r(80);
    height: r(40);
    background: url("./assets/rules.png") left center no-repeat;
    background-size: cover;
  }

  &__rules-modal {
    .za-popup {
      max-width: r(295);
      background: transparent;
    }

    .za-modal__body {
      padding: 0;

      .close {
        width: r(36);
        height: r(36);
        margin-top: r(20);
        margin-left: 50%;
        background: url("./assets/close.svg") no-repeat;
        background-size: contain;
        transform: translateX(-50%);
      }
    }
  }

  &__rules-container {
    position: relative;
    padding-bottom: r(20);
    background-color: white;
    border-radius: r(8);

    header {
      height: r(50);
      line-height: r(50);
      font-size: r(18);
      font-weight: bold;
      color: #7c1f06;
      background: url("./assets/rules-header-bg.png") left top no-repeat;
      background-size: contain;
    }

    .rules-body {
      max-height: r(315);
      margin-top: r(15);
      padding: 0 r(20);
      line-height: r(19);
      font-size: r(13);
      color: #000;
      overflow: auto;

      p:not(:first-of-type) {
        margin-top: 1em;
      }
    }
  }

  &__my-lottery {
    width: r(80);
    height: r(40);
    background: url("./assets/my-lottery.png") left center no-repeat;
    background-size: cover;
  }

  &__lottery-draw {
    position: absolute;
    left: 0;
    top: r(144.5);
    width: 100%;
    height: r(300);
    background: url("./assets/lottery.png") center top no-repeat;
    background-size: contain;

    &.drop {
      background-image: url("assets/lottery-drop.png");
    }

    .status {
      margin-top: r(75);
      height: r(105);

      .icon {
        width: r(50);
        height: r(50);
        margin: r(30) auto r(6.5);
        background-repeat: no-repeat;
        background-size: contain;

        &.disabled {
          background-image: url("./assets/face.png");
        }

        &.expired {
          background-image: url("./assets/clock.png");
        }
      }

      .status-content {
        font-size: r(18);
        font-weight: normal;
        color: #7c1f06;
      }
    }

    .btn {
      height: r(65);
      margin: r(18) r(40) 0;
      background: url("./assets/btn-bg.png") center top no-repeat;
      background-size: contain;
    }

    .disabled {
      background-image: url("./assets/btn-disabled-bg.png");
    }

    .expired {
      background-image: url("./assets/btn-expired-bg.png");
    }
  }
}

@media screen and (min-width: 670px) {
  .lottery-draw-for-new-user {
    &__lottery-draw {
      top: r(165);
    }
  }
}

@media screen and (min-width: 740px) {
  .lottery-draw-for-new-user {
    &__lottery-draw {
      top: r(180);
    }
  }
}
