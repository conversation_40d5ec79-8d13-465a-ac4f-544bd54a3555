import classNames from 'classnames';
import React, { useCallback, useEffect, useState } from 'react';
import { StaticToast } from 'src/components/common';
import { useOverflowHidden } from 'src/components/hooks';
import { CDN_PREFIX } from 'src/utils/staticData';
import { Modal } from 'zarm';
import { cookies, Deserialize, fetchJson, validate } from '../../utils';
import './LotteryDrawForNewUser.scss';

const prefixCls = 'lottery-draw-for-new-user';
const channelSource = cookies.get('channelSource');
const channelResourceCode = cookies.get('channelResourceCode');
const defaultActivityId = 95007;

const getConfigRule = (actId) => {
  switch (actId) {
  case 95007:
    return [
      '1、活动期间，添加企微坐席后，自动欢迎语推送抽奖链接（异常情况下可由企微坐席推送活动海报，引导用户参与），按照提示步骤，即可参与众安新人福利抽奖活动。同一参与者（同一微信及手机号码、IP地址视为同一参与者，下同）在活动时间内仅可参与抽奖活动的次数上限为1次。',
      '2、活动期间，凡以不正当手段(包括但不限于恶意刷单、机器作弊、扰乱系统、实施网络攻击等)参与本次活动的用户，众安互联网医院有权取消恶意用户的参与资格、终止其参与活动并回收所有奖励，必要时，有权暂停或者取消本次活动，同时保留向其追究法律责任的权力；如遇不可抗力(包括但不限于重大灾害事件、活动受政府机关指令需要停止举办或调整、活动中存在大面积作弊行为、活动遭遇严重网络攻击或系统故障导致活动不能正常进行)的影响，众安互联网医院可立即暂停、中止或终止活动。',
      '3、如对活动有疑问，欢迎拨打全国统一服务热线：1010-9955。举报及投诉电话：021-********。',
      '4、本活动最终解释权归平台所有。',
    ];
  case 370004:
  case 145002:
  case 150002:
    return [
      '1、活动期间，参与者首次登录互医，进入活动页面，按照提示步骤，即可参与众安新人福利抽奖活动。同一参与者（同一微信及手机号码、IP地址视为同一参与者，下同）在活动时间内仅可参与抽奖活动的次数上限为1次。',
      '2、活动期间，凡以不正当手段(包括但不限于恶意刷单、机器作弊、扰乱系统、实施网络攻击等)参与本次活动的用户，众安互联网医院有权取消恶意用户的参与资格、终止其参与活动并回收所有奖励，必要时，有权暂停或者取消本次活动，同时保留向其追究法律责任的权力；如遇不可抗力(包括但不限于重大灾害事件、活动受政府机关指令需要停止举办或调整、活动中存在大面积作弊行为、活动遭遇严重网络攻击或系统故障导致活动不能正常进行)的影响，众安互联网医院可立即暂停、中止或终止活动。',
      '3、如对活动有疑问，欢迎拨打全国统一服务热线：1010-9955。举报及投诉电话：021-********。',
      '4、本活动最终解释权归平台所有。',
    ];
  default:
    return null;
  }
};

interface DrawLotteryStatus {
  id?: number;
  partakeTimes?: number;
  partakeAccountId?: number;
  partakeUserId?: number;
  activityId?: number;
  partakeChannelResourceCode?: string;
  partakeChannelSource?: string;
  activityStatus?: 0 | 1 | 2 | 3; // 活动状态（0：下架，1：上线，2：待上线，3：已过期）
}

export default function(props) {
  const [visible, setVisible] = useState(false);
  const [status, setStatus] = useState<DrawLotteryStatus>({});

  const { location: { search = '' } } = props;
  const { activityId: actid = defaultActivityId } = Deserialize(search);

  const activityId = Number(actid);

  useOverflowHidden(visible);

  useEffect(() => {
    queryDrawLottery();
  }, []);

  const disabled: boolean = status.id != null; // 抽奖次数用完
  const expired: boolean = status.activityStatus != null && status.activityStatus != 1; // 活动结束

  const showMyLottery = () => {
    StaticToast.warning('您还没有奖品哦');
  };

  const showLotteryDrawRules = useCallback(() => {
    setVisible(true);
  }, []);

  const closeRuleModel = useCallback(() => {
    setVisible(false);
  }, []);

  const queryDrawLottery = () => {
    fetchJson({
      url: '/api/api/v1/patient/activity/getActivityDetail',
      type: 'POST',
      data: {
        activityId,
      },
      success: (res) => {
        if (res && res.code === '0') {
          setStatus(res.result || {});
        }
      },
      error: (e) => {
        //
      },
    });
  };

  const lotteryDraw = () => {
    if (expired) {
      if (validate.isFromMiniApplet()) {
        wx.miniProgram.redirectTo({
          url: '/pages/index',
        });
      } else {
        props.history.push({
          pathname: '/hospital/home',
          search: channelResourceCode ? `channelResourceCode=${channelResourceCode}` : `channelSource=${channelSource}`,
        });
      }
    } else if (status.id == null) {
      // 有抽奖机会
      fetchJson({
        url: '/api/api/v1/patient/activity/drawActivity',
        type: 'POST',
        data: {
          activityId,
          partakeChannelSource: channelSource,
          partakeChannelResourceCode: channelResourceCode,
        },
        isloading: true,
        success: (res) => {
          if (res && res.code === '0') {
            // StaticToast.error('谢谢参与');
            setStatus({ id: 1 });
          } else {
            StaticToast.error(res.message || '抽奖出错');
          }
        },
        error: (e) => {
          StaticToast.error(e.message);
        },
      });
    } else {
      StaticToast.error('抽奖机会已用完');
    }
  };

  const bg = `${CDN_PREFIX}static/lotteryDraw/bg.png`;
  return (
    <div className={`${prefixCls}__container`}>
      <img src={bg} alt='' />
      <div className={`${prefixCls}__fixed`}>
        <div className={`${prefixCls}__rules`} onClick={showLotteryDrawRules} />
        <div className={`${prefixCls}__my-lottery`} onClick={showMyLottery} />
      </div>
      <div className={classNames(`${prefixCls}__lottery-draw`, { drop: disabled || expired })}>
        {/* className: disabled 抽奖机会已用完, expired 活动过期 */}
        <div className='status text-center'>
          <div
            className={classNames('icon', {
              disabled,
              expired,
            })}
          />
          {disabled && <div className='status-content'>谢谢参与</div>}
          {expired && <div className='status-content'>活动已结束，下次早点来</div>}
        </div>
        <div
          className={classNames('btn', {
            disabled,
            expired,
          })}
          onClick={lotteryDraw}
        />
      </div>
      <Modal
        className={`${prefixCls}__rules-modal`}
        width='78%'
        visible={visible}
        onCancel={closeRuleModel}
      >
        <div className={`${prefixCls}__rules-container`}>
          <header className='text-center'>活动规则</header>
          <div className='rules-body'>
            {
              (getConfigRule(activityId) || []).map((item, index) => <p key={`rule${index}`}>{item}</p>)
            }
          </div>
        </div>
        <div className='close' onClick={closeRuleModel} />
      </Modal>
    </div>
  );
}
