@import "src/style/index";
$prefixCls: 'about-pages';

.#{$prefixCls} {
  min-height: 100vh;
  background-color: #fff;

  &__header {
    background-color: #19c597;
    background-image: url('https://cdn-qcloud.zhongan.com/a00000/za-asclepius/static/about/bg.png');
    background-repeat: no-repeat;
    background-position: 0 0;
    background-size: 100% auto;
    padding: r(130) 0 r(20);
  }

  &__title {
    line-height: r(26);
    font-size: r(18);
    font-weight: bold;
    color: rgba(255, 255, 255, 1);
    text-align: center;
    margin-bottom: r(11);

    &.black-theme {
      color: #4f4d4e;
      margin: r(26) 0 r(15);

      span {
        &::after,
        &::before {
          background: rgba(48, 207, 150, .49);
        }
      }
    }

    span {
      display: inline-block;
      position: relative;

      &::after,
      &::before {
        content: "";
        position: absolute;
        top: 50%;
        width: r(5);
        height: r(5);
        background: rgba(255, 255, 255, .49);
        transform-origin: center;
        transform: rotate(45deg);
        margin-top: r(-2.5);
      }

      &::before {
        right: r(-10);
      }

      &::after {
        left: r(-11);
      }
    }
  }

  &__summary {
    margin: 0 r(25);
    background: rgba(255, 255, 255, 0.12);
    padding: r(5.5) r(7);
    font-size: r(11);

    .summary-inner {
      position: relative;
      background: rgba(255, 255, 255, 0.12);
      padding: r(12) r(8) r(12) r(16);
      color: #fff;
      line-height: r(22);

      .summary-row {
        position: relative;

        &::after {
          content: "";
          position: absolute;
          top: r(9);
          left: -8px;
          width: 3px;
          height: 3px;
          border-radius: 50%;
          background-color: #fff;
        }
      }

      &::after,
      &::before,
      .after,
      .before {
        content: "";
        position: absolute;
        width: r(12);
        height: r(12);
        border-color: #fff;
        border-style: solid;
        border-width: 1px 0 0 1px;
        left: 0;
        top: 0;
      }

      &::after {
        top: auto;
        bottom: 0;
        border-width: 0 0 1px 1px;
      }

      .after {
        border-width: 1px 1px 0 0;
        left: auto;
        right: 0;
      }

      .before {
        border-width: 0 1px 1px 0;
        left: auto;
        bottom: 0;
        top: auto;
        right: 0;
      }
    }

    h5 {
      margin-bottom: r(8);
      font-size: r(15);
    }
  }

  &__ensure {
    padding: 0 r(25) r(33) r(45);

    .ensure-item {
      position: relative;
      background-color: #f8f8f8;
      padding: r(8) r(14) r(10) r(56.5);
      line-height: r(20);
      font-size: r(12);
      color: #999;
      min-height: r(80);

      &:not(:last-child) {
        margin-bottom: r(15);
      }

      &::after,
      &::before {
        content: "";
        position: absolute;
        width: 0;
        height: 0;
        border-style: solid;
      }

      &::after {
        right: 0;
        top: 0;
        border-width: 0 22px 22px 0;
        border-color: transparent #fff transparent transparent;
      }

      &::before {
        left: 0;
        bottom: 0;
        border-width: 18px 0 0 18px;
        border-color: transparent transparent transparent #fff;
      }

      h6 {
        line-height: r(21.5);
        font-weight: bold;
        font-size: r(16);
        color: #4e4e4e;
      }

      &-num {
        position: absolute;
        width: r(68);
        top: 50%;
        left: r(-22);
        transform: translate(0, -50%);
      }
    }
  }

  &__capabilities {
    background-color: #f8f8f8;
    overflow: hidden;
    padding: r(10) 0 r(15);

    &-body {
      @include display-flex;

      flex-wrap: wrap;

      li {
        width: 25%;
        padding: r(5) r(10) r(15) r(10);
        text-align: center;
        font-size: r(12);
        line-height: r(22);

        .icon {
          width: 30px;
          height: 30px;
        }
      }
    }
  }

  &__development {
    padding: r(10) 0 r(30);

    &-body {
      line-height: r(22);
      font-size: r(12);
      color: #666;
      padding: 0 r(30);
    }
  }
}
