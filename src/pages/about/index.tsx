import React, { ReactNode } from 'react';
// import classnames from 'classnames';
import { SvgIcon } from 'src/components/common';

import './about.scss';


interface CardProps {
  alias?: 'intro' | 'project' | 'condition' | 'flow';
  children?: string | ReactNode;
  extra?: string | ReactNode;
  title?: string | ReactNode;


}
const ensureList = [{
  title: "医生专业",
  desc: "众安医生平均具备5年以上临床经验，多数具备全国三级以上公立医院从业经验，持有国家认证的执业医师资格证书。"
}, {
  title: "服务专业",
  desc: "平台服务人员平均具备10年以上健康行业经验，经验丰富，响应迅速。"
}, {
  title: "平台专业",
  desc: "整合众安保险旗下丰富的线上医疗服务及线下医疗资源，倾心为您打造完善的健康服务体系。"
}]
const capabilitiesList = [
  {
    title: "在线问诊送药",
  }, {
    title: "就医垫付",
  }, {
    title: "肿瘤特药",
  }, {
    title: "重疾绿通",
  }, {
    title: "质子重离子",
  }, {
    title: "术后家庭护理",
  }, {
    title: "心理倾诉",
  }, {
    title: "体检服务",
  },
]



const About = (props: CardProps) => {
  const prefixCls = 'about-pages';

  return (
    <div className={`${prefixCls}`}>
      <header className={`${prefixCls}__header`}>
        <h3 className={`${prefixCls}__title`}><span>企业简介</span></h3>
        <div className={`${prefixCls}__summary`}>
          <div className="summary-inner">
            <p className="summary-row">众安互联网医院为众安保险旗下线上问诊就医平台。2019  年7月获得牌照，12月正式上线。</p>
            <p className="summary-row">金牌医师团队提供精准医疗服务，快速解决患者需求。</p>
            <p className="summary-row">与众同行，做有温度的线上健康管家。</p>
            <i className="after"></i>
            <i className="before"></i>
          </div>
        </div>
      </header>
      <h3 className={`${prefixCls}__title black-theme`}><span>服务保障</span></h3>
      <ul className={`${prefixCls}__ensure`}>
        {
          ensureList.map((item, index) => (
            <li className="ensure-item" key={`ensure${index}`}>
              <h6>{item.title}</h6>
              <p>{item.desc}</p>
              <p className="ensure-item-num"><img src={require(`./images/ensure0${index + 1}.png`)} alt="" /></p>
            </li>
          ))
        }
      </ul>
      <div className={`${prefixCls}__capabilities`}>
        <h3 className={`${prefixCls}__title black-theme`}><span>服务能力</span></h3>
        <ul className={`${prefixCls}__capabilities-body`}>
          {capabilitiesList.map((item, index) => (
            <li key={`capabilities-${index}`}>
              <SvgIcon className='icon' src={require(`./images/sprite-${index + 1}.svg`)} />
              <p>{item.title}</p>
            </li>
          ))}
        </ul>
      </div>
      <div className={`${prefixCls}__development`}>
        <h3 className={`${prefixCls}__title black-theme`}><span>发展愿景</span></h3>
        <p className={`${prefixCls}__development-body`}>致力于构建全方位线上医疗服务，打造专业健康服务平台。</p>
      </div>
    </div >
  );
}

export default About;