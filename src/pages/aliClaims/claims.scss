@import 'src/style/index';
$prefixCls: 'claimsinquiry-pages';

.#{$prefixCls} {
  &__hd {
    background-color: #fff;
    // height: r(303.5);
    position: relative;
    // padding-bottom: r(26);
    margin-bottom: r(19);
    padding-top: r(47);

    .todo {
      position: absolute;
      top: r(25);
      right: r(15);
      color: #00a864;
      font-size: 12px;
      transform: scale(0.9);
      display: flex;

      img {
        width: r(14);
        height: r(14);
      }
    }

    .logo {
      width: r(85);
      height: r(85);
      display: block;
      margin: 0 auto;
    }

    &::before,
    &::after {
      position: absolute;
      content: '';
      left: 50%;
      transform: translate(-50%, 0);
      bottom: -10px;
      width: 0;
      height: 0;
      border-style: solid;
      border-width: 10px 10px 0;
      border-color: #fff transparent transparent;
    }

    &::before {
      z-index: 2;
      bottom: -7px;
      border-color: #fff transparent transparent;
    }

    .guide-txt-cont {
      padding: r(18) r(5) r(36) r(5);
    }

    .guide-txt {
      width: r(241);
      position: relative;
      margin: 0 auto;
      height: r(35);
      line-height: r(35);
      text-align: center;
      font-weight: 600;
      color: $base-green;
      font-size: r(15);
      background: linear-gradient(270deg, rgba(223, 243, 234, 0) 0%, #eefdf6 48%, rgba(223, 243, 234, 0) 100%);
    }

    .illustrate {
      line-height: r(20);
      color: #666;
    }
  }

  &__core {
    margin: 0 r(15) r(15);
    padding: r(13.5) 0;
    background-color: #fff;
    border-radius: r(8);
    @include display-flex;

    &-col {
      @include display-flex;
      @include flex;

      justify-content: space-between;
      align-items: center;
      position: relative;
      padding: 0 r(15);

      &__left {
        display: flex;
        align-items: center;
      }

      .col-icon {
        width: r(35);
        height: r(35);
        margin-right: r(8);
      }

      .za-badge {
        &__content {
          width: 5px;
          height: 5px;
        }
      }

      .not-dot {
        .za-badge {
          &__content {
            display: none;
          }
        }
      }

      .arrow-right-box {
        img {
          width: r(14);
          height: r(14);
        }
      }

      .col-children {
        font-weight: 600;
        color: #333;
        font-size: r(15);

        .za-icon {
          font-size: r(14);
          color: #d6d6d6;
        }
      }

      .has-order {
        color: $base-green;
      }

      & + & {
        &::after {
          content: '';
          position: absolute;
          left: 0;
          top: 0;
          bottom: 0;
          width: r(1);
          background-color: #e6e6e6;
        }
      }
    }
  }

  &__process {
    margin: 0 r(15) r(15);
    padding: r(12) r(14);
    background-color: #fff;
    border-radius: r(8);

    h3 {
      font-size: r(16);
      font-weight: 600;
      color: #1e1e1e;
    }

    .process-inner {
      margin-top: r(12);
      border-radius: r(8);
      background: linear-gradient(180deg, #f7fcfa 0%, #f3fffb 100%);
    }

    .step {
      line-height: r(36);
      background: linear-gradient(270deg, #dbf4ea 0%, #ebf8f3 100%);
      border-radius: r(8) r(8) 0 0;
      font-size: r(12);
      color: #333;
      padding-left: r(15);
      @include display-flex;
      @include align-content(center);

      img {
        width: r(15);
        height: r(10);
        margin: 0 r(8);
      }
    }

    .process-desc {
      @include display-flex;

      padding: r(9) 0 r(12);

      li {
        font-size: r(12);
        color: #333;
        padding: 0 r(15);
        @include flex;

        &:last-child {
          text-align: right;
        }

        .label {
          font-weight: 600;
        }

        .value {
          color: #999;
          margin-top: 5px;
        }
      }
    }
  }

  &__modal {
    .za-modal__body {
      font-size: r(14);
      color: #666;
    }
  }
}
