/*
 * @description： 直赔-问诊流程
 */
import classnames from 'classnames';
import React, { useEffect, useState, useCallback } from 'react';
import { FixedButton } from 'src/components/common';
import { fetchJson } from 'src/utils/fetch';
import { Deserialize } from 'src/utils/serialization';
import { CDN_PREFIX } from 'src/utils/staticData';
import { Badge, Modal } from 'zarm';
import './claims.scss';

const prefixCls = 'claimsinquiry-pages';
const processList = [
  {
    title: '节省时间',
    desc: '无需线下排队',
  },
  {
    title: '专业服务',
    desc: '专业医生1v1服务',
  },
];
const Claims = (props) => {
  const {
    location: { search = '' },
  } = props;

  const { patientId = '', policyNo = '' } = Deserialize(search);
  const [state, setDataState] = useState({
    hasInquiryNum: 0,
    hasOrderNum: 0,
    isHasRights: false,
    processingOrder: [],
  });

  // 检查患者是否存在问诊单
  const checkHasInquiry = useCallback(() => {
    fetchJson({
      type: 'POST',
      url: '/api/api/v1/patient/inquiry/list',
      data: {
        productId: 1,
        inquiryStatusList: [1, 2, 3, 7, 8, 10, 11],
        inquiryType: 'I',
        excludeInquiryTimingTypeList: ['drug_replenish', 'mentalConsult'],
        policyNo,
        payInquiryType: 'text',
      },
    }).then((res) => {
      if (res.code === '0') {
        const { result = [] } = res;
        setDataState((prevState) => ({
          ...prevState,
          hasInquiryNum: result.length,
          processingOrder: result.filter((item) => [2, 3, 7].includes(item.inquiryStatus)),
        }));
      }
    });
  }, [state]);

  const checkDrugDirectPay = useCallback(() => {
    fetchJson({
      type: 'POST',
      url: '/api/api/v1/patient/user/rights/queryAliCaiNiaoEmployeeRights',
      data: {
        bizType: 'inquiry',
      },
      needLoading: true,
    }).then((res) => {
      if (res.code === '0') {
        const { result = [] } = res;
        if (result.length) {
          const hasRight = result.some((item) => item.bizType === 'inquiry');
          setDataState((prevState) => ({
            ...prevState,
            isHasRights: hasRight,
          }));
        } else {
          setDataState((prevState) => ({
            ...prevState,
            isHasRights: false,
          }));
        }
      }
    });
  }, [state]);
  useEffect(() => {
    checkHasInquiry();
    checkDrugDirectPay();
  }, []);
  const goTo = (url) => {
    props.history.push({
      pathname: url,
      search: `patientId=${patientId}&policyNo=${policyNo}&orderStatus=0`,
    });
  };
  const jumpInquriy = () => {
    goTo('/hospital/inquiryform');
  };
  const goToInquriy = useCallback(() => {
    const { isHasRights, hasInquiryNum } = state;
    if (!isHasRights) {
      Modal.alert({
        className: `${prefixCls}__modal`,
        title: '',
        content: '未查询到与您相关的有效服务权益，您可能无法使用后续服务',
        cancelText: '知道了',
      });
      return;
    } else if (hasInquiryNum) {
      Modal.alert({
        className: `${prefixCls}__modal`,
        content: '当前有进行中的订单，请等待订单结束后再次发起问诊',
        cancelText: '知道了',
      });
      return;
    }
    jumpInquriy();
    return;
  }, [state]);

  const gotoCurrentInquiry = () => {
    const item: any = state.processingOrder[0];
    props.history.push({
      pathname: '/hospital/await',
      search: `inquiryId=${item.id}&inquiryNo=${item.inquiryNo}`,
    });
  };
  return (
    <section className={prefixCls}>
      <header className={`${prefixCls}__hd`}>
        <img className='logo' src={`${CDN_PREFIX}applets/logo/za-logo.png`} />
        <div className='guide-txt-cont'>
          <h3 className='guide-txt'>嗨，欢迎体验在线问诊服务</h3>
        </div>
        {
          state.processingOrder.length ? (
            <div className='todo' onClick={gotoCurrentInquiry}>
              <span>有一个问诊待完成</span>
              <img className='logo' src={`${CDN_PREFIX}icon/icon_arrow_right_green.png`} />
            </div>
          ) : null
        }
      </header>
      <div className={`${prefixCls}__core`}>

        <div className={`${prefixCls}__core-col`} onClick={() => goTo('/hospital/myinquiry')}>
          <div className={`${prefixCls}__core-col__left`}>
            <div className={classnames('col-icon', { 'not-dot': !state.hasInquiryNum })}>
              <Badge shape='dot'>
                <img src={`${CDN_PREFIX}/icon/icon_inquiry.png`} alt='' />
              </Badge>
            </div>
            <div className='col-children'>问诊记录</div>
          </div>
          <p className='arrow-right-box'>
            <img src={`${CDN_PREFIX}/icon/icon_right_arrow.png`} alt='' />
          </p>
        </div>
      </div>
      <div className={`${prefixCls}__process`}>
        <h3>使用流程</h3>
        <div className='process-inner'>
          <p className='step'>
            选择问诊服务
            <i>
              <img src={require('./images/arrow-icon.png')} alt='' />
            </i>{' '}
            完成导诊流程{' '}
            <i>
              <img src={require('./images/arrow-icon.png')} alt='' />
            </i>{' '}
            发起问诊
          </p>
          <ul className='process-desc'>
            {processList.map((k, index) => (
              <li key={`item${index}`}>
                <p className='label'>{k.title}</p>
                <p className='value'>{k.desc}</p>
              </li>
            ))}
          </ul>
        </div>
      </div>

      <FixedButton buttonShape='round' prefixCls={`${prefixCls}__fix_button`} buttonClick={() => goToInquriy()} text='发起图文问诊' />
    </section>
  );
};
export default Claims;
