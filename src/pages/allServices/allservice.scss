@import "src/style/index";
$prefixCls: 'allservice_page';

.#{$prefixCls} {
  padding: 0 r(15) r(50);
  min-height: 100vh;
  background-color: #fff;

  .service_type {
    font-size: r(16);
    font-family: PingFangSC-Semibold, PingFang SC;
    font-weight: 600;
    color: #1e1e1e;
    padding-top: r(30);
  }

  .serviceList {
    @include display-flex;
    @include flex-wrap(wrap);
  }

  .service_item {
    width: 25%;
    padding-top: r(20);
    text-align: center;
    font-size: r(12);
    color: var(--color-text);

    p {
      font-size: r(14);
      font-weight: 400;
      color: #333;
      line-height: r(20);
    }
  }

  .icon_service {
    width: r(40);
    height: r(40);
    border-radius: 50%;
    margin-bottom: r(6);
  }
}
