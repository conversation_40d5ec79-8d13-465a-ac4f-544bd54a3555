<?xml version="1.0" encoding="UTF-8"?>
<svg width="80px" height="80px" viewBox="0 0 80 80" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>健康咨询</title>
    <defs>
        <linearGradient x1="16.2296809%" y1="0%" x2="50%" y2="66.0061201%" id="linearGradient-1">
            <stop stop-color="#F0C99A" offset="0%"></stop>
            <stop stop-color="#E48417" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="16.2296809%" y1="12.281912%" x2="50%" y2="62.074405%" id="linearGradient-2">
            <stop stop-color="#F0C99A" offset="0%"></stop>
            <stop stop-color="#E79333" offset="100%"></stop>
        </linearGradient>
        <path d="M41,-1.0658141e-14 C41.5522847,-1.07595941e-14 42,0.44771525 42,1 L42,30 C42,30.5522847 41.5522847,31 41,31 L31.404,31 L27.7750668,36.0613869 C27.453205,36.5101892 26.8284584,36.6130947 26.3796562,36.2912329 C26.3033197,36.2364876 26.2350777,36.1712594 26.1769427,36.0974716 L22.161,31 L1,31 C0.44771525,31 6.76353751e-17,30.5522847 0,30 L0,1 C-6.76353751e-17,0.44771525 0.44771525,-1.0556688e-14 1,-1.0658141e-14 L41,-1.0658141e-14 Z" id="path-3"></path>
        <filter x="-41.7%" y="-34.3%" width="183.3%" height="195.9%" filterUnits="objectBoundingBox" id="filter-4">
            <feOffset dx="0" dy="5" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="5" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feColorMatrix values="0 0 0 0 0.870588235   0 0 0 0 0.694117647   0 0 0 0 0.48627451  0 0 0 0.3 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
        </filter>
    </defs>
    <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="全部服务" transform="translate(-73.000000, -291.000000)">
            <g id="编组-20" transform="translate(30.000000, 271.000000)">
                <g id="健康咨询" transform="translate(43.000000, 20.000000)">
                    <circle id="椭圆形" fill-opacity="0.15" fill="url(#linearGradient-1)" cx="40" cy="40" r="40"></circle>
                    <g id="编组-3" transform="translate(19.000000, 22.000000)">
                        <g id="形状结合">
                            <use fill="black" fill-opacity="1" filter="url(#filter-4)" xlink:href="#path-3"></use>
                            <use fill="url(#linearGradient-2)" fill-rule="evenodd" xlink:href="#path-3"></use>
                        </g>
                        <path d="M9,13 C10.3807119,13 11.5,14.1192881 11.5,15.5 C11.5,16.8807119 10.3807119,18 9,18 C7.61928813,18 6.5,16.8807119 6.5,15.5 C6.5,14.1192881 7.61928813,13 9,13 Z M21,13 C22.3807119,13 23.5,14.1192881 23.5,15.5 C23.5,16.8807119 22.3807119,18 21,18 C19.6192881,18 18.5,16.8807119 18.5,15.5 C18.5,14.1192881 19.6192881,13 21,13 Z M33,13 C34.3807119,13 35.5,14.1192881 35.5,15.5 C35.5,16.8807119 34.3807119,18 33,18 C31.6192881,18 30.5,16.8807119 30.5,15.5 C30.5,14.1192881 31.6192881,13 33,13 Z" id="形状结合" fill="#FFFFFF"></path>
                    </g>
                </g>
            </g>
        </g>
    </g>
</svg>