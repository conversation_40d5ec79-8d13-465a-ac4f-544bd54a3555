<?xml version="1.0" encoding="UTF-8"?>
<svg width="80px" height="80px" viewBox="0 0 80 80" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>疫苗接种</title>
    <defs>
        <linearGradient x1="-3.55271368e-13%" y1="50%" x2="105.78316%" y2="50%" id="linearGradient-1">
            <stop stop-color="#FFC753" offset="0.0218531469%"></stop>
            <stop stop-color="#F9D98E" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="49.363162%" y1="50%" x2="50%" y2="0%" id="linearGradient-2">
            <stop stop-color="#FFCB5F" offset="0.0218531469%"></stop>
            <stop stop-color="#FFE098" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="-20.2113946%" y1="50%" x2="114.38498%" y2="50%" id="linearGradient-3">
            <stop stop-color="#FFCB5F" offset="0.0218531469%"></stop>
            <stop stop-color="#FFDD8D" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="23.4418186%" y1="50%" x2="66.6420118%" y2="27.9091651%" id="linearGradient-4">
            <stop stop-color="#FBA900" offset="0.0218531469%"></stop>
            <stop stop-color="#F9D98E" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="100%" y1="48.2522166%" x2="50%" y2="50%" id="linearGradient-5">
            <stop stop-color="#FAE49E" offset="0%"></stop>
            <stop stop-color="#F1C058" offset="100%"></stop>
        </linearGradient>
    </defs>
    <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="全部服务" transform="translate(-598.000000, -734.000000)">
            <g id="编组-20" transform="translate(30.000000, 714.000000)">
                <g id="编组-12" transform="translate(525.000000, 0.000000)">
                    <g id="疫苗接种" transform="translate(43.000000, 20.000000)">
                        <circle id="椭圆形" fill-opacity="0.16" fill="url(#linearGradient-1)" cx="40" cy="40" r="40"></circle>
                        <g id="编组-2" transform="translate(40.500000, 40.000000) scale(-1, 1) rotate(-30.000000) translate(-40.500000, -40.000000) translate(24.000000, 13.000000)">
                            <rect id="矩形" fill="url(#linearGradient-2)" x="14.58" y="0" width="3.24" height="34.02"></rect>
                            <rect id="矩形" fill="url(#linearGradient-3)" x="12.15" y="8.1" width="8.1" height="7.29"></rect>
                            <path d="M25.11,11.34 C26.4520519,11.34 27.54,12.4279481 27.54,13.77 L27.54,42.93 C27.54,44.2720519 26.4520519,45.36 25.11,45.36 L25.11,50.22 L27.54,50.22 C27.9873506,50.22 28.35,50.5826494 28.35,51.03 L28.35,52.65 C28.35,53.0973506 27.9873506,53.46 27.54,53.46 L4.86,53.46 C4.41264935,53.46 4.05,53.0973506 4.05,52.65 L4.05,51.03 C4.05,50.5826494 4.41264935,50.22 4.86,50.22 L7.29,50.22 L7.29,45.36 C5.94794806,45.36 4.86,44.2720519 4.86,42.93 L4.86,13.77 C4.86,12.4279481 5.94794806,11.34 7.29,11.34 L25.11,11.34 Z" id="形状结合" fill="url(#linearGradient-4)"></path>
                            <rect id="矩形" fill-opacity="0.3" fill="#FFFFFF" x="14.58" y="17.82" width="12.96" height="2.43"></rect>
                            <rect id="矩形" fill-opacity="0.6" fill="#FFFFFF" x="14.58" y="23.49" width="12.96" height="2.43"></rect>
                            <rect id="矩形" fill-opacity="0.3" fill="#FFFFFF" x="14.58" y="29.16" width="12.96" height="2.43"></rect>
                            <rect id="矩形" fill-opacity="0.6" fill="#FFFFFF" x="14.58" y="34.83" width="12.96" height="2.43"></rect>
                            <rect id="矩形" fill="url(#linearGradient-5)" x="0" y="42.12" width="32.4" height="3.24" rx="0.81"></rect>
                        </g>
                    </g>
                </g>
            </g>
        </g>
    </g>
</svg>