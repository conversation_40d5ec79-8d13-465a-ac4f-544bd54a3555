<?xml version="1.0" encoding="UTF-8"?>
<svg width="80px" height="80px" viewBox="0 0 80 80" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>视频问诊</title>
    <defs>
        <linearGradient x1="1.82318054%" y1="2.17706431%" x2="100%" y2="100%" id="linearGradient-1">
            <stop stop-color="#BAE1F6" offset="0%"></stop>
            <stop stop-color="#6DBBFA" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="1.82318054%" y1="42.3483303%" x2="100%" y2="58%" id="linearGradient-2">
            <stop stop-color="#BAE1F6" offset="0%"></stop>
            <stop stop-color="#6DBBFA" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="1.82318054%" y1="12.2139767%" x2="100%" y2="89.5061728%" id="linearGradient-3">
            <stop stop-color="#BAE1F6" offset="0%"></stop>
            <stop stop-color="#54ACF4" offset="100%"></stop>
        </linearGradient>
    </defs>
    <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="全部服务" transform="translate(-423.000000, -291.000000)">
            <g id="编组-20" transform="translate(30.000000, 271.000000)">
                <g id="编组-12" transform="translate(350.000000, 0.000000)">
                    <g id="视频问诊" transform="translate(43.000000, 20.000000)">
                        <circle id="椭圆形" fill-opacity="0.18" fill="url(#linearGradient-1)" cx="40" cy="40" r="40"></circle>
                        <g id="编组-19" transform="translate(21.000000, 19.000000)">
                            <path d="M8.61538462,0 L30.1538462,0 L28.5268627,5.81074817 C28.0625599,7.46899886 26.5511022,8.61538462 24.8290766,8.61538462 L14.0725666,8.61538462 C12.3802186,8.61538462 10.8873911,7.5074715 10.3971876,5.88767456 L8.61538462,0 L8.61538462,0 Z" id="矩形" fill="url(#linearGradient-2)" opacity="0.560732887" transform="translate(19.384615, 4.307692) scale(1, -1) translate(-19.384615, -4.307692) "></path>
                            <path d="M33.6492308,6.89230769 C36.4769287,6.89230769 38.7692308,9.18460977 38.7692308,12.0123077 L38.7692308,36.2338462 C38.7692308,39.0615441 36.4769287,41.3538462 33.6492308,41.3538462 L5.12,41.3538462 C2.29230208,41.3538462 3.2253651e-15,39.0615441 0,36.2338462 L0,12.0123077 C-1.23447154e-15,9.18460977 2.29230208,6.89230769 5.12,6.89230769 L33.6492308,6.89230769 Z M13.9948846,16.5873316 C13.2879602,16.5873316 12.7148846,17.1604071 12.7148846,17.8673316 L12.7148846,17.8673316 L12.7148846,30.0883034 C12.7148846,30.3016293 12.7682016,30.5115724 12.8699889,30.6990485 C13.207294,31.3203113 13.9843669,31.5505044 14.6056297,31.2131992 L14.6056297,31.2131992 L25.8601781,25.1027133 C26.07766,24.9846348 26.2562504,24.8060444 26.3743288,24.5885626 C26.711634,23.9672998 26.4814409,23.1902269 25.8601781,22.8529218 L25.8601781,22.8529218 L14.6056297,16.7424359 C14.4181536,16.6406486 14.2082105,16.5873316 13.9948846,16.5873316 Z" id="形状结合" fill="url(#linearGradient-3)"></path>
                        </g>
                    </g>
                </g>
            </g>
        </g>
    </g>
</svg>