<?xml version="1.0" encoding="UTF-8"?>
<svg width="80px" height="80px" viewBox="0 0 80 80" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>云药房</title>
    <defs>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-1">
            <stop stop-color="#A4E8D2" offset="0%"></stop>
            <stop stop-color="#62D3AB" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-2">
            <stop stop-color="#A4E8D2" offset="0%"></stop>
            <stop stop-color="#39C593" offset="100%"></stop>
        </linearGradient>
        <path d="M24.7222222,0 L28.7769586,6.12134402 C28.993892,6.44884334 29.1095766,6.83297587 29.1095766,7.22580645 C29.1095766,7.30043023 29.1054897,7.37409945 29.0975274,7.44660249 C29.6693995,8.36513775 30,9.45041255 30,10.6129032 L30,33 C30,36.3137085 27.3137085,39 24,39 L6,39 C2.6862915,39 1.87739444e-15,36.3137085 0,33 L0,10.6129032 C-1.42312524e-16,9.45083328 0.330361228,8.36592334 0.902289652,7.44696742 C0.852303692,6.9975731 0.953534843,6.52821191 1.22304136,6.12134402 L1.22304136,6.12134402 L5.27777778,0 L24.7222222,0 Z" id="path-3"></path>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-5">
            <stop stop-color="#62D3AB" offset="0%"></stop>
            <stop stop-color="#8DDCC3" offset="100%"></stop>
        </linearGradient>
    </defs>
    <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="全部服务" transform="translate(-73.000000, -460.000000)">
            <g id="编组-12" transform="translate(30.000000, 440.000000)">
                <g id="云药房" transform="translate(43.000000, 20.000000)">
                    <circle id="椭圆形" fill-opacity="0.18" fill="url(#linearGradient-1)" cx="40" cy="40" r="40"></circle>
                    <g id="编组-5" transform="translate(25.000000, 17.000000)">
                        <g id="矩形" transform="translate(0.000000, 8.000000)">
                            <mask id="mask-4" fill="white">
                                <use xlink:href="#path-3"></use>
                            </mask>
                            <use id="蒙版" fill="url(#linearGradient-2)" xlink:href="#path-3"></use>
                            <rect fill="#FFFFFF" opacity="0.767671131" mask="url(#mask-4)" x="-5.625" y="11.1428571" width="26.25" height="16.7142857" rx="1"></rect>
                            <rect fill="#79D8B7" mask="url(#mask-4)" x="9.375" y="13.9285714" width="1.875" height="11.1428571" rx="0.9375"></rect>
                            <path d="M10.3125,13.875 C10.8253358,13.875 11.2410714,14.2907356 11.2410714,14.8035714 L11.2410714,24.1964286 C11.2410714,24.7092644 10.8253358,25.125 10.3125,25.125 C9.79966416,25.125 9.38392857,24.7092644 9.38392857,24.1964286 L9.38392857,14.8035714 C9.38392857,14.2907356 9.79966416,13.875 10.3125,13.875 Z" fill="#79D8B7" mask="url(#mask-4)" transform="translate(10.312500, 19.500000) rotate(90.000000) translate(-10.312500, -19.500000) "></path>
                        </g>
                        <rect id="矩形" fill="url(#linearGradient-5)" transform="translate(14.500000, 4.000000) scale(1, -1) translate(-14.500000, -4.000000) " x="4" y="0" width="21" height="8" rx="2"></rect>
                    </g>
                </g>
            </g>
        </g>
    </g>
</svg>