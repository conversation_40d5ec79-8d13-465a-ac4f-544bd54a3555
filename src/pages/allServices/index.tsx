import React, { useEffect, useCallback, useState } from 'react';
import { fetchJson } from 'src/utils';
import { getBizScene } from 'src/utils/tool';
import './allservice.scss';

const AllServices = (props) => {
  const [serviceData, setServiceData]: any = useState([]);

  const getServiceData = useCallback(() => {
    fetchJson({
      url: '/api/api/v1/patient/classify/queryList',
      type: 'POST',
      data: {
        serviceType: 'normal',
        bizScene: getBizScene(),
      },
      isloading: false,
      success: (res) => {
        const { code, result } = res || {};
        if (code === '0') {
          setServiceData(result);
        }
      },
    });
  }, []);

  useEffect(() => {
    getServiceData();
  }, []);
  const jumpTo = useCallback((url) => {
    window.location.href = url;
  }, []);
  const prefixCls = 'allservice_page';
  return (
    <div className={`${prefixCls}`}>
      {serviceData.map((item, i) => {
        const { contentLists = [] } = item;
        return (
          !!contentLists.length && (
            <div className='service'>
              <p className='service_type'>{item.classifyName}</p>
              <div className='serviceList'>
                {contentLists.map((list) => {
                  const { attachmentDomainList = [] } = list;
                  const attachmentDomain = attachmentDomainList.find((item) => item.bizType === 'serviceContent') || {};
                  return (
                    <div
                      className='service_item'
                      key={`service${list.bizNo}`}
                      onClick={() => {
                        jumpTo(list.contentHref);
                      }}
                    >
                      <img className='icon_service' src={attachmentDomain.attachmentDownloadUrl} />
                      <p>{list.contentName}</p>
                    </div>
                  );
                })}
              </div>
            </div>
          )
        );
      })}
    </div>
  );
};

export default AllServices;
