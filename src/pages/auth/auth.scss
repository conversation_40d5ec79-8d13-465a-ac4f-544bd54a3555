@import 'src/style/index';

.auth_page {
  height: 100vh;
  background: #fff;

  .header {
    position: relative;
    height: r(193);
    padding-top: r(39);
    background: #fafafa;
    border-bottom: r(1) solid rgba(0, 0, 0, 0.05);
    text-align: center;
    font-size: r(14);
    color: rgba(0, 0, 0, 0.7);
    line-height: r(21);

    .auth-tips {
      margin: 0 30px;
    }

    &::after {
      content: '';
      position: absolute;
      width: r(12);
      height: r(12);
      bottom: r(-7);
      left: 50%;
      margin-left: r(-6);
      border-left: r(1) solid rgba(0, 0, 0, 0.05);
      border-bottom: r(1) solid rgba(0, 0, 0, 0.05);
      transform: rotate(-45deg);
      background: inherit;
    }

    .auth_company {
      margin-bottom: r(16);
      @include display-flex;
      @include align-items(center);
      @include justify-content(center);
    }

    .icon_logo {
      width: r(60);
      height: r(60);
      border-radius: r(10);
    }

    .icon_tranfor {
      position: relative;
      width: r(27);
      margin: 0 r(15);
      height: r(4);
      transform: scale(0.7);

      &::before {
        content: '';
        left: 0;
        position: absolute;
        width: r(27);
        height: r(2);
        background: #8d8988;
        border-radius: r(1);
      }

      &::after {
        content: '';
        right: r(5);
        top: r(-9);
        position: absolute;
        width: r(2);
        height: r(14);
        background: #8d8988;
        border-radius: r(1);
        transform: rotate(-60deg);
      }

      &.next_line {
        transform: rotate(180deg) scale(0.7);
      }
    }
  }

  .main {
    margin: r(25) auto 0;
    width: r(315);

    .auth_title {
      font-size: r(18);
      font-weight: bold;
      margin-bottom: r(8);
    }

    .auth_item {
      position: relative;
      padding-left: r(8);
      margin-top: r(4);
      font-size: r(14);
      line-height: r(20);
      color: rgba(38, 34, 31, 0.7);

      &::before {
        position: absolute;
        content: '';
        top: r(8);
        left: 0;
        width: r(3);
        height: r(3);
        border-radius: 50%;
        background: #cbcbcb;
      }
    }
  }

  .agreement_box {
    position: relative;
    padding-left: r(19);
    margin-top: r(30);
    font-size: r(12);
    color: rgba(51, 51, 51, 0.6);

    .shanzhen-label {
      font-size: 14px;
      margin-left: 10px;
    }

    &:not(:first-child) {
      margin-top: r(7);
    }
  }

  .za-checkbox {
    position: absolute;
    left: 0;
    top: 0;
    transform: scale(0.8);
  }

  .za-checkbox__inner {
    border-radius: 50%;
  }

  .link {
    color: var(--theme-primary);
  }

  .btn_auth {
    width: 100%;
    margin-top: r(20);
    height: r(44);
    border-radius: r(22);

    &.shanzhen {
      margin-top: r(46);
    }

    &.za-button--disabled {
      background: rgba(0, 0, 0, 0.3);
      border: none;
      opacity: 1;
    }
  }
}

.auth-modal {
  &__title {
    font-size: r(18);
    font-weight: bold;
    color: #1e1e1e;
  }

  &__content {
    font-size: r(12);
    color: #999;
    line-height: r(17);
  }

  .link {
    color: var(--theme-primary);
  }

  &__footer {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    margin-top: r(25);
  }

  &__btn {
    padding: r(8) r(20);
    text-align: center;
    width: 45%;
    border-radius: r(20);
    border: 1px solid var(--theme-primary);
    color: var(--theme-primary);

    &.agree {
      background-color: var(--theme-primary);
      border: 0;
      font-size: r(18);
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #fff;
    }
  }
}
