import React, { useEffect } from 'react';
import { jumpBeforeAuth } from 'src/utils/auth';
import { Deserialize } from 'src/utils/serialization';
import { THIRD_PARTNER_AUTH_CONFIG } from 'src/utils/staticData';
import {Modal} from 'zarm';
import './auth.scss';
import { fetchJson } from 'src/utils';
import { xflowPushEvent } from 'src/utils/pageTrack';

const CONFIG = {
  // 橄榄枝疫苗授权
  vaccine: THIRD_PARTNER_AUTH_CONFIG.GLZ_VACCINE,
  // 微医预约挂号授权
  register: THIRD_PARTNER_AUTH_CONFIG.WEIYI_SERVICE,
  // 荟宠宝 授权
  HCB: THIRD_PARTNER_AUTH_CONFIG.SSPS_PET,
  // 小鹿中医授权
  xiaolu: THIRD_PARTNER_AUTH_CONFIG.XLZY,
  // 壹点灵 心理测评
  ydltest: THIRD_PARTNER_AUTH_CONFIG.YDL_HEARTASSESS,
  // 车险药诊卡 - 当前由憧泰提供履约
  CXYZK: THIRD_PARTNER_AUTH_CONFIG.CHONGTAI,
};
const PreAuth = (props) => {
  const {
    location: { search = '' },
  } = props;
  const { service = '', partnerCode = '', businessType = '',...other } = Deserialize(search);
  const _config = CONFIG[service] || { partnerCode, businessType };

  // 查询是否在宽限期内
  const queryGracePeriod = () => {
    if(other.policyNo){
      fetchJson({
        url: `/api/api/v1/patient/policy/queryPolicyGracePeriod?policyNo=${other.policyNo}`,
        type: 'POST',
        isloading: true,
      }).then((res) => {
        if(res.code === '0'){
          if(res.result === 'Y'){
            Modal.alert({
              content:'您的购药权益已失效，请及时缴纳保费后可使用权益!',
              cancelText:'我知道了',
              onCancel: () => {
                xflowPushEvent(['click', 'ZAHLWYY_KXQDJFTC_WZDL	', '宽限期点击', { ZAHLWYY_CLICK_CONTENT: '宽限期待缴费弹窗_我知道了按钮' }]);
                jumpBeforeAuth({ ..._config, ...other, isReplacePage: true });
              },
            });
          }else{
            jumpBeforeAuth({ ..._config, ...other, isReplacePage: true });
          }
        }else{
          jumpBeforeAuth({ ..._config, ...other, isReplacePage: true });
        }
      });
    }else{
      jumpBeforeAuth({ ..._config, ...other, isReplacePage: true });

    }

  };
  useEffect(() => {
    queryGracePeriod();
  }, []);

  return <div></div>;
};

export default PreAuth;
