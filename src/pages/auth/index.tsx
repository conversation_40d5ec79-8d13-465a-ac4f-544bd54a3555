import { dmEnv } from '@dm/utils';
import React, { useCallback, useEffect, useState } from 'react';
import { Link } from 'react-router-dom';
import { jumpToApplet, storage } from 'src/utils';
import bridge, { getAliOpenInfo } from 'src/utils/bridge';
import cookies from 'src/utils/cookie';
import { fetchJson } from 'src/utils/fetch';
import format from 'src/utils/format';
import { sendMonitorEvent, xflowPushEvent } from 'src/utils/pageTrack';
import { Deserialize, Serialize } from 'src/utils/serialization';
import { THIRD_PLATFORM_RESOURCECODE, hospitalAlipayAppId, zaFsHealthAlipayAppId } from 'src/utils/staticData';
import validate from 'src/utils/validate';
import { Button, Checkbox, Modal } from 'zarm';
import { checkRights } from './api';
import './auth.scss';

/**
 * 是否枚举
 * 
 * 定义系统中表示"是"和"否"的标准枚举值
 * YES: 表示"是"，使用字符 'Y' 表示
 * NO: 表示"否"，使用字符 'N' 表示
 */
enum YESORNO_ENUM {
  YES = 'Y',
  NO = 'N',
}

/**
 * @description 有些第三方，即使进入了授权流程，也需要自动授权，不需要用户操作
 * - YIFENG 益丰
 * - CHONGTAI 憧泰
 */
const AutoAuthPartnerCodeList = [THIRD_PLATFORM_RESOURCECODE.YIFENG, THIRD_PLATFORM_RESOURCECODE.CHONGTAI];

const Auth = (props) => {
  const [checked, setChecked] = useState(false);
  const [backPassReportChecked, setBackPassReportChecked] = useState(false);
  const [info, setInfo]: any = useState({});
  const [visible, setVisible] = useState(false);

  const {
    location: { search = '', pathname = '' },
  } = props;

  const {
    partnerCode = '', businessType = '',
    bizNo = '', thirdBizNo = '', thirdBizType = '',
    bizId = '', channelPoint = '', target = '',
    relatedBizNo = '',
    backUrl='', city, policyNo, redirectUrl,
    thirdUserId = '', patientId = '',
    decode = '',thirdOrderId,
    isCheckRights, freeTrial = 'N',
    userRightsId,
    inquiryId,
    cardNo,
    orderNo,
    policyPersonDomain,
  } = Deserialize(search);

  /** 自动授权的三方code */
  const isAutoAuth = AutoAuthPartnerCodeList.includes(partnerCode);

  useEffect(() => {
    fetchJson({
      url: '/api/api/v1/patient/partner/cfg/query/list',
      type: 'POST',
      data: {
        option: {
          needAttachment: true,
        },
        partnerCode,
        businessType,
      },
      isloading: true,
      success: (res) => {
        if (res && res.code === '0') {
          const result = (res.result && res.result[0]) || {};
          (result.attchmentList || []).map((item) => {
            result[item.attachmentType] = item.attachmentDownloadUrl;
          });
          result.authNotice = (result.attchmentList || []).filter((item) => item.attachmentType === 'authNotice');
          setInfo(result);
        }
      },
    });
  }, []);

  const saveAuth = useCallback(async () => {
    if(!checked && !isAutoAuth) {
      return setVisible(true);
    }
    xflowPushEvent([
      'click',
      'ZAHLWYY_GHSQY',
      '挂号授权页',
      { ZAHLWYY_CLICK_CONTENT: '挂号授权页_授权' },
    ]);
    const res = await fetchJson({
      url: '/api/api/v1/patient/partner/user/auth/saveUserAuth',
      type: 'POST',
      data: {
        thirdPlatformCode: partnerCode,
        backPassReport: backPassReportChecked ? YESORNO_ENUM.YES : YESORNO_ENUM.NO,
      },
      isloading: true,
    });
    if (res && res.code === '0') {
      const { appId, jumpType, partnerServerUrl } = info;

      const params: any = {
        thirdPlatformCode: partnerCode,
        businessType,
        bizNo,
        thirdBizNo,
        thirdBizType,
        bizId,
        channelPoint,
        target: decode ? decodeURIComponent(target) : target,
        relatedBizNo,
        backUrl,
        city:decodeURIComponent(city),
        policyNo,
        redirectUrl:decodeURIComponent(redirectUrl),
        thirdUserId,
        patientId,
        thirdOrderId,
        userRightsId,
        inquiryId,
        cardNo,
        orderNo,
      };

      if(validate.isFromOwnMiniApplet()) {
        params.actualOpenId = cookies.get('openId') || '';
        params.appCode = 'hy';
      }

      // 前往方伞,如果是从保险小程序跳转，需要带上appCode和openId
      if(partnerCode === THIRD_PLATFORM_RESOURCECODE.ZA_UMBRELLA && validate.isFromZabxMiniApplet()){
        params.actualOpenId = cookies.get('openId') || '';
        params.appCode = 'ZABXMiniApp';
      }

      // 在互医小程序中，前往益丰，授权时需要带上appId和openId。
      if (partnerCode === THIRD_PLATFORM_RESOURCECODE.YIFENG && validate.isFromOwnMiniApplet()) {
        params.actualOpenId = cookies.get('openId') || '';
        params.appCode = storage.get('ownAppletAppid');
      }

      // 在互医小程序中，前往憧泰，授权时需要带上penId。
      if (partnerCode === THIRD_PLATFORM_RESOURCECODE.CHONGTAI && validate.isFromOwnMiniApplet()) {
        params.actualOpenId = cookies.get('openId') || '';
      }

      if (partnerCode === THIRD_PLATFORM_RESOURCECODE.YIYH && validate.isFromOwnMiniApplet()) {
        params.actualOpenId = cookies.get('openId') || '';
        params.isApplet = 'Y';
      }

      if (partnerCode === THIRD_PLATFORM_RESOURCECODE.SANGGUO && validate.isFromOwnMiniApplet()) {
        params.actualOpenId = cookies.get('openId') || '';
        params.isApplet = 'Y';
      }

      // 如果是益丰，且url上有校验商城权益的参数，则需要调用接口获取用户是否有商城权益
      if (partnerCode === THIRD_PLATFORM_RESOURCECODE.YIFENG && isCheckRights === 'Y') {
        const rightCheckRes = await checkRights();
        const { code, result } = rightCheckRes || {};
        if (code === '0' && !result) {
          // 益丰体验版
          if (freeTrial === 'Y') {
            const originTarget = decodeURIComponent(params.target!);
            const [url, qs] = originTarget.split('?');
            const queryParams = qs ? Deserialize(qs) : {};
            if (!queryParams.userLabel || queryParams.userLabel !== '1') {
              queryParams.userLabel = '1';
              const qsModified = Serialize(queryParams);
              params.target = encodeURIComponent(`${url}?${qsModified}`);
            }
          } else {
            location.replace('/hospital/static/no-rights');
            return;
          }
        }
      }

      if (policyPersonDomain) {
        try {
          const policyPersonDomainData = JSON.parse(decodeURIComponent(policyPersonDomain));
          params.policyPersonDomain = policyPersonDomainData;
        } catch (error) {
          sendMonitorEvent(
            'json_parse_error',
            'error',
            JSON.stringify({
              error,
              policyPersonDomain,
            })
          );
        }
      }

      // 当h5处于支付宝小程序时，需要将宿主环境的appId和openId一并给到授权接口（益丰），让后端交给第三方
      if (partnerCode === THIRD_PLATFORM_RESOURCECODE.YIFENG && validate.isAlipayApplet()) {
        const { aliAppletOpenId, aliAppletPartnerCode } = await getAliOpenInfo();
        switch (aliAppletPartnerCode) {
        case 'ZA_ASCLEPIUS_ALIPAY':
          params.appCode = hospitalAlipayAppId;
          break;
        case 'ZA_FSJK':
          params.appCode = zaFsHealthAlipayAppId;
          break;
        default:
          break;
        }
        params.actualOpenId = aliAppletOpenId;
      }

      fetchJson({
        url: '/api/api/v1/patient/partner/user/auth/generateToken',
        type: 'POST',
        data: params,
        isloading: true,
        success: (res) => {
          const { code, result } = res || {};
          if (code === '0') {
            let completeUrl = `${/^https?:\/\/.+/.test(result) ? '' : partnerServerUrl}${result}`;
            if (jumpType === 'applet') {
              jumpToApplet({
                appId,
                partnerServerUrl,
                token: result,
              });
              return;
            }
            // 针对直营APP，在跳转第三方前，把页面标题设置成“众安互联网医院”
            dmEnv.isApp() && bridge.setTitle('众安互联网医院');
            // 小鹿直接返回
            if (partnerCode === THIRD_PLATFORM_RESOURCECODE.XLZY) {
              location.replace(completeUrl);
            } else if (partnerCode === THIRD_PLATFORM_RESOURCECODE.ZA_MALL || partnerCode === THIRD_PLATFORM_RESOURCECODE.ZA_UMBRELLA) {
              const connector = completeUrl.includes('?') ? '&' : '?';
              if (format.GetQueryString('channelResourceCode') === 'JKXSSY2021YYLB') { // 来源于众安健康小程序的
                completeUrl = `${completeUrl}${connector}source=ZA_HEALTH`;
              } else if (dmEnv.isApp()) {
                completeUrl = `${completeUrl}${connector}source=ZA_INSURANCE_APP`;
              } else {
                completeUrl = `${completeUrl}${connector}source=ZA_HOSPITAL&appCode=hy&openId=${cookies.get('openId') || ''}`;
              }
              location.replace(completeUrl);
            } else {
              if (pathname === '/hospital/static/yunyaofang' || thirdBizType === 'detail') {
                if (thirdBizType === 'detail' && thirdBizNo) {
                  const qs = result.replace(/(\/#\/)\?/, (match, p1) => `${p1}pages/wode/goodsDetail?id=${thirdBizNo}&`);
                  completeUrl = `${partnerServerUrl}${qs}`;
                }
                location.replace(completeUrl);
                return;
              }
              location.replace(completeUrl);
            }
          }
        },
      });
    }
  }, [info, checked, backPassReportChecked]);

  useEffect(() => {
    if((checked && visible) || isAutoAuth) {
      setVisible(false);
      saveAuth();
    }
  }, [checked, visible]);

  if (isAutoAuth) {
    return <div></div>;
  }

  /** 是否需要用户授权回传健康体检报告 */
  const showPhysicalReport = info && info.backPassReport === YESORNO_ENUM.YES;

  return (
    <div className='auth_page'>
      <div className='header'>
        <div className='auth_company'>
          <img className='icon_logo' src={info.ourImage} />
          <div>
            <p className='icon_tranfor' />
            <p className='icon_tranfor next_line' />
          </div>
          <img className='icon_logo' src={info.partnerImage} />
        </div>
        <p>即将跳转至{info.partnerName}提供的页面</p>
        <p>后续服务由{info.partnerName}提供</p>
      </div>
      <div className='main'>
        <p className='auth_title'>同意授权以下权限可继续使用服务</p>
        {((info.extraInfo || '').split(';') || []).map((item, i) => (
          item && (
            <p className='auth_item' key={`item${i}`}>
              {item}
            </p>
          )
        ))}
        <div>
          <div className='agreement_box'>
            <Checkbox
              id='agreement'
              checked={checked}
              onChange={(e) => setChecked(e && e.target.checked || false)}
            />
            <label htmlFor='agreement'>
              我已仔细阅读
              {(info.authNotice || []).map((item) => (
                <Link
                  key={`authNotice${item.id}`}
                  className='link'
                  to={{
                    pathname: '/hospital/pdf',
                    search: `url=${encodeURIComponent(item.attachmentUrl)}`,
                  }}
                >
                  {item.attachmentName}
                </Link>
              ))}
              继续即表示我已知悉相关规则与风险并同意相关条款。
            </label>
          </div>
          {
            showPhysicalReport && (
              <div className='agreement_box'>
                <Checkbox
                  id='back-pass-report'
                  checked={backPassReportChecked}
                  onChange={(e) => setBackPassReportChecked(e && e.target.checked || false)}
                />
                <label htmlFor='back-pass-report'>
                  {info.backPassReportTips}
                  {/* 我同意授权第三方回传体检报告给众安互医及相关业务方 */}
                </label>
              </div>
            )
          }
        </div>
        {/* eslint-disable-next-line @typescript-eslint/no-misused-promises */}
        <Button theme='primary' className='btn_auth' onClick={saveAuth}>
          授权
        </Button>
      </div>
      <Modal
        visible={visible}
        title={<span className='auth_modal__title'>信息授权告知</span>}
        onCancel={() => setVisible(false)}
        className='auth-modal'
      >
        <div className='auth-modal__content'>请您仔细阅读{(info.authNotice || []).map((item) => (
          <Link
            key={`authNotice${item.id}`}
            className='link'
            to={{
              pathname: '/hospital/pdf',
              search: `url=${encodeURIComponent(item.attachmentUrl)}`,
            }}
          >
            {item.attachmentName}
          </Link>
        ))}，点击"同意"即表示您已阅读并同意相关规则条款。</div>
        <div className='auth-modal__footer'>
          <div className='auth-modal__btn' onClick={() => setVisible(false)}>不同意</div>
          <div className='auth-modal__btn agree' onClick={() => {
            setChecked(true);
          }}>同意</div>
        </div>
      </Modal>
    </div>
  );
};

export default Auth;
