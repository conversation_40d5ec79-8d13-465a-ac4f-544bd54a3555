import React, { useCallback, useEffect, useState } from 'react';
import { Button, Checkbox } from 'zarm';
import { Deserialize } from 'src/utils/serialization';
// import '@dm/reactCupUi/lib/react-cup-ui.css';
import { fetchJson } from 'src/utils/fetch';
// import { UPButton } from '@dm/reactCupUi';
import { THIRD_PLATFORM_RESOURCECODE } from 'src/utils/staticData';
import format from 'src/utils/format';

import { jumpToApplet } from 'src/utils';
import { dmEnv } from '@dm/utils';
import bridge from 'src/utils/bridge';
import './auth.scss';
import { xflowPushEvent } from 'src/utils/pageTrack';

//废弃

const Auth = (props) => {
  const [checkedA, setCheckedA]: any = useState(true);
  const [checkedB, setCheckedB]: any = useState(true);

  const [info, setInfo]: any = useState({});
  const {
    location: { search = '',pathname = '' },
  } = props;
  const { partnerCode = '', businessType = '', bizNo = '', thirdBizNo = '', thirdBizType = '' } = Deserialize(search);

  useEffect(() => {
    fetchJson({
      url: '/api/api/v1/patient/partner/cfg/query/list',
      type: 'POST',
      data: {
        option: {
          needAttachment: true,
        },
        partnerCode,
        businessType,
      },
      isloading: true,
      success: (res) => {
        if (res && res.code === '0') {
          const result = (res.result && res.result[0]) || {};
          (result.attchmentList || []).map((item) => {
            result[item.attachmentType] = item.attachmentDownloadUrl;
          });
          result.authNotice = (result.attchmentList || []).filter((item) => {
            return item.attachmentType === 'authNotice';
          });
          setInfo(result);
        }
      },
    });
  }, []);

  const saveAuth = useCallback(async () => {
    xflowPushEvent([
      'click',
      'ZAHLWYY_GHSQY',
      '挂号授权页',
      { ZAHLWYY_CLICK_CONTENT: '挂号授权页_授权' },
    ]);
    const res = await fetchJson({
      url: '/api/api/v1/patient/partner/user/auth/saveUserAuth',
      type: 'POST',
      data: {
        thirdPlatformCode: partnerCode,
      },
      isloading: true,
    });
    if (res && res.code === '0') {
      const { appId, jumpType, partnerServerUrl } = info;

      fetchJson({
        url: '/api/api/v1/patient/partner/user/auth/generateToken',
        type: 'POST',
        data: {
          thirdPlatformCode: partnerCode,
          businessType,
          bizNo,
          thirdBizNo,
          thirdBizType
        },
        isloading: true,
        success: (res) => {
          let { code, result } = res || {};
          if (code === '0') {
            if (jumpType === 'applet') {
              jumpToApplet({
                appId,
                partnerServerUrl,
                token: result,
              });
              return;
            }
            //针对直营APP，在跳转第三方前，把页面标题设置成“众安互联网医院”
            dmEnv.isApp() && bridge.setTitle('众安互联网医院');
            //小鹿直接返回
            if (partnerCode === THIRD_PLATFORM_RESOURCECODE.XLZY) {
              location.replace(result);
              //判断三方code是不是上药云药房，是的话要对参数进行转义
              // } else if (partnerCode === THIRD_PLATFORM_RESOURCECODE.SYY_DRUGSTROE) {
              //   console.log('接口result', result)
              //   //demo &cardType=c9KFWEH8pNM8xHUabZTtUw==&cardNumber=dmeJxvpqz2wgw2+k5xNt/Q==&orderNumber=AWtphWFtcPfRiSa2J3z0Vw==
              //   let { projectCode, cardType, cardNumber, orderNumber } = Deserialize(result);
              //   console.log('cardType', cardType, cardNumber, orderNumber)
              //   projectCode = encodeURIComponent(projectCode);
              //   cardType = encodeURIComponent(cardType);
              //   cardNumber = encodeURIComponent(cardNumber);
              //   orderNumber = encodeURIComponent(orderNumber);
              //   let newResult = `&projectCode=${projectCode}&cardType=${cardType}&cardNumber=${cardNumber}&orderNumber=${orderNumber}`;
              //   console.log('转义后的result', result)
              //   location.replace(`${info.partnerServerUrl}${newResult}`);
            } else if (partnerCode === THIRD_PLATFORM_RESOURCECODE.ZA_MALL) {
              const connector = result.includes('?') ? '&' : '?';
              if (format.GetQueryString('channelResourceCode') === 'JKXSSY2021YYLB') { //来源于众安健康小程序的
                result = `${result}${connector}source=ZA_HEALTH`    
              }else if (dmEnv.isApp()) {
                result = `${result}${connector}source=ZA_INSURANCE_APP`; 
              } else {
                result = `${result}${connector}source=ZA_HOSPITAL`; 
              }
              location.href = result //商城专题页面跳转之后点击返回需要回到专题页面
            }else {
              if (pathname === '/hospital/static/yunyaofang' || thirdBizType === 'detail') {
                let completeUrl = `${partnerServerUrl}${result}`;
                if (thirdBizType === 'detail' && thirdBizNo) {
                  const qs = result.replace(/(\/#\/)\?/, (match, p1) => `${p1}pages/wode/goodsDetail?id=${thirdBizNo}&`);
                  completeUrl = `${partnerServerUrl}${qs}`;
                }
                location.replace(completeUrl);
                return ;
              }
              location.replace(`${info.partnerServerUrl || ''}${result}`);
            }
          }
        },
      });
    }
  }, [info]);

  return (
    <div className='auth_page'>
      <div className='header'>
        <div className='auth_company'>
          <img className='icon_logo' src={info.ourImage} />
          <div>
            <p className='icon_tranfor' />
            <p className='icon_tranfor next_line' />
          </div>
          <img className='icon_logo' src={info.partnerImage} />
        </div>
        <p className='auth-tips'>本次服务由 {info.partnerName} 提供，确认授权后，将向其提供以下权限</p>
      </div>
      <div className='main'>
       
        <div className='agreement_box'>
          <Checkbox
            id='agreement'
            checked={checkedA}
            onChange={(e) => setCheckedA(e && e.target.checked)}
          />
          <label className='shanzhen-label'  htmlFor='agreement'>
            您的个人信息，包括：姓名、身份证件号、手机号等，仅用于实名医疗预约
          </label>
        </div>
        <div className='agreement_box'>
          <Checkbox
            id='agreement'
            checked={checkedB}
            onChange={(e) => setCheckedB(e && e.target.checked)}
          />
          <label className='shanzhen-label' htmlFor='agreement'>
            同步众安您的预约信息
          </label>
        </div>
        <Button theme='primary' className='btn_auth shanzhen' disabled={!(checkedA && checkedB)} onClick={saveAuth}>
          确认授权
        </Button>
      </div>
    </div>
  );
};

export default Auth;
