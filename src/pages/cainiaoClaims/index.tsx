/*
 * @description： 阿里非菜鸟-问诊流程
 */
import classnames from 'classnames';
import React, { useEffect, useState, useCallback } from 'react';
import { FixedButton } from 'src/components/common';
import { fetchJson } from 'src/utils/fetch';
import { Deserialize } from 'src/utils/serialization';
import { CDN_PREFIX } from 'src/utils/staticData';
import { Badge, Modal, Icon } from 'zarm';
import './claims.scss';

const prefixCls = 'claimsinquiry-pages';
const processList = [
  {
    title: '节省时间',
    desc: '无需线下排队',
  },
  {
    title: '质保放心药',
    desc: '专业处方开具',
  },
  {
    title: '专业服务',
    desc: '专业医生1v1服务',
  },
];
const Claims = (props) => {
  const {
    location: { search = '' },
  } = props;

  const { patientId = '', policyNo = '' } = Deserialize(search);
  const [state, setDataState] = useState({
    hasInquiryNum: 0,
    hasOrderNum: 0,
    isHasRights: false,
    processingOrder: [],
  });

  // 检查患者是否存在药品订单
  const checkHasOrder = useCallback(() => {
    fetchJson({
      type: 'POST',
      url: '/api/api/v1/patient/order/list/page',
      data: {
        currentPage: 1,
        pageSize: 15,
        policyNo,
        orderType: 'drugs',
        orderStatuses: [1, 3, 5, 6, 9],
      },
    }).then((res) => {
      if (res.code === '0') {
        const { result: { resultList = [] } = {} } = res;
        setDataState((prevState) => ({
          ...prevState,
          hasOrderNum: resultList.length,
        }));
      }
    });
  }, [state]);

  // 检查患者是否存在问诊单
  const checkHasInquiry = useCallback(() => {
    fetchJson({
      type: 'POST',
      url: '/api/api/v1/patient/inquiry/list',
      data: {
        productId: 1,
        inquiryStatusList: [1, 2, 3, 7, 8, 10, 11],
        inquiryType: 'I',
        excludeInquiryTimingTypeList: ['drug_replenish', 'mentalConsult'],
        policyNo,
        payInquiryType: 'text',
      },
    }).then((res) => {
      if (res.code === '0') {
        const { result = [] } = res;
        setDataState((prevState) => ({
          ...prevState,
          hasInquiryNum: result.length,
          processingOrder: result.filter((item) => [2, 3, 7].includes(item.inquiryStatus)),
        }));
      }
    });
  }, [state]);

  const checkDrugDirectPay = useCallback(() => {
    fetchJson({
      type: 'POST',
      url: '/api/api/v1/patient/user/rights/queryAliCaiNiaoEmployeeRights',
      data: {
        bizType: 'inquiry',
      },
      needLoading: true,
    }).then((res) => {
      if (res.code === '0') {
        const { result = [] } = res;
        if (result.length) {
          const hasRight = result.some((item) => item.bizType === 'inquiry');
          setDataState((prevState) => ({
            ...prevState,
            isHasRights: hasRight,
          }));
        } else {
          setDataState((prevState) => ({
            ...prevState,
            isHasRights: false,
          }));
        }
      }
    });
  }, [state]);
  useEffect(() => {
    checkHasInquiry();
    checkHasOrder();
    checkDrugDirectPay();
  }, []);
  const goTo = (url) => {
    props.history.push({
      pathname: url,
      search: `patientId=${patientId}&policyNo=${policyNo}&orderStatus=0`,
    });
  };
  const jumpInquriy = () => {
    goTo('/hospital/inquiryform');
  };
  const goToInquriy = useCallback(() => {
    const { isHasRights, hasInquiryNum } = state;
    if (!isHasRights) {
      Modal.alert({
        className: `${prefixCls}__modal`,
        title: '',
        content: '未查询到与您相关的有效服务权益，您可能无法使用后续服务',
        cancelText: '知道了',
      });
      return;
    } else if (hasInquiryNum) {
      Modal.alert({
        className: `${prefixCls}__modal`,
        content: '当前有进行中的订单，请等待订单结束后再次发起问诊',
        cancelText: '知道了',
      });
      return;
    }
    jumpInquriy();
    return;
  }, [state]);

  return (
    <section className={prefixCls}>
      <header className={`${prefixCls}__hd`}>
        <img className='logo' src={`${CDN_PREFIX}applets/logo/za-logo.png`} />
        <div className='guide-txt-cont'>
          <h3 className='guide-txt'>嗨，欢迎体验在线问诊服务</h3>
        </div>
      </header>
      <div className={`${prefixCls}__core`}>

        <div className={`${prefixCls}__core-col`} onClick={() => goTo('/hospital/myorder')}>
          <p className={classnames('col-icon', { 'not-dot': !state.hasOrderNum })}>
            <Badge shape='dot'>
              <img src={require('./images/drug-icon.png')} alt='' />
            </Badge>
          </p>
          <div className='col-children'>
            <p className='col-title'>药品订单</p>
            <p>
              {state.hasOrderNum ? <span className='has-order'>有{state.hasOrderNum}个订单待完成</span> : <span>查看详情</span>}
              <Icon type='arrow-right' />
            </p>
          </div>
        </div>

        <div className={`${prefixCls}__core-col`} onClick={() => goTo('/hospital/myinquiry')}>
          <p className={classnames('col-icon', { 'not-dot': !state.hasInquiryNum })}>
            <Badge shape='dot'>
              <img src={`${CDN_PREFIX}/icon/icon_inquiry.png`} alt='' />
            </Badge>
          </p>
          <div className='col-children'>
            <p className='col-title'>问诊记录</p>
            <p>
              {state.hasInquiryNum ? <span className='has-order'>有{state.hasInquiryNum}个订单待完成</span> : <span>查看详情</span>}
              <Icon type='arrow-right' />
            </p>
          </div>
        </div>
      </div>
      <div className={`${prefixCls}__process`}>
        <h3>使用流程</h3>
        <div className='process-inner'>
          <p className='step'>
            选择问诊服务
            <i>
              <img src={require('./images/arrow-icon.png')} alt='' />
            </i>{' '}
            发起问诊{' '}
            <i>
              <img src={require('./images/arrow-icon.png')} alt='' />
            </i>{' '}
            开具药品处方
          </p>
          <ul className='process-desc'>
            {processList.map((k, index) => (
              <li key={`item${index}`}>
                <p className='label'>{k.title}</p>
                <p className='value'>{k.desc}</p>
              </li>
            ))}
          </ul>
        </div>
      </div>

      <FixedButton buttonShape='round' prefixCls={`${prefixCls}__fix_button`} buttonClick={() => goToInquriy()} text='发起图文问诊' />
    </section>
  );
};
export default Claims;
