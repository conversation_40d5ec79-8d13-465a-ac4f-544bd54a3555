@import 'src/style/index';
$prefixCls: 'casedetail_page';

.#{$prefixCls} {
  position: relative;
  min-height: 100vh;
  background: linear-gradient(0deg, #fff 0%, #f5f5f5 100%);
  padding-top: r(10);
  font-family: PingFangSC-Regular, PingFang SC;

  &_case {
    white-space: nowrap;
    margin-top: r(10);

    &_item {
      display: inline-block;
      width: r(305);
      height: 506px;
      padding: 0 r(10);
      transition: all 0.2s linear;
      vertical-align: middle;
      opacity: 0.6;
      margin: r(10) 0 0;

      &:first-of-type {
        margin-left: r(35);
      }

      &.active {
        transform: scale(1.08);
        opacity: 1;
      }

      .inner {
        position: relative;
        width: 100%;
        height: 100%;
        background: #fff;
        box-shadow: 0 r(9) r(16) 0 rgba(0, 0, 0, 0.04);
        border-radius: r(8);
        white-space: normal;
        padding: r(10) r(12);
        overflow: hidden;

        .part {
          @include display-flex;

          position: relative;
          padding-top: r(20);

          &:first-of-type {
            padding-bottom: r(20);
            border-bottom: 1px solid rgba($color: #e6e6e6, $alpha: 0.2);
            margin-bottom: r(10);
          }

          .bg_icon {
            position: absolute;
            top: 0;
            left: 0;
            height: r(29);
          }

          .icon {
            position: relative;
            width: r(20);
            height: r(20);
            z-index: 1;
            margin-right: r(6);
          }

          p {
            position: relative;
            z-index: 1;
            font-size: r(13);
            color: #666;
            margin: r(3) 0;
            line-height: r(19.5);
          }

          .q_text {
            font-size: r(14);
            font-family: PingFangSC-Medium, PingFang SC;
            font-weight: 500;
            color: #333;
            line-height: r(21);
          }

          h5 {
            position: relative;
            z-index: 1;
            font-size: r(16);
            font-family: PingFangSC-Semibold, PingFang SC;
            font-weight: 600;
            color: #00a864;
            line-height: r(22.5);
          }

          .subtitle {
            font-size: r(13);
            font-family: PingFangSC-Semibold, PingFang SC;
            font-weight: 600;
            color: #666;
            line-height: r(19.5);
            margin: r(8) 0;
            padding-bottom: r(3);
          }
        }

        .cover {
          position: absolute;
          bottom: -10px;
          left: 0;
          width: 100%;
          height: r(80);
          // background: #fff;
          background: linear-gradient(180deg, rgba(255, 255, 255, 0) 0%, #fff 70%, #fff 100%);
          z-index: 5;
        }
      }
    }
  }

  &_chatbtn {
    width: r(315);
    height: r(44);
    margin: r(45) auto r(15);
    font-family: PingFangSC-Semibold, PingFang SC;
    font-weight: 600;
  }

  &_addbtn {
    width: r(315);
    height: r(44);
    margin: 0 auto r(25);
    font-family: PingFangSC-Semibold, PingFang SC;
    font-weight: 600;

    img {
      width: r(18);
      height: r(18);
    }
  }
}
