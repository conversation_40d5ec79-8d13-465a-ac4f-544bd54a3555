import React, { useEffect, useRef, useState } from 'react';
import { Deserialize } from 'src/utils/serialization';
import { jumpToWechatGuidance } from 'src/utils/auth';
import { SvgIcon } from 'src/components/common';
import { Link } from 'react-router-dom';
import classnames from 'classnames';
import { Button, Drag } from 'zarm';
import './casedetail.scss';
import { shallowEqual, useSelector } from 'react-redux';
import { ApplicationState } from 'src/store';
import { xflowPushEvent } from 'src/utils/pageTrack';

export const CASE = [
  {
    title: '痔疮',
    question: '持续了一个月左右，上完厕所擦拭后感觉有湿乎乎，擦不干净。近1个礼拜有异物感，有点疼，触碰后有肉瘤，不出血。一天2次大便，以往的症状就是大便不成形。',
    diagnosis: '综合评估患处图片、患者描述，诊断为痔疮。',
    opinion: [
      '注意休息，不要久坐久站，不要吃辛辣刺激性食物，不要饮酒，不要熬夜。注意合理饮食，多喝水，多吃富含纤维的蔬菜水果；适当运动，避免久坐久蹲，尽量减少如厕时间，不要在上厕所时玩手机看书，保持大便通畅。',
      '保持肛周卫生，如有条件可以便后温水清洗肛周。每天温水坐浴（女性经期不能坐浴），早晚各一次，水温稍微热点，不烫为宜，屁股泡在水里五分钟左右，这样可改善局部血液循环，保持局部卫生，缓解症状。',
      '用药建议。',
    ],
  },
  {
    title: '痤疮',
    question: '最近开始长痘，持续时间接近一个月了，范围主要在额头、鼻子。痘由起初红肿鼓包到痘内鼓破(白色脓包) 然后自己挤了。大多数痘在相同的位置上反复长。未进行过门诊治疗 有过一断时间使用酸类护肤品，但没有效果。 鼻子一圈闭口较多 用手能轻易挤出来。',
    diagnosis: '根据患者病史及图片，初步诊断为痤疮。',
    opinion: ['可能跟作息不规律、熬夜引起内分泌失调有关；', '不能熬夜喝酒，少吃油腻辛辣刺激性食物与甜食碳酸饮料奶茶纯牛奶之类高糖高脂的东西，日常也需要防晒。', '注意皮肤清洁，可使用氨基酸类界面活性剂等刺激性较小的洁面乳，并选用清爽型不易堵塞毛孔的保湿乳，调整皮肤水油平衡。', '用药建议。'],
  },
  {
    title: '偏头痛',
    question: '头很疼，影响睡眠和生活，反复发作，去看病怎么都不好，不知道怎么才能好起来！',
    diagnosis: '结合病史，线下体格检查、实验室检查及影像学检查，排除继发性头痛，诊断为偏头痛。',
    opinion: [
      '避免头痛诱发因素：要预防偏头疼的发作，首先消除或减少偏头疼的诱因，日常生活中应避免强光线的直接刺激，如避免直视汽车玻璃的反光，避免从较暗的室内向光线明亮的室外眺望。避免对视光线强烈的霓虹灯。避免情绪紧张，避免服用血管扩张剂等药物，避免饮用红酒和进食含奶酪的食物，咖啡、巧克力、熏鱼等。',
      '对有偏头痛的人来说，着重呼吸训练、调息的运动（例如瑜伽、气功），可帮助患者稳定自律神经系统、减缓焦虑、肌肉紧绷等症状。营造安静的环境，维持规律的作息，即使在假日也定时上床、起床。',
      '用药建议。',
    ],
  },
  {
    title: '上呼吸道感染',
    question: '突然开始咳嗽了，而且咳出来的是黄痰，早上起床的痰中有血，喉咙又痛又痒，有鼻涕，鼻涕当中还有一些血丝，没有发热。',
    diagnosis: '根据症状流行病史，目前考虑急性上呼吸道感染',
    opinion: [
      '对症治疗：病情较重或年老体弱者应卧床休息，忌烟、多饮水，室内保持空气流通。如有发热、头痛、肌肉酸痛等症状者，可选用解热镇痛药，如对乙酰氨基酚等。咽痛可用各种喉片如溶菌酶片含服服。鼻塞，鼻黏膜充血水肿时，可使用盐酸伪麻黄碱。感冒时常有鼻黏膜敏感性增高，频繁打喷嚏、流鼻涕，可选用马来酸氯苯那敏等抗组胺药。对于咳嗽症状较明显者，可给予右美沙芬、强力枇杷露等镇咳药。',
      '病因治疗：单纯病毒感染无需使用抗菌药物，有白细胞计数升高、咽部脓苔、咳黄痰等细菌感染证据时，应在医生指导下，酌情使用抗生素。',
      '中药治疗：具有清热解毒和抗病毒作用的中药亦可选用，有助于改善症状，缩短病程。小柴胡冲剂、板蓝根冲剂应用较为广泛。',
      '若每年某段时期出现反复咳嗽，需要行支气管舒张试验、呼吸功能等检查排除咳嗽变应性哮喘、慢性支气管炎等其他疾病。',
    ],
  },
  {
    title: '湿疹',
    question: '手臂和大腿上，出现了很多一粒一粒的小红斑还有丘疹，非常痒，挠了之后会有水泡，破了会有水流出来。',
    diagnosis: '综合评估患处图片、患者描述，诊断为湿疹。',
    opinion: [
      '反复发作可能与没有脱离过敏源有关，可作过敏原检查，如皮肤点刺试验或皮内试验、特异性IgE抗体及斑贴过筛试验等，以发现可能的致敏原。',
      '注意调整饮食，忌食辛辣刺激食物，避免进食易致敏的物品，如酒类，海鲜贝类食物应禁用，以清淡饮食为好；尽量减少外界不良刺激，如手抓，热水烫洗等；衣着应较宽松,轻软,避穿毛制品或尼龙织品。',
      '避免使用含刺激性界面活性剂的洗涤剂，加强皮肤保湿，强化皮肤屏障。',
      '根据皮损情况选用适当剂型和药物。急性湿疹局部生理盐水、3%硼酸或1：2000～1：10000高锰酸钾溶液冲洗、湿敷，炉甘石洗剂收敛、保护。亚急性、慢性湿疹应用合适的糖皮质激素霜剂、焦油类制剂或免疫调节剂，如他克莫司软膏、匹美莫司软膏。继发感染者加抗生素制剂。',
    ],
  },
  {
    title: '月经不调',
    question: '24岁还没结婚，我月经每个月老是拖后一个星期，月经量少，第一天还好，后面几天就稀稀拉拉的很少了，一碰到冷的会停。',
    diagnosis: '结合病史，月经史，线下体格检查、实验室检查、影像学检查及妇科检查，排除全身或女性生殖器病理原因引起的出血，诊断为月经不调。',
    opinion: [
      '月经失调主要是针对病因进行治疗。如果是生活习惯、情绪等因素引起的，首选需要改善不良的生活方式、调整情绪;如果是疾病因素导致的月经失调，则可能需要药物治疗或手术治疗。',
      '一般治疗：戒烟。不熬夜、避免劳累。有些女性因为运动量过大而出现月经改变，需要减少锻炼的强度或频率。如果是情绪因素引起的月经异常，则需要学会自我调节，控制剧烈的情绪波动，可通过有效的方式来缓解压力，必要时行心理咨询也会有所帮助。体重的剧烈变化也会影响月经。体重增加会使身体更难以排卵，所以减肥对治疗月经失调有益，但需要注意的是，极端的、突然的体重减轻也会导致月经不规律。注意保暖、避免受冷受寒，尤其是炎热季节防止过量的冷饮摄入。停止服用可引起月经失调的药物。',
      '药物治疗：由于个体差异大，用药不存在绝对的最好、最快、最有效，除常用非处方药外，应在医生指导下充分结合个人情况选择最合适的药物。药物治疗主要适用于排除子宫与生殖器官器质性病变的月经失调患者。患者需在医生指导下使用以下药物进行治疗。',
    ],
  },
  {
    title: '前列腺炎',
    question: '下腹部有不舒服的感觉，偶尔疼，小便正常去医院做了检查，说是细菌性前列腺炎。',
    diagnosis: '根据患者的病史、症状、直肠指诊、前列腺液检查及四杯试验等检查结果，诊断为前列腺炎。',
    reason: [
      '不规律的性生活：性生活或手淫过度频繁，会引起前列腺处于反复与持续不断的充血和水肿状态;而较长时间性生活中断，会导致前列腺液及代谢产物不能有规律地排出。另外，"忍精不射"或性生活达到高潮与射精之前中断性交，使之不射精，如此不断重复也会引起前列腺的慢性充血。前列腺的过度充血和水肿，有利于病原体感染尿道并经尿道扩散进入前列腺。',
      '不洁性交：病原体通过性交而导致尿道炎，继发感染前列腺。',
      '不良生活方式：久坐、长时间的骑跨动作可以压迫前列腺，导致盆腔、前列腺的充血水肿，可使局部的代谢产物堆积，前列腺腺管阻塞，前列腺液排泄不畅。',
      '不良饮食习惯：酗酒、大量食用辛辣刺激性食物，可引起尿道和前列腺血管扩张和充血，加重炎症反应。',
      '局部受凉：可能导致骨盆区域血循环障碍及肌肉痉挛，促进症状发作。',
    ],
    opinion: [
      '急性细菌性前列腺炎对抗菌药物的反应良好，应立即静脉注射广谱抗生素，迅速控制炎症。',
      '慢性细菌性前列腺炎应根据细菌培养和药敏试验结果选择合适的抗生素。',
      '慢性前列腺炎（慢性骨盆疼痛综合征)的治疗包括生活方式调整、药物治疗、心理治疗、局部理疗等，主要以改善症状、提高生活质量和促进相关功能恢复为目的。',
    ],
  },
];

const CaseDetail = (props) => {
  const {
    location: { search = '' },
  } = props;
  const { currentCase = 0 } = Deserialize(search);
  const prefixCls = 'casedetail_page';
  const [activeIndex, setActiveIndex] = useState(Number(currentCase));
  const translateX: any = useRef(0);
  const carouseItem: any = useRef(null);
  const carouse: any = useRef(null);
  const _CASE_LENGTH = CASE.length;

  const { showAddWechat } = useSelector((state: ApplicationState) => {
    return {
      showAddWechat: state.config.showAddWechat,
    };
  }, shallowEqual);

  useEffect(() => {
    onSlideTo(activeIndex);
    window.scroll({
      top: 0,
      behavior: 'smooth',
    });
  }, []);

  useEffect(() => {
    xflowPushEvent(['click', 'ZAHLWYY_DJDZWY', '大家都在问页', { ZAHLWYY_CLICK_CONTENT: `都在问_${CASE[activeIndex]?.title}` }]);
  }, [activeIndex]);

  // 判断当前是否在最后一页
  const isLastIndex = () => {
    return activeIndex >= _CASE_LENGTH - 1;
  };

  // 判断当前是否在第一页
  const isFirstIndex = () => {
    return activeIndex <= 0;
  };

  // 触屏事件
  const onDragStart = (event, dragState) => {
    event.preventDefault();
    if (activeIndex <= 0) {
      onJumpTo(0);
    } else if (activeIndex >= _CASE_LENGTH - 1) {
      onJumpTo(_CASE_LENGTH - 1);
    }
  };

  const onDragMove = (event, dragState) => {
    event.preventDefault();

    const { offsetX, offsetY } = dragState;
    const distanceX = Math.abs(offsetX);
    const distanceY = Math.abs(offsetY);

    if (distanceX < 5 || (distanceX >= 5 && distanceY >= 1.73 * distanceX)) {
      return false;
    }

    // 在尾页时禁止拖动
    if (isLastIndex() && offsetX < 0) {
      return false;
    }

    // 在首页时禁止拖动
    if (isFirstIndex() && offsetX > 0) {
      return false;
    }

    doTransitionHeight({ offsetX });
    doTransition({ x: translateX.current + offsetX }, 0);
    return true;
  };

  const onDragEnd = (_event, dragState) => {
    event?.preventDefault();
    const { offsetX = 0 } = dragState;
    // 判断滑动临界点
    // 1.滑动距离超过0，且滑动距离和父容器长度之比超过moveDistanceRatio
    const moveDistanceRatio = 0.25;
    // 2.滑动释放时间差低于moveTimeSpan
    // const moveTimeSpan = 600;
    const dom = carouse.current;
    const ratio = Math.abs(offsetX / dom.offsetWidth);
    let _activeIndex = activeIndex;
    if (ratio >= moveDistanceRatio) {
      const action = offsetX > 0 ? 'prev' : 'next';
      _activeIndex = action === 'next' ? _activeIndex + 1 : _activeIndex - 1;
      setActiveIndex(_activeIndex);
    }
    onSlideTo(_activeIndex);
  };

  const onSlideTo = (index) => {
    const animationDuration = 300;
    onMoveTo(index, animationDuration);
  };

  // 静默跳到指定编号
  const onJumpTo = (index) => {
    onMoveTo(index, 0);
  };

  // 移动到指定编号
  const onMoveTo = (index, animationDuration) => {
    const dom = carouseItem.current;
    const maxLength = _CASE_LENGTH;
    translateX.current = -dom.offsetWidth * index;
    doTransition({ x: translateX.current }, animationDuration);

    if (index > maxLength - 1) {
      index = 0;
    } else if (index < 0) {
      index = maxLength - 1;
    }

    setActiveIndex(index);
  };

  // 执行过渡动画
  const doTransition = (offset, animationDuration) => {
    const dom = carouse.current;
    let x = 0;
    let y = 0;

    ({ x } = offset);

    dom.style.WebkitTransformDuration = `${animationDuration}ms`;
    dom.style.transitionDuration = `${animationDuration}ms`;
    dom.style.WebkitTransform = `translate3d(${x}px, ${y}px, 0)`;
    dom.style.transform = `translate3d(${x}px, ${y}px, 0)`;
  };

  const doTransitionHeight = ({ offsetX = 0, init = false }) => {
    const scrollList = document.getElementsByClassName('casedetail_page_case_item');

    if (!scrollList.length) {
      return;
    }

    const radio = Math.abs(offsetX) / (carouseItem.current.offsetWidth - 60);

    if (offsetX < 0) {
      const activeItem: any = scrollList[activeIndex + 1];
      const nowItem: any = scrollList[activeIndex];
      activeItem.style.WebkitTransform = `scale(${Math.min(1.08, 1 + 0.08 * radio)})`;
      activeItem.style.transform = `scale(${Math.min(1.08, 1 + 0.08 * radio)})`;
      nowItem.style.WebkitTransform = `scale(${Math.max(1, 1.08 - 0.08 * radio)})`;
      nowItem.style.transform = `scale(${Math.max(1, 1.08 - 0.08 * radio)})`;
    }

    if (offsetX > 0) {
      const activeItem: any = scrollList[activeIndex - 1];
      const nowItem: any = scrollList[activeIndex];
      activeItem.style.WebkitTransform = `scale(${Math.min(1.08, 1 + 0.08 * radio)})`;
      activeItem.style.transform = `scale(${Math.min(1.08, 1 + 0.08 * radio)})`;
      nowItem.style.WebkitTransform = `scale(${Math.max(1, 1.08 - 0.08 * radio)})`;
      nowItem.style.transform = `scale(${Math.max(1, 1.08 - 0.08 * radio)})`;
    }

    if (init) {
      const prevItem: any = scrollList[activeIndex - 1];
      const activeItem: any = scrollList[activeIndex];
      const forwardItem: any = scrollList[activeIndex + 1];

      prevItem && (prevItem.style.transform = 'scale(1)');
      prevItem && (prevItem.style.WebkitTransform = 'scale(1)');

      forwardItem && (forwardItem.style.transform = 'scale(1)');
      forwardItem && (forwardItem.style.WebkitTransform = 'scale(1)');

      activeItem && (activeItem.style.transform = 'scale(1.08)');
      activeItem && (activeItem.style.WebkitTransform = 'scale(1.08)');
    }
  };

  const transitionEnd = () => {
    const dom = carouseItem.current;
    translateX.current = -dom.offsetWidth * activeIndex;
    console.log(-dom.offsetWidth);
    doTransition({ x: translateX.current }, 0);
    doTransitionHeight({ init: true });
  };

  return (
    <div className={prefixCls}>
      <Drag onDragStart={onDragStart} onDragMove={onDragMove} onDragEnd={onDragEnd}>
        <div ref={(ele) => (carouse.current = ele)} className={`${prefixCls}_case`} onTransitionEnd={transitionEnd}>
          {CASE.map((item, i) => {
            return (
              <div ref={(ele) => (carouseItem.current = ele)} className={classnames(`${prefixCls}_case_item`, { active: activeIndex === i })} key={+i}>
                <div className='inner'>
                  <div className='part'>
                    <SvgIcon className='bg_icon' type='img' src={require('./images/bg_q.png')} />
                    <SvgIcon className='icon' type='img' src={require('./images/q.png')} />
                    <p className='q_text'>{item.question}</p>
                  </div>
                  <div className='part'>
                    <SvgIcon className='bg_icon' type='img' src={require('./images/bg_a.png')} />
                    <SvgIcon className='icon' type='img' src={require('./images/a.png')} />
                    <div>
                      <h5>医管家答复</h5>
                      <p className='subtitle'>诊断结果:</p>
                      <p>{item.diagnosis}</p>

                      {item.reason && !!item.reason.length && (
                        <>
                          <p className='subtitle'>诱发因素:</p>
                          {item.reason.map((text, i) => (
                            <p key={+i}>
                              {i + 1}、{text}
                            </p>
                          ))}
                        </>
                      )}

                      {item.opinion && !!item.opinion.length && (
                        <>
                          <p className='subtitle'>诊断意见:</p>
                          {item.opinion.map((text, i) => (
                            <p key={+i}>
                              {i + 1}、{text}
                            </p>
                          ))}
                        </>
                      )}
                    </div>
                  </div>
                  <div className='cover'></div>
                </div>
              </div>
            );
          })}
        </div>
      </Drag>
      <Link to={{ pathname: '/hospital/chatmedicalmanage' }}>
        <Button
          className={`${prefixCls}_chatbtn`}
          block
          theme='primary'
          shape='round'
          onClick={() => {
            xflowPushEvent(['click', 'ZAHLWYY_DJDZWY', '大家都在问页', { ZAHLWYY_CLICK_CONTENT: `都在问_去咨询_${CASE[activeIndex].title}` }]);
          }}
        >
          去咨询医管家
        </Button>
      </Link>
      {showAddWechat && (
        <Button
          className={`${prefixCls}_addbtn`}
          block
          theme='primary'
          ghost
          shape='round'
          onClick={() => {
            //拉起小程序
            // jumpToWechatGuidance('6669');
            jumpToWechatGuidance();

          }}
        >
          加医管家微信
        </Button>
      )}
    </div>
  );
};

export default CaseDetail;
