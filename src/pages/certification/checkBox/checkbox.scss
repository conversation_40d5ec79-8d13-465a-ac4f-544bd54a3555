@import "src/style/index";

.checkbox-component {
  input[type="checkbox"] {
    display: none;
  }

  input[type="checkbox"] + .checkbox_label {
    position: relative;
    padding-left: r(28);
    color: #fff;
    font-size: r(24);
    cursor: pointer;
  }

  input[type="checkbox"] + .checkbox_label::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: r(24);
    height: r(24);
    border: 2px solid #c6c6c6;
    border-radius: 50%;
  }

  input[type="checkbox"] + .checkbox_label::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: r(24);
    height: r(24);
    zoom: 0;
    background-color: var(--theme-primary);
    border-radius: 50%;
    transition: all 0.2s linear;
    opacity: 0;
  }

  input[type="checkbox"] + .checkbox_label svg {
    position: absolute;
    top: r(0);
    right: r(1);
    z-index: 1;
  }

  input[type="checkbox"] + .checkbox_label #check-icon {
    stroke: #fff;
    stroke-dasharray: 36;
    stroke-dashoffset: 36;
  }

  input[type="checkbox"]:checked + .checkbox_label::after {
    zoom: 1;
    opacity: 1;
  }

  input[type="checkbox"]:checked + .checkbox_label #check-icon {
    animation-name: check-animation;
    animation-duration: 0.2s;
    animation-delay: 0.1s;
    animation-fill-mode: forwards;
    animation-timing-function: cubic-bezier(1, 0.12, 0.96, 0.62);
  }

  @keyframes check-animation {
    0% {
      stroke-dashoffset: 36;
    }

    100% {
      stroke-dashoffset: 0;
    }
  }
}
