import classnames from 'classnames';
import React, { useEffect, useState, useCallback } from 'react';
import { shallowEqual, useSelector, useDispatch } from 'react-redux';
import { Link } from 'react-router-dom';
import { StaticToast, SvgIcon, Card } from 'src/components/common';
import { ApplicationState } from 'src/store';
import { clear_patient, edit_patient_info } from 'src/store/patients/action';
import { clear_patient_list } from 'src/store/patients/action';
import { cookies } from 'src/utils';
import { fetchJson } from 'src/utils/fetch';
import format from 'src/utils/format';
import { Deserialize, TransformEnumArray } from 'src/utils/serialization';
import validate from 'src/utils/validate';
import { Cell, Input, Select, Picker, DatePicker, Button } from 'zarm';

import './certification.scss';

const EditPatientStepOne = (props) => {
  const { location: { search = '' } } = props;
  const { pathname = '', jumpSearch = '', refer = '' } = Deserialize(search);

  useEffect(() => {
    const fetchPatientNo = async () => {
      const res = await fetchJson({
        type: 'POST',
        url: '/api/api/v1/patient/bizno/getByType',
        data: { bizNoType: 'PATIENT_NO' },
      });

      if (res && res.code === '0') {
        const { result = '' } = res;
        dispatchEditPatientInfo('patientNo', result);
        dispatchEditPatientInfo('patientCertType', 'I');
      }
    };
    dispatchClearPatientList();
    dispatchClearPatient();
    setTimeout(() => {
      fetchPatientNo();
    });
  }, []);
  // 控制出生日期和性别是否可编辑 当身份证和身份证号码正确时 默认带出 不给选择
  const [IdNumberDisabled, setIdNumberDisabled] = useState(false);
  const [visiblePicker, setOpenPicker] = useState(false);
  const [birthdayVisiblePicker, setBirthdayVisiblePicker] = useState(false);
  const editPatientData = useSelector((state: ApplicationState) => state.patients.editPatient || {}, shallowEqual);

  const {
    BLOODSUGARMONITORTIMEENUM=[],
    GENDER,
    SOCIALSECURITYTYPE,
    CERTTYPE,
  }: any = useSelector((state: ApplicationState) => {
    const {
      BLOODSUGARMONITORTIMEENUM_OBJ={},
      CERTTYPE = [],
      PATIENTRELATION=[],
      GENDER=[],
      SOCIALSECURITYTYPE=[],
      BLOODSUGARMONITORTIMEENUM=[],
    } = state.dictionary;
    return {
      PATIENTRELATIONT: TransformEnumArray(PATIENTRELATION, 'resValue', 'resName'),
      GENDER: TransformEnumArray(GENDER, 'resValue', 'resName'),
      SOCIALSECURITYTYPE: TransformEnumArray(SOCIALSECURITYTYPE, 'resValue', 'resName'),
      BLOODSUGARMONITORTIMEENUM: TransformEnumArray(BLOODSUGARMONITORTIMEENUM, 'resValue', 'resName'),
      BLOODSUGARMONITORTIMEENUM_OBJ,
      CERTTYPE: TransformEnumArray(CERTTYPE, 'resCode', 'resName'),
    };
  });

  const {
    patientRelation = '',
    patientName = '',
    patientCertType = '',
    patientCertNo = '',
    patientGender = '',
    patientSocialSecurityType = '',
    patientWeight = '',
    patientBirthday = '',
    isRelative = false,
  } = editPatientData;


  const dispatch = useDispatch();
  const dispatchClearPatientList = () => dispatch(clear_patient_list());
  const dispatchEditPatientInfo = (key: string, value: any) => dispatch(edit_patient_info(key, value));
  const dispatchClearPatient = () => dispatch(clear_patient());

  useEffect(() => {
    if (patientCertType === 'I' && validate.isIdCard(patientCertNo)) {
      const value: any = validate.isIdCard(patientCertNo);
      setIdNumberDisabled(true);
      dispatchEditPatientInfo('patientBirthday', value.birthday);
      dispatchEditPatientInfo('patientGender', value.sex);
      dispatchEditPatientInfo('patientSocialSecurityType', '99');
    } else {
      setIdNumberDisabled(false);
      dispatchEditPatientInfo('patientBirthday', '');
      dispatchEditPatientInfo('patientGender', '');
    }
  }, [patientCertType, patientCertNo, patientRelation]);

  const patientInfoChange = useCallback(
    (key: string, value: any, args?: any) => {
      if (IdNumberDisabled && key === 'patientGender') {
        return;
      }
      if (key === 'patientDistrictCode') {
        dispatchEditPatientInfo('patientDistrictName', args);
      }
      dispatchEditPatientInfo(key, value);
    },
    [IdNumberDisabled],
  );

  const changePatientWeight = useCallback(
    (value) => {
      if (/^(\d+|\d+\.\d{0,2})$/.exec(value) || value === '') {
        patientInfoChange('patientWeight', value);
      }
    },
    [patientWeight],
  );

  // 校验当前页面的信息录入
  const validata = (editPatientData: any, isRelative) => {
    const {
      patientNo = '', patientName = '',
      patientCertType = '', patientCertNo = '',
      patientCertNoCOVER = '',
      patientWeight = '', patientBirthday = '',
    } = editPatientData;

    const isFIll = isRelative && patientCertNoCOVER === patientCertNo;
    const age = Number(patientBirthday ? format.GetAgeByBirthday(format.date(patientBirthday, 'yyyy-MM-dd')) : '');
    !isFIll && patientInfoChange('relativesId', '');

    if (patientNo === '') {
      StaticToast.warning('实名认证异常');
      return false;
    } else if (patientName.trim() === '') {
      StaticToast.warning('您还未输入姓名');
      return false;
    } else if (!validate.isUsername(patientName)) {
      StaticToast.warning('您输入的姓名格式错误');
      return false;
    } else if (patientCertNo.trim() === '') {
      StaticToast.warning('您还未输入证件号码');
      return false;
    } else if (patientCertType === 'I' && !validate.isIdCard(patientCertNo) && !isFIll) {
      StaticToast.warning('您输入的证件号码格式错误');
      return false;
    } else if(patientCertType !== 'I' && !validate.isNumberAndWord(patientCertNo) && !isFIll) {
      StaticToast.warning('您输入的证件号码格式错误');
      return false;
    } else if(patientCertType !== 'I' && !patientGender.trim()) {
      StaticToast.warning('您还未选择性别');
      return false;
    } else if(patientCertType !== 'I' && !patientBirthday.trim()) {
      StaticToast.warning('您还未输入出生日期');
      return false;
    } else if (patientWeight === '' && age >= 0 && age <= 12 && !isFIll) {
      StaticToast.warning('您还未输入体重');
      return false;
    } else if (Number(patientWeight) > 1000) {
      StaticToast.warning('体重最大为1000KG');
      return false;
    }
    return true;
  };

  const savePatientClick = useCallback(() => {
    if (validata(editPatientData, isRelative)) {
      fetchJson({
        url: '/api/api/v1/patient/patient/save',
        type: 'POST',
        data: {
          patientRelation: 1,
          ...editPatientData,
        },
        isloading: true,
        success: (res) => {
          if (res && res.code === '0') {
            dispatchClearPatientList();
            dispatchClearPatient();
            cookies.set('isRealAuth', 'Y');
            if (validate.isFromMiniApplet()) {
              if (pathname) {
                props.history.replace({
                  pathname: decodeURIComponent(pathname),
                  search: decodeURIComponent(jumpSearch),
                });
              } else {
                wx.miniProgram.switchTab({
                  url: '/pages/index',
                });
              }
            } else {
              // 特殊处理高济商城
              if (refer === 'gjMall') {
                props.history.goBack();
                return;
              }
              if (/http/.test(decodeURIComponent(pathname))) {
                location.href = decodeURIComponent(pathname);
              } else {
                props.history.replace({
                  pathname: decodeURIComponent(pathname),
                  search: decodeURIComponent(jumpSearch),
                });
              }
            }
          } else {
            const { message = '授权失败' } = res;
            cookies.set('isRealAuth', 'N');
            StaticToast.warning(message);
          }
        },
        error: (e) => { },
      });
    }
  }, [editPatientData]);

  const blur = useCallback(() => {
    const OBody = (document.documentElement || window || document.body);
    window.scroll({
      top: OBody.scrollTop,
      behavior: 'smooth',
    });
  }, []);

  const prefixCls = 'editpatient_page';

  return (
    <div className={`${prefixCls}`}>
      <div className={`${prefixCls}__guide`}>
        <h3>实名认证，确保您的健康服务安全合规</h3>
      </div>
      <Card prefixCls={`${prefixCls}__card`}>
        <Cell className={`input ${prefixCls}__cell`} title='姓名'>
          <Input
            type='text'
            placeholder='请输入真实姓名'
            onChange={(value) => {
              patientInfoChange('patientName', value);
            }}
            value={patientName}
            maxLength={15}
          />
        </Cell>
        <Cell className={`input ${prefixCls}__cell`} title='证件类型' hasArrow>
          <Select
            hasArrow={false}
            dataSource={CERTTYPE} placeholder='应监管要求录入'
            onOk={
              (selected: Array<{value: string, label: string}> = []) => {
                patientInfoChange('patientCertType', (selected[0] || {}).value);
              }
            }
            value={patientCertType}
          />
        </Cell>
        <Cell className={`input ${prefixCls}__cell`} title='证件号码'>
          <Input
            placeholder={patientCertType === 'Q' ? '婴幼儿可填写出生日期，格式19990101' : '应监管要求录入证件号码'}
            maxLength={patientCertType ==='I' ? 18 : 30}
            onChange={(value) => {
              patientInfoChange('patientCertNo', value);
            }}
            value={patientCertNo}
            onBlur={() => blur()}
          />
        </Cell>
        {
          patientCertType !== 'I' &&
          <>
            <Cell className={`input ${prefixCls}__cell`} title='性别'>
              <div className={`${prefixCls}__cell-relation`}>
                {GENDER.map((k, i) => (
                  <div
                    onClick={() => patientInfoChange('patientGender', k.value)}
                    className={classnames('gender button', {
                      'not-selected': patientGender !== k.value,
                      'disabled': IdNumberDisabled,
                    })}
                    key={`k${i}${k.value}`}
                  >
                    <SvgIcon width={15}
                      height={15}
                      className='svg'
                      src={require(`./images/sprite-icon-${k.value == 'M' ? 'boy' : 'girl'}.svg`)} />
                    {k.label}
                  </div>
                ))}
              </div>
            </Cell>
            <Cell
              className={`input ${prefixCls}__cell`}
              title='出生日期'
              hasArrow
              onClick={() => setBirthdayVisiblePicker(true)}>
              <div className='za-select__value'>{patientBirthday}</div>
              <DatePicker
                title='出生日期'
                max={new Date()}
                visible={birthdayVisiblePicker}
                value={patientBirthday ? new Date(patientBirthday) : null}
                onOk={(value) => {
                  setBirthdayVisiblePicker(false);
                  patientInfoChange('patientBirthday', format.date(value, 'yyyy-MM-dd'));
                }}
                onCancel={() => setBirthdayVisiblePicker(false)}
              />
            </Cell>
          </>
        }

        <Cell className={`input ${prefixCls}__cell`} title='体重(kg）'>
          <Input
            type='text'
            placeholder='12岁以下青少年需要录入此字段'
            onChange={(value) => changePatientWeight(value)}
            value={patientWeight}
            maxLength={6}
            onBlur={() => blur()}
          />
        </Cell>
      </Card>
      <div style={{ display: 'none' }}>
        <Cell className={`select ${prefixCls}__cell`} title='性别'>
          <div className={`${prefixCls}__cell-relation`}>
            {GENDER.map((k, i) => (
              <div
                onClick={() => patientInfoChange('patientGender', k.value)}
                className={classnames('gender button', {
                  'not-selected': patientGender !== k.value,
                  'disabled': IdNumberDisabled,
                })}
                key={`k${i}${k.value}`}
              >
                <SvgIcon width={15}
                  height={15}
                  className='svg'
                  src={require(`./images/sprite-icon-${k.value == 'M' ? 'boy' : 'girl'}.svg`)} />
                {k.label}
              </div>
            ))}
          </div>
        </Cell>
        <Cell className={`select ${prefixCls}__cell required cell-security`} title='社保类型'>
          <div className={`${prefixCls}__cell-relation`}>
            {SOCIALSECURITYTYPE.map((k, i) => (
              <div
                onClick={() => patientInfoChange('patientSocialSecurityType', k.value)}
                className={classnames('security-button button', {
                  'not-selected': patientSocialSecurityType !== k.value,
                })}
                key={`k${i}${k.value}`}
              >
                {k.label}
              </div>
            ))}
          </div>
        </Cell>
      </div>
      <Picker
        visible={visiblePicker}
        dataSource={BLOODSUGARMONITORTIMEENUM}
        itemRender={(item: any) => item.label}
        onOk={() => {
          setOpenPicker(false);
        }}
        onCancel={() => setOpenPicker(false)}
      />
      <div className='btn-wrapper'>
        <Button className='submit' theme='primary' onClick={() => savePatientClick()} block>确定</Button>
        <p className='know'>点击确定，即代表您已同意<Link to={{ pathname: '/hospital/agreement' }} style={{ color: '#309EEB' }}>《用户信息授权协议》</Link></p>
      </div>
    </div>
  );
};

export default EditPatientStepOne;
