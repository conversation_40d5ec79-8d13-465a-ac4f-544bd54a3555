import React, { useCallback, useState, useLayoutEffect } from 'react';
import { Deserialize } from 'src/utils/serialization';
import { StaticToast } from 'src/components/common';
import { fetchJson } from 'src/utils/fetch';
import validate from 'src/utils/validate';
import throttle from 'src/utils/throttle';
import cookies from 'src/utils/cookie';
import classnames from 'classnames';
import CCheckbox from './checkBox';
import { Button } from 'zarm';
import './certification.scss';

const certification = (props) => {
  /**
   * eventUrl: String
   * 目前进入实名页面的按钮 一共有三个 【我的家人】【视频问诊】【权益模块】，
   * 根据eventUrl参数来判断实名，认证成功后的跳转，
   * 增加这个参数的时候注意同时匹配小程序和H5两个路径
   */
  const { location: { search = '' } } = props;
  const { pathname = '', jumpSearch = '', refer = '' } = Deserialize(search);

  const [selfPatient, setSelfPatient]: [any, any] = useState([]);
  const [selectPatientId, setSelectPatientId] = useState('');

  useLayoutEffect(() => {
    const fetchSelfPatient = async () => {
      const res = await fetchJson({
        type: "POST",
        url: '/api/api/v1/patient/patient/querySelfPatients',
        data: {}
      });

      if (res && res.code === '0') {
        const { result = [] } = res;
        setSelfPatient(result);
      }
    }
    fetchSelfPatient();
  }, []);

  const handleSelect = useCallback((e, data) => {
    e.preventDefault();
    setSelectPatientId(data.id);
  }, [])

  const submit = throttle(useCallback(() => {
    fetchJson({
      url: '/api/api/v1/patient/patient/syncRealName',
      type: 'GET',
      data: { selectPatientId },
      isloading: true,
      success: res => {
        if (res && res.code === '0') {
          setTimeout(() => {
            cookies.set('isRealAuth', 'Y');
            StaticToast.success('信息绑定成功');
            if (validate.isFromMiniApplet()) {
              if (pathname) {
                props.history.replace({
                  pathname: decodeURIComponent(pathname),
                  search: decodeURIComponent(jumpSearch)
                })
              } else {
                wx.miniProgram.switchTab({
                  url: '/pages/index'
                });
              }
            } else {

              //特殊处理高济商城
              if (refer === 'gjMall') {
                props.history.goBack();
                return;
              }

              if (/http/.test(decodeURIComponent(pathname))) {
                location.href = decodeURIComponent(pathname);
              } else {
                props.history.replace({
                  pathname: decodeURIComponent(pathname),
                  search: decodeURIComponent(jumpSearch)
                })
              }
            }
          }, 2000);
        } else {
          const { message = '授权失败' } = res;
          StaticToast.warning(message);
        }
      },
      error: (e) => { }
    });
  }, [selectPatientId]), 2000);

  if (!selfPatient.length) {
    return null;
  }

  return (
    <div className='certification_select_page'>
      <h5>检测到当前登录手机号下有多个个人信息，请为账号绑定唯一个人信息</h5>
      {selfPatient.map(item => {
        return (
          <div className={classnames('info_item', { select: item.id === selectPatientId })} key={item.id} onClick={e => handleSelect(e, item)}>
            <div>
              <p>{item.patientName || ''}</p>
              <p className='certno'>{item.patientCertNo || ''}</p>
            </div>
            <CCheckbox className='check' id={item.id} checkClick={e => handleSelect(e, item)} checked={item.id === selectPatientId} />
          </div>
        )
      })}

      <div className="fixedbutton_component">
        <div className='fixed_part'>
          <Button className='submit' theme='primary' shape='round' onClick={() => submit()} block disabled={!selectPatientId}>绑定选中个人信息</Button>
        </div>
      </div>
    </div>
  )
};

export default certification;
