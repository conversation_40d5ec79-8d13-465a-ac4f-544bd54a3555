@import "src/style/index";

.chat_ygj_page {
  position: relative;
  height: 100vh;
  width: 100%;
  background: #f5f5f5;
  flex-direction: column;

  &_title {
    position: absolute;
    top: 0;
    width: 100%;
    background-color: #fff;
    font-size: r(11);
    font-family: PingFangSC, PingFang SC;
    font-weight: 400;
    color: #999;
    text-align: center;
    padding: r(6) 0;
    z-index: 100;
  }

  .msg-newline {
    display: flex;
    align-items: center;
    padding: 0 r(15);
    padding-bottom: r(20);

    .msg-text {
      font-size: r(12);
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      color: rgba(0, 0, 0, 0.4);
      padding: 0 r(8);
    }

    .msg-line {
      height: 1px;
      background-color: #e8e8e8;
      flex: 1;
      padding: 0 r(15);
    }
  }

  .text_main_color {
    color: #00a864;
  }

  .header {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    z-index: 2;
    text-align: center;
    // overflow: hidden;

    // &::after {
    //   content: '';
    //   position: absolute;
    //   width: r(32);
    //   height: r(32);
    //   background: inherit;
    //   bottom: r(-6);
    //   left: 50%;
    //   margin-left: r(-16);
    //   border-radius: 18%;
    //   transform: rotate(-26deg) skew(35deg) translateX(r(-3));
    //   box-shadow: 0 10px 10px -10px rgba(0, 0, 0, 0.05);
    // }

    .assistant_point {
      position: relative;
      padding: r(2) r(15);
      font-size: r(11);
      color: #999;
      text-align: center;
      background: #fff;
      z-index: 1;
    }

    .status_wrap {
      height: r(24);
      line-height: r(24);
    }

    .status {
      position: relative;
      display: inline-block;
      padding: 0 r(15) 0 r(27);
      font-size: r(12);
      background: #fff;
      border-radius: 0 0 r(10) r(10);
      box-shadow: 0 r(1) r(5) 0 rgba(0, 0, 0, 0.05);

      .icon_rest {
        position: absolute;
        height: r(8);
        width: r(8);
        left: r(14);
        top: r(7);
        border-radius: 50%;
        color: #fff;
        background: #ffb72b;

        &::before {
          content: '';
          position: absolute;
          left: r(1);
          top: r(3);
          width: r(6);
          height: r(2);
          background: #fff;
          box-shadow: 0 r(-5) r(5) 0 rgba(0, 0, 0, 0.05);
        }
      }

      &.active {
        &::before {
          content: '';
          position: absolute;
          height: r(5);
          width: r(5);
          left: r(15);
          top: r(9);
          border-radius: 50%;
          background: var(--theme-primary);
        }
      }

      &::after {
        content: '';
        position: absolute;
        left: r(-3);
        top: r(-6);
        width: 100%;
        box-sizing: content-box;
        padding: 0 r(3);
        height: r(8);
        border-radius: r(100);
        background: inherit;
        box-shadow: 0 r(-5) r(5) 0 rgba(0, 0, 0, 0.05);
      }
    }

    .add_wechat {
      color: #00a864;
      padding-left: r(4);
    }

    .icon_arrow {
      position: relative;
      display: none;
      height: r(12);
      width: r(12);
      right: r(10);
      margin-left: r(-15);
      transform: rotate(90deg);
    }

    &.family_doctor {
      .status {
        padding-right: r(30);
      }

      .icon_arrow {
        display: inline-block;
      }
    }

    // .narrow {
    //   position: absolute;
    //   width: r(40);
    //   height: r(20);
    //   left: 50%;
    //   margin-left: r(-20);
    //   bottom: r(-6);
    //   z-index: 1;

    //   &::before,
    //   &::after {
    //     content: '';
    //     position: absolute;
    //     height: r(10);
    //     width: 0;
    //     left: 50%;
    //     bottom: r(9);
    //     border-left: r(2) solid #cdcdcd;
    //   }

    //   &::before {
    //     transform: rotate(-60deg) translateX(r(-8));
    //   }

    //   &::after {
    //     transform: rotate(60deg) translateX(r(8));
    //   }
    // }
  }

  .doctorinfo_header {
    position: absolute;
    width: 100%;
    left: 0;
    top: 0;
    box-shadow: 0 2px 10px 0 rgba(0, 0, 0, 0.03);
    z-index: 3;
    background: #fff;
    height: 0;
    overflow: hidden;
    transition: height 0.5s;

    &.show {
      height: auto;
    }

    .doctorinfo_content {
      padding: r(16) r(15) r(20);

      .doctor-connect {
        display: flex;

        .doctor-information {
          flex: 1 1 auto;

          .doctor-sign {
            margin: r(3) 0 r(4);
          }
        }

        .doctor-Avatar {
          width: r(67);
          text-align: center;

          .doctorinfo_wechat {
            display: flex;
            align-items: center;
            justify-content: center;
            height: r(20);
            margin-top: r(7);
            font-size: r(12);
            color: #fff;
            background: #00bc70;
            border-radius: r(9.5);
            font-family: PingFangSC-Semibold, PingFang SC;

            .doctorinfo_icon_wechat {
              width: r(12);
              height: r(12);
              // color: #fff;
              margin-right: 3px;
              // --wechat-svg-color: #fff;
            }
          }
        }
      }
    }

    .header_avatar {
      width: r(43);
      height: r(43);
    }

    .doctorinfo_title {
      display: flex;
      align-items: center;
      line-height: r(20);
      font-size: r(17);
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: bold;
      color: #333;

      .free-tip {
        display: flex;
        align-items: center;
        color: #00a864;
        font-size: r(12);
        font-weight: normal;
        vertical-align: middle;

        .dot {
          display: inline-block;
          width: r(4);
          height: r(4);
          margin: 0 r(6);
          background-color: #00bc70;
          border-radius: 50%;
        }
      }
    }

    .doctorinfo_sign {
      display: inline-block;
      height: r(20);
      width: r(153);
    }

    .info_round {
      font-size: r(13);
      // transform: scale(0.85);
      // vertical-align: text-top;
    }

    .doctorinfo_tag {
      line-height: r(17);
      color: #333;
      font-size: r(11);
    }

    // .doctorinfo_icon_arrow {
    //   position: absolute;
    //   height: r(16);
    //   width: r(16);
    //   right: r(15);
    //   top: r(32);
    // }

    .icon_fold {
      position: absolute;
      height: r(15);
      width: r(44);
      left: 50%;
      bottom: r(0);
      margin-left: r(-22);
    }

    .doctorinfo_breif {
      display: flex;
      align-items: center;
      margin-top: r(6);
      font-size: r(12);
      color: #999;
      // @include line(2);
    }

    .doctorinfo_more {
      position: absolute;
      right: r(15);
      bottom: r(23);
      background: #fff;
      font-size: r(12);
      color: #00a864;
      padding-left: r(3);
    }

    .look-more {
      margin-left: 5px;
      margin-right: 3px;
      color: #00a864;
    }

    .doctorinfo_more_arrow {
      height: r(14);
      width: r(14);
      margin-top: -3px;
      color: #00a864;
    }
  }

  .main {
    position: absolute;
    top: 0;
    bottom: 0;
    width: 100%;
    padding: r(50) 0 r(105);
    overflow-y: scroll;

    .pull_msg_wrapper {
      min-height: 100%;
      padding-bottom: r(15);

      &::-webkit-scrollbar {
        display: none;
      }
    }

    .custom_control_pull {
      padding-bottom: r(10);
      font-size: r(12);
      color: #909090;
    }

    .no_message_tip {
      height: r(40);
      line-height: r(40);
      text-align: center;
      font-size: r(12);
      color: #909090;
    }

    .gird {
      height: r(30);
      @include display-flex;
      @include align-items(center);
      @include justify-content(center);

      i {
        height: r(7);
        width: r(7);
        border-radius: 100%;
        animation: LOADING 1.5s infinite;
        margin: 0 r(3);

        &:nth-child(1n) {
          animation-delay: 0s;
        }

        &:nth-child(2n) {
          animation-delay: 0.2s;
        }

        &:nth-child(3n) {
          animation-delay: 0.4s;
        }
      }
    }

    @keyframes LOADING {
      0% {
        transform: scale(0.5);
        background: rgba(0, 0, 0, 0.1);
      }

      50% {
        transform: scale(1);
        background: rgba(0, 0, 0, 0.3);
      }

      100% {
        transform: scale(0.5);
        background: rgba(0, 0, 0, 0.1);
      }
    }

    &.expand {
      padding-bottom: r(210);
    }

    .card_comp {
      width: r(345);
      margin: 0 auto r(20);
      box-sizing: border-box;
      background: #fff;
      box-shadow: 0 r(4) r(16) 0 rgba(0, 0, 0, 0.03);
      border-radius: r(8);
      overflow: hidden;
    }

    .tag_wrap {
      .tag {
        display: inline-block;
        width: r(74);
        height: r(26);
        margin: r(12) r(10) 0 0;
        line-height: r(26);
        background: rgba(0, 0, 0, 0.04);
        border-radius: r(13);
        border: 1px solid rgba(0, 0, 0, 0.1);
        font-size: r(12);
        color: rgba(0, 0, 0, 0.8);
        text-align: center;
        vertical-align: middle;

        &.active {
          color: #ec9131;
          background: rgba(236, 145, 49, 0.06);
          border: 1px solid rgba(236, 145, 49, 0.32);
        }
      }
    }

    .systen_message_comp {
      margin-bottom: r(20);
      padding: 0 r(15);
      text-align: center;
      font-size: r(12);

      .time {
        color: rgba(0, 0, 0, 0.6);
      }

      .systen_message {
        display: inline-block;
        padding: r(8) r(15);
        min-height: r(32);
        line-height: 1.4;
        background: rgba(0, 0, 0, 0.03);
        border-radius: r(16);
        color: rgba(0, 0, 0, 0.4);
        text-align: left;
        margin-top: r(15);
      }
    }
  }

  @keyframes blink {
    50% {
      opacity: 0;
    }
  }

  .chat_ygj_page-ChatLog:last-child {
    &.inputting {
      .chat_log_comp:not(.patient) {
        .msg_text {
          .p_msg_text:last-child {
            &::after {
              display: inline-block;
              content: '';
              width: 3px;
              height: 1em;
              background-color: #383838;
              vertical-align: middle;
              margin-left: 2px;
              margin-top: -4px;
              animation: blink 0.5s infinite;
            }
          }
        }
      }
    }
  }

  @keyframes ellipsis1 {
    0% {
      opacity: 1;
    }

    50% {
      opacity: 0.5; /* 文本宽度变小 */
    }

    100% {
      opacity: 0; /* 文本宽度变小 */
    }
  }

  @keyframes ellipsis2 {
    0% {
      opacity: 0.5;
    }

    50% {
      opacity: 1; /* 文本宽度变小 */
    }

    100% {
      opacity: 0.5; /* 文本宽度变小 */
    }
  }

  @keyframes ellipsis3 {
    0% {
      opacity: 0;
    }

    50% {
      opacity: 0.5; /* 文本宽度变小 */
    }

    100% {
      opacity: 1; /* 文本宽度变小 */
    }
  }

  .dot-animation {
    display: inline-block;
  }

  .dot1 {
    animation: ellipsis1 1s infinite;
    font-size: 20px;
    height: 5px;
    line-height: 5px;
    display: inline-block;
  }

  .dot2 {
    animation: ellipsis2 1s infinite;
    font-size: 20px;
    height: 5px;
    line-height: 5px;
    display: inline-block;
  }

  .dot3 {
    animation: ellipsis3 1s infinite;
    font-size: 20px;
    height: 5px;
    line-height: 5px;
    display: inline-block;
  }

  .footer {
    position: absolute;
    width: 100%;
    // height: r(60);
    bottom: 0;
    left: 0;
    padding-bottom: r(14);
    background: #fff;
    box-shadow: 0 r(1) 0 0 #e6e6e6;
    z-index: 2;

    &_item {
      background: #f7f7f9;
      border-radius: r(14);
      margin: r(15) 0 0 r(15);
      display: inline-block;
      text-align: center;
      color: #666;
      padding: r(5) r(6);
      vertical-align: middle;
      font-size: r(10);

      img {
        width: r(14);
        margin-right: r(2);
        margin-top: -2px;
      }
    }
  }

  .image_modal {
    .za-mask {
      background: black;
    }

    .za-popup {
      background: black;
      width: 100% !important;
      border-radius: 0;

      .za-modal__body {
        padding: 0;
      }

      .image_modal_carousel {
        .carousel__item__pic > img {
          width: 100%;
        }

        .carousel__item__pic {
          vertical-align: middle;
        }
      }
    }
  }

  .comp_zajk_wrap {
    position: absolute;
    right: r(12);
    height: r(59);
    width: r(62);
    bottom: r(126);
    z-index: 2;

    .zajk_img {
      width: 100%;
      height: 100%;
    }
  }
}

.modal_share_chat {
  position: fixed;
  height: 100vh;
  width: 100%;
  top: 0;
  left: 0;
  background: rgba(0, 0, 0, 0.78);
  z-index: 3;
  transform: scale(0);

  &.show {
    transition: all 0.3s;
    transform: scale(1);
  }

  .img_share_guide {
    position: absolute;
    width: r(196);
    height: r(84);
    right: r(7);
    top: r(7);
  }
}
