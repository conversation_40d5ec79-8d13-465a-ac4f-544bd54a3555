import React, { useEffect, useState, useCallback, useRef, useMemo } from 'react';
import { shallowEqual, useSelector, useDispatch } from 'react-redux';
import { Prompt } from 'react-router';
import { ImgWithDefault, ChatInput } from 'src/components/common';
import { ApplicationState } from 'src/store';
import {add_chat_msg, update_some_msg, add_receive_msg_res, shift_chat_msg, reset_chat_state, update_last_msg_or_add, edit_last_msg, delete_last_msg } from 'src/store/chatmedicalmanage/action';
import bridge from 'src/utils/bridge';
import { fetchJson } from 'src/utils/fetch';
import format from 'src/utils/format';
import { isFromAliER, THIRD_PLATFORM_RESOURCECODE } from 'src/utils/staticData';
import validate from 'src/utils/validate';
import { Pull, Carousel, Modal, Popup, Toast } from 'zarm';
import { FETCH_USER_CHANNEL } from '../../../store/channel/action-types';
import { ChatLog, Auth, CustomerService, OtherCard } from '../components';
import './index.scss';
import { Drainage } from '../../../services/channel-resource';
import { createSse } from './sse';
import classnames from 'classnames';


// 下拉刷新的状态值
const REFRESH_STATE = {
  normal: 0, // 普通
  pull: 1, // 下拉刷新（未满足刷新条件）
  drop: 2, // 释放立即刷新（满足刷新条件）
  loading: 3, // 加载中
  success: 4, // 加载成功
  failure: 5, // 加载失败
};
function isToday(date) {
  return format.date(date, 'yyyy-MM-dd') === format.date(new Date(), 'yyyy-MM-dd');
}

const SystemMessage = (props) => {
  const { reply_date = new Date(), reply_content: { content = '' } = {} } = props;
  return (
    <div className='systen_message_comp'>
      <p className='time'>{format.date(reply_date, isToday(reply_date) ? 'hh:mm' : 'MM月dd日 hh:mm')}</p>
      {content && <p className='systen_message'>{content}</p>}
    </div>
  );
};

declare global {
  interface Window {
    leaveConfirm: boolean
    drainages: Drainage[] | undefined;
    unsubscribe?: () => void;
  }
}

let consultNo = '';
let isReciving = false;

const setConsultNo = (text) => consultNo = text;

const AIChat = (props) => {

  const confirmLink = useSelector((state: ApplicationState) => {

    if(state.userChannel && state.userChannel.drainages) {
      return state.userChannel.drainages.find((item) => item.linkLocation === 'aiConsult');
    }
    return undefined;
  });
  // needSubscribeMsg 确定是否需要显示订阅医生消息, 取值：0/1 '1': 需要 其他不需要.
  // from = '', stub = '' 健康险双十一体检回调字段
  const [doctorHeadPortrait] = useState(require('./images/ai.png'));
  const [userHeadPortrait] = useState(require('./images/user.png'));
  const [sendVal, setSendVal] = useState('');
  // 是否发出去过消息（直营健康金需求） 健康险双十一复用此字段
  const [refreshing, setRefreshing] = useState(REFRESH_STATE.normal);

  const [currentUser]: any = useState({});
  const [allMessageShow, setAllMessageShow] = useState(false); // 标记拉取过历史记录是否已经加载完
  const [showShareGuide, setShowShareGuide] = useState(false);
  // 预览图片的弹窗
  const [showImageCarousel, setShowImageCarousel] = useState(false);
  const [imageCarouselActive, setImageCarouselActive] = useState(0);
  const [pageNum, setPageNum] = useState(1);

  const [modalVisible,setModalVisible] = useState<boolean>(false);
  const [imgSrc,setImgSrc] = useState<string | undefined>();
  const [jumpUrl,setJumpUrl] = useState<string | undefined>();
  const [inputDisabled, setInputDisabled] = useState(false);
  const [inputting, setInputting] = useState(false);
  const allowBack = useRef<Boolean>(false);

  const popWrapperEl: any = useRef(null);
  const containerRef: any = useRef(null);
  const popRef: any = useRef(null);

  const {
    msgList,
    consultInfo,
  } = useSelector((state: ApplicationState) => ({
    ...state.chat,
    userChannel:state.userChannel,
    PROFESSIONALTITLE_OBJ: state.dictionary.PROFESSIONALTITLE_OBJ,
  }), shallowEqual);


  useEffect(() => {
    if(isFromAliER()) {
      return Toast.show({
        content: '跳转有误，请重新发起问诊流程。如遇问诊问题可联系客服处理',
        stayTime: 60* 60 * 60 * 60 * 1000,
        mask: true,
      });
    }
    dispatch({ type: FETCH_USER_CHANNEL });
  }, []);

  const photoList = useMemo(() => {
    const list: any = [];
    msgList.forEach((item) => {
      if (item.msg_type == 4) {
        list.push({ source: item.reply_content.link });
      }
    });
    return list;
  }, [msgList]);

  const dispatch = useDispatch();
  const addMsg = (data) => dispatch(add_chat_msg(data));
  const updateSomeMsg = (data) => dispatch(update_some_msg(data));
  const addReceiveMsgRes = (data) => dispatch(add_receive_msg_res(data));
  const updateLastMsgOrAdd = (data) => dispatch(update_last_msg_or_add(data));
  const shiftMsg = (data) => dispatch(shift_chat_msg(data));
  const editLastMsg = (data) => dispatch(edit_last_msg(data));
  const deleteLastMsg = (data) => dispatch(delete_last_msg(data));
  const resetChatState = () => dispatch(reset_chat_state());


  const init = (success?) => {
    // 发起POST请求创建AI咨询会话
    fetchJson({
      type: 'POST',
      url: '/api/api/v1/patient/aigc/consult/create',
      data: {
      },
      isloading: false, // 不显示加载状态
    }).then((res) => {
      if(res.code === '200') {
        const { result = {} } = res || {};
        const { contentList = [], consultNo: newConsultNo } = result || {};

        // 如果已有咨询编号，则将AI返回的内容转换为聊天消息格式并添加到聊天记录中
        if(consultNo) {
          setTimeout(() => {
            addMsg(contentList.map((item) => ({
              reply_date: item.timestamp,
              reqid: item.timestamp,
              msg_type: 2, // 消息类型为2，表示AI回复
              user_type: 2, // 用户类型为2，表示AI
              reply_content: { content: item.content },
              msg_id: item.timestamp,
              consultNo: newConsultNo, // 使用新的咨询编号
            })));
          });
        }

        // 更新咨询编号
        setConsultNo(newConsultNo);
        // 滚动页面到底部，显示最新消息
        scrollBottom();
        // 如果传入了成功回调函数，则执行
        success && success();
      }
    });
  };

  const sendMsgToAi = (content, msgInfo?) => {
    let msg = msgInfo;
    if(!msgInfo) {
      // 如果没有提供消息信息，创建一个新的用户消息对象
      const reqid = new Date().getTime();
      msg = {
        action: 'reply',
        user_type: 1,  // 1 表示用户发送的消息
        reqid,
        msg_type: 2,   // 2 表示文本消息类型
        consultNo,     // 咨询编号
        reply_content: { content },
        msg_id: reqid,
      };

      // 将用户消息添加到消息列表
      addMsg([
        msg,
      ]);

      // 滚动到底部以显示新消息
      scrollBottom();
      // 添加消息接收记录
      addReceiveMsgRes(reqid);
    }

    // 添加一个"思考中"的临时消息，显示加载状态
    addMsg([{
      reply_date: `${msg.reqid}1`,
      reqid: `${msg.reqid}1`,
      msg_type: 2,
      user_type: 2,    // 2 表示系统/助手回复
      reply_content: { content: '思考中<span class="dot1">.</span><span class="dot2">.</span><span class="dot3">.</span>', mode: 'html' },
      msg_id: `${msg.reqid}1`,
      consultNo,
    }]);

    // 禁用输入框，防止用户在等待回复时继续输入
    setInputDisabled(true);
    setInputting(false);
    isReciving = false;
    // bridge.setTitle('正在输入中....');

    // 创建SSE连接获取流式响应
    createSse({
      data: {
        timeStamp: Date.now(),
        content,
        consultNo,
      },
      sendAssistantMsg(data: any) {
        const {
          value: {
            timestamp,
            completionId,
            content,
            hasError = false,
          } = Object.create(null), done, isError, isSendInquiryCard} = data;
        const error = hasError || isError;

        // 首次收到响应时，清空"思考中"的临时消息
        if(!isReciving) {
          isReciving = true;
          setInputting(true);
          editLastMsg({
            msg_id: completionId,
            reqid: completionId,
            consultNo,
            reply_content: { content: '' },
          });
        }

        // 保持滚动到最新消息
        scrollBottom();

        // 响应完成时的处理
        if(done) {
          // bridge.setTitle('众安智能咨询助手');
          setInputDisabled(false);

          // 如果需要发送咨询卡片
          if(isSendInquiryCard) {
            addMsg([{
              reply_date: `${timestamp}card`,
              reqid: completionId,
              msg_type: 6,  // 6 表示卡片消息类型
              user_type: 2,
              reply_content: { content: '', mode: 'service', cardType: 'onlineConsult' },
              msg_id: completionId,
              consultNo,
              currentConsultNo: consultNo,
            }]);
          }
          return;
        }

        // 处理错误情况
        if(error) {
          bridge.setTitle('众安智医慧问');
          setInputDisabled(false);
          deleteLastMsg({});
          // 更新原始消息，标记为错误
          updateSomeMsg({
            ...msg,
            hasError: error,
          });
          return;
        }

        // 正常情况下，更新或添加助手回复的消息内容
        setTimeout(() => {
          updateLastMsgOrAdd({
            reply_date: timestamp,
            reqid: completionId,
            msg_type: 2,
            user_type: 2,
            reply_content: { content },
            msg_id: completionId,
            consultNo,
          });
        }, 100);
        return;
      },
    });
  };

  useEffect(() => {
    resetChatState();
    init(() => refreshData(scrollBottom));
  }, []);

  const startNew = () => {
    if(inputDisabled) {
      return;
    }
    Modal.confirm({
      content: '是否重新开始咨询？',
      onOk: () => {
        fetchJson({
          type: 'POST',
          url: '/api/api/v1/patient/aigc/consult/end',
          data: {
            consultNo,
          },
          isloading: true,
        }).then((res)=> {
          if(res.code === '200') {
            init();
          }
        });
      },
    });
  };

  const refreshData = (success?) => {
    setRefreshing(REFRESH_STATE.loading);
    fetchJson({
      type: 'POST',
      url: '/api/api/v1/patient/aigc/consult/msg/history',
      data: {
        consultNo: '',
        pageNum,
        pageSize: 10,
        status: '',
      },
      isloading: true,
    }).then((res) => {
      if(res.code === '200') {
        const { result = {} } = res || {};
        const { resultList = [] } = result || {};
        if(resultList.length) {
          setPageNum(pageNum+1);
        } else {
          setTimeout(() => {
            setRefreshing(REFRESH_STATE.success);
            setAllMessageShow(true);
          }, 0);
          return;
        }
        const list: any = [];
        resultList.reverse().forEach((item) => {
          const contentList = item.contentList || [];
          const histConsultNo = item.consultNo || '';
          contentList.forEach((item) => {
            if (item.role !== 'ai') {
              addReceiveMsgRes(item.reqid || item.timestamp);
            }
            list.push({
              reply_date: item.timestamp,
              reqid: item.reqid || item.timestamp,
              msg_type: 2,
              user_type: item.role === 'user' ? 1 : 2,
              reply_content: { content: item.content },
              msg_id: item.timestamp,
              consultNo: histConsultNo,
            });
            if('isSendInquiryCard' in item && item.isSendInquiryCard) {
              list.push({
                reply_date: `${item.timestamp}card`,
                reqid: `${item.reqid || item.timestamp}card`,
                msg_type: 6,
                user_type: 2,
                reply_content: { content: '', mode: 'service', cardType: 'onlineConsult' },
                msg_id: item.reqid || item.timestamp,
                consultNo: histConsultNo,
                currentConsultNo: consultNo,
              });
            }
          });
        });
        shiftMsg(list);
        success && success();
        setRefreshing(REFRESH_STATE.normal);
      }
    });

  };

  // 发送选择患者
  const sendPatient = useCallback((msgInfo, key = 'MSG_CHANGE') => {
    updateSomeMsg(msgInfo);
    let msgContent = { ...msgInfo };
    if (key === 'HAS_AUTH') {
      const { reply_content = {}, reply_content: { final = {} } = {} } = msgInfo;
      msgContent = {
        ...msgContent,
        reply_content: {
          ...reply_content,
          final: {
            patient_id: final.patient_id,
            patient_name: final.patient_name,
          },
        },
      };
    }
  }, []);

  /* 点击 发送 按钮*/
  const submitSend = useCallback(() => {
    const value = sendVal.replace(/^\s+|\s+$/g,'');
    if (!value) {
      return;
    }
    sendMsgToAi(value);
    setSendVal('');
  }, [sendVal]);

  // 键盘收缩页面回弹
  const windowScrollBottom = () => {
    window.scroll({
      top: 0,
    });
    popWrapperEl.current.style = {};
  };

  // 页面滑动到底部
  const scrollBottom = useCallback((time = 100) => {
    setTimeout(() => {
      if (popWrapperEl.current && popRef.current) {
        popWrapperEl.current.scrollTo({
          top: Number(popRef.current.pull.getBoundingClientRect().height) + 1000,
          behavior: 'smooth',
        });
      }
    }, time);
  }, []);

  useEffect(() => {
    // 监听安卓软键盘事件 处理聊天信息滚动
    // 获取原窗口的高度
    if (!validate.isIos()) {
      const originalHeight = document.documentElement.clientHeight || document.body.clientHeight;
      window.onresize = function() {
        // 键盘弹起与隐藏都会引起窗口的高度发生变化
        const resizeHeight = document.documentElement.clientHeight || document.body.clientHeight;
        if (resizeHeight - 0 < originalHeight - 0) {
          // 当软键盘弹起，在此处操作
          scrollBottom();
        } else {
          // 当软键盘收起，在此处操作
        }
      };
    }
    return () => {
      window.onresize = null;
    };
  }, []);

  // Input focus
  const focusInput = useCallback(() => {
    if (!validate.isIos()) {
      return;
    }
    // 处理ios系统软键盘不收起 消息记录少的时候 使消息可以看见的问题
    if (popWrapperEl.current && popRef.current) {
      setTimeout(() => {
        const height = document.documentElement.scrollTop || window.pageYOffset || document.body.scrollTop;
        if (height) {
          popWrapperEl.current.style.top = `${height - 50}px`;
        }
        scrollBottom(0);
        // popWrapperEl.current.scrollTo({
        //   top: popRef.current.pull.getBoundingClientRect().height,
        //   behavior: 'smooth'
        // });
      }, 300);

      setTimeout(() => {
        const height = document.documentElement.scrollTop || window.pageYOffset || document.body.scrollTop;
        window.scroll({
          top: height + 40,
        });
      }, 300);
    }
  }, []);

  // 点击图片放大展示
  const photoClick = (reply_content) => {
    console.log('photoClick', reply_content);
    const { link = '' } = reply_content || {};
    photoList.forEach((element, index) => {
      if (element.source == link) {
        setShowImageCarousel(true);
        setImageCarouselActive(index);
      }
    });
  };

  const errorClick = (msg = {reply_content: {content: ''}}) => {
    updateSomeMsg({
      ...msg,
      hasError: false,
    });
    sendMsgToAi(msg.reply_content.content, msg);
  };

  const currentMsgIndex = msgList.findIndex((item) => item.consultNo === consultNo);

  return (
    <div ref={containerRef} className='chat_ygj_page'>
      <div className='chat_ygj_page_title'>
        *以下回复基于AI及众安真实问诊数据生成，仅供参考
      </div>
      <Popup visible={modalVisible} direction='center' >
        <a className='confirm-leave-modal-content' href={jumpUrl}>
          <img src={imgSrc} alt='挽留图' />
        </a>
        <a
          onClick={() => {
            allowBack.current = true;
            setModalVisible(false);
            window.reactHistory.goBack();
          }}
          className='confirm-leave-modal-ci'
        >
          <img src={require('../../../images/icon_close.png')} alt='' />
        </a>
      </Popup>
      <Prompt message={(location,action) => {

        if(allowBack.current) {
          return true;
        }

        if(confirmLink && action === 'POP') {
          setImgSrc(confirmLink.attachmentList[0].attachmentDownloadUrl);
          setJumpUrl(confirmLink.linkUrl);
          setModalVisible(true);
          return false;
        }

        return true;
      }} />

      <main className={'main'} ref={popWrapperEl}>
        <Pull
          ref={popRef}
          className='pull_msg_wrapper'
          refresh={{
            state: refreshing,
            handler: refreshData,
            render: (refreshState) => {
              const cls = 'custom_control_pull';
              if (allMessageShow) {
                return null;
              }
              switch (refreshState) {
              case REFRESH_STATE.pull:
                return <div className={cls}>下拉加载更多聊天记录</div>;
              case REFRESH_STATE.drop:
              case REFRESH_STATE.loading:
                return (
                  <div className={cls}>
                    <div className='gird'>
                      <i></i>
                      <i></i>
                      <i></i>
                    </div>
                  </div>
                );
              default:
                return null;
              }
            },
          }}
        >
          {allMessageShow && <p className='no_message_tip'>已没有历史信息了</p>}
          {msgList.map((item, index) => {
            const { msg_type, reply_date } = item;
            switch (msg_type) {
            case 1:
              return <SystemMessage key={`msg${reply_date || index}`} {...item} />;
            case 3:
              return <ChatLog key={`msg${reply_date || index}`} msgInfo={item} currentUser={{ ...currentUser, userHeadPortrait }} doctorHeadPortrait={doctorHeadPortrait} />;
            case 6: {
              const { reply_content: { mode = '' } = {} } = item; // "mode":"mobile|evaluate|service|realauth|patient"
              switch (mode) {
              case 'mobile':
                return null;
              case 'evaluate':
                return;
              case 'service':
                return <ChatLog key={`msg${reply_date}${index}`} msgInfo={item} currentUser={{ ...currentUser, userHeadPortrait }} doctorHeadPortrait={doctorHeadPortrait} updateSomeMsg={updateSomeMsg} />;
              case 'realauth':
                return <Auth key={`msg${reply_date || index}`} msgInfo={item} sendPatient={sendPatient} consultInfo={consultInfo} />;
              case 'patient':
                return <ChatLog key={`msg${reply_date || index}`} msgInfo={item} consultInfo={consultInfo} sendPatient={sendPatient} currentUser={{ ...currentUser, userHeadPortrait }} doctorHeadPortrait={doctorHeadPortrait} scrollBottom={scrollBottom} />;
              case 'cs':
                return <CustomerService key={`msg${reply_date || index}`} />;

              case 'tips':
              case 'bind':
                return <OtherCard key={`msg${reply_date || index}`} consultInfo={consultInfo} msgInfo={item} currentUser={{ ...currentUser, userHeadPortrait }} doctorHeadPortrait={doctorHeadPortrait} />;
              default:
                return null;
              }
            }
            default:
              return <div key={`msg${reply_date || index}`} className={classnames('chat_ygj_page-ChatLog', {
                inputting: inputDisabled && inputting,
              })}>
                {
                  index === currentMsgIndex &&
                  <div className='msg-newline'>
                    <span className='msg-line'></span>
                    <span className='msg-text'>以下为新对话</span>
                    <span className='msg-line'></span>
                  </div>
                }
                <ChatLog
                  errorClick={errorClick}
                  isAIChat
                  key={`msg${reply_date}${index}`}
                  msgInfo={item}
                  photoClick={photoClick}
                  currentUser={{ ...currentUser, userHeadPortrait }}
                  doctorHeadPortrait={doctorHeadPortrait} />
              </div>;
            }
          })}
        </Pull>
      </main>

      <footer className='footer'>
        <div className='footer_item'>
          <img src={require('../images/msg.png')} alt='' />
          <span onClick={() => startNew()}>咨询新问题</span>
        </div>
        <ChatInput
          popWrapperEl={popWrapperEl}
          sendVal={sendVal}
          handleChange={(e) => setSendVal(e.target.value)}
          handleFocus={focusInput}
          handleBlur={windowScrollBottom}
          handleSumbit={submitSend}
          chatSendMsg={sendMsgToAi}
          platformCode={THIRD_PLATFORM_RESOURCECODE.ZAHY}
          disabled={inputDisabled}
        />
      </footer>

      <Modal
        className='previewimage-component__imagemodal'
        visible={showImageCarousel}
        maskClosable
        onCancel={() => {
          setShowImageCarousel(false);
        }}
      >
        <div
          onClick={() => {
            setShowImageCarousel(false);
          }}
        >
          <Carousel className='image_modal_carousel' activeIndex={imageCarouselActive} showPagination={false}>
            {photoList.map((photo, index) => (
              <div className='carousel__item__pic' key={+index}>
                <ImgWithDefault src={photo.source} alt='' />
              </div>
            ))}
          </Carousel>
        </div>
      </Modal>
      <div className={`modal_share_chat ${showShareGuide ? 'show' : ''}`} onClick={() => setShowShareGuide(false)}>
        <img className='img_share_guide' src={require('src/images/share_guide.png')} />
      </div>
    </div>
  );
};

export default AIChat;
