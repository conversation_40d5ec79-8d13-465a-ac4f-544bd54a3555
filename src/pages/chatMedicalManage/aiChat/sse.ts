import { StaticToast } from 'src/components/common';
import { cookies, validate } from 'src/utils';
import { SSE } from 'sse.js';

export const createSse = (props: any) => {
  const { data, sendAssistantMsg } = props;

  // 创建SSE连接，用于与AI聊天服务建立服务器发送事件连接
  const sse = new SSE('/api/api/v1/patient/aigc/consult/chat/sse', {
    headers: {
      'Content-Type': 'application/json;charset=UTF-8',
      'X-Service-Name': 'za-cportal-operating',
      'Token': cookies.get('za_token') || '',
      'x-no-compression': 'true',
    },
    withCredentials: true,
    method: 'POST',
    payload: JSON.stringify(data),
  });

  // 处理服务器发送的消息事件
  sse.onmessage = (event: any) => {
    try {
      if (!event?.data) {
        return;
      }
      const data = JSON.parse(event.data);
      // 处理AI返回的错误情况
      if (data.hasError) {
        console.log(data, '报错');
        sse.close();
        sendAssistantMsg({
          isError: true,
          value: data,
        });
        return;
      }

      // 处理数据传输结束的情况
      if (data.finish) {
        sse.close();
        sendAssistantMsg({
          value: '',
          isStream: true,
          done: true,
          isSendInquiryCard: data.isSendInquiryCard,
        });
        if (!data?.finishReason) {
          console.log(data, '结束');
          sendAssistantMsg({
            value: data,
            isStream: true,
            done: true,
          });
        }
        return;
      }

      // 处理正常的流式数据
      sendAssistantMsg({
        value: data,
        isStream: true,
      });
    } catch (error) {
      // 异常处理，关闭连接并发送错误消息
      sse.close();
      sendAssistantMsg({
        isError: true,
      });
    }
  };

  // 监听SSE连接状态变化
  sse.onreadystatechange = (event: any) => {
    try {
      // 只处理readyState为2(HEADERS_RECEIVED)的情况
      if (event.readyState !== 2) {
        return;
      }
      if (!event?.source?.xhr?.response) {
        return;
      }
      console.log('非流式 response', JSON.parse(event?.source?.xhr?.response ?? '') ?? event);
      const data = JSON.parse(event?.source?.xhr?.response ?? '');

      // 处理登录过期的情况（code=20002）
      if(data.code === '20002') {
        const loginPage = '/hospital/login';
        const { pathname, search } = window.location;

        // 根据不同环境跳转到对应的登录页面
        if (validate.isAlipayApplet()) {
          return my && my.navigateTo({
            url: `/pages/login/index?pathname=${encodeURIComponent(pathname)}&search=${encodeURIComponent(search)}`,
          });
        }

        if (validate.isFromMiniApplet()) {
          return wx.miniProgram.redirectTo({
            url: `/pages/login?pathname=${encodeURIComponent(pathname)}&search=${encodeURIComponent(search)}`,
          });
        }

        // Web环境下的登录跳转
        window.reactHistory.replace({
          pathname: loginPage,
          search: `pathname=${encodeURIComponent(pathname)}&jumpSearch=${encodeURIComponent(search)}`,
        });
        return;
      }

      // 处理非流式响应数据
      sendAssistantMsg({
        value: JSON.parse(event?.source?.xhr?.response),
        isStream: false,
      });
    } catch (error) {
      // 异常处理
      sse.close();
      sendAssistantMsg({
        isError: true,
      });
    }
  };

  // 处理SSE连接错误
  sse.onerror = (err: any) => {
    console.log(err, 'err');
    sendAssistantMsg({
      isError: true,
    });
    const { status, error } = err;

    // 处理网络连接失败的情况
    if (error?.message === 'Failed to fetch') {
      sse.close();
      return;
    }

    // 处理401未授权错误（登录超时）
    if (status === 401) {
      StaticToast.error('登录超时');
      setTimeout(() => {
        // login(); // 登录函数调用被注释
      }, 3000);
    } else if (status) {
      sse.close();
    }
  };

  // 启动SSE流
  sse.stream();
  return sse;
};
