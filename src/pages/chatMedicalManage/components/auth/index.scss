@import "src/style/index";

.auth_comp {
  .auth_input_wrap {
    padding: 0 r(15) r(20);
  }

  .auth_title {
    height: r(50);
    line-height: r(50);
    font-size: r(16);
    font-weight: bold;
    color: #000;

    &.authed {
      padding-left: r(15);
    }
  }

  .auth_info {
    border-top: r(1) solid var(--border-disabled);
    padding: r(10) r(15) r(12);
    line-height: 2;
  }

  .anth_label {
    display: inline-block;
    width: r(65);
    color: rgba(0, 0, 0, 0.6);
  }

  .auth_input {
    margin: 0 auto r(12);
    border-radius: r(8);
    height: r(40);
    padding: 0 r(15);
    line-height: r(40);
    font-size: r(13);
    background: rgba(0, 0, 0, 0.02);
  }

  .auth_btn {
    height: r(44);
    margin-top: r(20);
    border-radius: r(22);
    color: #fff;
    border: none;
    font-weight: bold;

    &.za-button--disabled {
      background: rgba(0, 0, 0, 0.3);
      opacity: 1;
    }

    &.submited {
      background: var(--button-submit-color);
    }
  }
}
