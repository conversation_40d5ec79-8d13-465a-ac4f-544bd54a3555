
import React from 'react';
import { Icon } from 'zarm';
import './index.scss'
const prefixCls = 'chat-black-component';

const ChatBlack = (props) => {

  return (
    <section className={prefixCls}>
      <div className={`${prefixCls}__core`}>
        <h3><Icon type='warning-round-fill' theme="warning" size="sm" /> 因言辞不当，您已被限制咨询，3天后解除</h3>
        <p className={`${prefixCls}__tips`}>如您已经多次言辞不当，系统将终止为您服务，不再解除限制</p>
      </div>
    </section>
  )
}

export default ChatBlack;
