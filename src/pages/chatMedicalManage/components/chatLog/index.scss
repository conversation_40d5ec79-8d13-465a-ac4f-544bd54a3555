@import "src/style/index";

.chat_log_comp {
  padding: 0 r(15) r(20);
  @include display-flex;

  &.patient {
    @include justify-content(flex-end);

    .msg_wrap {
      border-radius: r(8) 0 r(8) r(8);
      background: rgba(48, 158, 235, 0.16);
    }
  }

  .avatar {
    display: block;
    width: r(40);
    height: r(40);
  }

  .msg_wrap {
    position: relative;
    margin: 0 r(10);
    border-radius: 0 r(8) r(8) r(8);
    background: #fff;
    max-width: 70%;
  }

  .icon_wrapper {
    position: absolute;
    top: 50%;
    margin-top: r(-10);
    left: r(-30);

    .chat_icon_loading {
      display: inline-block;
      width: r(20);
      height: r(20);
      border-width: 2px;
      border-color: #dedede #dedede #dedede transparent;
      border-style: solid;
      border-radius: 50%;
      margin-right: r(10);
      animation: chatRotateLoading 1s linear infinite;
    }

    .chat_icon_error {
      width: r(20);
      height: r(20);
    }
  }

  @keyframes chatRotateLoading {
    from {
      transform: rotate(0deg);
    }

    to {
      transform: rotate(360deg);
    }
  }

  .msg_img {
    max-width: r(130);
    border-radius: 0 r(10) r(10) r(10);
    border: r(1) solid #c6e7ff;

    &.user_img {
      border-radius: r(10) 0 r(10) r(10);
    }
  }

  .msg_text {
    max-width: 100%;
    padding: r(14);
    min-width: r(50);
    font-size: r(14);
    position: relative;

    .p_msg_text {
      word-break: break-all;
    }
  }

  .msg_word {
    max-width: r(252);
    height: r(47);
    line-height: r(47);
    padding: 0 r(14);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .icon_word {
    width: r(32);
    height: r(32);
    margin: 0 r(5) 0 r(-5);
  }

  .msg_card_wrap {
    width: r(252);
  }

  .card_title {
    padding-left: r(14);
    height: r(53);
    line-height: r(53);
    font-size: r(17);
    font-weight: bold;
    color: rgba(0, 0, 0, 0.8);
  }

  .card_content {
    padding: r(10) 0 r(14) r(14);
    line-height: 2.3;
    background: #fff;
    color: rgba(0, 0, 0, 0.8);
    font-size: r(14);
  }

  .card_label {
    display: inline-block;
    min-width: r(70);
    color: rgba(0, 0, 0, 0.5);
  }

  .choose_patient_comp {
    width: r(258);
    box-sizing: border-box;
    padding: r(18) 0 r(18) r(14);

    .choose_patient_title {
      font-size: r(16);
      font-weight: bold;
      color: #000;
    }

    .tag {
      width: auto !important;
      padding: 0 r(12);

      .add {
        margin-right: r(1);
        vertical-align: -0.2em;
      }
    }
  }

  .free_strong {
    font-weight: bold;
  }
  /* 支付宝蚂蚁患者信息 */
  .msg_ali-er {
    max-width: r(253);
    line-height: r(21);

    .er-title {
      padding: r(13) r(14) r(10);
      color: #333;
    }

    .er-body {
      padding: r(14);
      background-color: rgba(255, 255, 255, 0.38);
      font-size: r(14);

      .er-subtitle {
        color: #333;
        font-weight: 600;
        font-size: r(14);
        margin-bottom: r(4);
      }
    }
  }
}
