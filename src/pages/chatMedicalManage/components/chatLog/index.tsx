import React, { useState, useEffect, useCallback } from 'react';
import { Avatar, DrugMsg, ImgWithDefault, SvgIcon } from 'src/components/common';
import errorIcon from 'src/images/error.svg';
import { fetchJson } from 'src/utils/fetch';
import './index.scss';
import { shallowEqual, useSelector } from 'react-redux';
import { ApplicationState } from 'src/store';
import { validate } from 'src/utils';
import { jumpToWechatGuidance } from 'src/utils/auth';
import { safeHTMLComponent } from 'src/utils/tool';
import PatientListTags from '../patientTags';
import ServiceCard from '../serviceCard';
import { xflowPushEvent } from 'src/utils/pageTrack';

export const ChoosePatient = (props) => {
  const [tags, setTags] = useState([]);

  const { msgInfo, consultInfo: { consultId = 0 } = {}, sendPatient, scrollBottom } = props;

  const { reply_content: { final = {} }, action } = msgInfo;
  useEffect(() => {
    if (final && final.patient_id) {
      return;
    }
    const fetchPatient = async () => {
      const res = await fetchJson({
        type: 'POST',
        url: '/api/api/v1/patient/patient/list',
        data: {},
        isloading: false,
      });
      if (res && res.code === '0') {
        setTags(res.result || []);
        if (action === 'newMsgFlag') {
          scrollBottom && scrollBottom();
        }
      }
    };
    fetchPatient();
  }, []);

  const handleTag = useCallback((item) => {
    if (!final || !final.patient_id) {
      if (!consultId) {
        return;
      }
      const fetchSelectPatient = async () => {
        const res = await fetchJson({
          type: 'POST',
          url: '/api/api/v1/patient/medical/consult/summary/save',
          data: {
            consultId,
            patientId: item.id,
          },
          isloading: false,
        });
        if (res && res.code === '0') {
          const msg = {
            ...msgInfo,
          };
          console.log('处理数据', msgInfo);
          msg.reply_content.final = {
            patient_id: item.id,
            patient_name: item.patientName,
          };
          sendPatient && sendPatient(msg);
        }
      };
      fetchSelectPatient();
    }
  }, [consultId]);

  return (
    <div className='choose_patient_comp'>
      <p className='choose_patient_title'>请您明确权益使用人</p>
      <div>
        <div className='tag_wrap'>
          <PatientListTags patientList={tags} disabledSelected={final && final.patient_id} selectedPatient={final} selectPatientHandler={handleTag}{...props}/>
        </div>
      </div>
    </div>
  );
};

export const lineFeedContent = (content: any = '', isDanger = false) => {
  if (typeof content !== 'string') {
    return content;
  }
  if(isDanger) {
    return (content.split(/(\t|\n|\r|↵)/)).map((k, i) => ((k || '').trim() && <p className='p_msg_text' dangerouslySetInnerHTML={{ __html: safeHTMLComponent(k) }} key={`content-${i}`}></p >));
  }
  return (content.split(/(\t|\n|\r|↵)/)).map((k, i) => ((k || '').trim() && <p className='p_msg_text' key={`content-${i}`}>{ k }</p >));
};
const MsgAliERCard = (props) => {
  const { msgInfo } = props;
  const { reply_content: { content = '', title = '' } } = msgInfo;
  return (
    <div className='msg_ali-er '>
      <h4 className='er-title'>{title}</h4>
      <div className='er-body'>
        <h6 className='er-subtitle'>问诊人信息</h6>
        <div>{content}</div>
      </div>
    </div >
  );
};
const MsgElement = (props) => {

  const { msgInfo, photoClick } = props;
  const { msg_id, msg_type = '', user_type = '', reply_content = {}, reply_content: { content = '', mode = '', tips = '' } } = msgInfo || {};
  // 针对咨询页加加医管家微信消息体的a链接跳转
  const handleJump = useCallback((e) => {
    const node = e.target;
    if (node.tagName === 'A') {
      const url = node.getAttribute('href') || '';
      const [path] = url.split('?');
      // const { word = '' } = Deserialize(searchs)
      if (validate.isToWechatGuidence(path)) {
        // jumpToWechatGuidance(word);
        jumpToWechatGuidance();

      }
    }
  }, []);

  switch (msg_type) {
  case 2:
    return mode === 'html' ? <div className='msg_text' onClick={handleJump}>{
      lineFeedContent(content, true)}
    </div> : <div className='msg_text' onClick={handleJump}>{
      lineFeedContent(content)}
    </div>;
  case 3:
    if (tips === 'commodityRecommend') {
      return <DrugMsg.drugMsg replyContent={reply_content} msg_id={msg_id} />;
    }
    if (tips === 'usageDosage') {
      return <DrugMsg.drugUsage replyContent={reply_content} />;
    }
    return null;
  case 4:
    return <ImgWithDefault className={`msg_img ${user_type == 1 ? 'user_img' : ''}`} src={reply_content.localLink ? reply_content.localLink : `${reply_content.link}`} onClick={() => photoClick && photoClick(reply_content)} />;
    // return <img className={`msg_img ${user_type == 1 ? 'user_img' : ''}`} src={reply_content.localLink ? reply_content.localLink : `${reply_content.link}`} onClick={() => photoClick && photoClick(reply_content)} />;
  case 7:
    return (
      <p className='msg_word'>
        <SvgIcon className='icon_word' type='img' src={(reply_content.title || '').indexOf('pdf') > -1 ? require('src/svgs/icon_pdf.svg') : require('src/svgs/icon_word.svg')} />
        <a href={reply_content.link}>{reply_content.title || '文件'}</a>
      </p>
    );
  case 6: {
    switch (mode) { // mobile|evaluate|service|realauth|patient
    case 'service':
      return <ServiceCard {...props} />;
    case 'patient':
      return <ChoosePatient {...props} />;
    default:
      return null;
    }
  }
  case 8: {
    return <div className='msg_text'>{'[不支持的消息类型：微信小程序链接]'}</div>;
  }
  /* 支付宝蚂蚁渠道 带入的患者信息展示 */
  case 9: {
    return <MsgAliERCard {...props} />;
  }

  // case 10: {
  //   return <DrugMsg.drugMsg />
  // }
  default:
    return null;
  }
};


const ChatLog = (props) => {

  const { pathname = '' } = location;
  const isFamilyDoctor = pathname.includes('/hospital/chatfamilydoctor'); // 是从家庭医生进入

  const {
    msgInfo,
    msgInfo: { user_type = '', reqid = '', hasError = false },
    doctorHeadPortrait = '',
    currentUser: { userHeadPortrait = '', gender = '', age = '' } = {},
    isAIChat,
    errorClick,
  } = props;
  const {
    consultInfo: { isVipChannel = false } = {},
    doctorInfo: { headPortrait = '' } = {},
    doctorInfo = {}, receiveMsgRes = [],
  } = useSelector((state: ApplicationState) => state.chat, shallowEqual);

  const toDoctorIntro = useCallback(() => {
    xflowPushEvent({ eventTag: 'ZAHLWYY_SY', text: '首页', attrs: { ZAHLWYY_CLICK_CONTENT: '咨询页_医管家头像' } });
    window.reactHistory.push({
      pathname: '/hospital/doctorintro',
      search: `staffNo=${doctorInfo.staffNo}`,
    });
  }, [doctorInfo]);

  const toPersonalCenter = useCallback(() => {
    if (validate.isFromOwnMiniApplet()) {
      wx.miniProgram.reLaunch({
        url: '/pages/personalcenter',
      });
    } else {
      window.reactHistory.push({
        pathname: '/hospital/personalcenter',
      });
    }
  }, []);

  return (
    <div className={`chat_log_comp ${user_type == 1 ? 'patient' : ''}`}>
      {user_type !== 1 && (
        isFamilyDoctor
          ? <div onClick={isAIChat ? () => {} : toDoctorIntro}>
            <Avatar prefixCls='avatar' preUrl={doctorHeadPortrait || headPortrait} />
          </div>
          : <div>
            <Avatar prefixCls='avatar' preUrl={doctorHeadPortrait || headPortrait} />
          </div>
      )
      }
      <div className='msg_wrap'>
        {reqid && user_type === 1 && receiveMsgRes.indexOf(reqid) === -1 && <div className='icon_wrapper'>
          <span className='chat_icon_loading'></span>
        </div>}
        {
          user_type === 1 && hasError &&
          <div className='icon_wrapper' onClick={() => errorClick && errorClick(msgInfo)}>
            <img className='chat_icon_error' src={errorIcon} onClick={close} />
          </div>
        }
        <MsgElement {...props} />
      </div>
      {user_type == 1 &&
        <div onClick={isAIChat ? () => {} : toPersonalCenter}>
          <Avatar isVip={isVipChannel} prefixCls='avatar' preUrl={userHeadPortrait} gender={gender} age={age} />
        </div>
      }
    </div>
  );
};

export default ChatLog;
