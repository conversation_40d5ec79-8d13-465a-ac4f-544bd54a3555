import { dmEnv, dmBridge } from '@dm/utils';
import React, { useState, useCallback, useEffect } from 'react';
import { SvgIcon } from 'src/components/common';
import { fetchJson } from 'src/utils/fetch';
import validate from 'src/utils/validate';
import { Input, Button } from 'zarm';
import './index.scss';
import cookies from 'src/utils/cookie';

// SATISFACTORY("1", "还不错"),
//     ORDINARY("2", "一般"),
//     UNSATISFACTORY("3", "差"),
//     VERY_SATISFIED("4", "很满意"),
//     LIKE_IT_VERY_MUCH("5", "非常喜欢");

const scoreList = [
  {
    title: '非常满意',
    key: 'excellent',
    value: 5,
    icon: require('src/images/icon_excellent.png'),
    iconActive: require('src/images/icon_excellent_active.png'),
  },
  {
    title: '很满意',
    key: 'great',
    value: 4,
    icon: require('src/images/icon_satisfied.png'),
    iconActive: require('src/images/icon_satisfied_active.png'),
  },
  {
    title: '还不错',
    key: 'satisfied',
    value: 3,
    icon: require('src/images/icon_notbad.png'),
    iconActive: require('src/images/icon_notbad_active.png'),
  },
  {
    title: '一般',
    key: 'soso',
    value: 2,
    icon: require('src/images/icon_soso.png'),
    iconActive: require('src/images/icon_soso_active.png'),
  },
  {
    title: '不满意',
    key: 'unsatisfied',
    value: 1,
    icon: require('src/images/icon_unsatisfied.png'),
    iconActive: require('src/images/icon_unsatisfied_active.png'),
  },
];

const Evaluate = (props) => {
  const [score, setScore] = useState(5);
  const [text, setText] = useState('');
  const [submitFlag, setSubmitFlag] = useState(false);
  // const [showShareGuide, setShowShareGuide] = useState(false);
  // const [tags, setTags]: any = useState([]);
  // const [selectTags, setSelectTags]: any = useState([]);
  const {
    consultInfo: { consultId = 0 } = {},
    assistantInfo: { assistantId = 0 } = {},
    msgInfo: { tag = '' },
    onShare,
    // isFamilyDoctor,
  } = props;

  const showShare = validate.isFromMiniApplet() || dmEnv.isApp();

  const handleScore = useCallback(
    (value) => {
      if (submitFlag) {
        return;
      }
      setScore(value);
    },
    [submitFlag],
  );

  useEffect(() => {
    if (!tag) {
      return;
    }
    fetchJson({
      type: 'POST',
      url: '/api/api/v1/patient/docEvaluate/query/evaluate/list',
      data: {
        consultNo: tag,
      },
      isloading: false,
      needLogin: false,
    }).then((res) => {
      if (res && res.code === '0') {
        const item: any = (res.result && res.result[0]) || {};
        if (item.generalEvaluate) {
          setText(item.evaluateDesc);
          setScore(item.generalEvaluate);
          setSubmitFlag(true);
        }
      }
    });
  }, []);

  // const handleTags = useCallback((value) => {
  //   if (submitFlag) {
  //     return;
  //   }
  //   if (selectTags.includes(value)) {
  //     setSelectTags(selectTags.filter(item => item !== value));
  //   } else {
  //     setSelectTags([...selectTags, value]);
  //   }
  // }, [selectTags, submitFlag]);

  // 键盘收缩页面回弹
  const windowScrollBottom = () => {
    window.scroll({
      top: 0,
      behavior: 'smooth',
    });
  };

  const submit = async () => {
    if (submitFlag) {
      return;
    }
    const res = await fetchJson({
      type: 'POST',
      url: '/api/api/v1/patient/patient/submitConsultEvaluate',
      data: {
        bizType: 2, // 1-问诊单评价，2-咨询单评价
        evaluateDesc: text,
        staffId: tag ? 0 : assistantId,
        bizId: tag ? '' : consultId,
        consultNo: tag ? tag : '',
        generalEvaluate: score,
      },
      isloading: false,
    });
    if (res && res.code === '0') {
      setSubmitFlag(true);
    }
  };

  const handleShare = () => {
    if (dmEnv.isApp()) {
      const channelSource = cookies.get('channelSource');
      dmBridge
        .showShareView({
          shareType: '2,3' as any,
          dialogTitle: '',
          dialogDesc: '',
          dataType: '1',
          shareUrl: `${location.origin}/hospital/home?channelSource=${channelSource}`,
        })
        .then((res) => {
          dmBridge.share({
            shareType: res.data.shareType,
            url: `${location.origin}/hospital/home?channelSource=${channelSource}`,
            title: '家庭医管家 - 不限次健康咨询',
            imageUrl: 'https://cdn-qcloud.zhongan.com/a00000/za-asclepius/index_share.png',
            miniProgramId: 'wx3c564538ea8e3905',
            miniProgramPath: '/pages/index',
            miniProgramType: '' as any,
            shareMinProgramType: '' as any,
          });
        });
    } else {
      onShare && onShare();
    }
    // setShowShareGuide(true);
  };

  return (
    <div className='evaluate_comp card_comp'>
      {/* {
        (needPushAddWechatCard && !submitFlag &&!isFamilyDoctor) && <div className='e_add_wechat'>
          <div>
            <p className='e_add_wechat_title'>添加<span className='text_yellow'>健康管家</span>微信</p>
            <p>随时随地免费咨询</p>
            <p className='e_add_wechat_tags'>
              <span className='e_add_wechat_tag_item'>全家健康问题</span>
              <span className='e_add_wechat_tag_item'>日常用药指导</span>
              <span className='e_add_wechat_tag_item'>体检报告解读</span>
            </p>
          </div>
          <Button className='e_add_wechat_btn' theme='primary' shape='round' onClick={() => {
            // jumpToWechatGuidance('6674')
            jumpToWechatGuidance('https://mp.weixin.qq.com/s/I3ofrIdybxXKyY5rpiWwZw')
          }} >
            <img className='e_add_wechat_btn_icon' src={require('../../images/add_wechat_icon.svg')} />
            立即添加
          </Button>
          <img className='e_add_wechat_img' src={require('../../images/add_wechat_img.png')} />
          <img className='e_add_wechat_tips' src={require('../../images/e_add_wechat_tag.png')} />
        </div>
      } */}
      <p className='evaluate_title'>本次服务体验如何 ？</p>
      <div className='evaluate_option'>
        {scoreList.map(({ title, key, icon, iconActive, value }) => {
          if (submitFlag && value != score) {
            return null;
          }
          return (
            <div className='option' key={key} onClick={() => handleScore(value)}>
              <p>{title}</p>
              <SvgIcon className='icon' type='img' src={value == score ? iconActive : icon} />
              {/* {value == score && <div className={`icon_score_star`} style={{ width: `${width}em` }}></div>} */}
            </div>
          );
        })}
      </div>
      {submitFlag ? (
        <div>
          <p className='evaluate_desc'>{text}</p>
          {showShare && score != 2 && score != 1 && (
            <div className='evaluate_sahre_wrap'>
              <p className='share_title'>
                要不要分享给亲友？ 可享<span className='strong_color'>不限次</span>健康咨询
              </p>
              <Button block ghost className='btn_share' theme='primary' onClick={handleShare}>
                <img className='icon_share' src={require('src/images/icon_share.png')} />
                分享给好友
              </Button>
            </div>
          )}
          {/* {needPushAddWechatCard && score != 2 && score != 1 && (
            <p className='evaluate_wechat_guide' onClick={() => {
              // jumpToWechatGuidance('6662')
              jumpToWechatGuidance('https://mp.weixin.qq.com/s/Kktp6Ic7sDHiVnDUJpU0Rg');
            }}>
              <SvgIcon className='evaluate_icon_wechat' src={require('./../../images/sprite-icon_wechat.svg')} />
              <span>加{userServiceTypeCopywriting}微信，随时随地问医生</span>
              <SvgIcon className='evaluate_icon_arrow' src={require('src/svgs/sprite-arrow-right.svg')} />
            </p>
          )} */}
        </div>
      ) : (
        <div className='evaluate_content_wrap'>
          {/* <div className="tag_wrap">
            {
              tags.map((item, index) => {
                return <p className={`tag ${selectTags.includes(item) ? 'active' : ''}`} key={`tag${index}`} onClick={() => handleTags(item)}>专业靠谱</p>;
              })
            }
          </div> */}
          {(score == 2 || score == 1) && <Input className='evaluate_input' type='text' rows={4} maxLength={200} placeholder='谢谢您的意见，我们的服务及产品会为您而改进' onChange={(value) => setText(value.trim())} value={text} onBlur={windowScrollBottom} />}
          {/* eslint-disable-next-line @typescript-eslint/no-misused-promises */}
          <Button block className='evaluate_btn' theme='primary' disabled={!score} onClick={submit}>
            提交
          </Button>
        </div>
      )}
    </div>
  );
};

export default Evaluate;
