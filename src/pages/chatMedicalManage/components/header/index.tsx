import React, { useCallback } from 'react';
import { useSelector } from 'react-redux';
import { Avatar, SvgIcon } from 'src/components/common';
import { ApplicationState } from 'src/store';
// import { channelRightTextNames } from "src/pages/home/<USER>";
import { jumpToWechatGuidance } from 'src/utils/auth';
import { xflowPushEvent } from 'src/utils/pageTrack';

const Header = (props) => {

  const { isFamilyDoctor, showDoctorInfo, setShowDoctorInfo, isAllDoctorRest, needPushAddWechatCard = false, doctorInfo, assistantInfo = {}, doctorHeadPortrait, PROFESSIONALTITLE_OBJ,userServiceTypeCopywriting } = props;


  const { showAddWechat, hideAddWechat }: any = useSelector((state: ApplicationState) => ({
    // state.config.showAddWechat
    showAddWechat: state.userChannel.isHide === 'N',
    // 为了兼容之前的 showAddWechat
    hideAddWechat: true,
  }));

  const { pathname = '' } = location;
  const isHealthChat = pathname.includes('/hospital/chatmedical'); // 是健康资讯

  const toDoctorIntro = useCallback(() => {
    xflowPushEvent({ eventTag: 'ZAHLWYY_SY', text: '首页', attrs: { ZAHLWYY_CLICK_CONTENT: '咨询页_顶部栏进入介绍页' } });
    window.reactHistory.push({
      pathname: '/hospital/doctorintro',
      search: `staffNo=${doctorInfo.staffNo}`,
    });
  }, [doctorInfo]);


  const { workSeniority } = doctorInfo;

  return (
    <div>
      <header className={`header ${isFamilyDoctor ? 'family_doctor' : ''}`}>
        {
          isHealthChat && assistantInfo.highlightResume && <div className='assistant_point'>
            {assistantInfo.highlightResume}
          </div>
        }
        {
          // 是否隐藏为false或者医生都休息了显示
          (!hideAddWechat || isAllDoctorRest === 'Y') &&
          <div className='status_wrap'
            onClick={() => {
              isFamilyDoctor && setShowDoctorInfo(true);
            }}
          >
            <p className={`status ${isAllDoctorRest === 'Y' ? '' : 'active'}`}>
              {isAllDoctorRest === 'Y' ? (
                <span>
                  <i className='icon_rest' />
                  休息中
                </span>
              ) : (
                !hideAddWechat &&
                <span>{showAddWechat?`添加${isFamilyDoctor ? '医生团队' : '健康管家'}微信，随时随地免费问`:`有温度的${userServiceTypeCopywriting}`}</span>
                // <span>专属家庭医管家 {channelRight != null && <span style={{ color: '#666' }}>{channelRightTextNames[channelRight]}服务中</span>}</span>
              )}
              {
                !hideAddWechat &&
                <>
                  {showAddWechat && needPushAddWechatCard && <span onClick={() => {
                    jumpToWechatGuidance('https://mp.weixin.qq.com/s/5Wj8YWPiAWND0N2FXnAiug');
                    // jumpToWechatGuidance('6661', { doctorType: isFamilyDoctor ? 1 : 0 })

                  }} className='add_wechat'>加微信</span>}
                  {
                    isFamilyDoctor && showAddWechat && <span className='add_wechat'>加微信</span>
                  }
                </>
              }
            </p>
            <SvgIcon className='icon_arrow' type='img' src={require('src/pages/home/<USER>/icon_arrow.svg')} />
          </div>
        }
      </header>

      <div className={`doctorinfo_header ${showDoctorInfo ? 'show' : ''}`}>
        <div className='doctorinfo_content'>
          <div className='doctor-connect'>
            <div className='doctor-information'>
              <p className='doctorinfo_title'>
                {doctorInfo.staffName}
                {/* {channelRight != null && (
                  <span className="free-tip">
                    <span className="dot" />
                    {channelRightTextNames[channelRight]}服务中
                  </span>
                )} */}
                {/* <span className='doctorinfo_sign' onClick={toYGJIntro} >专属家庭医管家<Icon type="info-round" size="sm" className='info_round' /></span> */}
              </p>
              <p className='doctor-sign'>
                <img className='doctorinfo_sign' src={require('../../images/doctorinfo_gold_sign.svg')} />
              </p>
              <p>
                <span className='doctorinfo_tag'>全科金牌医生</span>
                {' | '}
                <span className='doctorinfo_tag'>{PROFESSIONALTITLE_OBJ[doctorInfo.staffProfessionalTitle]}</span>
                {' | '}
                <span className='doctorinfo_tag'>从医{workSeniority}年</span>
              </p>
            </div>
            <div className='doctor-Avatar'>
              <Avatar prefixCls='header_avatar' style={{ borderRadius: 4, overflow: 'hidden' }} preUrl={doctorHeadPortrait} />
              {showAddWechat && !hideAddWechat && (
                <div className='doctorinfo_wechat' onClick={() => {
                  // jumpToWechatGuidance('6661', { doctorType: isFamilyDoctor ? 1 : 0 })
                  jumpToWechatGuidance();
                } }>
                  <img className='doctorinfo_icon_wechat' src={require('../../images/sprite-icon_wechat.png')} />
                  <span>加微信</span>
                </div>
              )}
            </div>
          </div>
          <p className='doctorinfo_breif' onClick={toDoctorIntro}>
            更多信息：可提供服务、擅长领域、执业经历
            <span className='look-more'>查看全部</span>
            {/* <p className='doctorinfo_more' onClick={toDoctorIntro}>查看全部</p>*/}
            {/* src={require('src/svgs/sprite-arrow-right.svg')}  */}
            <img className='doctorinfo_more_arrow' src={require('../../images/chat-arrow-right.png')} />
          </p>
        </div>
        <img
          className='icon_fold'
          src={require('src/images/icon_fold.png')}
          onClick={() => {
            setShowDoctorInfo(false);
          }}
        />
      </div>
    </div>
  );
};

export default Header;
