@import "src/style/index";

.health-gold {
  position: fixed;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
  width: r(74);
  z-index: 990;

  img {
    width: r(72.5);
  }

  &__guide {
    position: relative;
    background-color: rgba($color: #000, $alpha: .7);
    padding: r(12) r(8) r(2);
    text-align: center;
    color: #fff;
    font-size: r(11);
    border-radius: r(5) r(5) r(8) r(8);
    min-height: r(50);

    .content {
      width: r(92);
      position: absolute;
      top: r(10);
      right: r(-9);
      transform: scale(.85);
      text-align: center;

      &.has {
        transform: scale(.92);
      }
    }

    .progress-rate {
      border-radius: r(5);
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      display: block;
      height: r(7);
      background-color: rgba($color: #000, $alpha: .6);

      &:after {
        position: absolute;
        border-radius: r(5);
        content: "";
        left: 0;
        top: 0;
        bottom: 0;
        width: 0%;
        background-color: #ffcc42;
      }

      &.rate {
        text-align: right;

        &:after {
          animation: Width 10s linear both;
        }

        .sprite-icon-components {
          position: absolute;
          right: 0;
          top: 0;
          width: r(10);
          height: r(10);
          z-index: 2;
        }
      }
    }

    .highlight {
      color: #ffcc42;
    }

    .placeholder {
      opacity: 0;
    }
  }
}

@keyframes Width {
  0% {
    width: 0%;
  }

  100% {
    width: 100%;
  }
}
