import React from 'react';
import { SvgIcon } from 'src/components/common';
import { validate } from 'src/utils';
import './msgSubscribe.scss';

function openMsgSubscribePage() {
  wx.miniProgram.redirectTo({
    url: '/pages/subscribeMessage',
  });
}

type MsgSubscribeProps = {
  visible: boolean;
}

export default function MsgSubscribe({ visible }: MsgSubscribeProps) {
  if (!validate.isFromMiniApplet() || !visible) return null;

  return (
    <div className='msg-subscribe-container' onClick={openMsgSubscribePage}>
      <SvgIcon className='msg-icon' type='img' src={require('./assets/subscribe-msg-icon.svg')} />
      <div className='msg'>点击此处让微信告诉你医生来消息了</div>
      <SvgIcon className='arrow' type='img' src={require('./assets/subscribe-arrow.svg')} />
    </div>
  );
}
