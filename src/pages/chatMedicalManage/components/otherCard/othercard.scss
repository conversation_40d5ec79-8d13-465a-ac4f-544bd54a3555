@import "src/style/index";

.bind_card_comp {
  padding: 0 r(15);
  font-size: r(12);
  color: #666;

  a,
  .text_main_color {
    color: #00a864;
  }

  .bind_card_comp_header {
    padding: r(18) 0 r(12);
    @include display-flex;
    @include align-items(center);
    @include justify-content(center);

    .bcard_comp_avatar {
      width: r(44);
      height: r(44);
      border: r(2) solid #e5f6ef;
      border-radius: 50%;
    }

    .icon_link {
      width: r(91);
      height: r(25);
      margin: 0 r(10);
    }
  }

  .bind_card_comp_title {
    font-size: r(15);
    font-family: PingFangSC-Medium, PingFang SC;
    font-weight: bold;
    color: #1e1e1e;
    text-align: center;
    margin-bottom: r(8);
  }

  .bind_card_comp_footer {
    margin-top: r(15);
    height: r(39);
    line-height: r(39);
    font-size: r(13);
    color: #00a864;
    @include borderTop($color: #E6E6E6);
  }

  .icon_question {
    width: r(14);
    height: r(14);
    margin-left: r(5);
    margin-top: r(-3);
  }
}

.tip_card_comp {
  padding: r(15);
  font-size: r(12);
  color: #666;

  .card_contnet_p {
    margin-top: r(3);
  }

  a,
  .text_main_color {
    color: #00a864;
  }

  .tip_card_comp_title {
    font-size: r(14);
    font-family: PingFangSC-Medium, PingFang SC;
    font-weight: bold;
    color: #333;
    padding-bottom: r(10);
    margin-bottom: r(10);
    @include borderBottom($color: #E6E6E6);

    .icon_card_tips {
      width: r(18);
      height: r(18);
      margin-right: r(5);
      margin-top: r(-2);
    }
  }
}

.bind_card_comp_modal {
  .za-popup {
    width: r(300) !important;
  }

  .za-modal__header {
    padding: r(20) r(15) r(10);
  }

  .za-modal__header__title {
    font-size: r(17);
    line-height: 1.2;
    font-family: PingFangSC-Medium, PingFang SC;
    font-weight: bold;
    color: #333;
  }

  .za-modal__body {
    padding: 0;
  }

  .bind_card_modal_content {
    padding: 0 r(20) r(20);
    font-size: r(14);
    color: rgba(0, 0, 0, 0.6);
  }

  .bind_card_modal_footer {
    padding: 0;
    color: #00a864;
    border: none;
    border-radius: 0;
    @include borderTop($color: #E6E6E6);
  }
}
