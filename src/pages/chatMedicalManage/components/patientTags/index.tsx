import classnames from 'classnames';
import React from 'react';
import { Link } from 'react-router-dom';
import { isFromCAINIAO, isFromAliNOCAINIAO } from 'src/utils/staticData';
import { Icon, ActivityIndicator } from 'zarm';
import './index.scss';

const prefixCls = 'patient-component';
export const PatientListTags = (props) => {
  const { patientList = [], selectPatientHandler = null, needLoading = false, disabledSelected = false, selectedPatient = {}, addPatient = null } = props;
  const patientId = selectedPatient.patient_id || selectedPatient.patientId;
  const disabled = disabledSelected && patientId;
  return (
    <div className={prefixCls}>
      {
        disabled ? (
          <span className={`${prefixCls}__tag active`}>{selectedPatient.patientName || selectedPatient.patient_name}</span>
        ) :
          <>
            {!patientList.length && needLoading && <div className={`${prefixCls}__loading`}><ActivityIndicator type='spinner' size='md' /></div>}
            {patientList.map((item: any, index) => (
              <span
                className={classnames(`${prefixCls}__tag`, { active: patientId == item.id })}
                key={`tag${index}`}
                onClick={() => (selectPatientHandler && selectPatientHandler(item))}
              >
                {item.patientName}
              </span>
            ))}
          </>
      }
      {!patientList.length && (
        <span onClick={() => {
          addPatient && addPatient();
        }} className={`${prefixCls}__tag`}>
          <Icon type='add' className='add' theme='default' size='sm' />新建
        </span>
      )}
    </div>
  );
};

export default PatientListTags;
