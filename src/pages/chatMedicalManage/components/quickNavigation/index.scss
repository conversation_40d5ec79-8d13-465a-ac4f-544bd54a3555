@import "src/style/index";

.quick-navigation {
  background-color: #fff;
  padding: 5px 0;
  border-bottom: 1px solid #e8e8e8;
  overflow: auto;

  &::-webkit-scrollbar {
    display: none;
  }

  .quick-nav-cont {
    display: flex;
    align-items: center;
  }

  .link-item {
    @include display-flex;
    @include justify-content(center);
    @include align-items(center);

    background-color: #fff;
    position: relative;
    border: 1px solid #d9d9d9;
    color: #333;
    text-align: center;
    border-radius: 10px;
    font-size: 12px;
    padding: 0 6px;
    min-width: 63px;
  }

  .link-item-container {
    margin-right: r(10);

    &:first-child {
      margin-left: 10px;
    }
  }

  .link-name {
    overflow: hidden;
    display: block;
    white-space: nowrap;
  }

  .hot-icon {
    width: 12px;
    height: 12px;
  }
}
