import React, { useEffect, useState, useRef, useCallback, useMemo } from 'react';
import { fetchJson } from 'src/utils/fetch';
import { commonPay } from 'src/utils/pay';
import { Deserialize, Serialize } from 'src/utils/serialization';
import { ydlUrl } from 'src/utils/staticData';
import { isFromAliER } from 'src/utils/staticData';
import { THIRD_PLATFORM_RESOURCECODE, THIRD_PARTNER_AUTH_CONFIG } from 'src/utils/staticData';
import throttle from 'src/utils/throttle';
import useInterval from 'src/utils/useInterval';
import validate from 'src/utils/validate';
import { Button } from 'zarm';
import './servisecard.scss';
import { jumpBeforeAuth, jumpBeforeFetchThirdInquiryAbility, jumpToAppletPublicMethod } from 'src/utils/auth';
import { getChannelResourceCode, cookies } from 'src/utils';
import { useImmediateChatAtWorks } from 'src/components/hooks';

import SvgIcon from 'src/components/common/svg';
import StaticToast from 'src/components/common/toast';
import { shallowEqual, useDispatch, useSelector } from 'react-redux';
import { fetch_order_detail } from 'src/store/order/action';
import { ApplicationState } from 'src/store';

// 心理倾诉服务 2000+ 人 ，肿瘤特药 5000 + 人   术后家庭护理 1000 + 人 质子重离子 2000+ 人 图文问诊 15000 + 人  视频问诊  13000 + 人
const cards = {
  onlineConsult: {
    title: '图文问诊服务',
    describe: ['在线阅片，快速答复', '不限问诊时长'],
    link: '/hospital/inquiryform',
    userNum: '15000',
  },
  mentalRelease: {
    title: '心理倾诉服务',
    describe: ['电话倾诉，无需预约', '国家二级咨询师服务'],
    link: ydlUrl,
    // link: '/hospital/mentalTalking',
    userNum: '2000',
  },
  tumourSpecialMedicine: {
    title: '肿瘤特药服务',
    describe: ['名医诊断，精准治疗', '药品直付，减轻负担'],
    link: '/hospital/service/baseInfo',
    userNum: '5000',
  },
  proton: {
    title: '质子重离子服务',
    describe: ['就医协助，安排预约', '针对肿瘤类治疗'],
    link: '/hospital/service/baseInfo',
    userNum: '2000',
  },
  homePostoperativeCare: {
    title: '术后护理服务',
    describe: ['专业人士上门护理', '覆盖多城，服务范围广'],
    link: '/hospital/servicelist',
    userNum: '1000',
  },
  greenChannel: {
    title: '重疾绿通服务',
    describe: ['快速安排，不耽误诊疗', '一站式就医服务'],
    link: '/hospital/servicelist',
    userNum: '3000',
  },
  ohClinic: {
    title: '视频问诊服务',
    describe: ['专业金牌医师面诊', '随时随地无所限制'],
    link: '/hospital/videointro',
    userNum: '13000',
  },
  registration: {
    title: '预约挂号服务',
    describe: ['全国7200+医院', '快速预约，便捷就医'],
    link: '',
    userNum: '13000',
    percent: '97%',
  },
  vaccine: {
    title: '疫苗服务服务',
    describe: ['HPV疫苗', '在线预约'],
    link: '',
    userNum: '13000',
    percent: '97%',
  },
  nucleicAcidTest: {
    title: '核酸检测服务',
    describe: ['无忧预约', '专人全程跟进'],
    link: '/hospital/service/natregister',
    userNum: '23232',
    percent: '98%',
  },
  registeredAssistance: {
    title: '门诊挂号协助服务',
    describe: ['快速安排', '预约专家门诊'],
    link: '/hospital/service/baseInfo',
    userNum: '',
    percent: '99%',
    extraText: '挂号无忧',
  },
  registeredAssistanceOnce: {
    title: '单次门诊挂号协助服务',
    describe: ['快速安排', '预约专家门诊'],
    link: '/hospital/service/baseInfo',
    userNum: '',
    percent: '99%',
    extraText: '挂号无忧',
  },
  urgencyCheck: {
    title: '检查加急服务',
    describe: ['快速安排', '协助安排检查加急'],
    link: '/hospital/service/baseInfo',
    userNum: '',
    percent: '99%',
    extraText: '加急处理，检查无忧',
  },
  accompanying: {
    title: '就医陪诊服务',
    describe: ['快速安排', '专人陪诊'],
    link: '/hospital/service/baseInfo',
    userNum: '',
    percent: '99%',
    extraText: '金牌优质陪诊',
  },
  hospitalService: {
    title: '住院绿通服务',
    describe: ['快速安排', '协助安排住院'],
    link: '/hospital/service/baseInfo',
    userNum: '',
    percent: '99%',
    extraText: '住院快速通道',
  },
  RecurrenceSeconddiagnosis: {
    title: '重疾二次诊疗',
    describe: ['快速安排', '协助安排住院'],
    link: '/hospital/service/baseInfo',
    userNum: '',
    percent: '99%',
    extraText: '住院快速通道',
  },
  secondDiagnosis: {
    title: '二次诊疗意见服务',
    describe: ['二次诊疗意见', '确定后续治疗方案'],
    link: '/hospital/service/baseInfo',
    userNum: '',
    percent: '99%',
    extraText: '专家权威提供二次诊疗意见',
  },
  PetService: {
    title: '宠物防疫服务',
    describe: ['多项体检，守护爱宠健康', '服务范围广'],
    link: '/hospital/service/baseInfo',
    userNum: '2000',
    percent: '99%',
  },
  HomeCare: {
    title: '上门居家护理服务',
    describe: ['专人上门，护理项目多样', '服务范围广'],
    link: '/hospital/service/baseInfo',
    userNum: '2000',
    percent: '99%',
  },
  ChineseandWesternRecovering: {
    title: '中西医康复服务',
    describe: ['专人上门，康复项目多样', '服务范围广'],
    link: '/hospital/service/baseInfo',
    userNum: '2000',
    percent: '99%',
  },
  childrendiseases: {
    title: '少儿罕见病特药服务',
    describe: ['特定药品，药费直付', '专人指导取药'],
    link: '/hospital/service/baseInfo',
    userNum: '2000',
    percent: '99%',
  },
  RehabilitationEvaluation: {
    title: '远程康复评估服务',
    describe: ['专业评估，出具康复评估书', '科学方案指导'],
    link: '/hospital/service/baseInfo',
    userNum: '2000',
    percent: '99%',
  },
};
// export const INQUIRY_STATUS = [
//   { label: '全部', value: 0 },
//   { label: '待付款', value: 1 },
//   { label: '待接诊', value: 2 },
//   { label: '问诊中', value: 3 },
//   { label: '已完成', value: 4 },
//   { label: '已评价', value: 5 },
//   { label: '已退款', value: 6 },
//   { label: '待授权', value: 7 },
//   { label: '已取消', value: 9 },
//   { label: '待接诊（已转诊）', value: 10 },
//   { label: '问诊中（已转诊）', value: 11 },
//   { label: '已完成（已转诊）', value: 12 },
// ]

const checkHasInquiry = (cb) => fetchJson({
  type: 'POST',
  url: '/api/api/v1/patient/inquiry/list',
  data: {
    inquiryStatusList: [1, 2, 3, 7, 8, 10, 11],
    inquiryTypes: ['I', 'V'],
    excludeInquiryTimingTypeList: ['drug_replenish', 'mentalConsult'],
  },
  success: (res) => {
    const { code = '', result = [] } = res;
    if (code === '0') {
      cb && cb(result);
    }
  },
});

// 特殊处理【视频问诊，图文问诊】服务流程，其余服务保持原有的逻辑
const CardButton = (props) => {
  const { cardInfo, handleClick, time, title = '', describe = [], msgInfo = {} } = props;
  const { cardNo = '', cardName = '', cardType = '', cardStatus = 1, extendMap: { isDiagnosis = false } = {}, cardUseDomain: { useStatus = '' } = {} } = cardInfo;
  const {
    msgList = [],
  } = useSelector((state: ApplicationState) => ({
    ...state.chat,
  }), shallowEqual);

  const dispatch = useDispatch();
  const fetchOrderDetail = (options: any, onSuccess?: any) => dispatch(fetch_order_detail(options, onSuccess));

  const startInquiry = useCallback(() => {
    const { cardNo, cardType, cardUseDomain: { thirdBizNo = '', useStatus = '' } = {}, staticCard = false } = cardInfo;
    if(staticCard) {
      checkHasInquiry((result) => {
        const [inquiry = {}] = result;
        if(inquiry.id) {
          window.reactHistory.push({
            pathname: '/hospital/await',
            search: `inquiryNo=${inquiry.inquiryNo}&inquiryId=${inquiry.id}`,
          });
        } else {
          const { currentConsultNo } = msgInfo;
          const descList = msgList.filter((item) => item.user_type === 1 && item.consultNo === currentConsultNo) || [];
          const desc = descList.map((item) => {
            const { reply_content: { content = '' } = {} } = item;
            return content;
          }).join(',');
          return window.reactHistory.push({
            pathname: '/hospital/inquiryplain',
            search: `desc=${encodeURIComponent(desc)}`,
          });
        }
      });
      return;
    }
    if (!cardNo) {
      StaticToast.error('此服务异常，请稍后再试');
    }
    if (cardType === 'ohClinic') {
      if (validate.isFromMiniApplet()) {
        if (thirdBizNo) {
          if (useStatus == 7) {
            window.reactHistory.push({
              pathname: '/hospital/videointro',
              search: `cardNo=${cardNo}`,
            });
          } else {
            fetchOrderDetail({ relatedBizNo: thirdBizNo }, (data) => {
              const payParams = JSON.stringify({
                payType: 'inquiry',
                orderRealAmount: data.orderRealAmount,
                orderNo: data.orderNo,
                orderTime: data.orderTime,
                inquiryNo: data.relatedBizNo,
                inquiryType: 'video',
              });
              const path = `/pages/pay?payParams=${encodeURIComponent(payParams)}`;
              jumpToAppletPublicMethod(path, data.orderNo);

            });
          }
        } else {
          window.reactHistory.push({
            pathname: '/hospital/videointro',
            search: `cardNo=${cardNo}`,
          });
        }
      } else {
        StaticToast.warning('本平台的视频问诊单列表功能暂未完成，您可请前往众安互联网医院小程序体验');
      }
    } else if (cardType === 'onlineConsult') {
      if (thirdBizNo) {
        fetchOrderDetail({ relatedBizNo: thirdBizNo }, (data) => {
          commonPay({
            orderType: 'inquiry',
            orderRealAmount: data.orderRealAmount,
            orderNo: data.orderNo,
            orderTime: data.orderTime,
            inquiryNo: data.relatedBizNo,
            inquiryType: 'text',
          });
        });
      } else {
        window.reactHistory.push({
          pathname: '/hospital/inquiryform',
          search: `cardNo=${cardNo}`,
        });
      }
    }
  }, [cardInfo]);

  const continueInquiry = useCallback(() => {
    const { cardNo, cardType, cardUseDomain: { thirdBizNo = '', bizId = '' } = {},staticCard = false } = cardInfo;
    if (!cardNo) {
      StaticToast.error('此服务异常，请稍后再试');
    }
    if(staticCard) {
      checkHasInquiry((result) => {
        const [inquiry = {}] = result;
        if(inquiry.id) {
          window.reactHistory.push({
            pathname: '/hospital/await',
            search: `inquiryNo=${inquiry.inquiryNo}&inquiryId=${inquiry.id}`,
          });
          return;
        } else {
          const { currentConsultNo } = msgInfo;
          const descList = msgList.filter((item) => item.user_type === 1 && item.consultNo === currentConsultNo) || [];
          const desc = descList.map((item) => {
            const { reply_content: { content = '' } = {} } = item;
            return content;
          }).join(',');
          return window.reactHistory.push({
            pathname: '/hospital/inquiryplain',
            search: `desc=${encodeURIComponent(desc)}`,
          });
        }
      });
      return;
    }

    if (cardType === 'ohClinic') {
      if (validate.isFromMiniApplet()) {
        jumpBeforeFetchThirdInquiryAbility({
          needTextRedirectTo: true,
          data: { inquiryNo: thirdBizNo, inquiryId: bizId },
        });
      } else {
        StaticToast.warning('本平台的视频问诊单列表功能暂未完成，您可请前往众安互联网医院小程序体验');
      }
    } else if (cardType === 'onlineConsult') {
      window.reactHistory.push({
        pathname: '/hospital/await',
        search: `inquiryNo=${thirdBizNo}&inquiryId=${bizId}`,
      });
    }
  }, [cardInfo]);


  const toReport = useCallback(() => {
    const { cardUseDomain: { bizId = '' } = {} } = cardInfo;
    if (!cardNo) {
      StaticToast.error('此服务异常，请稍后再试');
    }
    window.reactHistory.push({
      pathname: '/hospital/report',
      state: {
        inquiryId: bizId,
      },
    });
  }, [cardNo, cardInfo]);

  // 这里需要根据问诊单的状态和服务的状态来判断卡的状态
  const finalStatus = useMemo(() => {
    if ((cardType !== 'ohClinic' && cardType !== 'onlineConsult') || cardStatus == 1) {
      return -1;
    }
    if ((cardStatus == 2 && useStatus == 1) || (cardStatus == 2 && useStatus == 7)) {
      return 1;
    } else if (cardStatus == 2 && (useStatus == 2 || useStatus == 3)) {
      return 2;
      // } else if (cardStatus == 2 && isPrescription) {
      //   return 3;
    } else if ((useStatus == 4 || useStatus == 5 || cardStatus == 3) && isDiagnosis) {
      return 4;
    } else {
      return 5;
    }
  }, [cardStatus, useStatus, isDiagnosis]);

  const renderBtn = useMemo(() => {
    switch (finalStatus) {
    case 1:
      return (
        <Button className='service_btn first'>
            开始问诊
        </Button>
      );
    case 2:
      return (
        <Button className='service_btn first'>
            继续问诊
        </Button>
      );
      // case 3:
      //   return (
      //     <Button className="service_btn first" onClick={checkPrescription}>
      //       查看处方及购药
      //     </Button>
      //   );
    case 4:
      return (
        <Button className='service_btn first'>
            查看病历
        </Button>
      );
    default:
      return <Button className={`service_btn ${cardStatus === '1' ? 'first' : ''}`}>{cardStatus === '1' ? `${time ? `${time}s` : ''}开始申请` : '查看详情'}</Button>;
      // return <Button className={`service_btn`} onClick={handleClick}>查看详情</Button>;
    }
  }, [finalStatus, cardStatus, time]);

  const clickFn = useMemo(() => {
    switch (finalStatus) {
    case 1:
      return startInquiry;
    case 2:
      return continueInquiry;
      // case 3:
      //   return checkPrescription;
    case 4:
      return toReport;
    default:
      return handleClick;
      // return <Button className={`service_btn`} onClick={handleClick}>查看详情</Button>;
    }
  }, [finalStatus]);

  return <div className='service_top' onClick={clickFn}>
    <div>
      <p className='service_name'>{title || cardName}</p>
      {
        describe.map((item, index) => <p key={`service${index}`}>· {item}</p>)
      }
    </div>
    {
      cardInfo.isAvailable
        ? renderBtn
        // <Button className={`service_btn ${cardStatus === '1' ? 'first' : ''}`} onClick={handleClick}>{cardStatus === '1' ? `${time ? time + 's' : ''}开始使用` : '查看详情'}</Button>
        : <SvgIcon className='unuse_servicecard' type='img' src={require('src/svgs/icon_unuse_servicecard.svg')} />
    }
  </div>;
};

const ServiceCard = (props) => {
  const timerRef: any = useRef();
  const [time, setTime] = useState(0);
  const [cardInfo, setCardInfo]: any = useState({});
  const { getUnifiedCalculate } = useImmediateChatAtWorks();

  const {
    msgInfo,
    msgInfo: {
      reply_content: {
        biz_id = '',
        mode = '',
        cardType: staticCardType = '',
      } = {},
    } = {}, updateSomeMsg } = props || {};
  const { channelContractNo = '', channelInquiryNo = '' } = Deserialize(window.location.search || '');

  const { cardType = '' } = cardInfo;

  const { title = '', describe = [], userNum = '', percent = '', extraText = '' } = cards[cardType || ''] || {};

  useEffect(() => {
    if(staticCardType) {
      setCardInfo({
        cardType: staticCardType,
        cardNo: '111',
        isAvailable: true,
        cardUseDomain: {
        },
        cardStatus: 2,
        staticCard: true,
      });
      checkHasInquiry((inquiryList) => {
        const [inquiry] = inquiryList;
        setCardInfo({
          cardType: staticCardType,
          cardNo: '111',
          isAvailable: true,
          cardUseDomain: {
            useStatus: inquiry ? 2 : 1,
          },
          inquiry,
          cardStatus: 2,
          staticCard: true,
        });
      });
      return;
    }
    const fetchService = async () => {
      const res = await fetchJson({
        type: 'POST',
        url: '/api/api/v1/patient/medical/service/package/queryCardUseInfo',
        data: {
          cardNo: biz_id,
          option: {
            needQueryCardUse: true,
          },
        },
        isloading: false,
      });
      if (res && res.code === '0') {
        const cardData = res.result || {};
        const { patientId, cardType, cardNo, cardStatus, channelResourceCode = '', cardUseDomain: { illnessDescription = '', department = '' } = {} } = cardData;
        // const { cardType, cardStatus } = cardData;
        /* 下单接口，只在支付宝-蚂蚁渠道时调用，并且这张卡的信息对应的渠道也是 蚂蚁渠道 */
        if (validate.isAlipayApplet() && isFromAliER(getChannelResourceCode() || '') && isFromAliER(channelResourceCode) && cardType === 'onlineConsult' && cardStatus === '1') {
          getUnifiedCalculate({ workDepartment: department, patientId }).then(async (data) => {
            const { preferenceCalculateInfo: { order: { platformCode = '', orderRealAmount = 0, orderAmount = 0, orderGoods = [] } = {} } = {} } = data;

            const inquiryData: {
              inquiryNo?: string;
              staffId?: string;
              [key: string]: number | null | string | undefined | object;
            } = {
              cardNo,
              patientId,
              orderGoods,
              orderAmount,
              illnessDescription,
              platformCode,
              orderRealAmount,
              receptionDepartmentCode: department,
              inquiryLanguage: 'zh_CN',
              channelInquiryRoute: { channelInquiryNo, channelContractNo },
            };
            const res = await fetchJson({
              type: 'POST',
              url: '/api/api/v1/patient/bizno/getByType',
              isloading: false,
              data: { bizNoType: 'INQUIRY_NO' },
            });
            const { code = '', result = '' } = res;
            if (code === '0' && result) {
              inquiryData.inquiryNo = result;
            }
            submitToDoctor(inquiryData);
          });
          setCardInfo(cardData);
          return;
        }
        setCardInfo(cardData);
        if (msgInfo.action === 'newMsgFlag' && (cardType === 'onlineConsult' || cardType === 'ohClinic') && cardStatus === '1') {
          setTime(3);
          timerRef.current = setTimeout(() => {
            toDetail(cardData);
          }, 3000);
          updateSomeMsg({
            ...msgInfo,
            action: '',
          });
        }
      }
    };
    fetchService();
    return () => {
      timerRef.current && clearTimeout(timerRef.current);
    };
  }, []);

  const submitToDoctor = useCallback((inquiryData) => {
    const { orderGoods = [], orderGoods: [firstItem = {}] = [], platformCode = '', illnessDescription = '', inquiryNo = '', cardNo = '', channelInquiryRoute = {} } = inquiryData || {};
    const { goodsId = '' } = firstItem;
    const data: any = {
      businessEntry: {
        ...inquiryData,
        illnessDescription: !illnessDescription ? '其他' : illnessDescription,
        inquiryThirdRelationList: [{ thirdPlatformCode: platformCode }],
        staffId: THIRD_PLATFORM_RESOURCECODE.ZAHY === platformCode ? goodsId : '',
      },
      orderPreferenceList: [],
      orderGoods,
      platformCode,
      orderType: 'inquiry',
      relatedBizNo: inquiryNo,
      orderAmount: 0,
      orderRealAmount: 0,
      bizType: 'I',
      productId: 1,
      cardNo,
    };
    fetchJson({
      type: 'POST',
      url: '/api/api/v1/patient/order/unifiedOrder',
      data,
      isloading: false,
      needToast: false,
    }).then((res) => {
      const { code = '', result = {} } = res;
      if (code === '0') {
        if (result.orderRealAmount === 0) {
          const { businessEntry = {} } = result;
          const { channelInquiryNo = '', channelContractNo = cookies.get('channelContractNo') } = channelInquiryRoute;
          const searchs = Serialize({ channelInquiryNo, channelContractNo, inquiryId: businessEntry.id, inquiryNo });
          window.reactHistory.replace({
            pathname: '/hospital/await',
            search: searchs,
          });
          return;
        }
        jumpNext('/hospital/error', { message: '', type: 'no_initiate' });
      } else {
        jumpNext('/hospital/error', { message: res.message, type: 'no_initiate' });
      }
    });
  }, []);
  const jumpNext = (url, param = {}) => {
    if (!url) {
      return;
    }
    const params = JSON.stringify(param);
    my.redirectTo({ url: `/pages/webview/index?src=${encodeURIComponent(url)}&param=${params}` });
  };

  // eslint-disable-next-line @typescript-eslint/require-await
  const toDetail = useCallback(throttle(async (card: any = {}) => {
    if (!card.isAvailable) {
      return;
    }
    const { cardType = '', cardStatus = '', cardNo = '' } = card;
    const { link } = cards[cardType || ''] || {};
    if (!cardNo) {
      return;
    }
    if (cardStatus == '1') {  // "服务开始" "1" ,"进行中" "2", "服务结束" "3", "已失效"  "4"

      if (cardType === 'mentalRelease') {
        // location.href = link;
        window.reactHistory.push({
          pathname: link,
          search: `cardNo=${cardNo}`,
        });
        return;
      }

      if (cardType === 'registration') {
        jumpBeforeAuth && jumpBeforeAuth(THIRD_PARTNER_AUTH_CONFIG.WEIYI_SERVICE);
        return;
      }

      if (cardType === 'vaccine') {
        jumpBeforeAuth && jumpBeforeAuth(THIRD_PARTNER_AUTH_CONFIG.GLZ_VACCINE);
        return;
      }
      window.reactHistory.push({
        pathname: link,
        search: `cardNo=${cardNo}${card.policyNo ? `&policyNo=${card.policyNo}` : ''}`,
      });
    } else {

      if (cardType === 'ohClinic' || cardType === 'onlineConsult') {
        window.reactHistory.push({
          pathname: '/hospital/myinquiry',
        });
        return;
      }
      window.reactHistory.push({
        pathname: '/hospital/servicelist',
      });
    }
  }, 4000), []);

  const handleClick = () => {
    timerRef.current && clearTimeout(timerRef.current);
    toDetail(cardInfo);
  };

  useInterval(() => {
    setTime(time - 1);
  }, time === 0 ? null : 1000);

  if (!cardInfo || !cardInfo.cardNo) {
    return null;
  }

  return (
    <React.Fragment>
      {
        mode === 'service' && <div className='service_card_comp service_card'>
          <CardButton handleClick={handleClick} time={time} cardInfo={cardInfo} title={title} describe={describe} msgInfo={msgInfo}/>
          <div className='service_bottom'>
            {userNum && <p><span className='strong_num'>{userNum}+</span>人已使用此服务</p>}
            {(percent || userNum) && <p><span className='strong_num'>{percent || '99%'}</span>好评率</p>}
            {extraText && <p>{extraText}</p>}
          </div>
        </div>
      }
    </React.Fragment>
  );
};

export default ServiceCard;
