import { useEffect } from 'react';
import cookies from 'src/utils/cookie';
import { fetchJson } from 'src/utils/fetch';
import { Deserialize } from 'src/utils/serialization';

//寿险、车险渠道进入小程序带入的userId提交给后端进行绑定
const useSomeChannelBizPush = (props) => {
  const { userId: thirdUserId = '', sign = '' } = Deserialize(window.location.search || '');
  const saveChannelBizPushData = () => {
    // this.thirdChannelBizOpt = { thirdUserId: option.userId, sign: option.sign };
    if (!thirdUserId) { return };
    fetchJson({
      url: "/api/api/v1/patient/user/saveChannelBizPushData",
      type: "POST",
      data: {
        sign,
        thirdUserId,
        channelOpenId: cookies.get('openId'),
        channelSource: cookies.get('channelSource') || '',
        channelResourceCode: cookies.get('channelResourceCode'),
      },
      needLoading: false,
      needLogin: false
    });
  }
  useEffect(() => {
    saveChannelBizPushData();
  }, []);
}

export default useSomeChannelBizPush;