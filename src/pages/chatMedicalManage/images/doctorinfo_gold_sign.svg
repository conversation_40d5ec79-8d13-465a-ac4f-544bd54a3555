<?xml version="1.0" encoding="UTF-8"?>
<svg width="186px" height="40px" viewBox="0 0 186 40" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title> 头部金色标签</title>
    <defs>
        <linearGradient x1="0%" y1="50%" x2="100%" y2="50%" id="linearGradient-1">
            <stop stop-color="#F9E7C3" offset="0%"></stop>
            <stop stop-color="#F5D193" offset="100%"></stop>
        </linearGradient>
    </defs>
    <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="2_展开后-/-绑定-/-小帖士" transform="translate(-30.000000, -270.000000)">
            <g id="-头部金色标签" transform="translate(30.000000, 270.000000)">
                <rect id="矩形备份" fill="url(#linearGradient-1)" x="0" y="0" width="186" height="40" rx="4"></rect>
                <text id="专属家庭医生" font-family="PingFangSC-Medium, PingFang SC" font-size="22" font-weight="400" fill="#5F3312">
                    <tspan x="12" y="28">专属家庭医生</tspan>
                </text>
                <g id="弱操作-/-icon-/-24-info_02" transform="translate(152.000000, 9.000000)">
                    <circle id="椭圆形" stroke="#5F3312" stroke-width="2" cx="11" cy="11" r="10"></circle>
                    <circle id="椭圆形" fill="#5F3312" cx="11" cy="6.41666667" r="1.375"></circle>
                    <rect id="矩形" fill="#5F3312" x="10.0833333" y="9.625" width="1.83333333" height="8.25" rx="0.916666667"></rect>
                </g>
            </g>
        </g>
    </g>
</svg>