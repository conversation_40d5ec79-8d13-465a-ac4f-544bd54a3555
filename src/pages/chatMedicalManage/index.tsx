import React, { useEffect, useState, useCallback, useRef, useMemo } from 'react';
import { shallowEqual, useSelector, useDispatch } from 'react-redux';
import { ImgWithDefault, ChatInput } from 'src/components/common';
import GotoPersonalCenter from 'src/components/common/gotoPersonalCenter';
import { useUserWarning } from 'src/components/hooks';
import homeSpecialistConfig from 'src/pages/home/<USER>/specialist/utils';
import { ApplicationState } from 'src/store';
import { fetch_consult_info, add_chat_msg, shift_chat_msg, edit_chat_info, update_some_msg, add_receive_msg_res, reset_chat_state } from 'src/store/chatmedicalmanage/action';
import { getIsBlackUser, setIsBlackUser, storage } from 'src/utils';
import { getChannelResourceCode } from 'src/utils';
import bridge from 'src/utils/bridge';
import cookies from 'src/utils/cookie';
import format from 'src/utils/format';
import { Pull, Carousel, Modal, Popup, Toast } from 'zarm';
import { FETCH_USER_CHANNEL } from '../../store/channel/action-types';
import { HelianHealth, ChatLog, Evaluate, PhoneMessage, Auth, HealthGold, CustomerService, OtherCard, HealthGoldOutlinePage, ChatBlack, ZajkBack, Header } from './components';
import './chatMedicalManage.scss';
import MsgSubscribe from './components/msgSubscribe';
import QuickNavigation from './components/quickNavigation';
import useWebsocket from './websocket';
import { fetchJson } from 'src/utils/fetch';
import { Deserialize } from 'src/utils/serialization';
import validate from 'src/utils/validate';
import { dmEnv } from '@dm/utils';
import { isFromAliER, THIRD_PLATFORM_RESOURCECODE } from 'src/utils/staticData';
import { Prompt } from 'react-router';
import { Drainage } from '../../services/channel-resource';

// 自动回复指令
const AUTO_REPLY = {
  newUserDrawAct: '免费新人专属福利',
  buyGoods: '严选好物大放价',
  bonus: '我知道健康咨询类问题可以咨询医管家！',
};

// 下拉刷新的状态值
const REFRESH_STATE = {
  normal: 0, // 普通
  pull: 1, // 下拉刷新（未满足刷新条件）
  drop: 2, // 释放立即刷新（满足刷新条件）
  loading: 3, // 加载中
  success: 4, // 加载成功
  failure: 5, // 加载失败
};
const DOCTOR_DEFAULT_AVATER = require('src/images/doctor_default_avater.png');

const filterXSS = (html) => String(html).replace(/(<[^<>]+>)|(alert\([^()]+)\)|script|javascript/g, '');

function isToday(date) {
  return format.date(date, 'yyyy-MM-dd') === format.date(new Date(), 'yyyy-MM-dd');
}

const SystemMessage = (props) => {
  const { reply_date = new Date(), reply_content: { content = '' } = {} } = props;
  return (
    <div className='systen_message_comp'>
      <p className='time'>{format.date(reply_date, isToday(reply_date) ? 'hh:mm' : 'MM月dd日 hh:mm')}</p>
      {content && <p className='systen_message'>{content}</p>}
    </div>
  );
};

declare global {
  interface Window {
    leaveConfirm: boolean
    drainages: Drainage[] | undefined;
    unsubscribe?: () => void;
  }
}


const ChatMedicalManage = (props) => {

  const confirmLink = useSelector((state: ApplicationState) => {

    if(state.userChannel && state.userChannel.drainages) {
      return state.userChannel.drainages.find((item) => item.linkLocation === 'consult');
    }
    return undefined;
  });

  const {
    location: { pathname = '', search = '' },
  } = props;
  // todo: 不要忘了去掉首页调试入口
  // needSubscribeMsg 确定是否需要显示订阅医生消息, 取值：0/1 '1': 需要 其他不需要.
  // from = '', stub = '' 健康险双十一体检回调字段
  const { channelContractNo = '', channelInquiryNo = '', needSubscribeMsg = '0', bonus = '', homeType = '', from = '', stub = '' } = Deserialize(search);
  const isSpecialistSource = pathname.includes('/hospital/healthmanage'); // 是从专科模块进入
  const isFamilyDoctor = pathname.includes('/hospital/chatfamilydoctor'); // 是从家庭医生进入
  const userServiceTypeCopywriting = isFamilyDoctor?'家庭医生':'私人医生';
  const homeTypes = storage.get('homeType') || homeType;
  const { healthmanageDoctorIcon = DOCTOR_DEFAULT_AVATER } = homeSpecialistConfig[homeTypes] || {};
  const pageTitleRef: any = useRef(null);
  const [doctorHeadPortrait, setDoctorHeadPortrait] = useState(healthmanageDoctorIcon);
  const [showDoctorInfo, setShowDoctorInfo] = useState(false);
  const [sendVal, setSendVal] = useState('');
  // 是否发出去过消息（直营健康金需求） 健康险双十一复用此字段
  const [isHaveMessage, setHaveMessage] = useState(false);
  const [refreshing, setRefreshing] = useState(REFRESH_STATE.normal);

  const [showFooter, setShowFooter] = useState(true);
  const [currentUser, setCurrentUser]: any = useState({});
  const [sendRights, setSendRights] = useState(false); // 标记可以发送权益消息
  const [sendBusyTips, setSendBusyTips] = useState(false); // 标记第一次拉取的历史记录已经完成 可以设置hasPull 并且可以提示护士繁忙
  const [hasPull, setHasPull] = useState(false); // 标记拉取过历史记录，给allMessageShow使用
  const [allMessageShow, setAllMessageShow] = useState(false); // 标记拉取过历史记录是否已经加载完
  const [showShareGuide, setShowShareGuide] = useState(false);
  // 预览图片的弹窗
  const [showImageCarousel, setShowImageCarousel] = useState(false);
  const [imageCarouselActive, setImageCarouselActive] = useState(0);

  const useWarningStatus = useUserWarning();
  const [isBlackUser, setCurrentBackUser] = useState(getIsBlackUser());
  const [modalVisible,setModalVisible] = useState<boolean>(false);
  const [imgSrc,setImgSrc] = useState<string | undefined>();
  const [jumpUrl,setJumpUrl] = useState<string | undefined>();
  const allowBack = useRef<Boolean>(false);

  const msgHeightRef: any = useRef(null); // 下拉拉取历史消息时，消息滚动到当前位置
  const popWrapperEl: any = useRef(null);
  const containerRef: any = useRef(null);
  const popRef: any = useRef(null);
  const isRealAuth: string = cookies.get('isRealAuth') || 'N';

  const {
    msgList,
    historyTime,
    roomId = '',
    userHeadPortrait = '',
    consultInfo,
    // thirdPatientInfoDomain,
    doctorInfo = {},
    assistantInfo = {},
    PROFESSIONALTITLE_OBJ,
    // channelRight,
    // showAddWechat,
  } = useSelector((state: ApplicationState) => {
    // const { channelRight } = state.userChannel;
    console.log('state--',state);
    return {
      ...state.chat,
      userChannel:state.userChannel,
      PROFESSIONALTITLE_OBJ: state.dictionary.PROFESSIONALTITLE_OBJ,
      // channelRight,
      // showAddWechat: state.config.showAddWechat,
    };
  }, shallowEqual);


  useEffect(() => {
    if(isFromAliER()) {
      return Toast.show({
        content: '跳转有误，请重新发起问诊流程。如遇问诊问题可联系客服处理',
        stayTime: 60* 60 * 60 * 60 * 1000,
        mask: true,
      });
    }
    dispatch({ type: FETCH_USER_CHANNEL });
  }, []);

  useEffect(() => {
    if (isHaveMessage && from === 'physicalFitnessTest') {
      fetchJson({
        type: 'POST',
        url: '/api/api/v1/patient/task/zaHealh11TaskCallBack',
        data: {
          bizCode: stub,
        },
        needLoading: false,
      });
    }
  }, [isHaveMessage]);

  // console.log('msgList', msgList)

  const { isNurseOnDuty, isAllDoctorRest, consultNo, consultStatus, isAllNurseBusy, needPushAddWechatCard } = consultInfo;

  const photoList = useMemo(() => {
    const list: any = [];
    msgList.forEach((item) => {
      if (item.msg_type == 4) {
        list.push({ source: item.reply_content.link });
      }
    });
    return list;
  }, [msgList]);

  const dispatch = useDispatch();
  const fetchConsultInfo = (data?) => dispatch(fetch_consult_info(data));
  const addMsg = (data) => dispatch(add_chat_msg(data));
  const shiftMsg = (data) => dispatch(shift_chat_msg(data));
  const editChatInfo = (data) => dispatch(edit_chat_info(data));
  const updateSomeMsg = (data) => dispatch(update_some_msg(data));
  const addReceiveMsgRes = (data) => dispatch(add_receive_msg_res(data));
  const resetChatState = () => dispatch(reset_chat_state());

  // 处理websocket的消息
  const handleMsg = useCallback(
    (res) => {
      console.log('handleMsg', res);
      const { action, list = [], msg, next, users } = res;
      switch (action) {
      case 'history': {
        // 咨询单进行中的时候默认拉取一次历史记录，此时设置滚动到底部,其余时候设置加载成功
        if (msgList.length === 0) {
          scrollBottom(500);
        } else {
          scrollToCurrentHeight();
          setRefreshing(REFRESH_STATE.normal);
        }
        if (list.length > 0) {
          list.reverse();
          shiftMsg(list);
          setAllMessageShow(false);
        } else {
          if (hasPull) {
            setAllMessageShow(true);
          }
          // setAllMessageShow(true);
        }
        setSendBusyTips(true);
        next && editChatInfo({ historyTime: next });
        break;
      }
      case 'news': {
        const {
          reply_content: { mode = '' },
        } = msg;
          // 实名卡消息
          // if (mode === 'realauth') {
          //   return;
          // }

        // 给收到服务卡创建一个标记，3s自动跳转，patient创建一个标记，历史消息的时候就不用滚动
        if (mode === 'service' || mode === 'patient') {
          msg.action = 'newMsgFlag';
        }
        addMsg([msg]);
        scrollBottom(200);
        break;
      }
      case 'auth': {
        // 按用户信息显示用户头像
        const me =
            (users || []).filter((item) => item.isme)[0] || {};
        setCurrentUser(me);
        setSendRights(true);
        break;
      }
      case 'reply': {
        !isHaveMessage && setHaveMessage(true); // 第一次发消息时触发
        addReceiveMsgRes(res.reqid);
        break;
      }
      case 'event': {
        const { value = '', key = '' } = res;
        if (key === 'PEER_STATUS') {
          bridge.setTitle(`${value === 'TYPING' ? '对方正在输入中...' : pageTitleRef.current || doctorInfo.staffName}`);
        } else if (key == 'BLACK_USER') {
          // 监听用户是否为黑名单
          const nextVal = value == 'Y' ? true : false;
          setIsBlackUser(nextVal);
          setCurrentBackUser(nextVal);
        } else if (key == 'CHAT_END') {
          // 结束咨询事件
          setShowFooter(false);
          if (popWrapperEl.current) {
            popWrapperEl.current.style = {};
          }
        } else if (key == 'CHANGE_NURSE') {
          // 护士信息变更更新,重新调用护士信息接口
          initConsultInfo();
        }
        break;
      }
      default:
        addMsg([msg]);
        scrollBottom();
        console.log('default', msg);
      }
    },
    [historyTime, msgList.length === 0, doctorInfo.staffName, hasPull],
  );

  const { wsGetHistoryMessage, wsSendMessage } = useWebsocket(roomId, handleMsg);
  const initConsultInfo = ()=>{
    const { pathname, search } = window.location;
    const channelInquiryRouteOption = {
      channelInquiryRoute: { channelInquiryNo, channelContractNo },
      // userServiceType  整型   0-首页健康咨询 1-签约医生
      userServiceType: isFamilyDoctor ? 1 : 0,
    };
    if (isRealAuth === 'Y') {
      // 03.08 支付宝渠道需要添加的字段 channelContractNo && channelInquiryNo
      fetchConsultInfo(channelInquiryRouteOption);
    } else {
      const fetchcheck = async () => {
        const res = await fetchJson({
          type: 'GET',
          url: '/api/api/v1/patient/medical/consult/checkThresholdReached', // 如果这个患者多次问诊又没有实名就需要先去实名
          data: {},
          isloading: false,
        });
        if (res && res.code === '0') {
          if (res.result) {
            console.log('-------------------这里是咨询页 实名-------------------');
            props.history.replace({
              pathname: `/hospital/certification${isRealAuth === 'NY' ? '/select' : ''}`,
              search: `pathname=${encodeURIComponent(pathname)}&jumpSearch=${encodeURIComponent(search)}`,
            });
          } else {
            fetchConsultInfo(channelInquiryRouteOption);
          }
        }
      };
      fetchcheck();
    }
  };
  useEffect(() => {
    initConsultInfo();
    return () => {
      // 退出当前页面时 清空咨询单数据
      resetChatState();
    };
  }, []);

  // 显示医生介绍弹窗逻辑
  // useEffect(() => {
  //   if (!isAllDoctorRest) {
  //     return
  //   }
  //   const time = storage.get('showDoctorIndroduceDate');
  //   const today = format.date(new Date(), 'yyyy-MM-dd');
  //   if (time !== today) {
  //     storage.set('showDoctorIndroduceDate', today, 24 * 60);
  //     setShowModal(true);
  //     timerRef.current = setTimeout(() => {
  //       setShowModal(false);
  //     }, 3000);
  //   }
  //   return () => {
  //     timerRef.current && clearTimeout(timerRef.current)
  //   }
  // }, [isAllDoctorRest]);

  useEffect(() => {
    if (!isSpecialistSource) {
      if (isFamilyDoctor) {
        if (doctorInfo && doctorInfo.staffName) {
          pageTitleRef.current = '家庭医生团队';
          bridge.setTitle(pageTitleRef.current);
        }
      } else {
        if (assistantInfo && assistantInfo.assistantName) {
          pageTitleRef.current = `私人医生  ${doctorInfo.staffName}`;
          bridge.setTitle(pageTitleRef.current);
        }
      }
      setDoctorHeadPortrait(doctorInfo.headPortrait ? `${doctorInfo.headPortrait}` : DOCTOR_DEFAULT_AVATER);
    } else {
      pageTitleRef.current = '众安健康顾问';
    }
  }, [doctorInfo, assistantInfo, isAllDoctorRest]);

  useEffect(() => {
    // 权益页进入的时候发送权益消息
    if (sendRights) {
      const { search } = window.location;
      // 发送消息给医生端
      const isCurrentBlackUser = getIsBlackUser();
      let { rightsname = '', subname = '' } = Deserialize(search);
      if (consultStatus !== '2' && isAllDoctorRest !== 'Y' && !isCurrentBlackUser) {
        if ((rightsname || subname) && wsSendMessage) {
          subname = AUTO_REPLY[subname] || subname;
          const decodeSubname = filterXSS(decodeURIComponent(subname));
          sendMsg(
            {
              content: rightsname ? `您好，我想进行${filterXSS(decodeURIComponent(rightsname))}服务` : `您好，我想${decodeSubname}`,
            },
            2,
            !!AUTO_REPLY[subname],
          );
        }
        /* 支付宝 蚂蚁渠道 首次进入推送患者信息 */
        // let { illnessDescription = '', symptomDuration = '' } = thirdPatientInfoDomain;
        /* 推送 患者信息*/
        /* illnessDescription &&
          symptomDuration &&
          sendMsg(
            {
              title: '您好，我是好医保·门诊险用户，想进行图文问诊服务2。',
              content: ` 问诊人：${patientName}，${patientGender}，${patientAge}岁 <br />
          主诉：${illnessDescription} <br />
          症状持续时间：${symptomDuration}`,
            },
            9,
          );*/
      }
    }
  }, [sendRights, isAllDoctorRest]);

  useEffect(() => {
    // 推送医生繁忙的系统提示
    const isCurrentBlackUser = getIsBlackUser();
    if (sendBusyTips && isAllNurseBusy && doctorInfo.staffName && !isCurrentBlackUser) {
      // addMsg([
      //   {
      //     msg_type: 1,
      //     user_type: 2,
      //     reply_content: {
      //       content: `请稍等片刻，${doctorInfo.staffName.slice(0, 1)}医生正在查看您的健康档案`,
      //     },
      //     reply_date: new Date().getTime(),
      //   },
      // ]);
    }
  }, [sendBusyTips, isAllNurseBusy, doctorInfo]);

  const refreshData = useCallback(() => {
    setRefreshing(REFRESH_STATE.loading);
    if (allMessageShow || (!sendBusyTips && isAllDoctorRest !== 'Y')) {
      setTimeout(() => {
        setRefreshing(REFRESH_STATE.success);
      }, 0);
      return;
    }
    setHasPull(true);
    if (popRef.current) {
      msgHeightRef.current = popRef.current.pull.getBoundingClientRect().height;
    }
    !historyTime && editChatInfo({ historyTime: new Date().getTime() });
    // wsSendMessage && wsSendMessage(`{"action":"history","time": ${historyTime || new Date().getTime()},"direction":"up","limit":10}`);
    wsGetHistoryMessage && wsGetHistoryMessage(historyTime);
    setTimeout(() => {
      setRefreshing(REFRESH_STATE.normal);
    }, 2000);
  }, [historyTime, allMessageShow, sendBusyTips, isAllDoctorRest]);

  // 发送聊天信息
  const sendMsg = useCallback((content, type = 2, is_const = false) => {
    // msg type   - 1 : 系统提示 - 2 : 文本 - 3 : 卡片 - 4 : 图片 - 5 : 语， 9：支付宝蚂蚁推送患者信息

    // team 53003 直营app进来，且bonus=1，content='健康金打卡'
    if (dmEnv.isApp() && bonus == '1' && AUTO_REPLY.bonus == content.content) {
      is_const = true;
    }
    const msg = {
      action: 'reply',
      msg: {
        msg_type: type,
        reply_content: content,
        is_const,
      },
      reqid: new Date().getTime(),
    };
    wsSendMessage && wsSendMessage(JSON.stringify(msg));
    addMsg([
      {
        msg_type: type,
        user_type: 1,
        reply_content: content,
        is_const,
        reqid: msg.reqid,
      },
    ]);
    scrollBottom();
  }, []);

  // 发送选择患者
  const sendPatient = useCallback((msgInfo, key = 'MSG_CHANGE') => {
    updateSomeMsg(msgInfo);
    let msgContent = { ...msgInfo };
    if (key === 'HAS_AUTH') {
      const { reply_content = {}, reply_content: { final = {} } = {} } = msgInfo;
      msgContent = {
        ...msgContent,
        reply_content: {
          ...reply_content,
          final: {
            patient_id: final.patient_id,
            patient_name: final.patient_name,
          },
        },
      };
    }
    const msg = {
      action: 'event',
      key,
      msg: msgContent,
      reqid: new Date().getTime(),
    };
    wsSendMessage && wsSendMessage(JSON.stringify(msg));
  }, []);

  /* 点击 发送 按钮*/
  const submitSend = useCallback(() => {
    const value = sendVal.replace(/^\s+|\s+$/g,'');
    if (!value) {
      return;
    }
    sendMsg(
      {
        content: value,
      },
      2,
    );
    setSendVal('');
  }, [sendVal]);

  // 键盘收缩页面回弹
  const windowScrollBottom = () => {
    window.scroll({
      top: 0,
    });
    popWrapperEl.current.style = {};
  };

  // 页面滑动到底部
  const scrollBottom = useCallback((time = 100) => {
    setTimeout(() => {
      if (popWrapperEl.current && popRef.current) {
        popWrapperEl.current.scrollTo({
          top: Number(popRef.current.pull.getBoundingClientRect().height) + 500,
          behavior: 'smooth',
        });
      }
    }, time);
  }, []);

  const scrollToCurrentHeight = useCallback(() => {
    setTimeout(() => {
      if (popWrapperEl.current && popRef.current) {
        popWrapperEl.current.scrollTo({
          top: popRef.current.pull.getBoundingClientRect().height - (msgHeightRef.current || 0),
        });
      }
    }, 0);
  }, []);

  useEffect(() => {
    // 监听安卓软键盘事件 处理聊天信息滚动
    // 获取原窗口的高度
    if (!validate.isIos()) {
      setShowDoctorInfo(false);
      const originalHeight = document.documentElement.clientHeight || document.body.clientHeight;
      window.onresize = function() {
        // 键盘弹起与隐藏都会引起窗口的高度发生变化
        const resizeHeight = document.documentElement.clientHeight || document.body.clientHeight;
        if (resizeHeight - 0 < originalHeight - 0) {
          // 当软键盘弹起，在此处操作
          scrollBottom();
        } else {
          // 当软键盘收起，在此处操作
        }
      };
    }
    return () => {
      window.onresize = null;
    };
  }, []);

  // Input focus
  const focusInput = useCallback(() => {
    setShowDoctorInfo(false);
    console.log(popWrapperEl.current.scrollHeight, popWrapperEl.current.clientHeight);
    if (!validate.isIos()) {
      return;
    }
    // 处理ios系统软键盘不收起 消息记录少的时候 使消息可以看见的问题
    if (popWrapperEl.current && popRef.current) {
      setTimeout(() => {
        const height = document.documentElement.scrollTop || window.pageYOffset || document.body.scrollTop;
        if (height) {
          popWrapperEl.current.style.top = `${height - 50}px`;
        }
        scrollBottom(0);
        // popWrapperEl.current.scrollTo({
        //   top: popRef.current.pull.getBoundingClientRect().height,
        //   behavior: 'smooth'
        // });
      }, 300);

      setTimeout(() => {
        const height = document.documentElement.scrollTop || window.pageYOffset || document.body.scrollTop;
        window.scroll({
          top: height + 40,
        });
      }, 300);
    }
  }, []);

  // 点击图片放大展示
  const photoClick = (reply_content) => {
    console.log('photoClick', reply_content);
    const { link = '' } = reply_content || {};
    photoList.forEach((element, index) => {
      if (element.source == link) {
        setShowImageCarousel(true);
        setImageCarouselActive(index);
      }
    });
  };

  if (dmEnv.isApp() && bonus == '1' && isAllDoctorRest === 'Y') {
    return <HealthGoldOutlinePage />;
  }
  const channelResourceCode = getChannelResourceCode();
  // 判断资源位是否是最福利或者众安小贷

  const isZuifuliOrZaloanFunc = (channelResourceCode: any) => {
    const resourceCodeArr = ['XDQYFQK', 'ZFLapp_JKBKTWWZ', 'ZFLapp_JKBKYYGH', 'ZFLapp_JKBKXLZX', 'ZFLapp_JKBKYMJZ', 'ZFLapp_JKBKJKZX', 'TEST_ZFLapp_JKBKYYGH', 'TEST_ZFLapp_JKBKXLZX', 'TEST_ZFLapp_JKBKYMJZ', 'TEST_ZFLapp_JKBKJKZX'];
    return resourceCodeArr.includes(channelResourceCode);
  };
  const isZuifuliOrZaloan = isZuifuliOrZaloanFunc(channelResourceCode);

  return (
    <div ref={containerRef} className='chat_ygj_page'>
      <Popup visible={modalVisible} direction='center' >
        <a className='confirm-leave-modal-content' href={jumpUrl}>
          <img src={imgSrc} alt='挽留图' />
        </a>
        <a
          onClick={() => {
            allowBack.current = true;
            setModalVisible(false);
            window.reactHistory.goBack();
          }}
          className='confirm-leave-modal-ci'
        >
          <img src={require('../../images/icon_close.png')} alt='' />
        </a>
      </Popup>
      <Prompt message={(location,action) => {

        if(allowBack.current) {
          return true;
        }

        if(confirmLink && action === 'POP') {
          setImgSrc(confirmLink.attachmentList[0].attachmentDownloadUrl);
          setJumpUrl(confirmLink.linkUrl);
          setModalVisible(true);
          return false;
        }

        return true;
      }} />
      {
        isAllDoctorRest && <Header
          needPushAddWechatCard={needPushAddWechatCard}
          isFamilyDoctor={isFamilyDoctor}
          userServiceTypeCopywriting={userServiceTypeCopywriting}
          showDoctorInfo={showDoctorInfo}
          setShowDoctorInfo={setShowDoctorInfo}
          isAllDoctorRest={isAllDoctorRest}
          doctorInfo={doctorInfo}
          assistantInfo={assistantInfo}
          doctorHeadPortrait={doctorHeadPortrait}
          PROFESSIONALTITLE_OBJ={PROFESSIONALTITLE_OBJ}
        // channelRight={channelRight}
        // showAddWechat={showAddWechat}
        />
      }

      {(isNurseOnDuty || isAllDoctorRest) && (
        <main className={'main'} ref={popWrapperEl}>

          <Pull
            ref={popRef}
            className='pull_msg_wrapper'
            refresh={{
              state: refreshing,
              handler: refreshData,
              render: (refreshState) => {
                const cls = 'custom_control_pull';
                if (allMessageShow) {
                  return null;
                }
                switch (refreshState) {
                case REFRESH_STATE.pull:
                  return <div className={cls}>下拉加载更多聊天记录</div>;
                case REFRESH_STATE.drop:
                case REFRESH_STATE.loading:
                  return (
                    <div className={cls}>
                      <div className='gird'>
                        <i></i>
                        <i></i>
                        <i></i>
                      </div>
                    </div>
                  );
                default:
                  return null;
                }
              },
            }}
          >
            {allMessageShow && <p className='no_message_tip'>已没有历史信息了</p>}
            {msgList.map((item, index) => {
              const { msg_type, reply_date } = item;
              switch (msg_type) {
              case 1:
                return <SystemMessage key={`msg${reply_date || index}`} {...item} />;
              case 3:
                return <ChatLog key={`msg${reply_date || index}`} msgInfo={item} currentUser={{ ...currentUser, userHeadPortrait }} doctorHeadPortrait={doctorHeadPortrait} />;
              case 6: {
                const { reply_content: { mode = '' } = {} } = item; // "mode":"mobile|evaluate|service|realauth|patient"
                switch (mode) {
                case 'mobile':
                  return <PhoneMessage key={`msg${reply_date || index}`} msgInfo={item} doctorInfo={doctorInfo} assistantInfo={assistantInfo} consultInfo={consultInfo} updateSomeMsg={updateSomeMsg} isFamilyDoctor={isFamilyDoctor} userServiceTypeCopywriting={userServiceTypeCopywriting}/>;
                case 'evaluate':
                  return (
                    <Evaluate
                      key={`msg${reply_date || index}`}
                      msgInfo={item}
                      doctorInfo={doctorInfo}
                      isFamilyDoctor={isFamilyDoctor}
                      userServiceTypeCopywriting={userServiceTypeCopywriting}
                      assistantInfo={assistantInfo}
                      consultInfo={consultInfo}
                      scrollBottom={scrollBottom}
                      onShare={() => {
                        setShowShareGuide(true);
                      }}
                    />
                  );
                case 'service':
                  return <ChatLog key={`msg${reply_date || index}`} msgInfo={item} currentUser={{ ...currentUser, userHeadPortrait }} doctorHeadPortrait={doctorHeadPortrait} updateSomeMsg={updateSomeMsg} />;
                case 'realauth':
                  return <Auth key={`msg${reply_date || index}`} msgInfo={item} sendPatient={sendPatient} consultInfo={consultInfo} />;
                case 'patient':
                  return <ChatLog key={`msg${reply_date || index}`} msgInfo={item} consultInfo={consultInfo} sendPatient={sendPatient} currentUser={{ ...currentUser, userHeadPortrait }} doctorHeadPortrait={doctorHeadPortrait} scrollBottom={scrollBottom} />;
                case 'cs':
                  return <CustomerService key={`msg${reply_date || index}`} />;

                case 'tips':
                case 'bind':
                  return <OtherCard key={`msg${reply_date || index}`} consultInfo={consultInfo} msgInfo={item} currentUser={{ ...currentUser, userHeadPortrait }} doctorHeadPortrait={doctorHeadPortrait} />;
                default:
                  return null;
                }
              }
              default:
                return <ChatLog key={`msg${reply_date || index}`} msgInfo={item} photoClick={photoClick} currentUser={{ ...currentUser, userHeadPortrait }} doctorHeadPortrait={doctorHeadPortrait} />;
              }
            })}
          </Pull>
        </main>
      )}

      {isBlackUser || useWarningStatus.userBlackFlag ? (
        <ChatBlack />
      ) : (
        isAllDoctorRest === 'N' &&
        Number(consultStatus || '') < 3 &&
        showFooter && (
          <footer className='footer'>
            <QuickNavigation />
            <MsgSubscribe visible={needSubscribeMsg === '1'} />
            <ChatInput popWrapperEl={popWrapperEl} sendVal={sendVal} businessNo={consultNo} handleChange={(e) => setSendVal(e.target.value)} handleFocus={focusInput} handleBlur={windowScrollBottom} handleSumbit={submitSend} chatSendMsg={sendMsg} platformCode={THIRD_PLATFORM_RESOURCECODE.ZAHY} />
          </footer>
        )
      )}

      {/* <TeamFeature
        direction="top"
        visible={showModal}
        afterClose={() => setShowModal(false)}
      /> */}

      <HealthGold bonusInstruction={AUTO_REPLY.bonus} isHaveMessage={isHaveMessage} setSendInputVal={setSendVal} {...props} />


      <Modal
        className='previewimage-component__imagemodal'
        visible={showImageCarousel}
        maskClosable
        onCancel={() => {
          setShowImageCarousel(false);
        }}
      >
        <div
          onClick={() => {
            setShowImageCarousel(false);
          }}
        >
          <Carousel className='image_modal_carousel' activeIndex={imageCarouselActive} showPagination={false}>
            {photoList.map((photo, index) => (
              <div className='carousel__item__pic' key={+index}>
                <ImgWithDefault src={photo.source} alt='' />
              </div>
            ))}
          </Carousel>
        </div>
      </Modal>
      <div className={`modal_share_chat ${showShareGuide ? 'show' : ''}`} onClick={() => setShowShareGuide(false)}>
        <img className='img_share_guide' src={require('src/images/share_guide.png')} />
      </div>
      {(isNurseOnDuty || isAllDoctorRest) && isZuifuliOrZaloan && (
        <GotoPersonalCenter containerRef={containerRef} />
      )}
      <ZajkBack from={from} />
      <HelianHealth {...props} />
    </div>
  );
};

export default ChatMedicalManage;
