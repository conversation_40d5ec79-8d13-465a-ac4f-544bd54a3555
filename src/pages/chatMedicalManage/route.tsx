/*
 * @Author: <EMAIL> <EMAIL>
 * @Date: 2022-12-12 16:23:57
 * @LastEditTime: 2023-01-03 13:20:17
 * @LastEditors: sen.lv <EMAIL>
 * @FilePath: \za-asclepius-patient-h5\src\pages\chatMedicalManage\route.tsx
 * @Description:
 */
/*
 * @date :2021-03-09
 * @description：图文问诊模块路由
 */
const prefix = '/hospital/';
const route = [
  // 医管家：chatmedicalmanage，专科众安健康顾问：healthmanage
  {
    path: `${prefix}chatmedicalmanage`,
    name: 'ChatMedicalManage',
    component: () => import(/* webpackPrefetchPlaceHolder */ '../inquiryForm/aiGuideNext'),
    auth: true,
    exact: true,
    title: '众安互联网医院',
    keepAlive: true,
    realAuth: false,
  },
  {
    path: `${prefix}chatmedicalmanageguide`,
    name: 'ChatMedicalManage',
    component: () => import(/* webpackPrefetchPlaceHolder */ './index'),
    auth: true,
    exact: true,
    title: '众安互联网医院',
  },
  {
    path: `${prefix}aiChat`,
    name: 'AIChat',
    component: () => import(/* webpackPrefetchPlaceHolder */ './aiChat'),
    auth: true,
    exact: true,
    title: '众安智能咨询助手',
  },
  {
    path: `${prefix}aiChat/intro`,
    name: 'AIChatIntro',
    component: () => import(/* webpackPrefetchPlaceHolder */ './aiChat/Intro'),
    auth: false,
    exact: true,
    realAuth: false,
    title: '众安智能咨询助手',
  },
  {
    path: `${prefix}chatmedical`,
    name: 'chatmedical',
    component: () => import(/* webpackPrefetchPlaceHolder */ './index'),
    auth: true,
    exact: true,
    title: '众安互联网医院',
  }, {
    path: `${prefix}healthmanage`,
    name: 'ChatHealthMedicalManage',
    component: () => import(/* webpackPrefetchPlaceHolder */ './index'),
    auth: true,
    exact: true,
    title: '众安健康顾问',
  }, {
    path: `${prefix}chatfamilydoctor`,
    name: 'ChatFamilyDoctor',
    component: () => import(/* webpackPrefetchPlaceHolder */ './index'),
    auth: true,
    exact: true,
    title: '众安互联网医院',
  },
];

export default route;
