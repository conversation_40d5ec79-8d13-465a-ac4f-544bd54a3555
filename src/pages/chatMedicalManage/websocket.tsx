import { useState, useEffect, useCallback, useRef } from 'react';
import { shallowEqual, useSelector, useDispatch } from 'react-redux';
import { StaticToast } from 'src/components/common';
import { ApplicationState } from 'src/store';
import { edit_chat_info, update_consult_info, reset_msg_state } from 'src/store/chatmedicalmanage/action';
import cookies from 'src/utils/cookie';
import { Deserialize } from 'src/utils/serialization';
import { getChannelResourceCode } from 'src/utils/channel';
import { isFromAliER } from 'src/utils/staticData';
import { storage } from 'src/utils';

const RECONNECT_LIMITED = 3;
const pageKey = `pageYGJVisibility`;

enum ReadyState {
  UNINSTANTIATED = -1,
  CONNECTING = 0,
  OPEN = 1,
  CLOSING = 2,
  CLOSED = 3,
}

const useWebsocket = (roomId, handleMsg) => {

  const wsref: any = useRef();
  const handleRef: any = useRef();
  const afterAuthRef: any = useRef();
  const [websocket, setWebsocket]: any = useState(null);
  const [reconnectTimes, setReconnectTimes] = useState(0);

  const { historyTime, consultInfo: { consultStatus = '', isAllDoctorRest = '', consultNo = '' } } = useSelector((state: ApplicationState) => {
    return state.chat;
  }, shallowEqual);
  const dispatch = useDispatch();
  const editChatInfo = (data) => dispatch(edit_chat_info(data));
  const resetMSGState = () => dispatch(reset_msg_state());
  const updateConsult = (data?) => dispatch(update_consult_info(data));

  useEffect(() => {
    if (roomId) {
      // const url = 'ws://127.0.0.1:7777/ws';
      const url = cookies.get('env_ws');
      const ws = new WebSocket(url || '');
      wsref.current = ws;
      setWebsocket(ws);
    }
    return () => {
      if (wsref.current) {
        wsref.current.close();
        setWebsocket(null);
        wsref.current = null;
      }
    }
  }, [reconnectTimes, roomId]);

  useEffect(() => {
    handleRef.current = handleMsg;
  }, [handleMsg]);

  useEffect(() => {
    afterAuthRef.current = () => {
      console.log('auth', consultStatus, historyTime);
      if ((isAllDoctorRest !== 'Y' || isFromAliER(getChannelResourceCode() || ''))  && historyTime) {
        // let options = {
        //   "action": "history",
        //   "time": historyTime || new Date().getTime(),
        //   "direction": "up",
        //   "limit": 10,
        //   "consult_no": isFromAliER(getChannelResourceCode() || '') ? consultNo : "", //蚂蚁渠道 只展示单个咨询单的消息，解决服务卡跳转问题
        // };
        // websocket.send(JSON.stringify(options));
        wsGetHistoryMessage(historyTime);
        // up往上翻历史，down往下翻历史；
      }
    }
  }, [consultStatus, historyTime, websocket, isAllDoctorRest]);

  useEffect(() => {
    if (websocket) {
      //websocket链接成功回调
      websocket.onopen = () => {
        console.log('connected');
        websocket.send(`{"action":"gettime","reqid":${new Date().getTime()}}`);
      };
      //websocket错误处理
      websocket.onerror = () => {
        console.log('error');
        if (reconnectTimes <= RECONNECT_LIMITED) {
          setReconnectTimes(reconnectTimes + 1);
        } else {
          StaticToast.warning('无法连接到聊天服务器，请稍后再试');
          websocket && websocket.close();
        }
      };
      //
      websocket.onmessage = (evt) => {
        const res = JSON.parse(evt.data);
        console.log('message')
        console.log(res);

        const fn = handleRef.current;

        //获取服务器时间 用来拉取历史记录
        if (res.result && res.result.action === 'gettime') {
          if (res && res.code === '0') {
            // afterAuthRef.current && afterAuthRef.current();
            if (res.result.time) {
              editChatInfo({ historyTime: res.result.time });
            };
            const token = cookies.get('za_token') || '';
            // 添加 scene 字段，用于 体检报告解读入口，消息的主动推送
            let { scene = '' ,pointCode = ''} = Deserialize(window.location.search);
            websocket.send(`{"action":"auth","user_type":1,"token":"${token}","room_id":${roomId},"scene":"${scene}","point_code":"${pointCode}"}`);

            if(!isFromAliER(getChannelResourceCode() || '') && consultNo) {
              websocket.send(`{"action":"validBiz","biz_type":"CONSULT","biz_no":"${consultNo}"}`);
            }
          }
        }
        //检验咨询单是否在有效状态，如果不是则获取新的咨询单
        if(res.result && res.result.action === 'validBiz' && res.code != 0) {
          updateConsult();
        }
        //监听用户验证信息
        if (res.result && res.result.action === 'auth') {
          if (res && res.code === '0') {
            fn && fn(res.result);
            afterAuthRef.current && afterAuthRef.current();
          }
        }
        //处理咨询单已创建医生关诊下班这种零界点用户发不了消息的特殊情况
        if (res.result && res.result.action === 'reply' && res.code === '32001') {
          fn && fn(res.result);
        }
        //监听用户发送的信息是否成功
        if (res.result && res.result.action === 'reply' && res.code === '0') {
          fn && fn(res.result);
        }
        //监听拉取用户之前的聊天记录
        if (res.result && res.result.action === 'history') {
          fn && fn(res.result);
        }
        //监听用户收到的信息
        if (res.action && res.action === 'news') {
          if (res.msg && res.msg.msg_id) {
            websocket.send(`{"action":"event","key":"HAS_READ","value":"${res.msg.msg_id}"}`);
          }
          fn && fn(res);
        }
        //监听event
        if (res.action && res.action === 'event') {
          fn && fn(res);
        }
      };

      websocket.onclose = () => {
        resetMSGState();
        setWebsocket(null);
        console.log('close');
      }
    }
  }, [websocket, consultNo]);

  const watchPageVisibilityState = useCallback(() => {
    if (document.visibilityState === 'visible') {
      let nowTime = Date.now();
      const preTime = storage.get(pageKey) || nowTime;
      const diffTime = (nowTime - preTime) / 1000 / 60;
      if (diffTime >= 3 || (!websocket || websocket.readyState == 2 || websocket.readyState == 3)) {
        storage.remove(pageKey);
        window.location.reload();
      }
    } else {
      storage.set(pageKey, Date.now(), 65);
    }
  }, [websocket]);

  useEffect(() => {
    document.addEventListener('visibilitychange', watchPageVisibilityState);
    return () => {
      document.removeEventListener('visibilitychange', watchPageVisibilityState);
    };
  }, [websocket]);

  const wsSendMessage = useCallback(message => {
    const ws = wsref.current;
    if (ws && ws.readyState === ReadyState.OPEN) {
      ws.send(message);
    } else {
      StaticToast.warning('无法连接到聊天服务器，请稍后再试');
    }
  }, [websocket]);
  const wsGetHistoryMessage = useCallback(historyTime => {
    let options = {
      "action": "history",
      "time": historyTime || new Date().getTime(),
      "direction": "up",
      "limit": 10,
      "consult_no": isFromAliER(getChannelResourceCode() || '') ? consultNo : "", //蚂蚁渠道 只展示单个咨询单的消息，解决服务卡跳转问题
    };
    wsSendMessage(JSON.stringify(options));
  }, [websocket]);

  return {
    wsSendMessage,
    wsGetHistoryMessage,
  }
}

export default useWebsocket;
