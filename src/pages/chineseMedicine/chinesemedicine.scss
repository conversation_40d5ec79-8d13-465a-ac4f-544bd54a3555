@import "src/style/index";

.chinesemedicine_page {
  padding-bottom: r(60);

  .sugar_recommond {
    padding: 0 r(15);
  }

  .background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: r(170);
    background: linear-gradient(230deg, #3e4565 0%, #323953 100%);
    z-index: -1;
  }

  .head {
    margin: r(17) auto;
    width: r(345);
    height: r(285);
    padding-top: r(10);
    box-shadow: 0 r(10) r(13) 0 rgba(221, 227, 244, 0.2);

    .bg_intro {
      margin: 0 auto r(15);
      display: block;
      width: r(323);
      height: r(132);
      border-radius: r(2);
      overflow: hidden;
    }

    .text {
      margin: 0 auto r(15);
      width: r(315);
      font-size: r(13);
      line-height: r(21);
      color: var(--color-text);
    }

    .from {
      margin: 0 auto;
      width: r(315);
      height: r(30.5);
      background: rgba($color: #f0f2f8, $alpha: 0.5);
      border-radius: r(2);
      @include display-flex;
      @include align-items(center);
      @include justify-content(center);

      color: #41c670;
      font-size: r(13);

      img {
        display: block;
        height: r(18);
        width: r(16);
        margin-right: r(12);
      }
    }
  }

  .title {
    @include display-flex;
    @include align-items(center);

    padding: 0 r(17);
    color: var(--color-text);
    font-size: r(16);
    font-weight: 600;
    margin: r(25) 0 r(10);

    .icon {
      display: block;
      width: r(21);
      height: r(21);
      margin-right: r(8);
    }
  }

  .recommand_doctor_item {
    padding: r(15) r(20);
    margin: 0 r(15);
    margin-bottom: r(10);

    .header {
      @include display-flex;
      @include align-items(center);
      @include justify-content(flex-start);

      .doctor_info {
        @include flex;

        .top {
          margin-bottom: r(4);

          .name {
            display: inline-block;
            font-size: r(18);
            font-weight: bold;
            margin-left: r(10);
            color: #000;
          }

          .staffType {
            font-size: r(15);
            font-weight: bold;
            margin-left: r(8);
            color: #464646;
          }
        }

        .bottom {
          color: #309eeb;
          font-size: r(14);
          margin-left: r(10);
        }
      }
    }

    .tag_item {
      height: r(22);
      line-height: r(22);
      font-size: r(13);
      padding: r(2) r(10);
      background: var(--theme-light-bg);
      border-radius: r(4);
      color: var(--theme-primary);
      margin-left: r(8);

      &:first-of-type {
        margin-left: 0;
      }
    }

    .skill {
      color: #909090;
      font-size: r(12);
      margin: r(10) 0;
      line-height: r(18);
      @include line(2);
    }
  }

  .intro_wrapper {
    position: relative;
    margin: 0 auto;
    width: r(345);
    box-shadow: 0 r(10) r(13) 0 rgba(221, 227, 244, 0.2);
    padding: r(15);
    color: #666;
    font-size: r(13);
    line-height: r(21);
  }
}
