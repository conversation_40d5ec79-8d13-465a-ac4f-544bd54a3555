import React, { useEffect, useState, useCallback } from 'react';
import { useSelector } from 'react-redux';
import { Card, SvgIcon, Avatar } from 'src/components/common';
import { ApplicationState } from 'src/store';
import { fetchJson } from 'src/utils/fetch';
import './chinesemedicine.scss';
import { xflowPushEvent } from 'src/utils/pageTrack';

const ChineseMedicine = (props) => {
  const [recommandDoctor, setRecommandDoctor]: [any, any] = useState([]);

  const { PROFESSIONALTITLE_OBJ, DEPARTMENT_OBJ }: any = useSelector((state: ApplicationState) => ({
    PROFESSIONALTITLE_OBJ: state.dictionary.PROFESSIONALTITLE_OBJ,
    DEPARTMENT_OBJ: state.department.obj,
  }));

  useEffect(() => {
    const fetchRecommandDoctor = async () => {
      const res = await fetchJson({
        type: 'POST',
        url: '/api/api/v1/patient/doctor/list/page',
        data: {
          option: {
            doctorSortFactor: 'HIGH_OPINION_RATE',
            // isQueryMedicalStaffService: true,
            isQueryHeadPortrait: true,
          },
          currentPage: 1,
          pageSize: 3,
          staffTypes: ['doctor'],
          workDepartment: '50',
        },
        needLogin: false,
      });

      if (res && res.code === '0') {
        const data: any[] = [];
        const { result: { resultList = [] } = {} } = res;
        resultList.length &&
          resultList.forEach((element) => {
            data.push({
              ...element,
              avatar: `${element.headPortrait}` || '',
            });
          });
        setRecommandDoctor(data);
      }
    };
    pageScrollTop();
    fetchRecommandDoctor();
  }, []);

  const recommandDoctorClick = useCallback((item) => {
    const { id = '', staffName = '' } = item;
    xflowPushEvent(['click', 'ZAHLWYY_ZBZX_ZYK', '专病中心_中医科', { ZAHLWYY_CLICK_CONTENT: `专病中心_中医咨询_${staffName}` }]);
    props.history.push({
      pathname: '/hospital/doctordetail',
      search: `staffId=${id}`,
    });
  }, []);

  const pageScrollTop = useCallback(() => {
    window.scroll({ top: 0, behavior: 'smooth' });
  }, []);

  return (
    <div className='chinesemedicine_page'>
      <div className='background'></div>
      <Card prefixCls='head'>
        <SvgIcon type='img' className='bg_intro' src={require('src/image/chinese-medicine-intro.png')} />
        <div className='text'>中医科采用中药治疗各种冠心病、心律失常、脑梗塞、脑动脉硬化、顽固性头痛、急慢性肾炎、泌尿系结石、男科病、脾胃病、糖尿病以及心身疾病。运用针灸、理疗等方法治疗中风、偏瘫、风湿性关节炎、哮喘等疾病。</div>
      </Card>

      <div className='title'>
        <SvgIcon className='icon' src={require('src/svgs/sprite-recommand-doctor.svg')} />
        在线名医推荐
      </div>

      {recommandDoctor.map((item) => (
        <Card prefixCls='recommand_doctor_item' onClick={() => recommandDoctorClick(item)} key={`doctor_item_${item.id}`}>
          <div className='header'>
            <Avatar preUrl={item.avatar} />
            <div className='doctor_info'>
              <p className='top'>
                <span className='name'>{item.staffName}</span>
                <span className='staffType'>{PROFESSIONALTITLE_OBJ[item.staffProfessionalTitle]}</span>
              </p>
              {/* <p className="bottom">{item.firstWorkOrgName} {DEPARTMENT_OBJ[item.workDepartment]}</p> */}
              <p className='bottom'>{DEPARTMENT_OBJ[item.workDepartment]}</p>
            </div>
          </div>
          <div className='skill'>{item.staffSkills}</div>
          <div>
            {item.tags &&
                item.tags.split(',').map((item: any, index: number) => (
                  <span className='tag_item' key={`tag_item_${index}`}>
                    {item}
                  </span>
                ))}
          </div>
        </Card>
      ))}

      <div className='title'>
        <SvgIcon className='icon' src={require('src/svgs/sprite-icon-chinese-inner.svg')} />
        中医内科
      </div>
      <Card prefixCls='intro_wrapper'>
        中医内科学是以中医理论阐述内科疾病的病因病机、证候特征、辨证论治及预防、康复、调摄规律的一门临床学科。
        采用中药治疗为主，以脏腑、经络、气血津液等病理生理学说为指导，系统地反映了中医辨证论治的特点中医内科学既是一门临床学科，又是学习和研究中医其它临床学科的基础，为中医学的一门主干学科，具有非常重要的学科地位。
      </Card>

      <div className='title'>
        <SvgIcon className='icon' src={require('src/svgs/sprite-icon-chinese-outer.svg')} />
        中医外科
      </div>
      <Card prefixCls='intro_wrapper'>
        中医外科是以中医药理论为指导，研究外科疾病发生、发展及其防治规律的一门临床学科。 包括疮疡、乳房疾病、瘿、瘤、皮肤及性传播疾病、肛门直肠疾病、泌尿男性生殖系统疾病、周围血管和淋巴疾病及外科其他疾病等内容。采用内服中药外用药膏等清热解毒、活血化瘀、拔毒生肌等治疗手段，避免开刀流血之苦。
      </Card>
    </div>
  );
};

export default ChineseMedicine;
