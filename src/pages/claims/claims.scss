@import 'src/style/index';
$prefixCls: 'claimsinquiry-pages';

.#{$prefixCls} {
  &__hd {
    background-color: #fff;
    // height: r(303.5);
    padding: r(18) r(15) r(16);
    border-bottom: r(1) solid #e6e6e6;
    position: relative;
    margin-bottom: r(26);

    .logo {
      width: r(60);
      height: r(60);
      display: block;
      margin: 0 auto;
    }

    &::before,
    &::after {
      position: absolute;
      content: '';
      left: 50%;
      transform: translate(-50%, 0);
      bottom: -8px;
      width: 0;
      height: 0;
      border-style: solid;
      border-width: 8px 26px 0;
      border-color: #e6e6e6 transparent transparent;
    }

    &::before {
      z-index: 2;
      bottom: -7px;
      border-color: #fff transparent transparent;
    }

    .guide-txt {
      margin: r(15) r(5);
      background: linear-gradient(90deg, rgba(230, 247, 240, 0) 0%, rgba(230, 247, 240, 0.5) 17%, #e6f7f0 50%, rgba(230, 247, 240, 0.5) 83%, rgba(230, 247, 240, 0) 100%);
      position: relative;
      height: r(40);
      line-height: r(40);
      font-weight: 600;
      color: $base-green;
      text-align: center;

      &::before,
      &::after {
        position: absolute;
        content: '';
        top: 0;
        left: 0;
        right: 0;
        height: 1px;
        background: linear-gradient(to right, #fff 0%, #c4f1df 50%, #fff 100%);
      }

      &::before {
        top: auto;
        bottom: 0;
      }
    }

    .illustrate {
      line-height: r(20);
      color: #666;
    }
  }

  &__core {
    margin: 0 r(15) r(15);
    padding: r(12) 0;
    background-color: #fff;
    border-radius: r(8);
    @include display-flex;

    &-col {
      @include display-flex;
      @include flex;

      position: relative;
      padding-left: r(12.5);

      .col-icon {
        width: r(23);
        height: r(23);
        margin-right: r(4);
      }

      .za-badge {
        &__content {
          width: 5px;
          height: 5px;
        }
      }

      .not-dot {
        .za-badge {
          &__content {
            display: none;
          }
        }
      }

      .col-children {
        color: #999;

        .col-title {
          font-weight: 600;
          color: #333;
          font-size: r(16);
          margin-bottom: r(6);
        }

        .za-icon {
          font-size: r(14);
          color: #d6d6d6;
        }
      }

      .has-order {
        color: $base-green;
      }

      & + & {
        &::after {
          content: '';
          position: absolute;
          left: 0;
          top: 0;
          bottom: 0;
          width: r(1);
          background-color: #e6e6e6;
        }
      }
    }
  }

  &__process {
    margin: 0 r(15) r(15);
    padding: r(12) r(14);
    background-color: #fff;
    border-radius: r(8);

    h3 {
      font-size: r(16);
      font-weight: 600;
      color: #1e1e1e;
    }

    .process-inner {
      margin-top: r(12);
      border-radius: r(8);
      background: linear-gradient(180deg, #f7fcfa 0%, #f3fffb 100%);
    }

    .step {
      line-height: r(36);
      background: linear-gradient(270deg, #dbf4ea 0%, #ebf8f3 100%);
      border-radius: r(8) r(8) 0 0;
      font-size: r(13);
      color: #155039;
      font-weight: 600;
      @include display-flex;
      @include justify-content(center);
      @include align-content(center);

      img {
        width: r(15);
        height: r(10);
        margin: 0 r(8);
      }
    }

    .process-desc {
      @include display-flex;

      padding: r(9) 0 r(12);

      li {
        font-size: r(12);
        color: #333;
        padding: 0 r(10);
        @include flex;

        .label {
          font-weight: 600;
        }

        .value {
          color: #999;
          margin-top: 5px;
        }
      }
    }
  }

  &__modal {
    .za-modal__body {
      font-size: r(14);
      color: #666;
    }
  }
}
