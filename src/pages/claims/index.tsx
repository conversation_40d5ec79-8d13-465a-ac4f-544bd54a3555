/*
 * @description： 直赔-问诊流程
 */
import classnames from 'classnames';
import React, { useEffect, useState, useCallback } from 'react';
import { FixedButton } from 'src/components/common';
import { fetchJson } from 'src/utils/fetch';
import { Deserialize } from 'src/utils/serialization';
import { CDN_PREFIX } from 'src/utils/staticData';
import { Icon, Badge, Modal } from 'zarm';
import './claims.scss';

const prefixCls = 'claimsinquiry-pages';
const processList = [
  {
    title: '节省时间',
    desc: '无需线下排队',
  },
  {
    title: '质保放心药',
    desc: '专业处方开具',
  },
  {
    title: '报销方便',
    desc: '支付即报销',
  },
];
const Claims = (props) => {
  const {
    location: { search = '' },
  } = props;

  const { patientId = '', policyNo = '', userRightsId = '' } = Deserialize(search);
  const [state, setDataState] = useState({
    hasInquiryNum: 0,
    hasOrderNum: 0,
    isWaitingPeriod: true,
    isHasRights: false,
  });

  // 检查患者是否存在问诊单
  const checkHasInquiry = useCallback(() => {
    fetchJson({
      type: 'POST',
      url: '/api/api/v1/patient/inquiry/list',
      data: {
        productId: 1,
        inquiryStatusList: [1, 2, 3, 7, 8, 10, 11],
        inquiryType: 'I',
        excludeInquiryTimingTypeList: ['drug_replenish', 'mentalConsult'],
        policyNo,
        payInquiryType: 'text',
      },
    }).then((res) => {
      if (res.code === '0') {
        const { result = [] } = res;
        setDataState((prevState) => ({
          ...prevState,
          hasInquiryNum: result.length,
        }));
      }
    });
  }, [state]);

  // 检查患者是否存在药品订单
  const checkHasOrder = useCallback(() => {
    fetchJson({
      type: 'POST',
      url: '/api/api/v1/patient/order/list/page',
      data: {
        currentPage: 1,
        pageSize: 15,
        policyNo,
        orderType: 'drugs',
        orderStatuses: [1, 3, 5, 6, 9],
      },
    }).then((res) => {
      if (res.code === '0') {
        const { result: { resultList = [] } = {} } = res;
        setDataState((prevState) => ({
          ...prevState,
          hasOrderNum: resultList.length,
        }));
      }
    });
  }, [state]);

  const checkDrugDirectPay = useCallback(() => {
    fetchJson({
      type: 'POST',
      url: '/api/api/v1/patient/user/rights/list/query',
      data: {
        vasCodes: ['DrugDirectPay','HronicDrugDirectPay', 'ChronicDrugDirectPay'],
        policyNo,
        rightsType: 'inquiry',
      },
      needLoading: true,
    }).then((res) => {
      if (res.code === '0') {
        const { result = [] } = res;
        if (result.length) {
          const isWaitingPeriod = result.some((k) => {
            const effectTime = new Date(k.rightsEffectTime.replace(/-/gi, '/')).getTime();
            const nowTime = Date.now();
            return nowTime <= effectTime;
          });
          setDataState((prevState) => ({
            ...prevState,
            isHasRights: true,
            isWaitingPeriod,
          }));
        } else {
          setDataState((prevState) => ({
            ...prevState,
            isHasRights: false,
            isWaitingPeriod: false,
          }));
        }
      }
    });
  }, [state]);
  useEffect(() => {
    checkHasInquiry();
    checkHasOrder();
    checkDrugDirectPay();
  }, []);
  const goTo = (url) => {
    props.history.push({
      pathname: url,
      search: `patientId=${patientId}&policyNo=${policyNo}&orderStatus=0&userRightsId=${userRightsId}`,
    });
  };
  const jumpInquriy = () => {
    goTo('/hospital/inquiryform');
  };
  const goToInquriy = useCallback(() => {
    const { isHasRights, isWaitingPeriod, hasInquiryNum } = state;
    // 有权益 、处于等待期
    if (isHasRights && isWaitingPeriod && !hasInquiryNum) {
      Modal.confirm({
        className: `${prefixCls}__modal`,
        width: '80%',
        content: '您尚处于等待期，问诊开药后无法享受直赔报销，是否继续发起问诊？',
        cancelText: '放弃问诊',
        okText: '继续问诊',
        onOk: () => jumpInquriy(),
      });
      return;
    } else if (!isHasRights && !isWaitingPeriod && !hasInquiryNum) {
      Modal.confirm({
        className: `${prefixCls}__modal`,
        title: '未查询到您的购药理赔权益',
        content: '您可以继续使用问诊，如您需要问诊开药则无法享受理赔，如有疑问请联系客服',
        cancelText: '放弃问诊',
        okText: '继续问诊',

        onOk: () => jumpInquriy(),
      });
      return;
    } else if (hasInquiryNum) {
      Modal.alert({
        className: `${prefixCls}__modal`,
        content: '当前有进行中的订单，请等待订单结束后再次发起问诊',
        cancelText: '我知道了',
      });
      return;
    }
    jumpInquriy();
    return;
  }, [state]);
  return (
    <section className={prefixCls}>
      <header className={`${prefixCls}__hd`}>
        <img className='logo' src={`${CDN_PREFIX}applets/logo/za-logo.png`} />
        <h3 className='guide-txt'>嗨，欢迎体验在线问诊直赔</h3>
        <p className='illustrate'>在线问诊直赔是指，在互联网医院问诊后，医生开具用药处方，结算时直接按比例报销费用的服务</p>
      </header>
      <div className={`${prefixCls}__core`}>
        <div className={`${prefixCls}__core-col`} onClick={() => goTo('/hospital/myorder')}>
          <p className={classnames('col-icon', { 'not-dot': !state.hasOrderNum })}>
            <Badge shape='dot'>
              <img src={require('./images/drug-icon.png')} alt='' />
            </Badge>
          </p>
          <div className='col-children'>
            <p className='col-title'>药品订单</p>
            <p>
              {state.hasOrderNum ? <span className='has-order'>有{state.hasOrderNum}个订单待完成</span> : <span>查看详情</span>}
              <Icon type='arrow-right' />
            </p>
          </div>
        </div>
        <div className={`${prefixCls}__core-col`} onClick={() => goTo('/hospital/myinquiry')}>
          <p className={classnames('col-icon', { 'not-dot': !state.hasInquiryNum })}>
            <Badge shape='dot'>
              <img src={require('./images/inquiry-icon.png')} alt='' />
            </Badge>
          </p>
          <div className='col-children'>
            <p className='col-title'>问诊记录</p>
            <p>
              {state.hasInquiryNum ? <span className='has-order'>有{state.hasInquiryNum}个订单待完成</span> : <span>查看详情</span>}
              <Icon type='arrow-right' />
            </p>
          </div>
        </div>
      </div>
      <div className={`${prefixCls}__process`}>
        <h3>使用流程</h3>
        <div className='process-inner'>
          <p className='step'>
            发起线上问诊
            <i>
              <img src={require('./images/arrow-icon.png')} alt='' />
            </i>{' '}
            开具药品处方{' '}
            <i>
              <img src={require('./images/arrow-icon.png')} alt='' />
            </i>{' '}
            支付直接报销
          </p>
          <ul className='process-desc'>
            {processList.map((k, index) => (
              <li key={`item${index}`}>
                <p className='label'>{k.title}</p>
                <p className='value'>{k.desc}</p>
              </li>
            ))}
          </ul>
        </div>
      </div>

      <FixedButton buttonShape='round' prefixCls={`${prefixCls}__fix_button`} buttonClick={() => goToInquriy()} text='发起问诊' />
    </section>
  );
};
export default Claims;
