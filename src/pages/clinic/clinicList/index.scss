@import 'src/style/index';

.clicic-list-page {
  display: block;
  -webkit-overflow-scrolling: touch;
  overflow-y: auto;
  height: 100vh;

  .clinic-class {
    height: r(44);
  }

  .clinic-class-wrap {
    z-index: 2;
    position: fixed;
    top: 0;
    left: 0;
    align-items: center;
    background-color: white;
    width: 100%;
    flex-shrink: 1;
    height: r(44);
    display: flex;
    flex-direction: row;
    border-bottom: 1px solid #f4f4f4;

    &-left {
      flex-shrink: 1;
      width: 100%;
      align-items: center;
      display: flex;
      flex-direction: row;
    }

    &-right {
      font-size: 15px;
      font-weight: 400;
      color: #3e9aff;
      flex-shrink: 0;
      margin: 0 r(16);
    }

    .addr {
      width: r(20);
      height: r(20);
      margin-left: r(10);
      margin-right: r(4);
    }

    .city {
      margin-right: r(3);
      font-size: 15px;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 500;
      color: #333;
      line-height: r(21);
    }

    .select_arrow {
      width: r(14);
      height: r(14);
    }
  }

  .clinic-list {
    background-color: white;
    width: 100%;

    .clinic-item {
      position: relative;
      margin: 0 r(11.5) 0 r(15);
      padding: r(17.5) 0 r(16);
      border-bottom: 1px solid #eee;
      display: flex;
      flex-direction: row;
      align-items: flex-start;

      .left-img {
        flex-shrink: 0;
        width: r(45);
        height: r(45);
        margin-right: r(5);
        display: flex;
        align-items: center;
      }

      .right-container {
        flex-shrink: 0;
        height: 100%;
        width: r(42);
        position: relative;
        display: flex;
        align-items: center;

        .right-img {
          width: r(18);
          height: r(18);
          margin-left: r(16);
          transition: transform 0.3s;
        }
      }

      .clinic-container {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        flex-shrink: 1;
        width: 80%;
        padding-left: r(15);

        .clinic-name {
          width: 100%;
          margin-bottom: r(5);
          font-size: 16px;
          font-weight: 500;
          color: #333;
          line-height: 22.5px;
        }

        .clinic-line2-container {
          width: 100%;
          display: flex;
          flex-direction: row;
          align-items: center;

          .clinic-detail {
            width: 100%;
            flex-shrink: 1;
            font-size: 13px;
            font-family: PingFang-SC-Regular, PingFang-SC;
            font-weight: 400;
            color: #666;
            line-height: 23.5px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }

          .clinic-arrow {
            width: r(8.5);
            height: r(14);
            flex-shrink: 0;
          }
        }
      }
    }

    .clinic-line {
      border-bottom: 1px solid #f4f4f4;
      margin: 0 r(15);
    }
  }

  .noView {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 100%;

    .noView-img {
      width: r(200);
      height: r(200);
      margin-top: r(54);
      margin-bottom: r(10.5);
    }

    .noView-text {
      text-align: center;
      font-size: 16px;
      font-family: PingFang-SC-Regular, PingFang-SC;
      font-weight: 400;
      color: #333;
      line-height: r(22.5);
    }

    .noView-btn {
      display: flex;
      margin-top: r(32);
      width: r(114);
      height: r(40);
      border-radius: 20px;
      border: 2px solid #999;
      font-size: 16px;
      font-family: PingFang-SC-Regular, PingFang-SC;
      font-weight: 400;
      color: #333;
      line-height: r(22.5);
      align-items: center;
      justify-content: center;
    }
  }
}
