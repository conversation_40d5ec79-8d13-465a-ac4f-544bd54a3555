import React, { useEffect, useState, useRef } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { ApplicationState } from 'src/store';
import { select_clinic } from 'src/store/clinic/action';
import { fetchJson } from 'src/utils';
import { Deserialize } from 'src/utils/serialization';
import {Pull} from 'zarm';
import './index.scss';

const LOAD_STATE = {
  normal: 0, // 普通
  abort: 1, // 中止
  loading: 2, // 加载中
  success: 3, // 加载成功
  failure: 4, // 加载失败
  complete: 5, // 加载完成（无新数据）
};

const ClinicListPage = (props) => {
  const {
    location: { search = '' },
  } = props;
  const pullRef = useRef();
  const { serviceId } = Deserialize(search);
  const dispatch = useDispatch();
  console.log('serviceId', serviceId);
  const [list, setList]: any = useState([]);
  const [loading, setLoading] = useState(LOAD_STATE.normal);
  const [pageNum, setPageNum] = useState(1);
  const selectClinicAction = (clinic) =>  dispatch(select_clinic(clinic));

  const { selectCity, point }: any = useSelector((state: ApplicationState) => ({
    selectCity: state.clinic.selectCity,
    point: state.clinic.point,
  }));
  useEffect(() => {
    const params: any = {
      city: selectCity,
      serviceId,
    };
    if(point.lat && point.lng){
      params.latitude  = point.lat;
      params.longitude = point.lng;
    }
    console.log('startfetchjson');
    fetchData(1);

  }, []);

  const gotoSelectCity = () => {
    props.history.push('/hospital/static/selectcity');
  };
  const onSelectClinic = (item) => {
    selectClinicAction(item);
    props.history.goBack();
  };
  const fetchData = (pageNum, pageSize = 15) => {
    const params: any = {
      city: selectCity,
      serviceId,
    };
    if(point.lat && point.lng){
      params.latitude  = point.lat;
      params.longitude = point.lng;
    }
    params.pageNum = pageNum;
    params.pageSize = pageSize;
    fetchJson({
      url: '/api/api/v1/patient/teethCleanService/getMerchantList',
      type: 'POST',
      data: params,
    }).then((res) => {
      console.log('merchantList res',res);
      if (res && res.code === '0') {
        if(res.result.length !== 0){
          setLoading(LOAD_STATE.success);
          setPageNum(pageNum);
          res.result.forEach((item) => {
            try{
              const imgs = JSON.parse(item.images);
              item.imgs = imgs;
            }catch(e){
              console.log('parse error');
              item.imgs = [];
            }

          });
          const newList = list.concat(res.result);
          setList(newList);
        }else{
          setLoading(LOAD_STATE.complete);
        }
      }
    });
  };
  const loadData = () => {
    if(loading === LOAD_STATE.complete){
      return ;
    }
    console.log('loadData');
    setLoading(LOAD_STATE.loading);
    fetchData(pageNum + 1);
  };
  return (
    <div className='clicic-list-page'>
      <div className='clinic-class'>
        <div className='clinic-class-wrap'>
          <div className='clinic-class-wrap-left' onClick={gotoSelectCity}>
            <img
              className='addr'
              src={'https://zayl-jiezhong-prd.oss-cn-hzfinance.aliyuncs.com/app/miniprogram/imgs/20210128/clinicInfo/<EMAIL>'}
            />
            <div className='city'>
              <div>
                {selectCity}
              </div>
            </div>
            <img
              className='select_arrow'
              src={'https://zayl-jiezhong-prd.oss-cn-hzfinance.aliyuncs.com/app/miniprogram/imgs/clinicService/<EMAIL>'}
            />
          </div>
        </div>
      </div>

      <Pull
        ref={pullRef}
        load={{
          state: loading,
          distance: 50,
          handler: loadData,
        }}
      >
        <div className='clinic-list' id='clinic-list'>
          {list && list.map((item: any) => (
            <div className='clinic-item' key={item.id} onClick={() => onSelectClinic(item)}>
              <img
                className='left-img'
                src={item.imgs.length > 0 ? item.imgs[0] : 'https://zayl-jiezhong-prd.oss-cn-hzfinance.aliyuncs.com/app/miniprogram/imgs/clinicService/<EMAIL>'}
              />
              <div className='clinic-container'>
                <div
                  className='clinic-name'
                >
                  {item.name}
                </div>
                <div
                  className='clinic-line2-container'
                >
                  <div className='clinic-detail'>
                    { item.distance && `${(item.distance / 1000).toFixed(2)}km | `}
                    {item.address}
                  </div>
                  <img
                    className='clinic-arrow'
                    src={'https://zayl-jiezhong-prd.oss-cn-hzfinance.aliyuncs.com/app/miniprogram/imgs/clinicService/<EMAIL>'}
                  />
                </div>
              </div>

            </div>
          ))}

        </div>
      </Pull>

      {
        loading === 3 && list.length === 0 && (
          <div className='noView'>
            <img
              className='noView-img'
              src={'https://zayl-jiezhong-prd.oss-cn-hzfinance.aliyuncs.com/app/miniprogram/imgs/20210128/clinicInfo/<EMAIL>'}
            />
            <div className='noView-text'>
                            当前城市无适用门店，请选择其他城市
            </div>
            <div
              className='noView-btn'
              onClick={gotoSelectCity}
            >
                            切换城市
            </div>
          </div>
        )
      }

    </div>
  );
};
export default ClinicListPage;
