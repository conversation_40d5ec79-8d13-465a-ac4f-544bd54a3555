@import "src/style/index";

$cdnUrl: 'https://cdn-qcloud.zhongan.com/a00000/za-asclepius/static/jzCleanTeeth';

.jz-clinic-teeth-page {
  position: relative;

  .top-img {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    z-index: -10;
  }

  .tab-cont {
    position: relative;
    top: r(240);
    left: 0;
    margin: 0 r(10) 0 r(10);

    .tab-bg {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      z-index: -1;
    }

    .tab-list {
      display: flex;
      padding: r(10) r(4);
    }

    .tab-item {
      position: relative;
      flex: 1;
      font-size: 15px;
      color: #f2731a;
      // line-height: 19px;
      text-align: center;

      &.active {
        color: #fff;
      }

      .tab-text {
        font-weight: bold;
      }

      .btn-bg-img {
        position: absolute;
        top: r(-10);
        left: 0;
        z-index: -1;
      }

      .tab-item-line {
        position: absolute;
        top: r(10);
        right: 0;
        height: r(16);
        width: r(1);
        background-color: #ff7841;
        opacity: 0.5;
      }

      // text-shadow: 0px 2px 4px rgba(255,95,10,0.9400);
    }
  }

  .img-list {
    padding-bottom: r(140);
  }

  .address-cont {
    position: relative;
    margin-top: r(258);
    border-top-left-radius: 20px;
    border-top-right-radius: 20px;
    background-color: #fff;
    padding: r(15) r(15) 0 r(15);

    &.no-margin {
      margin-top: 0;
      border-top-right-radius: 0;
      border-top-left-radius: 0;
    }

    .icon-triangle {
      position: absolute;
      top: -11px;
      width: 0;
      height: 0;
      border-left: 11px solid transparent;
      border-right: 11px solid transparent;
      border-bottom: 11px solid #fff;

      &.icon-triangle-0 {
        left: r(61);
      }

      &.icon-triangle-1 {
        left: r(178);
      }

      &.icon-triangle-2 {
        right: r(61);
      }
    }

    .address-box {
      border-bottom: 1px solid #e6e6e6;
      @media (-webkit-min-device-pixel-ratio: 2) {
        border-bottom: 0.5px solid #e6e6e6;
      }
    }

    .city-row {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .city-row-left {
        display: flex;
        align-items: center;
      }

      .icon-address {
        width: r(18);
        margin-right: r(4);
      }

      .icon-change {
        width: r(16);
        margin-right: r(4);
      }

      .city-name {
        color: #00a864;

        &.no-city {
          color: #ff5050;
        }
      }

      .city-name-box {
        font-weight: bold;
        font-size: 16px;
      }

      .other-store {
        color: #666;
        font-size: 13px;
      }
    }

    .store-cont {
      margin: r(12.5) 0 0 0;
      padding-bottom: r(17.5);
      display: flex;
      align-items: center;

      .store-img {
        width: r(44);
        margin-right: r(8);
      }

      .store-detail {
        flex: 1;

        .store-name-box {
          display: flex;
          align-items: center;
          justify-content: space-between;
        }

        .store-name {
          color: #333;
          font-size: 15px;
        }

        .store-distance {
          padding-left: r(10);
          color: #999;
          font-size: 13px;
        }

        .store-address {
          color: #666;
          font-size: 13px;
          margin-top: r(6);
        }
      }
    }

    .empty {
      height: r(110);
      display: flex;
      flex-direction: row;
      justify-content: center;
      align-items: center;

      .img_empty {
        width: r(100);
        height: r(100);
      }

      .city_empth {
        height: r(110);
        font-size: 14px;
        font-weight: 400;
        color: #ccc;
        display: flex;
        align-items: center;
        justify-content: flex-start;
      }
    }
  }

  .buy-cont {
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
    background-color: #fff;
    padding: r(12) r(12.5) r(15) r(20);

    .radio-txt {
      color: rgba(51, 51, 51, 0.6);
      font-size: 12px;
    }

    .radio-txt {
      padding-left: r(4);
    }

    .agreement-highlight {
      color: #00a864;
      font-size: 12px;
    }

    .price-cont {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-top: r(20);

      .price-box {
        font-size: 15px;
        color: #666;
      }

      .price-num {
        font-size: 21px;
        color: #ff5050;
        font-weight: bold;
        margin-left: r(6);
      }

      .btn-buy {
        width: r(150);
      }
    }
  }
}
