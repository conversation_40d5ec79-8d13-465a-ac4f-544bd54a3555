/**
 * 洁众洁牙套餐购买
 * author: wurenjie
 */

import React, { useEffect, useState } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { LinkToPDFPreview, StaticToast } from 'src/components/common';
import { ApplicationState } from 'src/store';
import { select_city, select_clinic, set_point } from 'src/store/clinic/action';
import { fetchJson } from 'src/utils';
import loadJS from 'src/utils/loadJS';
import { Serialize, Deserialize } from 'src/utils/serialization';
import { AMapUrl } from 'src/utils/staticData';
import { Button, Checkbox } from 'zarm';

import './index.scss';


interface prodcutItem {
  productPrice: number;
  productShowName: string;
  productCode: string;
  productName: string;
  id: number;
  orderType: string;
  productRealPrice: number;
  productSupplier: string;
}


const JZCleanTeethPage = (props) => {
  const {
    location: { search = '' },
  } = props;
  const { tabIndex: paramTabIndex } = Deserialize(search);
  const { id = '', checkDetail = '', checkIndex = '', subOrderType = '' } = Deserialize(search);
  const dispatch = useDispatch();
  const [activeIndex, setActiveIndex] = useState(parseInt(paramTabIndex) || 0);
  const [agreementCheck, setAgreementCheck] = useState(true);
  const [storeCount, setStoreCount] = useState(0);
  const selectCityAction = (city) => dispatch(select_city(city));
  const selectClinicAction = (clinic) => dispatch(select_clinic(clinic));
  const setPoint = (point) => dispatch(set_point(point));
  const [productList, setProductList]: any = useState([]);
  const [groupInfo, setGroupInfo]: any = useState({});
  const [thumbnail, setThumbnail] = useState('');
  const [serviceId, setServiceId] = useState('');

  const imgUrlPrefix = 'https://cdn-qcloud.zhongan.com/a00000/za-asclepius/static/jzCleanTeeth';
  // const tabList = [{ tabName: '超声波/洁牙套餐', testServiceId: '95001', prdServiceId:'15012', serviceName: '超声波洁牙套餐' }, { tabName: '儿童/涂氟套餐', testServiceId: '95005' ,prdServiceId:'15010', serviceName: '儿童涂氟套餐' }, { tabName: '儿童/窝沟封闭', testServiceId: '95006', prdServiceId:'15009', serviceName: '儿童窝沟封闭' }];
  const tab1ImgList = [`${imgUrlPrefix}/<EMAIL>`, `${imgUrlPrefix}/<EMAIL>`, `${imgUrlPrefix}/<EMAIL>`, `${imgUrlPrefix}/<EMAIL>`, `${imgUrlPrefix}/<EMAIL>`, `${imgUrlPrefix}/<EMAIL>`];
  const tab3ImgList = [`${imgUrlPrefix}/<EMAIL>`, `${imgUrlPrefix}/<EMAIL>`, `${imgUrlPrefix}/<EMAIL>`, `${imgUrlPrefix}/<EMAIL>`];
  const tab2ImgList = [`${imgUrlPrefix}/<EMAIL>`, `${imgUrlPrefix}/<EMAIL>`, `${imgUrlPrefix}/<EMAIL>`, `${imgUrlPrefix}/<EMAIL>`, `${imgUrlPrefix}/<EMAIL>`];
  const selectProduct = productList.length > 0 ? (id ? productList.find((item) => item.thirdProductCode === id) : productList[activeIndex]) : {};
  const { selectCity, selectClinic, point }: any = useSelector((state: ApplicationState) => ({
    selectCity: state.clinic.selectCity,
    selectClinic: state.clinic.selectClinic,
    point: state.clinic.point,
  }));

  useEffect(() => {
    (window as any)._AMapSecurityConfig = {
      securityJsCode: 'c0fa2bbf8c3195387018e3a5147b9aca',
    };
    loadJS(AMapUrl, () => {
      (window as any).AMapLoader.load({
        key: '9ca5b0077e97eb8a4607821f0ba9f988', // 申请好的Web端开发者 Key，调用 load 时必填
        version: '2.0', // 指定要加载的 JS API 的版本，缺省时默认为 1.4.15
      }).then((res) => {
        AMapGetLocation();
      });
    });
  }, []);

  useEffect(() => {
    // 通过城市获取groupNo获取productList获取serviceId
    getCityPrice();
  }, [selectCity]);


  useEffect(() => {
    getClinicCount();
    getFirstClinic();
  }, [selectCity, serviceId, point]);

  useEffect(() => {
    if (productList.length > 0) {
      setServiceId(productList[activeIndex].thirdProductCode);
    }
  }, [activeIndex]);

  // const wxGetLocation = () => {
  //     if (validate.isFromMiniApplet()) {
  //         wx.miniProgram.getLocation({
  //             type: 'wgs84',
  //             success: (res) => {
  //                 const latitude = res.latitude
  //                 const longitude = res.longitude
  //                 console.log('lat',latitude);
  //                 console.log('lng',longitude);
  //                 setPoint({
  //                     latitude,
  //                     longitude
  //                 })
  //                 const myGeo = new BMap.Geocoder();
  //                 myGeo.getLocation(new BMap.Point(latitude, longitude), (result) => {
  //                     if (result) {
  //                         alert(result.address);
  //                         if (result?.address?.city) {
  //                             selectCityAction(result.address.city);
  //                         }
  //                     }else{
  //                         BMapGetLocation();
  //                     }
  //                 });
  //             },
  //             fail: (res) => {
  //                 console.log('fail res',res);
  //                 BMapGetLocation();
  //             }
  //         })
  //     }else{
  //         BMapGetLocation();
  //     }
  // }

  const AMapGetLocation = () => {
    console.log('AMapGetLocation', selectCity);
    // 处理选择城市返回的情况，不重新定位
    if (selectCity) {
      return;
    }
    /** 高德地图 */
    const AMap = (window as any).AMap;

    /**
     * @description 通过ip获取城市，再查询城市经纬度
     */
    const getPointByCity = () => {
      AMap.plugin('AMap.CitySearch', function() {
        const citySearch = new AMap.CitySearch();
        citySearch.getLocalCity((csStatus, csResult) => {
          console.log('AMap IP定位结果', csStatus, csResult);
          const { info, infocode, city } = csResult;
          if (csStatus === 'complete' && info === 'OK') {
            // 查询成功，csResult即为当前所在城市信息
            AMap.plugin('AMap.Geocoder', function() {
              const geocoder = new AMap.Geocoder({
                city: infocode, // city 指定进行编码查询的城市，支持传入城市名、adcode 和 citycode
              });

              const address = city;

              geocoder.getLocation(address, (status, result) => {
                console.log('AMap 逆地理编码', status, result);
                if (status === 'complete' && result.info === 'OK') {
                  // result中对应详细地理坐标信息
                  console.log(result);
                  selectCityAction(city);
                  const { location } = result.geocodes[0];
                  const { lat, lng } = location;
                  console.log('location', location);
                  if (lat && lng) {
                    setPoint({
                      lat,
                      lng,
                    });
                  }
                }
              });
            });
          }
        });
      });
    };

    getPointByCity();
  };


  const getProductList = (groupNo) => {
    if (!groupNo) {
      return;
    }
    fetchJson({
      url: '/api/api/v1/patient/product/group/detail',
      type: 'GET',
      data: {
        groupNo,
      },
      needLoading: true,
    }).then((res) => {
      if (res && res.code === '0') {
        setProductList(res.result?.productList);
        setGroupInfo(res.result);
        if (id) {
          const firstItem: any = res.result?.productList.find((item) => item.thirdProductCode === id);
          const thumbImg = firstItem.attachmentList && firstItem.attachmentList.filter((item) => item.attachmentType === 'commodityPicture')[0].attachmentDownloadUrl || '';
          setThumbnail(thumbImg);
          setServiceId(firstItem.thirdProductCode);
        } else {
          const [firstItem = {}]: any = res.result.productList;
          const thumbImg = firstItem.attachmentList && firstItem.attachmentList.filter((item) => item.attachmentType === 'commodityPicture')[0].attachmentDownloadUrl || '';
          setThumbnail(thumbImg);
          setServiceId(res.result?.productList[activeIndex].thirdProductCode);
        }

      }
    });
  };


  const getClinicCount = () => {
    if (!selectCity || !serviceId) {
      return;
    }
    fetchJson({
      url: '/api/api/v1/patient/teethCleanService/getMerchantCount',
      type: 'POST',
      data: {
        city: selectCity,
        serviceId,
      },
    }).then((res) => {
      if (res && res.code === '0') {
        setStoreCount(res.result);
      }
    });
  };
  const getCityPrice = () => {
    fetchJson({
      url: '/api/api/v1/patient/teethCleanService/getCityPrice',
      type: 'POST',
      data: {
        city: selectCity || '上海市',
      },
    }).then((res) => {
      if (res && res.code === '0') {
        getProductList(res.result);
      }
    });

  };

  const getFirstClinic = () => {
    if (!selectCity || !serviceId) {
      return;
    }
    const params: any = {
      city: selectCity,
      serviceId,
    };
    if (point.lat && point.lng) {
      params.latitude = point.lat;
      params.longitude = point.lng;
    }
    fetchJson({
      url: '/api/api/v1/patient/teethCleanService/getMerchantList',
      type: 'POST',
      data: params,
    }).then((res) => {
      if (res && res.code === '0') {
        res.result.forEach((item) => {
          const imgs = JSON.parse(item.images);
          item.imgs = imgs;
        });
        if (res.result.length > 0) {
          if (!selectClinic.name) {
            selectClinicAction(res.result[0]);
          }
        } else {
          selectClinicAction({});
        }
      }
    });
  };

  const gotoSelectCity = () => {
    props.history.push({
      pathname: '/hospital/static/selectcity',
    });
  };
  const onTabHandler = (index) => {
    setActiveIndex(index);
    window.reactHistory.replace({
      search: Serialize({ tabIndex: index, checkDetail }),
    });
  };
  const onRadioCheck = (e) => {
    e.persist();
    setAgreementCheck(e.target.checked);
  };
  const gotoClinicSelect = () => {
    props.history.push({
      pathname: '/hospital/clinic/cliniclist',
      search: `serviceId=${serviceId}`,
    });
  };

  const buy = () => {
    if (!selectCity) {
      StaticToast.warning('请选择城市');
      return;
    }
    if (!agreementCheck) {
      StaticToast.warning('请勾选服务协议');
      return;
    }
    if (!selectClinic.name) {
      StaticToast.warning('当前城市无可用门店，请切换其他城市');
      return;
    }
    const selectProduct: prodcutItem = productList.length > 0 ? (id ? productList.find((item) => item.thirdProductCode === id) : productList[activeIndex]) : {};
    props.history.push({
      pathname: '/hospital/physicalExamination/orderConfirm',
      state: {
        productPrice: selectProduct.productPrice,
        productRealPrice: selectProduct.productRealPrice,
        id: selectProduct.id,
        productCode: selectProduct.productCode,
        productName: selectProduct.productName,
        groupNo: groupInfo.groupNo,
        groupName: groupInfo.groupName,
        groupNum: 1,
        attachmentList: thumbnail,
        orderType: 'teethCleanServices',
        productSupplier: selectProduct.productSupplier,
        city: selectCity,
        serviceId,
        subOrderType,
      },
    });
  };
  return (
    <div className={'jz-clinic-teeth-page'}>
      {
        id === '' && checkDetail !== '1' && (
          <React.Fragment>
            <div className='top-img'>
              <img src={`${imgUrlPrefix}/<EMAIL>`} alt='' />
            </div>
            <div className='tab-cont'>
              <img className='tab-bg' src={`${imgUrlPrefix}/<EMAIL>`} alt='' />
              <div className='tab-list'>
                {
                  productList.map((item, index) => {
                    const [txt1, txt2] = item.productShowName.split('/');
                    return (
                      <div key={index} className={activeIndex === index ? 'tab-item active' : 'tab-item'} onClick={() => onTabHandler(index)}>
                        {
                          activeIndex === index && <img className='btn-bg-img' src={`${imgUrlPrefix}/<EMAIL>`} />
                        }
                        <div className='tab-text'>{txt1}</div>
                        <div className='tab-text'>{txt2}</div>
                        {
                          ((activeIndex === 0 && index === 1) || (activeIndex === 2 && index === 0)) && <div className='tab-item-line'></div>
                        }
                      </div>
                    );
                  })
                }
              </div>
            </div>
          </React.Fragment>

        )
      }
      <div className={id === '' && checkDetail !== '1' ? 'address-cont' : 'address-cont no-margin'}>
        <div className={`icon-triangle icon-triangle-${activeIndex}`} />
        <div className='address-box'>
          <div className='city-row'>
            <div className='city-row-left'>
              <img className='icon-address' src={`${imgUrlPrefix}/icon-address.png`} alt='' />
              <span className='city-name-box'><span className={selectCity ? 'city-name' : 'city-name no-city'} onClick={gotoSelectCity}>{selectCity || '请选择'}</span>适用门店（{storeCount}）</span>
            </div>
            {
              storeCount > 0 ? (
                <div onClick={gotoClinicSelect}>
                  <img className='icon-change' src={`${imgUrlPrefix}/icon-change.png`} alt='' />
                  <span className='other-store'>其他门店</span>
                </div>
              ) : (
                <div onClick={gotoSelectCity}>
                  <img className='icon-change' src={`${imgUrlPrefix}/icon-change.png`} alt='' />
                  <span className='other-store'>切换城市</span>
                </div>
              )
            }
          </div>
          {
            selectClinic.name ? (
              <div className='store-cont'>
                <img className='store-img' alt='' src={selectClinic.imgs?.length && selectClinic.imgs[0]} />
                <div className='store-detail'>
                  <div className='store-name-box'>
                    <div className='store-name'>{selectClinic.name}</div>
                    {
                      selectClinic.distance && <div className='store-distance'>{`${(selectClinic.distance / 1000).toFixed(2)}km`}</div>
                    }
                  </div>
                  <div className='store-address'>{selectClinic.address}</div>
                </div>
              </div>
            ) : (
              <div className='empty'>
                <img src={`${imgUrlPrefix}/appoint_empty.png`} className='img_empty' />
                <div className='city_empth'>
                  当前城市无适用门店 <br></br> 请选择其他城市
                </div>
              </div>
            )
          }
        </div>
      </div>
      <div className='img-list' style={{ display: (serviceId === '95001' || serviceId === '15012' || checkIndex === '0') ? 'block' : 'none' }}>
        {
          tab1ImgList.map((item, index) => (
            <img key={index} src={item} alt='' />
          ))
        }
      </div>
      <div className='img-list' style={{ display: (serviceId === '95005' || serviceId === '15010' || checkIndex === '1') ? 'block' : 'none' }}>
        {
          tab2ImgList.map((item, index) => (
            <img key={index} src={item} alt='' />
          ))
        }
      </div>
      <div className='img-list' style={{ display: (serviceId === '95006' || serviceId === '15009' || checkIndex === '2') ? 'block' : 'none' }}>
        {
          tab3ImgList.map((item, index) => (
            <img key={index} src={item} alt='' />
          ))
        }
      </div>
      {
        checkDetail !== '1' &&
        <div className='buy-cont'>
          <div className='agreement'>
            <Checkbox defaultChecked onChange={onRadioCheck}></Checkbox>
            <span className='radio-txt'>购买代表同意授权以下权限</span>
            <span className='agreement-highlight'>
              <LinkToPDFPreview fileName='口腔业务授权书.pdf'>《服务告知及个人信息授权》</LinkToPDFPreview>
            </span>
          </div>
          <div className='price-cont'>
            <div className='price-box'>价格<span className='price-num'>¥{selectProduct.productRealPrice}</span></div>
            <Button className='btn-buy' theme='primary' shape='round' size='md' onClick={buy}>立即购买</Button>
          </div>
        </div>
      }
    </div>
  );
};
export default JZCleanTeethPage;
