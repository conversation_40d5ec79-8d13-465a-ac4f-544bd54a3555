import React, { useCallback, useEffect } from "react";
import { StaticToast, SvgIcon } from "src/components/common";
import { Button } from "zarm";
import { generateTokenForClinic } from 'src/utils/auth';
import { Deserialize } from 'src/utils/serialization';
import { select_city, select_clinic, set_point } from 'src/store/clinic/action';
import { useDispatch } from 'react-redux';
import './index.scss';


const ClinicPaySuccessPage = (props) => {
    const {
        location: { search = '' },
    } = props;
    const { orderId } = Deserialize(search);
    const dispatch = useDispatch()
    const selectCityAction = (city) => dispatch(select_city(city));
    const selectClinicAction = clinic => dispatch(select_clinic(clinic))
    const setPoint = point => dispatch(set_point(point))

    useEffect(() => {
        // 支付成功后清空选择的城市、诊所、定位
        selectCityAction('');
        selectClinicAction({});
        setPoint({})
    }, [])

    const toBook = () => {
        if (!orderId) {
            StaticToast.warning('没有订单id，请联系客服');
        }
        generateTokenForClinic({
            orderId
        })
    };

    const toMyOrder = useCallback(() => {
        props.history.push({
            pathname: '/hospital/myorder',
        });
    }, []);


    return <div className='page_osp_pay_success'>
        <div className="card">
            <SvgIcon className="icon_sucess" src={require('src/svgs/sprite-icon_pay_success.svg')} />
            <p>支付成功</p>
            <div className='btns_wrap'>
                <Button className="btn" shape='round' onClick={toMyOrder}>
                    查看订单
                </Button>
                <Button theme="primary" className="btn" shape='round' onClick={toBook}>
                    立即预约
                </Button>
            </div>
        </div>
    </div>
};

export default ClinicPaySuccessPage;
