/*
 * @authors: renji<PERSON>
 * @description: 洁众洁牙套餐购买路由
 */
const route = [
  {
    path: '/hospital/clinic/cliniclist',
    name: 'clinicList',
    component: () => import(/* webpackPrefetchPlaceHolder */ './clinicList'),
    auth: false,
    exact: true,
    title: '门店列表',
  },
  {
    path: '/hospital/clinic/jzTeethIntro',
    name: 'jzTeethIntro',
    component: () => import(/* webpackPrefetchPlaceHolder */ './jzTeethIntro'),
    auth: false,
    exact: true,
    title: '众安互联网医院',
  },
  {
    path: '/hospital/clinic/paysuccess',
    name: 'clinicPaysuccess',
    component: () => import(/* webpackPrefetchPlaceHolder */ './paySuccess'),
    auth: true,
    exact: true,
    title: '众安互联网医院',
  },

];
export default route;

