import React from 'react';
import { Link, RouteComponentProps } from 'react-router-dom';
import { CDN_PREFIX } from 'src/utils/staticData';
import './guide.scss';

const guideBackgroundImages = {
  video: {
    header: `${CDN_PREFIX}static/618/video-header.png`,
    body: `${CDN_PREFIX}static/618/video.png`,
  },
  inquiryAndDrug: {
    header: `${CDN_PREFIX}static/618/drug-header.png,`,
    body: `${CDN_PREFIX}static/618/drug.png,`,
  },
  registration: {
    header: `${CDN_PREFIX}static/618/registration-header.png,`,
    body: `${CDN_PREFIX}static/618/registration.png,`,
  },
} as const;

type GuideFor618Props = RouteComponentProps<{ type: keyof typeof guideBackgroundImages }>;

export default function GuideFor618(props: GuideFor618Props) {
  const {
    params: { type },
  } = props.match;

  const { header, body } = guideBackgroundImages[type];

  return (
    <div className='guide-618-container'>
      <header className='guide-header'>
        <img src={header} alt='' />
        <Link to='/hospital/mycoupon' className='btn' />
      </header>
      <section>
        <img src={body} alt='' />
      </section>
    </div>
  );
}
