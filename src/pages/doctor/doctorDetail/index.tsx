/*
 * @description： 专科中心 -> 名医图文问诊
 */
import React, { useCallback } from 'react';
import SpecialistDoctorIntro from 'src/pages/specialist/component/intro';
import { FixedButton } from 'src/components/common';
import { shallowEqual, useSelector } from 'react-redux';
import { ApplicationState } from 'src/store';
import './doctordetail.scss';

const prefixCls = 'doctordetail-pages';
const ChooseTextInquiryPage = (props) => {
  const { docotorDetail } = useSelector((state: ApplicationState) => {
    const { doctor: { detail = {} } = {} } = state;
    return { docotorDetail: detail };
  }, shallowEqual);
  const goToInquiryForm = useCallback(() => {
    props.history.push({
      pathname: '/hospital/inquiryform',
      search: `staffId=${docotorDetail.id}&inquiryTimingType=assign_doctor`,
    });
  }, [docotorDetail]);
  return (
    <div className={prefixCls}>
      <SpecialistDoctorIntro inquirySourceType='doctor' {...props} />
      <FixedButton buttonShape="round" prefixCls="fix_button" buttonClick={goToInquiryForm} text="立即问诊" />
    </div>
  );
};
export default ChooseTextInquiryPage;
