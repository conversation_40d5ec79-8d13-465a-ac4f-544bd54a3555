// @import "src/style/index";

// .doctorevalute_page {
//   position: relative;

//   .head_wrapper {
//     padding: r(20) r(15) 0 r(20);
//     height: r(100);
//     @include display-flex;
//     @include align-items(center);
//     @include justify-content(center);

//     .rating {
//       .number {
//         color: #ffec00;
//         font-size: r(35);
//         font-weight: bold;
//         text-align: center;

//         span {
//           font-size: r(14);
//         }
//       }

//       .text {
//         color: #fff;
//         font-size: r(14);
//         text-align: center;
//       }
//     }

//     .line {
//       margin: 0 r(9) 0 r(9);
//       width: r(1);
//       height: r(65);
//       background: rgba(255, 255, 255, .3);
//     }

//     .tags {
//       @include display-flex;
//       @include align-items(center);

//       flex-wrap: wrap;
//       flex: 1;

//       .tag_item {
//         margin: r(3) 0 r(3) r(8);
//         display: inline-block;
//         height: r(25);
//         line-height: r(26);
//         padding: 0 r(3);
//         background: #e7f8f3;
//         font-size: r(13);
//         color: $dark-green;
//         border-radius: r(3);
//         text-align: center;
//       }
//     }
//   }

//   .evaluate_wrapper {
//     padding: r(5) r(15);
//     position: relative;

//     .za-pull__load--show {
//       display: none !important;
//     }
//   }

//   .custom_cover {
//     width: 100%;
//     background: #f8f8f8;
//     color: #9b9b9b;
//     font-size: r(14);
//     text-align: center;
//     margin: r(20) 0 r(60);
//   }
// }
