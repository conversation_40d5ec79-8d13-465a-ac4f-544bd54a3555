// import React, { useCallback, useState } from 'react';
// import { useSelector, shallowEqual, useDispatch } from 'react-redux';
// import { fetch_doctor_evaluate } from 'src/store/doctor/action';
// import { Card, Evaluate, Background } from 'src/components/common';
// import { Pull, ActivityIndicator } from 'zarm';
// import { ApplicationState } from 'src/store';
// import './doctorevaluate.scss';

// const LOAD_STATE = {
//   normal: 0,   // 普通
//   abort: 1,    // 中止
//   loading: 2,  // 加载中
//   success: 3,  // 加载成功
//   failure: 4,  // 加载失败
//   complete: 5, // 加载完成（无新数据）
// };

// const tagParse = (data) => {
//   const obj = {}
//   Object.keys(data).forEach(i => {
//     data[i].forEach(k => {
//       obj[k.id] = k.tagName;
//     });
//   })
//   return obj;
// }

// const DoctorEvaluate = (props) => {

//   const { location: { state } } = props;
//   const [staffId] = useState(state && state.staffId);

//   //刷新组件使用的两个状态值 loadStatus && refreshStatus
//   const [loadStatus, setLoadStatus] = useState(LOAD_STATE.normal);

//   const dispatch = useDispatch();
//   const fetchDoctorEvaluate = (staffId: string, options?: any, onSuccess?: any) => dispatch(fetch_doctor_evaluate(staffId, options, onSuccess));
//   const { currentPage, totalPage, doctorEvaluate, TAG } = useSelector((state: ApplicationState) => {
//     return {
//       doctorEvaluate: state.doctor.evaluate,
//       TAG: tagParse(state.inquiry.evaluateTags),
//       currentPage: state.doctor.evaluate.currentPage,
//       totalPage: state.doctor.evaluate.totalPage
//     };
//   }, shallowEqual);

//   // 下拉加载
//   const loadData = useCallback(() => {
//     setLoadStatus(LOAD_STATE.loading);
//     // console.log('下拉加载啦·······');
//     if (currentPage === totalPage) {
//       setLoadStatus(LOAD_STATE.complete);
//     } else {
//       fetchDoctorEvaluate(staffId, {
//         pageSize: 10,
//         currentPage: Number(currentPage) + 1
//       }, () => {
//         setLoadStatus(LOAD_STATE.success);
//       });
//     }
//   }, [totalPage, currentPage]);

//   return (
//     <div className='doctorevalute_page'>
//       <Background />
//       <div className="head_wrapper">
//         <div className='rating'>
//           <p className='number'>{doctorEvaluate.rating || 0}<span>%</span></p>
//           <p className='text'>综合好评率</p>
//         </div>
//         <div className='line'></div>
//         <div className="tags">
//           {doctorEvaluate.tags ? doctorEvaluate.tags.map(item => <div className='tag_item' key={`evaluate_tag_${item}`} >{item}</div>) : null}
//         </div>
//       </div>

//       <Card >
//         <Pull className='evaluate_wrapper'
//           load={{
//             state: loadStatus,
//             distance: 50,
//             handler: loadData,
//             render: (loadState) => {
//               const cls = 'custom-control';
//               switch (loadState) {
//                 case LOAD_STATE.loading:
//                   return <div className={cls}><ActivityIndicator type="spinner" /></div>;
//                 default:
//                   return null;
//               }
//             },
//           }}>
//           {doctorEvaluate.data ? doctorEvaluate.data.map(item => <Evaluate key={`evaluate_componnent_${item.id}`} renderData={item} TAG={TAG} />) : null}
//         </Pull>
//       </Card>
//       {loadStatus === 5 ? <div className='custom_cover'>—— 已经到底啦 ——</div> : null}
//     </div>
//   );
// }

// export default DoctorEvaluate;