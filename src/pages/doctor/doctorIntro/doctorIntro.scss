@import "src/style/index";

.doctorintro_page {
  height: 100vh;
  overflow-y: scroll;

  .header {
    height: r(288);
    position: relative;
    font-size: 0;

    .life_photo {
      width: 100%;
      height: 100%;
    }

    .header_content {
      position: relative;
      bottom: r(84);
      height: r(84);
      margin: 0 r(15);
      padding: 0 r(20) 0 r(27);
      border-radius: r(8) r(8) 0 0;
      background: rgba(0, 0, 0, 0.59);
      border: r(1) solid rgba(255, 245, 227, 0.8);
      border-bottom: none;
      color: rgba(255, 255, 255, 0.7);
      font-size: r(12);
      @include display-flex;
      @include align-items(center);
      @include justify-content(space-between);

      &::before,
      &::after {
        content: '';
        position: absolute;
        height: r(77);
        width: r(1);
        top: r(6);
        background: linear-gradient(180deg, rgb(255, 245, 227, 0), rgba(0, 0, 0, 0.8));
      }

      &::before {
        left: r(-1);
      }

      &::after {
        right: r(-1);
      }

      .title {
        font-size: r(16);
        font-weight: bold;
        color: rgba(255, 232, 190, 0.7);
        margin-bottom: r(5);
      }

      .doctor_name {
        font-weight: 600;
        font-size: r(21);
        margin-right: r(5);
        color: #ffe8be;
      }

      .icon_badge {
        width: r(74);
        height: r(72);
      }
    }
  }

  .intro_wrap {
    width: r(345);
    border-radius: r(8);
    margin: r(10) auto 0;
    font-size: r(13);
    color: #666;

    &.tab_wrap {
      height: r(80);
      color: #999;
      @include display-flex;
      @include align-items(center);

      .tab {
        position: relative;
        flex: 1;
        height: r(60);
        padding: 0 r(19) 0 r(15);
        line-height: 1.7;
        @include display-flex;
        @include align-items(center);
        @include justify-content(space-between);

        &:not(:last-child) {
          &::after {
            content: '';
            position: absolute;
            right: 0;
            top: -50%;
            border-right: r(1) solid #ececec;
            height: 200%;
            transform: scale(0.5);
          }
        }
      }

      .intro_tab_img {
        width: r(40);
        height: r(40);
      }
    }

    .title {
      font-size: r(16);
      font-weight: bold;
      color: #000;
    }

    &.doctor_intro {
      padding: r(18) r(15);

      .intro_item {
        position: relative;
        padding-left: r(25);
        margin-top: r(16);
        word-break: break-all;
      }

      .intro_item_img {
        position: absolute;
        width: r(18);
        height: r(18);
        left: 0;
        top: r(2);
      }

      .intro_title {
        font-size: r(15);
        font-weight: bold;
        margin-bottom: r(5);
        color: #333;
      }

      .noellipse_hide {
        position: absolute;
        top: 0;
        z-index: -1;
        opacity: 0;
      }

      .ellipse {
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 3;
        -webkit-box-orient: vertical;
      }
    }

    .btn_more {
      position: relative;
      width: r(66);
      margin: r(20) auto 0;
      padding-right: r(13);

      &::before,
      &::after {
        content: '';
        position: absolute;
        border-left: 2px solid #bbb;
        border-bottom: 2px solid #bbb;
        transform: rotate(-45deg) scale(0.8);
        height: r(9);
        width: r(9);
        right: r(0);
        top: r(1);
      }

      &::after {
        opacity: 0.5;
        top: r(7);
      }
    }

    &.qrcode_wrap {
      height: r(96);
      padding: 0 r(15);
      @include display-flex;
      @include align-items(center);
      @include justify-content(space-between);

      .title {
        margin-bottom: r(10);
      }

      .qrcode {
        width: r(60);
        height: r(60);
        min-width: r(60);
        margin-left: r(67);
        border-radius: r(6);
      }
    }
    /* 添加医生二维码 */
    &.doctor_qrcode {
      padding: r(15);
      @include display-flex;
      @include align-items(center);
      @include justify-content(space-between);

      h3 {
        font-size: r(16);
        font-weight: 600;
        color: #1e1e1e;
        line-height: r(22.5);
        margin-bottom: r(5);
      }

      .qrcode-img {
        width: r(60);
        height: r(60);
        background: #e6e6e6;
        border-radius: 6px;
        padding: 2px;

        .qrcode-icon {
          width: r(56);
          height: r(56);
          border-radius: 3px;
        }
      }
    }
  }

  .footer {
    height: r(91);
    line-height: r(91);
    text-align: center;
    @include iphone-bottom-fixed;

    .img_footer {
      width: r(216);
    }
  }
}

.doctor_intro_modal,
.doctor_intro_rights_modal {
  padding: 0;
  // @include align-items(flex-end);

  .za-popup__wrapper {
    padding: 0;
    @include align-items(flex-end);
  }

  .za-popup {
    margin: 0;
    // height: r(285);
    width: 100% !important;
    border-radius: r(8) r(8) 0 0;
    // @include iphone-bottom-fixed;
  }

  .za-modal__body {
    padding: 0;
  }
}

.doctor_intro_rights_modal {
  .doctor_rights {
    padding: r(18) 0 r(18) r(36);
    @include flex-wrap(wrap);
  }

  .za-modal__header {
    position: relative;
    height: r(50);
    padding: r(16) 0 0;
    line-height: r(50);
    font-size: r(17);
    font-weight: bold;

    &::after {
      position: absolute;
      content: '';
      left: -50%;
      bottom: 0;
      width: 200%;
      border-bottom: r(1) solid #e6e6e6;
      transform: scale(0.5);
    }
  }

  .za-modal__header__title {
    line-height: 1.2;
    font-size: r(17);
    font-weight: bold;
    color: rgba(0, 0, 0, 0.8);
  }

  .za-modal__header__close {
    color: #d8d8d8;
    font-size: r(22);
    top: r(14);
  }
}

.doctor_intro_modal {
  .za-popup {
    height: r(290);
    overflow: visible;
  }

  .za-modal__header {
    padding: 0;

    .za-modal__header__close {
      z-index: 1;
    }
  }

  .za-modal__body {
    overflow: hidden;
  }

  .modal_doctor_bg {
    position: relative;
    height: r(118);
    background: linear-gradient(129deg, rgb(237, 209, 168) 0%, rgb(230, 196, 150) 100%);
    box-shadow: 0 2px 1px 0 #fff;
    border-radius: r(8) r(8) 0 0;
    opacity: 0.6;

    &::after {
      content: '';
      position: absolute;
      bottom: r(-24);
      left: -5%;
      right: -5%;
      background: inherit;
      border-radius: 50%;
      height: r(60);
      width: 110%;
    }
  }

  .modal_header_doctor {
    position: absolute;
    width: 100%;
    top: 0;

    .doctor_avtar_wrap {
      margin: r(-20) auto 0;
      text-align: center;
      height: r(74);
      width: r(74);
      border-radius: 50%;
      background: linear-gradient(180deg, rgba(237, 208, 169, 1), rgba(207, 174, 122, 1));
    }

    .avtar {
      width: r(70);
      height: r(70);
      background: #ccc;
      border-radius: 50%;
      margin-top: r(2);
    }

    .avtar_bottom {
      display: block;
      width: r(77);
      height: r(28);
      margin: r(-16) auto;
    }

    .doctor_title {
      margin-top: r(20);
      text-align: center;
      font-size: r(21);
      font-weight: 600;
      color: #5f3312;
    }

    .doctor_tags {
      margin-top: r(8);
      @include display-flex;
      @include justify-content(center);
    }

    .tag_item {
      padding: 0 r(4);
      margin: 0 r(2.5);
      height: r(15);
      line-height: r(15);
      font-size: r(11);
      color: #5f3312;
      background: rgba(255, 255, 255, 0.2);
      border-radius: r(2);
      border: r(1) solid rgba(95, 51, 18, 0.2);
    }
  }

  .doctor_say {
    margin-top: r(25);
    height: r(80);
    font-size: r(15);
    color: #666;
    @include display-flex;
    @include align-items(center);
    @include justify-content(center);

    .icon_comma {
      width: r(24);
      height: r(24);
      margin: r(-8) r(14) 0;
    }

    .next_quote {
      transform: rotate(180deg);
    }
  }

  .btn_chat_wrap {
    margin: 0 r(15);

    .btn_chat {
      font-size: r(17);
      font-weight: bold;
    }

    .chat_icon {
      width: r(20);
      height: r(19);
      margin: r(-2) r(5) 0;
    }
  }

  .za-modal__header__close {
    color: #dab583;
    font-size: r(22);
    top: r(14);
  }
}
