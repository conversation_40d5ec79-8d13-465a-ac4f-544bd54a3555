import React, { useCallback, useEffect, useLayoutEffect, useRef, useState } from 'react';
import { useSelector, shallowEqual } from 'react-redux';
import { Avatar, BrandSlogan, Card, Rights } from 'src/components/common';
import { CONSULT_LIST, RIGHTS_OBJ } from 'src/pages/home/<USER>';
import { ApplicationState } from 'src/store';
import { jumpToWechatGuidance } from 'src/utils/auth';
import { fetchJson } from 'src/utils/fetch';
import { Deserialize } from 'src/utils/serialization';
import validate from 'src/utils/validate';
import { Button, Modal, Pull } from 'zarm';
import './doctorIntro.scss';
import { docorInfoMap } from './staticData';

const DoctorIntro = (props) => {
  // const cdnDoctorQrcode: string = 'http://cdn-qcloud.zhongan.com/a00000/za-asclepius/enterprise-wechat/6662.png';
  const { location: { search = '' } } = props;
  const { staffNo = '' } = Deserialize(search);
  const [doctorVisible, setDoctorVisible] = useState(false);
  const [rightsVisible, setRightsVisible] = useState(false);
  const [showMore, setShowMore] = useState(false);
  const [forbidLoad, setforbidLoad] = useState(false); // showMore时候会出发pull的加载，现加一个控制参数
  const [doctorInfo, setDoctorInfo]: any = useState({});
  const [rightsList, setRightsList]: any = useState([]);
  const [modalInfo, setModalInfo]: any = useState({});

  const ellipseRef: any = useRef(null);
  const noEllipseRef: any = useRef(null);

  const isMiniEnv = validate.isFromMiniApplet() || validate.isFromWeixin();

  const { reduxStaffNo } = useSelector((state: ApplicationState) => {
    const { staffNo = '' } = state.chat.doctorInfo;
    return { reduxStaffNo: staffNo };
  }, shallowEqual);

  const { PROFESSIONALTITLE_OBJ, showAddWechat }: any = useSelector((state: ApplicationState) => ({
    PROFESSIONALTITLE_OBJ: state.dictionary.PROFESSIONALTITLE_OBJ,
    showAddWechat: state.config.showAddWechat,
  }));

  useEffect(() => {
    if (!staffNo && !reduxStaffNo) {
      return;
    }
    fetchJson({
      url: '/api/api/v1/patient/doctor/detail',
      type: 'POST',
      data: {
        staffNo: staffNo || reduxStaffNo,
        option: {
          isQueryHeadPortrait: true,
          isQueryAttachment: true,
        },
      },
      isloading: false,
    }).then((res) => {
      if (res && res.code === '0') {
        const { attachmentList = [] } = res.result;
        const info: any = {};
        attachmentList.map(({ attachmentType, attachmentDownloadUrl }) => {
          info[attachmentType] = attachmentDownloadUrl;
        });
        setDoctorInfo({
          ...res.result,
          ...info,
        });
      }
    });
  }, []);

  useEffect(() => {
    fetchJson({
      url: '/api/api/v1/patient/policy/queryPolicyVasInfo',
      type: 'POST',
      data: {},
      isloading: false,
    }).then((res) => {
      const { result, code } = res || {};
      if (code === '0') {
        const orderRights = result.length && result.sort((a, b) => a.vasIndex - b.vasIndex);
        setRightsList([...orderRights.filter((i) => i.isSelected === true && RIGHTS_OBJ[i.vasCode]), ...orderRights.filter((i) => i.isSelected !== true && RIGHTS_OBJ[i.vasCode])]);
      }
    });
  }, []);

  const toChat = useCallback(() => {
    props.history.push({
      pathname: '/hospital/chatmedicalmanage',
    });
  }, []);

  const moreInfo = docorInfoMap[staffNo || reduxStaffNo] || {};

  useLayoutEffect(() => {
    if (doctorInfo.staffName) {
      console.log(noEllipseRef.current.clientHeight, ellipseRef.current.clientHeight);
      if (noEllipseRef.current.clientHeight > ellipseRef.current.clientHeight) {
        setShowMore(false);
      } else {
        setShowMore(true);
      }
    }
  }, [doctorInfo]);

  const { staffName = '', staffProfessionalTitle = '', headPortrait = '', workSeniority = '', staffSkills = '', staffIntroduction = '', lifePhoto = '', qrCode = '' } = doctorInfo;

  // const tagList = tags.split(',') || [];

  console.log(doctorInfo);

  const tagList = staffName ? [PROFESSIONALTITLE_OBJ[staffProfessionalTitle], `从医${workSeniority}年`] : [];

  const { modalTitle, modalList } = modalInfo;

  const handlerLoad = useCallback(() => {
    if (forbidLoad) {
      setTimeout(() => {
        setforbidLoad(false);
      }, 1000);
      return;
    }
    setDoctorVisible(true);
  }, [forbidLoad]);

  return (
    <div className='doctorintro_page'>
      <Pull
        load={{
          state: 0,
          handler: handlerLoad,
        }}
      >
        <header className='header'>
          <img className='life_photo' src={`${lifePhoto}`} />
          <div className='header_content'>
            <div>
              <p className='title'><span className='doctor_name'>{staffName}</span>家庭医生</p>
              <p>{PROFESSIONALTITLE_OBJ[staffProfessionalTitle]}｜从医{workSeniority}年</p>
            </div>
            <img className='icon_badge' src={require('src/images/icon_family_doctor_badge.png')} />
          </div>
        </header>
        <Card prefixCls='intro_wrap tab_wrap'>
          <div className='tab' onClick={() => {
            setRightsVisible(true);
            setModalInfo({
              modalTitle: '健康咨询',
              modalList: CONSULT_LIST,
            });
          }}>
            <div>
              <p className='title'>健康咨询</p>
              <p>线上健康咨询</p>
            </div>
            <img className='intro_tab_img' src={require('src/images/icon_doctor_consult.png')} />
          </div>
          <div className='tab' onClick={() => {
            setRightsVisible(true);
            setModalInfo({
              modalTitle: '权益询用',
              modalList: rightsList,
            });
          }}>
            <div>
              <p className='title'>权益询用</p>
              <p>权益咨询使用</p>
            </div>
            <img className='intro_tab_img' src={require('src/images/icon_doctor_right.png')} />
          </div>
        </Card>
        {showAddWechat && <Card prefixCls='intro_wrap doctor_qrcode' onClick={() => {
          // jumpToWechatGuidance('6663', { doctorType: 1 })
          jumpToWechatGuidance();
        }}>
          <div>
            <h3>家庭医生二维码</h3>
            <p>加医生微信，随时随地问医生</p>
          </div>
          <p className='qrcode-img'>
            <img className='qrcode-icon' src={qrCode} />
          </p>
        </Card>}
        <Card prefixCls='intro_wrap doctor_intro'>
          <p className='title'>医生介绍</p>
          <div className='intro_item'>
            <img className='intro_item_img' src={require('src/images/icon_doctor_professional.png')} />
            <p className='intro_title'>专业资质</p>
            <p>{moreInfo.professional || '全科金牌医生'}</p>
          </div>
          <div className='intro_item'>
            <img className='intro_item_img' src={require('src/images/icon_doctor_field.png')} />
            <p className='intro_title'>擅长领域</p>
            <p>{moreInfo.staffSkills || staffSkills}</p>
          </div>
          <div className='intro_item'>
            <img className='intro_item_img' src={require('src/images/icon_doctor_experience.png')} />
            <p className='intro_title'>执业经历</p>
            <p className={`${showMore ? '' : 'ellipse'}`} ref={ellipseRef}>{moreInfo.experience || staffIntroduction}</p>
            <p className='noellipse_hide' ref={noEllipseRef}>{moreInfo.experience || staffIntroduction}</p>
          </div>
          {
            !showMore && doctorInfo.staffName && <div>
              <p className='btn_more' onClick={() => {
                setShowMore(true);
                setforbidLoad(true);
              }}>查看更多</p>
            </div>
          }
        </Card>
        {
          isMiniEnv && false && <Card prefixCls='intro_wrap qrcode_wrap'>
            <div>
              <p className='title'>医生微信二维码</p>
              <p>长按二维码添加微信</p>
            </div>
            <img className='qrcode' src={`${qrCode}`} />
          </Card>
        }
        <BrandSlogan />
      </Pull>

      <Modal
        className='doctor_intro_rights_modal'
        visible={rightsVisible}
        closable
        maskClosable
        title={modalTitle}
        onCancel={() => setRightsVisible(false)}
      >
        <Rights prefixCls='doctor_rights' list={modalList}></Rights>
      </Modal>

      <Modal
        className='doctor_intro_modal'
        visible={doctorVisible}
        closable
        maskClosable
        onCancel={() => setDoctorVisible(false)}
      >
        <div className='modal_doctor_bg'></div>
        <div className='modal_header_doctor'>
          <div className='doctor_avtar_wrap'>
            {/* <img className="avtar" /> */}
            <Avatar prefixCls='avtar' preUrl={headPortrait ? `${headPortrait}` : require('src/images/doctor_default_avater.png')} />
          </div>
          <img className='avtar_bottom' src={require('src/images/icon_doctor_avtarbt.png')} />
          <div className='doctor_title'>{staffName}</div>
          <div className='doctor_tags'>
            {
              tagList.map((item, index) => item && <p className='tag_item' key={`tag${index}`}>{item}</p>)
            }
          </div>
        </div>
        <div className='doctor_say'>
          <img className='icon_comma' src={require('src/images/icon_doctor_comma.png')} />
          <p>有什么疑问，随时咨询我</p>
          <p className='icon_comma'></p>
          {/* <img className="icon_comma next_quote" src={require('src/images/icon_doctor_comma.png')} /> */}
        </div>
        <div className='btn_chat_wrap'>
          <Button className='btn_chat' block shape='round' theme='primary' onClick={toChat}>
            <img className='chat_icon' src={require('src/images/icon_chat.png')} />立即咨询
          </Button>
        </div>
      </Modal>
    </div>
  );
};

export default DoctorIntro;
