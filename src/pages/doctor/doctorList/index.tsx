import React from 'react';
import ChooseDoctorComponent from 'src/pages/specialist/component/excellentDoctorList/index';
import './doctorlist.scss';

const DoctorList = (props) => {
  const goGoTextInquiry = (item) => {
    const { staffNo = '' } = item;
    props.history.push({
      pathname: `/hospital/doctordetail`,
      search: `staffNo=${staffNo}`,
    });
  };
  return <ChooseDoctorComponent className="doctorlist-page" doctorOption={{ staffTypes: ['doctor'], staffAttributeList: [1, 2], isInquiry: 'Y', productId: 1 }} jumpToTextInquiry={goGoTextInquiry} {...props} />;
};

export default DoctorList;
