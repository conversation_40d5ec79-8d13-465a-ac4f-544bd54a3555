/*
 * @authors :<PERSON>
 * @description：  患者管理模块路由
 */
const route = [
  {
    path: '/hospital/doctordetail',
    name: 'DoctorDeta<PERSON>',
    component: () => import(/* webpackPrefetchPlaceHolder */ './doctorDetail'),
    auth: true,
    exact: true,
    title: '医生主页',
  },
  {
    path: '/hospital/doctorlist',
    name: 'DoctorList',
    component: () => import(/* webpackPrefetchPlaceHolder */ './doctorList'),
    auth: true,
    exact: true,
    title: '选择医生',
  },
  {
    path: '/hospital/doctorintro',
    name: 'DoctorIntro',
    component: () => import(/* webpackPrefetchPlaceHolder */ './doctorIntro'),
    auth: true,
    exact: true,
    title: '医生介绍',
  },
];
export default route;

