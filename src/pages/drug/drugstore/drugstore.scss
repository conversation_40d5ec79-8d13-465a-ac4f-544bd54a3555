@import 'src/style/index';
$prefixCls: 'drugstore-pages';

.#{$prefixCls} {
  background-color: #fff;
  min-height: 100vh;
  padding: r(15);

  &__item {
    margin-bottom: r(13);
    padding: r(21) r(20) r(10);
    background-repeat: no-repeat;
    background-size: 100%;
    min-height: r(167.5);

    .title {
      margin-bottom: r(8);
      padding: r(4) 0;
      font-size: r(20);
      font-weight: 600;
      line-height: r(20);
      color: #155039;
      @include display-flex;

      .tag {
        @include display-flex;
        @include align-items(center);
        @include justify-content(center);

        width: r(30);
        height: r(15);
        line-height: r(15);
        margin-left: r(4);
        color: #fff;
        text-align: center;
        background: #ff5050;
        border-radius: r(7.5) r(7.5) r(7.5) 0;
        border: r(1) solid #fafafa;

        em {
          display: inline-block;
          font-size: r(12);
          font-weight: normal;
          transform: scale(0.8);
        }
      }
    }

    .feature-row {
      font-size: r(14);
      font-weight: 600;
      color: #333;
      line-height: r(20);
      padding-left: r(20);
      background-size: r(14);
      background-repeat: no-repeat;
      background-position: 0 50%;

      & + .feature-row {
        margin-top: r(7);
      }
    }

    .btn {
      width: r(80);
      height: r(30);
      line-height: r(29);
      margin-top: r(12);
      font-size: r(15);
      font-weight: 600;
      text-align: center;
      color: #fff;
      border-radius: r(16);
      box-shadow: 0 4px 10px 0 rgba(0, 168, 100, 0.1);
    }
    //高济
    &.gaoji {
      .title {
        color: #153e65;
      }

      .btn {
        background-color: #61bcff;
        border-color: #61bcff;
      }
    }
    //上药云慢病
    &.syymb {
      .title {
        color: #874d10;
      }

      .btn {
        background: #ec9131;
        border-color: #ec9131;
      }
    }

    &.syyxb {
      .title {
        color: #086b6d;
      }

      .btn {
        background: #1fada7;
        border-color: #1fada7;
      }
    }
  }
}

.chronicmedicinewy-page {
  min-height: 100vh;
  background: linear-gradient(180deg, #e6feee 0%, #f5f5f5 63%);
  padding-bottom: r(74);
  box-sizing: border-box;

  &__header {
    display: block;
    width: 100%;
    max-width: 750px;
    margin: auto;
  }

  &__fixed {
    position: fixed !important;
    bottom: 0;
    left: 0;
    right: 0;
    padding: r(10) r(15) r(20);
    background: #fff;

    @include onepx(top, #e6e6e6);
  }
}
