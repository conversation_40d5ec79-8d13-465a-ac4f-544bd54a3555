<?xml version="1.0" encoding="UTF-8"?>
<svg width="28px" height="28px" viewBox="0 0 28 28" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>Entry / Radio / 28 已选择</title>
    <defs>
        <linearGradient x1="0%" y1="50%" x2="100%" y2="50%" id="linearGradient-1">
            <stop stop-color="#CBECEB" offset="0%"></stop>
            <stop stop-color="#B3EAE8" offset="100%"></stop>
        </linearGradient>
    </defs>
    <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="云药房过渡页-0903+小病0元" transform="translate(-70.000000, -1442.000000)">
            <g id="编组-9" transform="translate(30.000000, 1268.000000)">
                <g id="Entry-/-Radio-/-28-已选择" transform="translate(40.000000, 174.000000)">
                    <circle id="椭圆形" fill="url(#linearGradient-1)" cx="14" cy="14" r="14"></circle>
                    <path d="M7,14.4188034 L12.3846154,19.8034188 M21,10.1111111 L12.3846154,19.8034188" id="tick--白" stroke="#FFFFFF" stroke-width="3" stroke-linecap="round" stroke-linejoin="round"></path>
                </g>
            </g>
        </g>
    </g>
</svg>