/*
 * @description： 药品相关 - 云药房选择页
 */
import classnames from 'classnames';
import React, { useEffect, useState } from 'react';
import { StaticToast } from 'src/components/common';
import { pushEvent, jumpBeforeAuth, validate, Deserialize } from 'src/utils';
import { THIRD_PLATFORM_RESOURCECODE } from 'src/utils/staticData';
import { Button } from 'zarm';
import HeaderPic from './images/chronicmedicinewy-page_header.webp';
import drugStoreConfig, { checkYYFRightPlus, getActivationState, handleActiveRight } from './utils';
import './drugstore.scss';

const prefixCls = 'drugstore-pages';

const DrugStore = (props) => {
  const { search } = window.location;
  const { vasCode, userRightsId, policyNo } = Deserialize(search);
  const [configs, setConfig] = useState([...drugStoreConfig]);
  const [isActivated, setIsActivated] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  const getRight = async () => {
    const SYYRes: any = await checkYYFRightPlus({ vasCode, userRightsId, policyNo }); // 上药云慢病
    const { rights, userRights } = SYYRes;
    // rights 为空对象时，表示无权益，跳转无权益页面
    if (Object.keys(rights).length === 0) {
      // 跳转无权益页面
      props.history.replace('/hospital/static/no-rights');
      return;
    }

    /**
     * 处理药店配置数据
     *
     * @description
     * 1. 遍历药店配置数组，根据用户权益状态设置每个药店的可见性
     * 2. 当用户拥有对应权益时(rights[k.vasCode] === '1')，设置visible为true并记录权益ID
     * 3. 对于国健药品折扣(gjDrugDiscount)，额外设置国健保单号
     * 4. 返回合并后的配置对象，包含原始配置和新增的权益相关属性
     */
    const nextConfig = [...drugStoreConfig].map((k: any) => {
      const d: any = {};
      if (rights[k.vasCode] === '1') {
        d.visible = true;
        d.userRightsId = userRights[k.vasCode];
        if (k.vasCode === 'gjDrugDiscount') {
          d.gjPolicyNo = rights.gjPolicyNo;
        }
      }
      return {
        ...k,
        ...d,
      };
    });
    // 挑出visible为true的权益
    const visibleList = nextConfig.filter((item) => item.visible);
    if (
      visibleList.length === 1 && ['chronicmedicinewy'].includes(visibleList[0].vasCode)
    ) {
      getActivationState(userRightsId || visibleList[0].userRightsId).then((res) => {
        if (res.code === '0') {
          setIsActivated(res.result);
        }
      });
    }
    setConfig(nextConfig);
  };

  useEffect(() => {
    getRight();
  }, []);

  useEffect(() => {
    // 预防跳到云药房返回不刷新的情况
    const pageShowFunc = (e) => {
      if(e.persisted){
        getRight();
      }
    };
    window.addEventListener('pageshow', pageShowFunc);
    return () => {
      window.removeEventListener('pageshow',pageShowFunc);
    };
  }, []);

  /**
   * 跳转到药店页面
   *
   * @param {Object} item - 药店信息对象
   * @param {string} item.openAppletsUrl - 小程序跳转链接
   * @param {string} item.title - 药店标题
   * @param {string} item.platformCode - 平台代码
   * @param {Object} item.authConfig - 授权配置
   * @param {string} item.gjPolicyNo - 国健保单号
   *
   * @description
   * 1. 记录用户点击事件
   * 2. 如果是国健商城平台，则进行授权跳转并传入保单号
   * 3. 如果是在微信环境但不是小程序环境，直接跳转到小程序链接
   * 4. 其他情况下，进行授权后跳转
   */
  const goToStore = (item) => {
    const { openAppletsUrl = '', title = '', platformCode = '', authConfig = {} } = item;
    pushEvent({ eventTag: 'ZAHLWYY_YYF_GDY', text: '云药房_过渡页', attrs: { ZAHLWYY_CLICK_CONTENT: `${title}` } });
    if (platformCode === THIRD_PLATFORM_RESOURCECODE.GJSC) {
      jumpBeforeAuth({
        ...authConfig,
        bizNo: item.gjPolicyNo,
      });
      return;
    }
    if (!validate.isFromMiniApplet() && validate.isFromWeixin()) {
      location.href = openAppletsUrl;
    } else {
      jumpBeforeAuth(authConfig);
    }
  };

  /** 展示的权益列表 */
  const visibleList = configs.filter((item) => item.visible);

  /** 表示只展示慢病无忧 */
  const onlyShowChronicmedicinewy = visibleList.length === 1 && visibleList.every((item) => item.vasCode === 'chronicmedicinewy');

  const btnText = isActivated ? '购买药品' : '立即激活';

  const handleChronicmedicinewyGoto = () => {
    if (isActivated) {
      goToStore(visibleList[0]);
    } else {
      setIsLoading(true);
      handleActiveRight(userRightsId || visibleList[0].userRightsId).then((res) => {
        if (res.code === '0') {
          StaticToast.success('成功激活权益，您可享受购药优惠');
          getRight();
          setTimeout(() => {
            goToStore(visibleList[0]);
            setIsLoading(false);
          }, 2000);
        } else {
          setIsLoading(false);
        }
      }).catch(() => {
        setIsLoading(false);
      });
    }
  };

  if(onlyShowChronicmedicinewy) {
    return (
      <div className='chronicmedicinewy-page'>
        <img className='chronicmedicinewy-page__header' src={HeaderPic} alt='慢病无忧' />
        <div className='chronicmedicinewy-page__fixed'>
          <Button loading={isLoading} disabled={isLoading} onClick={handleChronicmedicinewyGoto} className='chronicmedicinewy-page__fixed-btn' shape='round' theme='primary' block>{btnText}</Button>
        </div>
      </div>
    );
  }

  return (
    <section className={classnames(prefixCls)}>
      {configs.map((k) => {
        const { featureDesc = [] } = k;
        return k.visible ? (
          <div className={`${prefixCls}__item ${k.className}`} key={`item-${k.uid}`} style={{ backgroundImage: `url(${k.bg})` }} onClick={() => goToStore(k)}>
            <div className='title'>
              <p>{k.title}</p>
              {!!k.tag && (
                <span className='tag'>
                  <em>{k.tag}</em>
                </span>
              )}
            </div>
            {featureDesc.map((u, i) => (
              <p className='feature-row' key={`row${i}`} style={{ backgroundImage: `url(${k.featureIcon})` }}>
                {u}
              </p>
            ))}
            <Button size='sm' theme='primary' className='btn'>
              去看看
            </Button>
          </div>
        ) : null;
      })}
    </section>
  );
};
export default DrugStore;
