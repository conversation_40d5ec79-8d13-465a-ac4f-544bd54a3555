/*
 * @description： 上药云
 */
import { fetchJson, jumpBeforeAuth, validate } from 'src/utils';
import format from 'src/utils/format';
import { CDN_PREFIX, THIRD_PLATFORM_RESOURCECODE, THIRD_PARTNER_AUTH_CONFIG } from 'src/utils/staticData';
// import { Serialize } from 'src/utils/serialization';

const storeConfig = [
  {
    className: 'gaoji',
    platformCode: THIRD_PLATFORM_RESOURCECODE.GJSC,
    uid: format.guid(),
    vasCode: 'gjDrugDiscount',
    bg: `${CDN_PREFIX}applets/menu/drug-select-gaoji.png`,
    tag: '满减',
    featureIcon: require('./images/gaoji-check.svg'),
    title: '特惠购药',
    visible: false,
    authConfig: THIRD_PARTNER_AUTH_CONFIG.GJSC,
    featureDesc: ['满100减20', '家中常备药一应俱全'],
  },
  {
    className: 'syy',
    platformCode: THIRD_PLATFORM_RESOURCECODE.SYY_DRUGSTROE,
    uid: format.guid(),
    bg: `${CDN_PREFIX}applets/menu/drug-select-shangyaoyun.png`,
    featureIcon: require('./images/syy-check.svg'),
    title: '海量云药房',
    visible: false,
    featureDesc: ['万种药品随意选', '药企直供 品质保障'],
    authConfig: THIRD_PARTNER_AUTH_CONFIG.SYY_DRUGSTROE,
    openAppletsUrl: 'https://online.za-doctor.com/hospital/open-applets/2245032',
  },
  {
    className: 'syymb',
    platformCode: THIRD_PLATFORM_RESOURCECODE.SYY_MB,
    uid: format.guid(),
    vasCode: 'ChronicDisease',
    bg: `${CDN_PREFIX}applets/menu/drug-select-shangyaoyunmanbing.png`,
    featureIcon: require('./images/syymb-check.svg'),
    title: '慢病无忧',
    visible: false,
    featureDesc: ['8折购药', '常见品类'],
    authConfig: THIRD_PARTNER_AUTH_CONFIG.SYY_MB,
    openAppletsUrl: 'https://online.za-doctor.com/hospital/open-applets/13270001',
    userRightsId: '',
  },
  {
    className: 'syymb',
    platformCode: THIRD_PLATFORM_RESOURCECODE.MB_NEW,
    uid: format.guid(),
    vasCode: 'chronicmedicinewy',
    bg: `${CDN_PREFIX}applets/menu/drug-select-shangyaoyunmanbing.png`,
    featureIcon: require('./images/syymb-check.svg'),
    title: '慢病药无忧',
    visible: false,
    featureDesc: ['8折购药', '常见品类 一应具全'],
    authConfig: THIRD_PARTNER_AUTH_CONFIG.MB_NEW,
    openAppletsUrl: 'https://online.za-doctor.com/hospital/open-applets/13265001',
  },
  {
    className: 'syyxb',
    platformCode: THIRD_PLATFORM_RESOURCECODE.SYY_XB,
    uid: format.guid(),
    vasCode: 'FreeAilments',
    bg: `${CDN_PREFIX}applets/menu/drug-select-shangyaoyunxiaobing.png`,
    featureIcon: require('./images/syyxb-check.svg'),
    title: '小病0元治',
    visible: false,
    featureDesc: ['百余种小病选其一', '0元送药到家'],
    authConfig: THIRD_PARTNER_AUTH_CONFIG.SYY_XB,
    openAppletsUrl: 'https://online.za-doctor.com/hospital/open-applets/2420060',
  },
];

// 统一查询高济、上药云慢病、小病0元治权益 显示上药云慢病入口需要查询是否有门急诊保单
export const checkYYFRight = (vasCode?) => new Promise((resolve) => {
  fetchJson({
    url: '/api/api/v1/patient/pharmacy/query/rights',
    type: 'POST',
    data: {
      vasCodeList: vasCode === 'chronicmedicinewy' ? ['chronicmedicinewy', 'gjDrugDiscount', 'FreeAilments'] : ['ChronicDisease', 'gjDrugDiscount', 'FreeAilments'],
      option: {
        needInvalidPolicy: false, // 过滤失效保单
      },
    },
    needLoading: true,
  }).then((res) => {
    if (res.code === '0') {
      const { result = [] } = res || {};
      const rights: any = {};
      result.forEach((item) => {
        const { policyVasInfos = [] } = item || {};
        policyVasInfos.forEach((info) => {
          const { vasCode, isSelected } = info || {};
          if (isSelected) {
            rights[vasCode] = '1';
            if (vasCode === 'gjDrugDiscount') {
              rights.gjPolicyNo = item.policyNo;
            }
          }
        });
      });
      resolve(rights);
    } else {
      resolve({});
    }
  });
});
// 去云药房
export const jumpToDefaultYYF = async (option: any = {}) => {
  const SYYRes: any = await checkYYFRight(); // 上药云慢病
  if (!Object.keys(SYYRes || {}).length) {
    // 没有 GJ 购药权益，直接进入上药云
    const { authConfig: jumpSYYSearch = {}, openAppletsUrl = '' } = storeConfig.find((k) => k.platformCode === THIRD_PLATFORM_RESOURCECODE.SYY_DRUGSTROE) || {};

    if (!validate.isFromMiniApplet() && validate.isFromWeixin()) {
      location.replace(openAppletsUrl);
    } else {
      jumpBeforeAuth({ ...jumpSYYSearch,policyNo: SYYRes.policyNo, isReplacePage: true });
    }
  }
};

/**
 * @description 获取激活状态
 */
export const getActivationState = async (userRightsId) => fetchJson({
  url: '/api/api/v1/patient/user/rights/userRightsIsActive',
  type: 'GET',
  data: {
    userRightsId,
  },
});

/**
 * @description 激活权益
 */
export const handleActiveRight = async (userRightsId) => fetchJson({
  url: '/api/api/v1/patient/user/rights/activeRights',
  type: 'POST',
  data: {
    userRightsId,
  },
});


/**
 * @description 统一查询高济、上药云慢病、小病0元治权益 显示上药云慢病入口需要查询是否有门急诊保单【增强版，返回更多数据】
 * @param param0
 * @returns { rights: {[vasCode]: '1' | undefined}, userRights: {[vasCode]: number}}
 */
export const checkYYFRightPlus = ({ vasCode, userRightsId, policyNo }: {vasCode?: string; userRightsId?: string; policyNo?: string;}) => new Promise((resolve) => {
  fetchJson({
    url: '/api/api/v1/patient/pharmacy/query/rights',
    type: 'POST',
    data: {
      vasCodeList: vasCode === 'chronicmedicinewy' ? ['chronicmedicinewy', 'gjDrugDiscount', 'FreeAilments'] : ['ChronicDisease', 'gjDrugDiscount', 'FreeAilments'],
      option: {
        needInvalidPolicy: false, // 过滤失效保单
      },
    },
    needLoading: true,
  }).then((res) => {
    if (res.code === '0') {
      const { result = [] } = res || {};
      const rights: any = {};
      const userRights: any = {};
      result.forEach((item) => {
        const { policyVasInfos = [] } = item || {};
        policyVasInfos.forEach((info) => {
          const { vasCode, isSelected } = info || {};
          if (isSelected) {
            rights[vasCode] = '1';
            if (vasCode === 'gjDrugDiscount') {
              rights.gjPolicyNo = item.policyNo;
            }
          }

          // 根据policyNo确定唯一权益userRightsId
          if (policyNo === item.policyNo && vasCode === info.vasCode) {
            userRights[vasCode] = info.userRightsId;
          }
        });
      });
      resolve({ rights, userRights });
    } else {
      resolve({});
    }
  });
});

export default storeConfig;
