@import "src/style/index";

.drugdetail_page {
  padding-bottom: r(80);

  .carousel {
    .za-carousel__item {
      img {
        width: 100%;
        height: r(300);
      }
    }
  }

  .base {
    padding: r(15);
    background: #fff;

    & > p:first-of-type {
      color: #464646;
      font-size: r(15);
      font-weight: 600;
      line-height: r(21);
      margin-bottom: r(5);
    }

    & > p:nth-of-type(2) {
      color: #9b9b9b;
      font-size: r(14);
      line-height: r(21);
      font-weight: 400;
    }

    .a {
      margin-top: r(15);
      @include display-flex;
      @include align-items(center);
      @include justify-content(space-between);

      .price {
        color: #ff5050;
        font-size: r(21);
        font-weight: 600;

        span {
          font-size: r(14);
        }
      }

      .control_number {
        @include display-flex;
        @include align-items(center);

        .decrease,
        .increase {
          @include display-flex;
          @include align-items(center);
          @include justify-content(center);

          width: r(27);
          height: r(27);
          border-radius: r(5);
          border: r(1) solid rgba($color: #000, $alpha: 0.3);
          color: rgba($color: #000, $alpha: 0.3);
          font-size: r(25);
          // text-align: center;
          // line-height: r(21);
        }

        .increase {
          border: r(1) solid var(--theme-primary);
          color: var(--theme-primary);
        }

        .amount {
          font-size: r(18);
          color: #333;
          padding: 0 r(15);
        }
      }
    }
  }

  .service {
    width: 100%;
    height: r(50);
    background: #fff;
    margin: r(10) 0;
    color: #333;
    font-size: r(14);
    line-height: r(50);
    padding: 0 r(15);

    span {
      color: #9b9b9b;
      margin-right: r(10);
    }
  }

  .detail {
    background: #fff;
    padding: r(20) r(14);
    color: #333;
    font-size: r(14);
    margin-bottom: r(10);

    .title {
      @include display-flex;
      @include align-items(center);
      @include justify-content(space-between);

      font-size: r(16);
      font-weight: 600;
      border-bottom: r(1) solid #e6e6e6;
      padding-bottom: r(15);
      margin-bottom: r(14);

      .see_all {
        color: var(--theme-success);
        font-size: r(15);
        font-weight: 400;

        .icon {
          color: var(--theme-success);
          width: r(16);
          height: r(15);
        }
      }
    }

    .text_item {
      @include display-flex;
      @include align-items(center);

      font-size: r(13);
      min-height: r(26);
      line-height: r(20);
      padding-top: r(3);

      & > p:first-of-type {
        color: #9b9b9b;
        width: r(60);
      }

      & > p:last-of-type {
        @include flex;
      }
    }
  }

  .picture_list {
    background: #fff;
    padding: r(20) r(15);
    color: #333;
    font-size: r(14);
    margin-bottom: r(10);

    .title {
      @include display-flex;
      @include align-items(center);
      @include justify-content(space-between);

      font-size: r(16);
      font-weight: 600;
      padding-bottom: r(15);
    }
  }

  .warning {
    background: #f0f0f0;
    padding: r(10) r(15);

    .title {
      font-size: r(16);
      font-weight: 600;
      color: #666;
    }

    .text {
      font-size: r(14);
      color: #9b9b9b;
      line-height: r(20);
    }
  }

  .bar {
    position: fixed;
    bottom: 0;
    left: 0;
    height: r(60);
    width: 100%;
    background: #fff;
    box-shadow: 0 r(-1) r(2) #e6e6e6;
    z-index: 1001;
    @include display-flex;
    @include align-items(center);
    @include justify-content(space-between);

    &.z_index {
      z-index: inherit;
    }

    .cart {
      @include display-flex;
      @include align-items(center);

      background: #fff;
      position: relative;
      flex-direction: column;
      color: #9b9b9b;
      font-size: r(12);
      width: r(73);
      margin: r(10) 0;
      padding: r(5) 0;
      border-right: r(1) solid #d8d8d8;

      .pot {
        position: absolute;
        min-width: r(13);
        height: r(11.5);
        padding: 0 r(2);
        background: #ff5050;
        border-radius: r(6);
        line-height: r(11.5);
        text-align: center;
        color: #fff;
        font-size: r(9);
        top: r(1);
        right: r(15);
      }

      .icon {
        width: r(22);
        height: r(22);
        margin-bottom: r(5);
      }
    }

    .price {
      color: #ff5050;
      font-size: r(21);
      font-weight: 600;
      padding: 0 r(15);
    }

    .ok {
      width: r(133);
      height: r(60);
      line-height: r(60);
      text-align: center;
      background: var(--theme-success);
      color: #fff;
      font-size: r(16);
    }
  }
}

.cart_popup {
  width: 100%;
  background: #fff;
  padding-bottom: r(60);

  .inner {
    padding: r(15);

    .head {
      @include display-flex;
      @include align-items(center);
      @include justify-content(space-between);

      & > p:first-of-type {
        color: #333;
        font-size: r(16);
        font-weight: 600;
      }

      & > p:last-of-type {
        color: #9b9b9b;
        font-size: r(13);
        @include display-flex;
        @include align-items(center);

        .icon {
          width: r(12);
          height: r(12);
          margin-right: r(3);
        }
      }
    }

    .goods_wrapper {
      overflow: scroll;
      max-height: r(270);
      width: 100%;
      -webkit-overflow-scrolling: touch;

      .good {
        // margin: r(15) 0;
        height: r(90);
        width: 100%;
        @include display-flex;
        @include align-items(center);
        @include justify-content(space-between);

        .photo {
          width: r(80);
          height: r(80);
          border-radius: r(5);
          margin-right: r(15);
        }

        .info {
          @include flex;

          & > p:first-of-type {
            color: #464646;
            font-size: r(15);
            @include line(2);
          }

          & > p:last-of-type {
            color: #ff5050;
            font-size: r(15);
            margin-top: r(5);
          }
        }

        .control_number {
          @include display-flex;
          @include align-items(center);

          .decrease,
          .increase {
            width: r(27);
            height: r(27);
            border-radius: r(5);
            border: r(1) solid rgba($color: #000, $alpha: 0.3);
            color: rgba($color: #000, $alpha: 0.3);
            font-size: r(25);
            @include display-flex;
            @include align-items(center);
            @include justify-content(center);
          }

          .increase {
            border: r(1) solid var(--theme-primary);
            color: var(--theme-primary);
          }

          .amount {
            font-size: r(18);
            color: #333;
            padding: 0 r(15);
          }
        }
      }
    }
  }

  .express_tips {
    width: 100%;
    height: r(40);
    line-height: r(40);
    text-align: center;
    font-size: r(13);
    background: #fff7cb;
    color: #ff8300;
  }
}

.drug_detail_popup {
  width: 100%;
  height: r(450);
  background: #fff;
  padding: r(15) 0;
  position: relative;

  .title {
    color: #333;
    font-weight: 600;
    font-size: r(16);
    padding: 0 r(15) r(15);
  }

  .inner {
    padding: 0 r(15) r(75);
    overflow: scroll;
    height: r(400);
    width: 100%;
    -webkit-overflow-scrolling: touch;

    .text_item {
      @include display-flex;
      @include align-items(center);

      font-size: r(13);
      min-height: r(40);
      line-height: r(20);
      padding: r(6) 0;
      border-bottom: r(1) solid #ececec;
      // box-shadow: 0 r(-1) r(-1) 0 #ececec;

      & > p:first-of-type {
        color: #9b9b9b;
        width: r(100);
      }

      & > p:last-of-type {
        @include flex;
      }
    }
  }
}
