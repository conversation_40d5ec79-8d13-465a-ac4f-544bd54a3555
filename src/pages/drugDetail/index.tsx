import classnames from 'classnames';
import React, { useState, useCallback, useMemo, useEffect } from 'react';
import { shallowEqual, useSelector, useDispatch } from 'react-redux';
import { StaticToast, Card, FixedButton, SvgIcon } from 'src/components/common';
import { ApplicationState } from 'src/store';
import { modify_shoppingCart, edit_shoppingCart } from 'src/store/prescription/action';
import './drugdetail.scss';

import arrow_svg from 'src/svgs/sprite-arrow-right.svg';
import cart_svg from 'src/svgs/sprite-cart.svg';
import delete_svg from 'src/svgs/sprite-delete.svg';
import { Carousel, Popup } from 'zarm';

const NY = {
  N: '否',
  Y: '是',
};

const DrugDetail = (props) => {
  const { location: { state = {} } } = props;
  const [drugId, setDrugId] = useState(state && state.id || '');
  const [shoppingCartShow, setShoppingCartShow] = useState(false);
  const [drugDetailShow, setDrugDetailShow] = useState(false);

  const { medicineInfo, shoppingInfo, shoppingCart } = useSelector((state: ApplicationState) => ({
    medicineInfo: state.prescription.detail.prescriptionMedicineList.filter((element) => element.drug.id === drugId)[0] || {},
    shoppingInfo: state.prescription.shoppingCart.orderGoods.filter((element) => element.goodsId === drugId)[0] || {},
    shoppingCart: state.prescription.shoppingCart || {},
  }), shallowEqual);

  const { DRUGTYPE_OBJ }: any = useSelector((state: ApplicationState) => ({
    DRUGTYPE_OBJ: state.dictionary.DRUGTYPE_OBJ,
  }));

  const dispatch = useDispatch();
  const modifyShoppingCart = (key: string, value: any) => dispatch(modify_shoppingCart(key, value));
  const editShoppingCart = (key: string, value: any) => dispatch(edit_shoppingCart(key, value));

  useEffect(() => {
    setDrugId(state && state.id);
  }, [state]);

  const drugsBannerList = useMemo(() => {
    if (medicineInfo.drug.drugsPictureList && medicineInfo.drug.drugsPictureList.length) {
      return medicineInfo.drug.drugsPictureList.slice(0, 2);
    }
    return [];
  }, [medicineInfo]);

  const totalNums = useMemo(() => {
    const nums = shoppingCart.orderGoods && shoppingCart.orderGoods.length ? shoppingCart.orderGoods.reduce((acc, cur) => acc + Number(cur.goodsNum), 0) : 0;
    return nums;
  }, [shoppingCart]);

  const increaseClick = useCallback((drugId, drugNumber, drugPrice, drugMaxNumber) => {
    if (parseInt(drugNumber) === parseInt(drugMaxNumber)) {
      StaticToast.warning('不可超过处方单的购药数量限制');
      return;
    }
    modifyShoppingCart(drugId, drugNumber + 1);
    const orderAmount = (Number(shoppingCart.orderAmount) + Number(drugPrice)).toFixed(2);
    editShoppingCart('orderAmount', orderAmount);
  }, [shoppingCart, shoppingInfo, drugId]);

  const decreaseClick = useCallback((drugId, drugNumber, drugPrice) => {
    if (drugNumber === 0) {
      return;
    }
    modifyShoppingCart(drugId, drugNumber - 1);
    const orderAmount = (Number(shoppingCart.orderAmount) - Number(drugPrice)).toFixed(2);
    editShoppingCart('orderAmount', orderAmount);
  }, [shoppingCart, shoppingInfo, drugId]);


  const clearAllGoods = useCallback(() => {
    shoppingCart.orderGoods.map((element) => {
      modifyShoppingCart(element.goodsId, 0);
    });
    editShoppingCart('orderAmount', 0);
  }, [shoppingCart, shoppingInfo, drugId]);

  const drugClick = useCallback((id) => {
    setShoppingCartShow(false);

    props.history.replace({
      pathname: '/hospital/drugdetail',
      state: {
        id,
      },
    });
  }, []);

  const toShoppingCart = useCallback(() => {
    if (totalNums === 0) {
      StaticToast.warning('您的购物车是空的哦');
      return;
    }

    props.history.push({
      pathname: '/hospital/shoppingcart',
      search: 'initAddress=true',
    });
  }, [totalNums]);

  const { drug: {
    drugName = '',
    drugCommonName = '',
    indication = '',
    drugBrandName = '',
    drugSellingPrice = '',
    drugSpecifications = '',
    drugsPictureList = [],
    drugManufactorName = '',
    drugsCharacter = '',
    drugDosageForm = '',
    drugUsageDosage = '',
    drugType = '',
    intendedFor = '',
    functionIndications = '',
    adverseReactions = '',
    taboo = '',
    note = '',
    isContainingHemp = '',
    isWhetherAbortion = '',
    remark = '',
  } = {} } = medicineInfo;
  const { goodsNum = 0, goodsRealPrice = 0, goodsMaxNumber = 0 } = shoppingInfo;

  return <div className='drugdetail_page'>
    <Carousel autoPlay loop direction='left' className='carousel' showPagination={true} animationDuration={500}>
      {drugsBannerList.map((item) => <div className='carousel__item__pic' key={item.id}>
        <img src={`${item.attachment.attachmentDownloadUrl}`} alt='' draggable={false} />
      </div>)}
    </Carousel>
    <div className='base'>
      <p>{drugName || drugCommonName}</p>
      <p>{indication}</p>
      <div className='a'><p className='price'><span>¥</span>{Number(drugSellingPrice).toFixed(2)}</p>
        <div className='control_number'>
          <div className='decrease' onClick={() => decreaseClick(drugId, goodsNum, goodsRealPrice)}>-</div>
          <div className='amount'>{shoppingInfo.goodsNum}</div>
          <div className='increase' onClick={() => increaseClick(drugId, goodsNum, goodsRealPrice, goodsMaxNumber)}>+</div>
        </div>
      </div>
    </div>

    <div className='service'><span>服务&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>100%正品·隐私服务·认证药店</div>

    <div className='detail'>
      <div className='title'>
        <p>产品参数</p>
        <p className='see_all' onClick={() => setDrugDetailShow(true)}>查看全部
          <SvgIcon className='icon' src={arrow_svg} />
        </p>
      </div>
      <div className='text_item'>
        <p>适用症</p>
        <p>{indication || '暂无相关信息'}</p>
      </div>
      <div className='text_item'>
        <p>品牌</p>
        <p>{drugBrandName || '暂无相关信息'}</p>
      </div>
      <div className='text_item'>
        <p>规格</p>
        <p>{drugSpecifications || '暂无相关信息'}</p>
      </div>
    </div>

    <div className='picture_list'>
      <p className='title'>商品详情</p>
      {drugsPictureList.map((pic) => <img key={pic.id} src={pic.attachment && `${pic.attachment.attachmentDownloadUrl}` || ''} alt='' />)}
      <Card prefixCls='warning'>
        <p className='title'>注意事项</p>
        <p className='text'>请自诩阅读药瓶说明书注意事项、禁忌内容，按说明书或在药师指导下购买和使用，将药品置于儿童无法触及的位置。</p>
      </Card>
    </div>

    <div className={classnames('bar', { z_index: drugDetailShow })}>
      <div className='cart' onClick={() => setShoppingCartShow(!shoppingCartShow)}>
        <SvgIcon className='icon' src={cart_svg} />
        <div className='pot'>{totalNums}</div>
        <p>购药清单</p>
      </div>
      <div className='price'>
        <p><span>¥</span>{Number(shoppingCart.orderAmount).toFixed(2)}</p>
      </div>
      <div className='ok' onClick={() => toShoppingCart()}>选好了</div>
    </div>

    <Popup visible={shoppingCartShow} onMaskClick={() => setShoppingCartShow(false)} direction='bottom' >
      <div className='cart_popup'>
        <div className='inner'>
          <div className='head'>
            <p>已选商品</p>
            <p onClick={() => clearAllGoods()}><SvgIcon className='icon' src={delete_svg} />清空全部</p>
          </div>
          <div className='goods_wrapper'>
            {shoppingCart.orderGoods.map((item) => item.goodsNum ? <div className='good' key={item.goodsId}>
              <img className='photo' src={item.goodsPicture.length && item.goodsPicture[0].attachment && `${item.goodsPicture[0].attachment.attachmentDownloadUrl}` || ''} onClick={() => drugClick(item.goodsId)} />
              <div className='info' onClick={() => drugClick(item.goodsId)}>
                <p>{item.goodsName}</p>
                <p>¥{item.goodsRealPrice}</p>
              </div>
              <div className='control_number'>
                <div className='decrease' onClick={() => decreaseClick(item.goodsId, item.goodsNum, item.goodsRealPrice)}>-</div>
                <div className='amount'>{item.goodsNum}</div>
                <div className='increase' onClick={() => increaseClick(item.goodsId, item.goodsNum, item.goodsRealPrice, item.goodsMaxNumber)}>+</div>
              </div>
            </div> : null)}
          </div>
        </div>
        <div className='express_tips'>满¥88包邮，免¥6邮费</div>
      </div>
    </Popup>

    <Popup visible={drugDetailShow} onMaskClick={() => setDrugDetailShow(false)} direction='bottom' >
      <div className='drug_detail_popup'>
        <p className='title'>产品参数</p>
        <div className='inner'>
          <div className='text_item'>
            <p>药品名称</p>
            <p>{drugName}</p>
          </div>
          <div className='text_item'>
            <p>药品通用名称</p>
            <p>{drugCommonName}</p>
          </div>
          <div className='text_item'>
            <p>适用症</p>
            <p>{indication || '暂无相关信息'}</p>
          </div>
          <div className='text_item'>
            <p>品牌</p>
            <p>{drugBrandName || '暂无相关信息'}</p>
          </div>
          <div className='text_item'>
            <p>规格</p>
            <p>{drugSpecifications || '暂无相关信息'}</p>
          </div>
          <div className='text_item'>
            <p>生产企业</p>
            <p>{drugManufactorName}</p>
          </div>
          <div className='text_item'>
            <p>性状</p>
            <p>{drugsCharacter}</p>
          </div>
          <div className='text_item'>
            <p>剂型</p>
            <p>{drugDosageForm}</p>
          </div>
          <div className='text_item'>
            <p>用法用量</p>
            <p>{drugUsageDosage}</p>
          </div>
          <div className='text_item'>
            <p>药品类型</p>
            <p>{DRUGTYPE_OBJ[drugType]}</p>
          </div>
          <div className='text_item'>
            <p>适用人群</p>
            <p>{intendedFor}</p>
          </div>
          <div className='text_item'>
            <p>功能主治</p>
            <p>{functionIndications}</p>
          </div>
          <div className='text_item'>
            <p>不良反应</p>
            <p>{adverseReactions}</p>
          </div>
          <div className='text_item'>
            <p>禁忌</p>
            <p>{taboo}</p>
          </div>
          <div className='text_item'>
            <p>注意事项</p>
            <p>{note}</p>
          </div>
          <div className='text_item'>
            <p>是否含麻</p>
            <p>{NY[isContainingHemp]}</p>
          </div>
          <div className='text_item'>
            <p>是否打胎</p>
            <p>{NY[isWhetherAbortion]}</p>
          </div>
          <div className='text_item'>
            <p>备注信息</p>
            <p>{remark}</p>
          </div>
        </div>
        <FixedButton prefixCls='fix_button' buttonClick={() => setDrugDetailShow(false)} text='关闭' />
      </div>
    </Popup>
  </div>;
};


export default DrugDetail;
