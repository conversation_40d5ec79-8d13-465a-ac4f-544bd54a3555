@import "src/style/index";

body {
  background: #f8f8f8;
}

.electronicSignature-page {
  position: relative;
  height: 100%;
  orientation: portrait;
  overflow: hidden;

  .canvas {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 80%;
    z-index: 3;
  }

  .canvas-send {
    position: absolute;
    top: 0;
    left: 0;
    z-index: 2;
    // width: 70%;
    width: r(600);
    height: r(160);
    // height: 40%;
    opacity: 0;
  }

  .canvas-regular {
    position: absolute;
    top: 0;
    left: 0;
    z-index: 2;
    // width: 70%;
    // height: 40%;
    width: r(130);
    height: r(70);
    opacity: 0;
  }

  .canvas-cover {
    width: 100%;
    height: 80%;
    background: #fff;
    border: r(1) solid #f2f2f2;
    color: #ddd;
    z-index: 1;

    .title {
      margin: 0 auto;
      text-align: center;
      width: r(200);
      height: r(26);
      background: #f2f2f2;
      border-radius: r(5);
      color: #aaa;
      line-height: r(26);
    }

    .desc {
      position: absolute;
      background: #fff;
      transform: translateX(-50%);
      height: r(80);
      line-height: r(80);
      top: 30%;
      left: 50%;
      font-size: r(20);
      z-index: 1;
    }
  }

  .btn-group {
    // margin: r(15) 0;
    height: 20%;
    width: 100%;
    @include display-flex;
    @include align-items(center);
    @include justify-content(space-around);

    .btn {
      width: r(120);
    }
  }
}

.sign_popup_node {

  // transform: rotate(90deg);
  .za-modal {
    // transform: rotate(90deg);
  }

  .za-popup {
    width: r(260) !important;

    .za-modal__body {
      padding: 0;
    }

    .text {
      padding: r(20);
      text-align: center;
      border-bottom: r(1) solid #e6e6e6;
    }

    .cancel_button {
      border: none;
      width: 50%;
      height: r(60);
      border-right: r(1) solid #e6e6e6;
      border-radius: 0;
    }

    .confirm_button {
      border: none;
      width: 50%;
      height: r(60);
      border-radius: 0;
      color: #44cda7;
    }
  }
}
