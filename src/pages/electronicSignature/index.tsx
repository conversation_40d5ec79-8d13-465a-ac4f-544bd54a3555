import React, { Component } from 'react';
import { StaticToast } from 'src/components/common';
import { fetchJson } from 'src/utils/fetch';
import format from 'src/utils/format';
import Validate from 'src/utils/validate';
import { Button, Modal } from 'zarm';
import Draw from './draw';
import './electronicSignature.scss';


declare global {
  interface Window {
    WeixinJSBridge: any;
    wx: any;
  }
}

interface PropsFromState {

}


interface PropsFromDispatch {

}

interface ElectronicSignatureState {
  accessToken: string;
  userName: string;
  wrapperStyle: React.CSSProperties;
  degree: number;
  png: string;
  modalVisible: boolean;
}

type AllProps = PropsFromDispatch & PropsFromState;


class ElectronicSignature extends Component<AllProps, ElectronicSignatureState>{
  private draw: any;
  private drawSend: any;
  private drawRegular: any;
  popupNode: any = React.createRef();
  constructor(props: AllProps) {
    super(props);
    this.state = {
      accessToken: format.GetQueryString('accessToken') || '',
      userName: decodeURIComponent(format.GetQueryString('userName') || ''),
      degree: 90,
      png: '',
      wrapperStyle: {},
      modalVisible: false,
    };
  }
  componentDidMount() {
    this.getHorizontalStyle();
  }
  initCanvas = () => {
    const { degree } = this.state;
    this.draw = new Draw(document.querySelector('#canvas'), -degree);
    this.drawSend = new Draw(document.querySelector('#canvas-send'), -0);
    this.drawRegular = new Draw(document.querySelector('#canvas-regular'), -0);
  };
  clear = () => {
    this.draw.clear();
    this.drawSend.clear();
    this.drawRegular.clear();
  };
  getHorizontalStyle() {
    const { degree } = this.state;
    const d = document;
    const w = window.innerWidth || d.documentElement.clientWidth || d.body.clientWidth;
    const h = window.innerHeight || d.documentElement.clientHeight || d.body.clientHeight;
    const length = (h - w) / 2;
    const width = h;
    const height = w;
    this.draw && this.draw.removeChild(document.querySelector('#canvas'));
    this.draw && this.draw.appendChild(document.createElement('#canvas'));
    this.drawSend && this.drawSend.removeChild(document.querySelector('#canvas-send'));
    this.drawSend && this.drawSend.appendChild(document.createElement('#canvas-send'));
    this.drawRegular && this.drawRegular.removeChild(document.querySelector('#canvas-regular'));
    this.drawRegular && this.drawRegular.appendChild(document.createElement('#canvas-regular'));
    setTimeout(() => {
      this.initCanvas();
    }, 200);
    this.setState({
      wrapperStyle: {
        transform: `rotate(${degree}deg) translate(${length}px,${length}px)`,
        width: `${width}px`,
        height: `${height}px`,
        transformOrigin: 'center center',
      },
    });
  }
  getPNGImage = () => {
    // const { userName } = this.state
    this.drawSend.drawImage(this.draw.canvas, {
      dx: 60,
      dy: 18,
      scale: 0.8,
    });
    this.drawSend.drawOutLine(20);
    // this.drawRegular.drawText(userName)
    this.drawRegular.drawImage(this.draw.canvas, {
      dx: 9.5,
      dy: 5.5,
      scale: 0.85,
    });
    this.drawRegular.drawOutLine(5);
    const png = this.drawSend.getPNGImage();
    const regularPng = this.drawRegular.getPNGImage();
    this.setState({
      png: regularPng,
    });
    this.setState({
      modalVisible: false,
    });
    this.uploadImage(png, regularPng);
  };
  uploadImage = (png, regularPng) => {
    const { accessToken, userName } = this.state;
    if (!accessToken || !userName) {
      StaticToast.warning('缺少参数，请重新扫描二维码');
      return;
    }
    const blob = this.drawSend.dataURLtoBlob(png);
    const regularBlob = this.drawRegular.dataURLtoBlob(regularPng);
    const formData = new FormData();
    formData.append('accessToken', accessToken);
    formData.append('writeFiles', blob, `signedPhotoForWrite_${accessToken}.png`);
    formData.append('regularFiles', regularBlob, `signedPhoto_${accessToken}.png`);
    fetchJson({
      type: 'upload',
      url: '/api/api/v1/doctor/elecSign/userSign',
      data: formData,
      needLogin: false,
      success: (res: any) => {
        if (res && res.code === '0') {
          this.clear();
          StaticToast.success('提交电子签名成功');
          setTimeout(() => {
            if (Validate.isFromWeixin()) {
              window.wx.closeWindow();
            } else {
              StaticToast.warning('非微信环境，请手动关闭退出～');
            }
          }, 2000);
        } else {
          const { message = '请求失败' } = res;
          StaticToast.warning(message);
        }
      },
    });
  };
  render() {
    const { wrapperStyle, modalVisible } = this.state;
    return (
      <>
        <div className='electronicSignature-page' style={wrapperStyle}>
          <canvas className='canvas' id='canvas'></canvas>
          <canvas className='canvas-send' id='canvas-send'></canvas>
          <canvas className='canvas-regular' id='canvas-regular'></canvas>
          <div className='canvas-cover'>
            <div className='title'>签名请书写清晰</div>
            <div className='desc'>手写区域，请横屏签名</div>
          </div>
          <div className='btn-group'>
            {/*  */}
            <Button className='btn' onClick={this.clear}>重置</Button><Button className='btn' theme='primary' onClick={() => this.setState({ modalVisible: true })}>提交</Button>
          </div>
          <Modal
            className='sign_popup'
            visible={modalVisible}
            maskClosable
            onCancel={() => this.setState({ modalVisible: false })}
            mountContainer={() => this.popupNode.current}
            title='提示'
          >

            <div className='text'>是否确认提交?</div>
            <Button className='cancel_button' onClick={() => this.setState({ modalVisible: false })}>取消</Button>
            <Button className='confirm_button' onClick={this.getPNGImage}>确认</Button>
          </Modal>


          {/* <img src={png} alt="" /> */}
        </div>

        <div
          className='sign_popup_node'
          // style={{ position: 'relative', zIndex: 10 }}
          ref={this.popupNode}
        />
      </>
    );
  }
}

export default (ElectronicSignature);
