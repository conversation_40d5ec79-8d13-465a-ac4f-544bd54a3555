/*
 * @description： 企微 服务 中间页
 */
import React, { useEffect, useState } from 'react';
import { StaticToast, } from 'src/components/common';
import { fetchJson } from 'src/utils/fetch';
import { Deserialize } from 'src/utils/serialization';
import { Modal } from 'zarm';
import { SingleServiceCard } from 'src/pages/serviceCardList/index';

import './bridge.scss';
const prefixCls = 'bridges-pages';


const Bridges = (props) => {
  const { location: { search = '' } } = props;
  const { cardNo = '' } = Deserialize(search);
  const [result, setResult] = useState({ id: '' });
  //撤销服务
  const revoke = () => {
    Modal.confirm({
      title: '确认信息',
      content: '确定取消该服务',
      onCancel: () => { },
      onOk: () => {
        fetchJson({
          type: 'POST',
          url: '/api/api/v1/patient/medical/service/package/revoke',
          data: { cardNo },
          isloading: true,
        }).then(res => {
          if (res && res.code === '0') {
            fetchServiceCard();
            StaticToast.success('取消服务成功');
          }
        });
      }
    });
  }
  const fetchServiceCard = () => {
    fetchJson({
      type: 'POST',
      url: '/api/api/v1/patient/medical/service/package/queryCardUseInfo',
      data: {
        cardNo,
        option: {
          needQueryServiceItemsExtInfo: true,
          needQueryPatient: true,
          needAttachment: true,
          needQueryCardUse: true,
          needQueryItem: true,
          needQueryCardUseApprovalRecord: true,
        }
      },
      isloading: true,
      success: (res) => {
        if (res && res.code === '0') {
          let { result = {} } = res;
          setResult(result);
        } else {
          StaticToast.error(res.message);
        }
      },
    });
  }
  useEffect(() => {
    fetchServiceCard();
  }, [])
  return (
    <div className={prefixCls}>
      {result.id && <SingleServiceCard item={result} revoke={revoke}{...props} />}
    </div>
  )
}
export default Bridges;
