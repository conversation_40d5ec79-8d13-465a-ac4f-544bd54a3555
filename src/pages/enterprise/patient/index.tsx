/*
 * @description： 企微用户-患者选择
 */
import React, { useEffect, useState } from 'react';
import { ChoosePatient } from 'src/pages/chatMedicalManage/components/chatLog';
// import { shallowEqual, useSelector, useDispatch } from 'react-redux';
import cookies from 'src/utils/cookie';
import { Deserialize } from 'src/utils/serialization';
import { StaticToast, } from 'src/components/common';
import { fetchJson } from 'src/utils/fetch';
import './patient.scss';

const prefixCls = 'patients-pages';

const RealName = (props) => {
  const { location: { search = '' } } = props;
  const { id = '' } = Deserialize(search);
  let [selectPatientInfo, setSelectPatientInfo] = useState<{ patient_id?: number | string, patient_name?: string, patient_code?: string, isSelected: boolean, consultId: number | string }>({ patient_id: '', patient_name: '', patient_code: '', isSelected: false, consultId: '' });
  const queryIsHasSelected = () => {
    if (!id) {
      StaticToast.error('缺少消息id');
      return
    };
    fetchJson({
      type: 'GET',
      url: '/api/api/v1/patient/workwx/isSelected',
      data: {
        messageId: id
      },
      isloading: false,
    }).then((res) => {
      const { code = '', result = {} } = res;
      if (code === '0') {
        setSelectPatientInfo({
          consultId: result.imConsultId,
          patient_id: result.patientId,
          patient_name: result.patientName,
          isSelected: result.isSelected
        });
      } else {
        StaticToast.error(res.message);
      }
    });

  }
  const sendPatient = (msg) => {
    let { reply_content: { final = {} } = {} } = msg;
    fetchJson({
      type: 'POST',
      url: '/api/api/v1/patient/workwx/selectPatient',
      data: {
        messageId: id,
        channelResourceCode: cookies.get('channelResourceCode'),
        patientId: final.patient_id,
        patientName: final.patient_name,
      },
      isloading: false,
    }).then((res) => {
      const { code = '', result = {} } = res;
      if (code === '0') {
        setSelectPatientInfo(prevState => ({
          ...prevState,
          patient_id: final.patient_id,
          patient_name: final.patient_name,
          isSelected: result.isSelected
        }));
      } else {
        StaticToast.error(res.message);
      }
    });
  }

  useEffect(() => {
    queryIsHasSelected();
  }, [])

  return (
    <div className={`${prefixCls} chat_log_comp`}>
      <ChoosePatient msgInfo={{ reply_content: { final: selectPatientInfo } }} sendPatient={sendPatient} consultInfo={{ consultId: selectPatientInfo.consultId }} />
    </div>
  )
}
export default RealName;
