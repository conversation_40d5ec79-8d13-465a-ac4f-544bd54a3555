/*
 * @description： 企微用户实名
 */
import React, { useEffect, useState } from 'react';
import { Auth } from 'src/pages/chatMedicalManage/components';
import { StaticToast, } from 'src/components/common';
import { fetchJson } from 'src/utils/fetch';
import { Deserialize } from 'src/utils/serialization';
import { fetch_consult_detail } from 'src/store/chatmedicalmanage/action';
import { shallowEqual, useSelector, useDispatch } from 'react-redux';
import { ApplicationState } from 'src/store';
import './realname.scss';

const prefixCls = 'real-pages';

const RealName = (props) => {
  const { location: { search = '' } } = props;
  const dispatch = useDispatch();
  const { consultNo = '', } = Deserialize(search);
  let [realAuthInfo, setRealAuthInfo] = useState({ patient_id: '', patient_name: '', patient_code: '' });
  const consultDetail = useSelector((state: ApplicationState) => {
    let { consultDetail } = state.chat;
    return consultDetail;
  }, shallowEqual);

  const getRealAuth = () => {
    fetchJson({
      type: 'POST',
      url: '/api/api/v1/patient/patient/list',
      data: {
        patientRelation: 1,
      },
      isloading: false,
    }).then((res) => {
      const { code = '', result = [] } = res;
      if (code === '0') {
        let [item = {}] = result;
        item.patientCertNo && setRealAuthInfo({
          patient_id: item.id,
          patient_name: item.patientName,
          patient_code: item.patientCertNo
        });
      } else {
        StaticToast.error(res.message);
      }
    });

  }

  useEffect(() => {
    dispatch(fetch_consult_detail({ consultNo }))
    getRealAuth();
  }, [])
  return (
    <div className={prefixCls}>
      <Auth msgInfo={{ reply_content: { final: realAuthInfo } }} sendPatient={() => getRealAuth()} consultInfo={{ consultId: consultDetail.id || '' }} />
    </div>
  )
}
export default RealName;
