import React, { useEffect } from 'react';
import { StaticToast } from 'src/components/common';
import { Deserialize, fetchJson, jumpBeforeAuth } from 'src/utils';

const RecommendDetailPage = props => {
    const { location: { search = '' } } = props;
    const { recommendNo } = Deserialize(search);
    useEffect(() => {
        if(!recommendNo){
            StaticToast.error('缺少推荐商品号');
            return ;
        }
        fetchJson({
            url: '/api/api/v1/patient/commodity/recommend/queryRecommendDetailByRecommendNo',
            type: 'POST',
            data: {
                recommendNo: recommendNo
            },
            isloading: true,
        }).then(res => {
            if (res.code === '0' && res.result && res.result.length > 0) {
                const item = res.result[0];
                jumpBeforeAuth({
                    partnerCode: item.sourceCode === 'za_haoyaoshi' ? 'ZA_HAOYAOSHI' : 'ZA_MALL',
                    businessType: 'commodityRecommend',
                    bizNo: item.commodityId,
                    relatedBizNo: recommendNo
                })
            }else{
                StaticToast.error('查找推荐商品失败')
            }
        })
    }, [])
    return (
        <div className='recommend-detail-page'>
            <div>

            </div>
        </div>
    )
}
export default RecommendDetailPage