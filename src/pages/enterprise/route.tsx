/*
 * @date :2021-03-31
 * @description：视频问诊模块路由
 */
const prefix = '/hospital/enterprise/';
const route = [
  {
    path: `${prefix}realname`, // 实名=realname
    name: 'realName',
    component: () => import(/* webpackPrefetchPlaceHolder */ './realname'),
    auth: true,
    exact: true,
    realAuth: false,
    title: '实名认证',
  },
  {
    path: `${prefix}patient-select`, // 患者选择=patient-select
    name: 'patientSelect',
    component: () => import(/* webpackPrefetchPlaceHolder */ './patient'),
    auth: true,
    exact: true,
    realAuth: true,
    title: '选择权益使用人',
  },
  {
    path: `${prefix}bridge`, // 服务卡中间过渡页
    name: 'Bridge',
    component: () => import(/* webpackPrefetchPlaceHolder */ './bridge'),
    auth: true,
    exact: true,
    realAuth: true,
    title: '患者服务卡',
  },
  {
    path: `${prefix}temp`, // 服务卡中间过渡页
    name: 'Temp',
    component: () => import(/* webpackPrefetchPlaceHolder */ './temp'),
    auth: false,
    exact: true,
    realAuth: false,
    title: '中间过渡页',
  },
  {
    path: `${prefix}recommend/detail`, // 企微推荐商品
    name: 'recommendDetail',
    component: () => import(/* webpackPrefetchPlaceHolder */ './recommend/detail'),
    auth: false,
    exact: true,
    realAuth: false,
    title: '众安互联网医院',
  },
];

export default route;
