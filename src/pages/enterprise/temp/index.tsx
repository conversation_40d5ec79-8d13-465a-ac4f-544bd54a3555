/*
 * @description： 企微用户实名
 */
import React, { useCallback, useState } from 'react';
// import { Deserialize } from 'src/utils/serialization';
import { Input, Cell, Button } from 'zarm';
import './realname.scss';

const prefixCls = 'real-pages';

const Test = (props) => {
  // const { location: { search = '' } } = props;
  const [cardNo, setCardNo] = useState('');
  const [id, setMessageId] = useState('');
  const [consultNo, setConsultNo] = useState('');
  const [channelContractNo, setChannelContractNo] = useState('');
  const [channelInquiryNo, setChannelInquiryNo] = useState('');
  const goNext = useCallback(() => {
    if (cardNo) {
      window.location.replace(`/hospital/enterprise/bridge?cardNo=${cardNo}`);

      // window.reactHistory.replace({
      //   pathname: "/hospital/enterprise/bridge",
      //   search: `cardNo=${cardNo}`
      // })
      return;
    }
    if (id) {
      window.location.replace(`/hospital/enterprise/patient-select?id=${id}`);
      // window.reactHistory.replace({
      //   pathname: "/hospital/enterprise/patient-select",
      //   search: `id = ${id} `
      // })
      return;
    }
    if (consultNo) {
      window.location.replace(`/hospital/enterprise/realname?consultNo=${consultNo}`);

      // window.reactHistory.replace({
      //   pathname: "/hospital/enterprise/realname",
      //   search: `consultNo = ${consultNo} `
      // })
      return;
    }
    return;
  }, [consultNo, id, cardNo]);
  const goNextAlipay = useCallback(
    (scenes = '') => {
      let path = encodeURIComponent(`/pages/bridge/index?antSerContractNo=${channelContractNo}&antSerUseNo=${channelInquiryNo}`);
      let startup = 'query=channelResourceCode%3DANT_INS_OUTPATIENT_CLAIM';
      if (scenes == 2) {
        startup = 'channelResourceCode=ANT_INS_OUTPATIENT_CLAIM';
      }
      window.location.href = `https://ds.alipay.com/?scheme=${encodeURIComponent(`alipays://platformapi/startapp?appId=2021002124617095&page=${path}&${startup}`)}`;
      return;

    },

    [channelContractNo, channelInquiryNo],
  );
  const goNextThirdPartyMini = useCallback(() => {
    const thirdPartyMiniUrl = `alipays://platformapi/startapp?appClearTop=false&startMultApp=YES&appId=68687811&url=https%3A%2F%2Frenderpre.alipay.com%2Fp%2Fyuyan%2F180020010001198920%2Fmedical-detail-bridge.html%3FserContractNo%3D${channelContractNo}`;
    // window.location.href = `https://ds.alipay.com/?scheme=${encodeURIComponent(`${thirdPartyMiniUrl}`)}`;
    my.ap.navigateToAlipayPage({
      path: thirdPartyMiniUrl,
      success: (res) => {
        my.alert({ content: '系统信息' + JSON.stringify(res) });
      },
      fail: (error) => {
        my.alert({ content: '系统信息' + JSON.stringify(error) });
      },
    });
    return;
  }, [channelContractNo, channelInquiryNo]);
  const goNextThirdPartyMini2 = useCallback(() => {
    const thirdPartyMiniUrl = decodeURIComponent(`https%3A%2F%2Frenderpre.alipay.com%2Fp%2Fyuyan%2F180020010001198920%2Fmedical-detail-bridge.html%3FserContractNo%3D${channelContractNo}`);
    console.log(thirdPartyMiniUrl, 'thirdPartyMiniUrl--');
    window.location.href = thirdPartyMiniUrl;
    return;
  }, [channelContractNo, channelInquiryNo]);
  const goNextThirdPartyMini3 = useCallback(() => {


    // alipays://platformapi/startapp?appid=68687811&url=xxx
    let path =`https%3A%2F%2Frender.alipay.com%2Fp%2Fyuyan%2F180020010001198920%2Fmedical-detail-bridge.html%3FserContractNo%3D${channelContractNo}`;
    window.location.href = `https://ds.alipay.com/?scheme=${encodeURIComponent(`alipays://platformapi/startapp?appId=68687811&url=${path}`)}`;
    return;
  }, [channelContractNo, channelInquiryNo]);
  return (
    <div className={prefixCls}>
      <>
        <Cell title="服务卡No">
          <Input
            clearable
            type="text"
            placeholder="请输入"
            value={cardNo}
            onChange={(value) => {
              setCardNo(value);
            }}
          />
        </Cell>
        <Cell title="消息Id">
          <Input
            type="text"
            placeholder="请输入"
            value={id}
            onChange={(value) => {
              setMessageId(value);
            }}
          />
        </Cell>
        <Cell title="咨询单号">
          <Input
            type="text"
            placeholder="请输入"
            value={consultNo}
            onChange={(value) => {
              setConsultNo(value);
            }}
          />
        </Cell>
      </>
      <Button theme="primary" onClick={goNext} style={{ marginTop: 10 }} block>
        下一步
      </Button>
      <br />
      <br />
      <br />
      <h3>以下为支付宝入口 配置</h3>
      <>
        <Cell title="合约号1">
          <Input
            clearable
            type="text"
            placeholder="请输入 antSerContractNo"
            value={channelContractNo}
            onChange={(value) => {
              setChannelContractNo(value);
            }}
          />
        </Cell>
        <Cell title="使用单号">
          <Input
            type="text"
            placeholder="请输入 antSerUseNo"
            value={channelInquiryNo}
            onChange={(value) => {
              setChannelInquiryNo(value);
            }}
          />
        </Cell>
      </>
      <Button theme="primary" onClick={goNextAlipay} style={{ marginTop: 10 }} block>
        唤起支付宝小程序
      </Button>
      <br />
      <Button theme="primary" ghost onClick={() => goNextAlipay(2)} style={{ marginTop: 10 }} block>
        场景2
      </Button>
      <Button theme="primary" ghost onClick={() => goNextThirdPartyMini()} style={{ marginTop: 10 }} block>
        测试拿药
      </Button>
      <Button theme="primary" onClick={() => goNextThirdPartyMini2()} style={{ marginTop: 10 }} block>
        测试拿药2
      </Button>
      <Button theme="primary" onClick={() => goNextThirdPartyMini3()} style={{ marginTop: 10 }} block>
        共享单车
      </Button>
    </div>
  );
};
export default Test;
