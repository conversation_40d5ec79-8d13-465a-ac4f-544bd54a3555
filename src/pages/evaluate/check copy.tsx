import React, { useState, useEffect } from 'react';
import { useSelector, shallowEqual, useDispatch } from 'react-redux';
import { fetch_evaluate_by_inquiry, fetch_evaluate_tags, clear_evaluate } from 'src/store/inquiry/action';
import { Card, Avatar, Star, Background } from 'src/components/common';
import { ApplicationState } from 'src/store';
import { Input } from 'zarm';
import './evaluate.scss';

// const validate = (data) => {
//   return true
// }

const START_OBJ = {
  1: '非常不满意',
  2: '不满意',
  3: '一般',
  4: '满意',
  5: '非常满意'
}

const EvaluateCheck = (props) => {
  const { location: { state } } = props;
  const [avatar] = useState(state.avatar || '');
  const [inquiryId] = useState(state && state.inquiryId || '');

  const dispatch = useDispatch();
  const fetchEvaluateByInquiry = (inquiryId) => dispatch(fetch_evaluate_by_inquiry(inquiryId));
  const fetchEvaluateTags = () => dispatch(fetch_evaluate_tags());
  const clearEvaluate = () => dispatch(clear_evaluate());

  useEffect(() => {
    clearEvaluate();
  }, []);

  useEffect(() => {
    fetchEvaluateTags();
    if (inquiryId) {
      fetchEvaluateByInquiry(inquiryId);
    }
  }, [inquiryId]);

  const { evaluate, tagsData } = useSelector((state: ApplicationState) => {
    return {
      evaluate: state.inquiry.evaluate,
      tagsData: state.inquiry.evaluateTags,
    };
  }, shallowEqual);

  const { PROFESSIONALTITLE_OBJ }: any = useSelector((state: ApplicationState) => {
    return {
      PROFESSIONALTITLE_OBJ: state.dictionary.PROFESSIONALTITLE_OBJ,
    }
  });

  if (JSON.stringify(tagsData) === '{}' || JSON.stringify(evaluate) === '{}') {
    return null
  }


  return (
    <div className='evaluate_page'>
      <Background />
      <Avatar prefixCls='avatar' preUrl={avatar || ''} />
      <Card prefixCls='evaluate_wrapper'>

        <p className='name'>{evaluate.inquiry.staffName || ''}</p>
        <p className='staff'>{PROFESSIONALTITLE_OBJ[evaluate.inquiry.staffProfessionalTitle] || ''}</p>
        <Star prefixCls='star' value={evaluate.totalScore} />
        <p className='text'>{START_OBJ[evaluate.totalScore]}</p>

        <div className="tags_wrapper">
          {evaluate.totalScore && tagsData ? JSON.parse(evaluate.evaluateTags).map(item => {
            const tag = tagsData[evaluate.totalScore].filter(i => i.id === item)[0];
            return <div className='tag active' key={item}>
              {tag.tagName}
            </div>
          }) : null}
        </div>

        {evaluate.evaluateDesc ? <Input
          className='input input-readonly'
          showLength
          type="text"
          rows={4}
          maxLength={200}
          value={evaluate.evaluateDesc}
          readOnly
        /> : null}

        <p className='title'>专业评分</p>
        <div className="star_wrapper">
          <div className="item">
            <p>医生服务态度</p>
            <Star prefixCls='right' value={evaluate.answerScore} />
          </div>
          <div className="item">
            <p>医生专业能力</p>
            <Star prefixCls='right' value={evaluate.systemScore} />
          </div>
          <div className="item">
            <p>及时性准时性</p>
            <Star prefixCls='right' value={evaluate.serviceAttitudeScore} />
          </div>
        </div>

        <p className='title'>系统评价</p>
        <div className="star_wrapper">
          <div className="item">
            <p>及时性准时性</p>
            <Star prefixCls='right' value={evaluate.professionalAbilityScore} />
          </div>
        </div>
      </Card>
    </div>
  );
}

export default EvaluateCheck;
