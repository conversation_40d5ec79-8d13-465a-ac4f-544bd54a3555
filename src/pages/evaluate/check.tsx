import React, { useState, useEffect, useMemo } from 'react';
import { useSelector, shallowEqual, useDispatch } from 'react-redux';
import { UmpEvaluateComp } from 'src/components/common';
import { ApplicationState } from 'src/store';
import { fetch_evaluate_by_inquiry, fetch_evaluate_tags, clear_evaluate } from 'src/store/inquiry/action';
import './evaluate.scss';
import { Deserialize } from 'src/utils';

// const validate = (data) => {
//   return true
// }

const EvaluateCheck = (props) => {
  const { location: { state: _state , search = '' } } = props;
  const _searchObj = Deserialize(search);
  const state = _state || _searchObj;
  // const [avatar] = useState(state.avatar || '');
  const [inquiryId] = useState(state && state.inquiryId || '');

  const dispatch = useDispatch();
  const fetchEvaluateByInquiry = (inquiryId) => dispatch(fetch_evaluate_by_inquiry(inquiryId));
  const fetchEvaluateTags = () => dispatch(fetch_evaluate_tags());
  const clearEvaluate = () => dispatch(clear_evaluate());

  useEffect(() => {
    clearEvaluate();
  }, []);

  useEffect(() => {
    fetchEvaluateTags();
    if (inquiryId) {
      fetchEvaluateByInquiry(inquiryId);
    }
  }, [inquiryId]);

  const { evaluate = {}, tagsData = {} } = useSelector((state: ApplicationState) => ({
    evaluate: state.inquiry.evaluate,
    tagsData: state.inquiry.evaluateTags,
  }), shallowEqual);

  const tags = useMemo(() => {
    const tagsArr: string[] = [];
    if (evaluate.evaluateTags) {
      const evaluateTags = JSON.parse(evaluate.evaluateTags || '') || [];
      (tagsData[evaluate.generalEvaluate] || []).forEach((item) => {
        if (evaluateTags.includes(item.id)) {
          tagsArr.push(item.tagName);
        }
      });
    }
    return tagsArr.join(',');
  }, [evaluate, tagsData]);

  if (JSON.stringify(tagsData) === '{}' || JSON.stringify(evaluate) === '{}') {
    return null;
  }

  return (
    <div className='evaluate_page'>
      <UmpEvaluateComp
        isFromCheck={true}
        totalScore={evaluate.generalEvaluate || evaluate.totalScore}
        evaluateTags={tags}
        evaluateDesc={evaluate.evaluateDesc}
      />
    </div>
  );
};

export default EvaluateCheck;
