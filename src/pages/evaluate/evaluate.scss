@import "src/style/index";

.evaluate_page {
  .header {
    height: r(220);
    padding: r(25) r(15) 0;
    background: url('../../svgs/umpe_head_clock.svg') no-repeat, url('../../svgs/umpevaluate_header.svg') no-repeat;
    background-size: r(80) r(80), 100% 100%;
    background-position: 98.4% r(6), left top;
    color: #fff;
    margin-bottom: r(-140);
    font-size: r(13);

    .title {
      font-size: r(18);
      margin-bottom: r(6);
      font-family: PingFangSC-Semibold, PingFang SC;
      font-weight: 600;
    }
  }
  // .avatar {
  //   display: block;
  //   width: r(60);
  //   height: r(60);
  //   left: 50%;
  //   top: r(15);
  //   transform: translateX(-50%);
  //   position: absolute;
  //   z-index: 2;
  // }

  // .evaluate_wrapper {
  //   margin-top: r(46);
  //   padding: r(38) r(15) r(20);
  //   margin-bottom: r(50);

  //   .name {
  //     text-align: center;
  //     color: #333;
  //     font-size: r(18);
  //     font-weight: 600;
  //   }

  //   .staff {
  //     color: #9b9b9b;
  //     font-size: r(15);
  //     text-align: center;
  //     margin: r(4) 0;
  //   }

  //   .star {
  //     margin: 0 auto;
  //     @include align-items(center);
  //     @include justify-content(center);

  //     .star_item {
  //       width: r(22);
  //       height: r(21);
  //       margin: r(10) r(10);
  //     }
  //   }

  //   .text {
  //     margin: r(8) 0 r(15);
  //     position: relative;
  //     text-align: center;
  //     color: #333;
  //     font-size: r(16);

  //     &:before {
  //       content: '';
  //       width: r(115);
  //       height: r(1);
  //       background: #e6e6e6;
  //       position: absolute;
  //       top: 50%;
  //       left: 0;
  //     }

  //     &:after {
  //       content: '';
  //       width: r(115);
  //       height: r(1);
  //       background: #e6e6e6;
  //       position: absolute;
  //       top: 50%;
  //       right: 0;
  //     }
  //   }

  //   .tags_wrapper {
  //     @include display-flex;
  //     @include align-items(center);
  //     @include justify-content(flex-start);

  //     flex-wrap: wrap;

  //     .tag {
  //       margin: 0 r(4) r(10);
  //       min-width: r(100);
  //       height: r(30);
  //       padding: 0 r(14);
  //       border-radius: r(15);
  //       line-height: r(28);
  //       border: r(1) solid #e6e6e6;
  //       color: #333;
  //       font-size: r(14);
  //       text-align: center;

  //       &.active {
  //         background: var(--theme-success);
  //         border-color: var(--theme-success);
  //         color: #fff;
  //       }
  //     }
  //   }

  //   .input {
  //     border: r(1) solid rgba(230, 230, 230, 1);
  //     border-radius: r(5);
  //     padding: r(10);
  //     font-size: r(14);

  //     .za-input {
  //       @include placeholder;

  //       font-size: r(14);
  //     }

  //     &.input-readonly {
  //       word-break: break-all;
  //     }
  //   }

  //   .title {
  //     margin-top: r(23);
  //     color: #333;
  //     font-size: r(15);
  //     font-weight: 600;
  //     padding-bottom: r(7);
  //     border-bottom: r(1) solid #e6e6e6;
  //   }

  //   .star_wrapper {
  //     .item {
  //       @include display-flex;
  //       @include align-items(center);

  //       margin-top: r(15);
  //       color: #333;
  //       font-size: r(14);

  //       .right {
  //         margin-left: r(20);

  //         .star_item {
  //           width: r(20);
  //           height: r(19);
  //           margin-right: r(15);
  //         }
  //       }
  //     }
  //   }

  //   .za-input--disabled input,
  //   .za-input--disabled textarea {
  //     color: #333;
  //   }
  // }
}

.evaluatesuccess_page {
  @include display-flex;

  padding-top: r(40);
  min-height: 100vh;

  .evaluate {
    @include flex;

    img {
      margin: r(56) auto r(20);
      width: r(88);
      height: r(88);
      display: block;
    }

    .text {
      color: #333;
      font-size: r(19);
      font-weight: 600;
      text-align: center;
    }

    .button_wrapper {
      @include display-flex;
      @include align-items(center);
      @include justify-content(space-around);

      .btn {
        margin-top: r(35);
        width: r(140);
        height: r(44);
        border-radius: r(4);
        color: var(--theme-success);
        border: r(1) solid var(--theme-success);
        line-height: r(42);
        text-align: center;
        font-size: r(16);

        &:active {
          color: #fff;
          background: var(--theme-success);
        }
      }
    }
  }
}
