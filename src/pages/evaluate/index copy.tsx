import React, { useState, useCallback, useEffect } from 'react';
import { useSelector, shallowEqual, useDispatch } from 'react-redux';
import { Card, Avatar, Star, FixedButton, StaticToast, Background } from 'src/components/common';
import { submit_evaluate, fetch_evaluate_tags } from 'src/store/inquiry/action';
import { ApplicationState } from 'src/store';
import classnames from 'classnames';
import { Input } from 'zarm';
import './evaluate.scss';
import { xflowPushEvent } from 'src/utils/pageTrack';

const validate = (data) => {
  const { evaluateDesc = '' } = data;
  if (evaluateDesc.trim() === '') {
    StaticToast.warning('您还没输入问诊建议');
    return false;
  }
  if (evaluateDesc.trim().length < 10) {
    StaticToast.warning('问诊意见不能少于10个字');
    return false;
  }
  return true;
};

const START_OBJ = {
  1: '非常不满意',
  2: '不满意',
  3: '一般',
  4: '满意',
  5: '非常满意',
};

const Evaluate = (props) => {
  const {
    location: { state },
  } = props;
  const [info] = useState(state || {});
  const [totalScore, setTotalScore] = useState(5); //总评分
  const [answerScore, setAnswerScore] = useState(5); //及时性准时性
  const [systemScore, setSystemScore] = useState(5); //系统使用效果
  const [serviceAttitudeScore, setServiceAttitudeScore] = useState(5); //医生服务态度
  const [professionalAbilityScore, setProfessionalAbilityScore] = useState(5); //医生专业能力
  const [evaluateTags, setEvaluateTags]: [any, any] = useState([]);
  const [evaluateDesc, setEvaluateDesc] = useState('');
  // const [throttle, setThrottle] = useState(true);

  const dispatch = useDispatch();
  const submitEvaluate = (data: any, onSuccess: any) => dispatch(submit_evaluate(data, onSuccess));
  const fetchEvaluateTags = () => dispatch(fetch_evaluate_tags());

  const tagData = useSelector((state: ApplicationState) => {
    return state.inquiry.evaluateTags;
  }, shallowEqual);

  const { PROFESSIONALTITLE_OBJ }: any = useSelector((state: ApplicationState) => {
    return {
      PROFESSIONALTITLE_OBJ: state.dictionary.PROFESSIONALTITLE_OBJ,
    };
  });

  useEffect(() => {
    fetchEvaluateTags();
  }, []);

  const totalScoreClick = useCallback((value) => {
    setTotalScore(value);
    setEvaluateTags([]);
  }, []);
  const answerScoreClick = useCallback((value) => {
    setAnswerScore(value);
  }, []);
  const systemScoreClick = useCallback((value) => {
    setSystemScore(value);
  }, []);
  const serviceAttitudeScoreClick = useCallback((value) => {
    setServiceAttitudeScore(value);
  }, []);
  const professionalAbilityScoreClick = useCallback((value) => {
    setProfessionalAbilityScore(value);
  }, []);

  const tagsClick = useCallback(
    (value) => {
      if (evaluateTags.includes(value)) {
        setEvaluateTags(evaluateTags.filter((item) => item !== value));
      } else {
        setEvaluateTags([...evaluateTags, value]);
      }
    },
    [evaluateTags],
  );

  const submit = useCallback(() => {
    const data = {
      totalScore,
      answerScore,
      systemScore,
      serviceAttitudeScore,
      professionalAbilityScore,
      evaluateTags: JSON.stringify(evaluateTags),
      evaluateDesc,
      inquiryId: info.inquiryId,
      staffId: info.staffId,
    };
    xflowPushEvent(['click', 'ZAHLWYY_TWWZ_WZWCY', '图文问诊_问诊完成页', { ZAHLWYY_CLICK_CONTENT: '图文问诊_问诊完成页' }]);
    validate(data) &&
      submitEvaluate(data, () => {
        props.history.replace({
          pathname: '/hospital/evaluatesuccess',
          state: info,
        });
      });
  }, [info, totalScore, answerScore, systemScore, serviceAttitudeScore, professionalAbilityScore, evaluateTags, evaluateDesc]);

  const blur = useCallback(() => {
    window.scroll({
      top: 0,
      behavior: 'smooth',
    });
  }, []);

  if (JSON.stringify(tagData) === '{}') {
    return null;
  }

  return (
    <div className='evaluate_page'>
      <Background />
      <Avatar prefixCls='avatar' preUrl={info.avatar || ''} />
      <Card prefixCls='evaluate_wrapper'>
        <p className='name'>{info.staffName || ''}</p>
        <p className='staff'>{PROFESSIONALTITLE_OBJ[info.staffProfessionalTitle]}</p>
        <Star prefixCls='star' value={totalScore} starClick={totalScoreClick} />
        <p className='text'>{START_OBJ[totalScore]}</p>
        <div className='tags_wrapper'>
          {tagData[totalScore].map((item) => (
            <div className={classnames('tag', { active: evaluateTags.includes(item.id) })} key={`tag_item_${item.id}`} onClick={() => tagsClick(item.id)}>
              {item.tagName}
            </div>
          ))}
        </div>

        <Input className='input' showLength type='text' rows={4} maxLength={200} placeholder='记录问诊中印象深刻的感受和建议，给予其他病友帮助' onChange={(value) => setEvaluateDesc(value)} value={evaluateDesc} onBlur={() => blur()} />

        <p className='title'>专业评分</p>
        <div className='star_wrapper'>
          <div className='item'>
            <p>医生服务态度</p>
            <Star prefixCls='right' value={answerScore} starClick={answerScoreClick} />
          </div>
          <div className='item'>
            <p>医生专业能力</p>
            <Star prefixCls='right' value={systemScore} starClick={systemScoreClick} />
          </div>
          <div className='item'>
            <p>及时性准时性</p>
            <Star prefixCls='right' value={serviceAttitudeScore} starClick={serviceAttitudeScoreClick} />
          </div>
        </div>

        <p className='title'>系统评价</p>
        <div className='star_wrapper'>
          <div className='item'>
            <p>系统使用效果</p>
            <Star prefixCls='right' value={professionalAbilityScore} starClick={professionalAbilityScoreClick} />
          </div>
        </div>
      </Card>
      <FixedButton buttonClick={() => submit()} text='提交' />
    </div>
  );
};

export default Evaluate;
