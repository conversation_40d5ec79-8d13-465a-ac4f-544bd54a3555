import React, { useCallback, useEffect, useState } from 'react';
import { shallowEqual, useDispatch, useSelector } from 'react-redux';
import { StaticToast, UmpEvaluateComp } from 'src/components/common';
import { ApplicationState } from 'src/store';
import { fetch_evaluate_tags, fetch_inquiry_detail, submit_evaluate } from 'src/store/inquiry/action';
import { Deserialize, fetchJson } from 'src/utils';
import { xflowPushEvent } from 'src/utils/pageTrack';
import './evaluate.scss';


const validate = (data) => {
  const { evaluateDesc = '', generalEvaluate } = data;
  if (generalEvaluate === 5) {
    return true;
  }
  if (evaluateDesc.trim() === '') {
    StaticToast.warning('您还没输入问诊建议');
    return false;
  }
  if (evaluateDesc.trim().length < 10) {
    StaticToast.warning('问诊意见不能少于10个字');
    return false;
  }
  return true;
};

const Evaluate = (props) => {
  const { location: { state, search = '' } } = props;
  // const { partnerCode = '', businessType = '', thirdPlatformCode = '', bizNo = '' } = Deserialize(search);
  const { inquiryId = '', staffId = '', refer = '' } = Deserialize(search);
  const [info] = useState(state || {});
  const [totalScore, setTotalScore] = useState(5);  // 总评分
  const [evaluateTags, setEvaluateTags]: [any, any] = useState([]);
  const [evaluateDesc, setEvaluateDesc] = useState('');
  const [submitFlag, setSumbitFlag] = useState(false);
  const [showFeedback, setShowFeedback] = useState(false);

  const dispatch = useDispatch();
  const submitEvaluate = (data: any, onSuccess: any) => dispatch(submit_evaluate(data, onSuccess));
  const fetchEvaluateTags = () => dispatch(fetch_evaluate_tags());
  const fetchInquiryDetail = (options: any, onSuccess?: any) => dispatch(fetch_inquiry_detail(options, onSuccess));

  const tagData = useSelector((state: ApplicationState) => state.inquiry.evaluateTags, shallowEqual);

  const getShowFeedback = async () => {
    const res = await fetchJson({
      type: 'POST',
      url: '/api/api/v1/switch/config/findSwitchConfigByCode',
      data: {
        switchCode: 'DOCTORS_EVALUATE_OPEN',
      },
      isloading: false,
    });
    const { code = '', result = {} } = res;
    if (code === '0' && result) {
      setShowFeedback(result.switchStatus === 'Y');
      return;
    }
  };

  useEffect(() => {
    getShowFeedback();
    fetchEvaluateTags();
    fetchInquiryDetail({
      inquiryId: inquiryId || info.inquiryId,
      option: {
        needDiagnosis: true,
        needPatient: true,
        needStaff: true,
        needThirdRelation: true,
        needQueryPrescription: true,
      },
    });
  }, []);

  const totalScoreClick = useCallback((value) => {
    setTotalScore(value);
    setEvaluateTags([]);
  }, []);

  const tagsClick = useCallback((value) => {
    if (evaluateTags.includes(value)) {
      setEvaluateTags(evaluateTags.filter((item) => item !== value));
    } else {
      setEvaluateTags([...evaluateTags, value]);
    }
  }, [evaluateTags]);

  const submit = useCallback(() => {
    const data = {
      generalEvaluate: totalScore,
      evaluateTags: JSON.stringify(evaluateTags),
      evaluateDesc,
      inquiryId: info.inquiryId || inquiryId,
      staffId: info.staffId || staffId,
    };
    xflowPushEvent({ eventTag: 'ZAHLWYY_TWWZ_WZWCY', text: '图文问诊_问诊完成页', attributes: '图文问诊_问诊完成页' });
    validate(data) && submitEvaluate(data, () => {
      if (refer === 'selfvideo') {
        setSumbitFlag(true);
      }
      props.history.replace({
        pathname: '/hospital/evaluatesuccess', state: {
          ...info,
          inquiryId: info.inquiryId || inquiryId,
          staffId: info.staffId || staffId,
        },
      });
    });
  }, [info, totalScore, evaluateTags, evaluateDesc]);


  if (JSON.stringify(tagData) === '{}') {
    return null;
  }

  return (
    <div className='evaluate_page'>
      <UmpEvaluateComp
        totalScore={totalScore}
        totalScoreClick={totalScoreClick}
        tags={tagData[totalScore]}
        evaluateTags={evaluateTags}
        tagsClick={tagsClick}
        evaluateDesc={evaluateDesc}
        setEvaluateDesc={setEvaluateDesc}
        submitFlag={submitFlag}
        submit={submit}
      />
      {
        showFeedback &&
        <p
          style={{
            color: '#666666',
            fontSize: '13px',
            marginTop: 40,
            textAlign: 'center',
            textDecoration: 'underline',
          }}
          onClick={() => window.location.href = 'https://supervision.ssc-hn.com/app#/welcome-feedback'}
        >问题反馈</p>
      }
    </div>
  );
};

export default Evaluate;
