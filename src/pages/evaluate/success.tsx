/*
 * @Author: <EMAIL> <EMAIL>
 * @Date: 2021-10-29 10:40:47
 * @LastEditTime: 2023-03-01 14:38:17
 * @LastEditors: <EMAIL> <EMAIL>
 * @FilePath: /za-asclepius-patient-h5/src/pages/evaluate/success.tsx
 * @Description: 
 */
import React, { useCallback } from 'react';
import { useSelector } from 'react-redux';
import { Card, Background, SvgIcon } from 'src/components/common';
import { ApplicationState } from 'src/store';
import './evaluate.scss';
import evaluate_success_icon from './images/evaluate_success.svg';

const EvaluateSuccess = (props) => {
  const { location: { state = {} } } = props;
  const { type = '' } = state;

  const {id, inquiryNo, inquiryThirdRelationList = []} = useSelector((state: ApplicationState) => {
    return state.inquiry.datail;
  })

  const toMyInquiry = useCallback(() => {
    const inquiryThirdRelation = inquiryThirdRelationList[0] || {};

    props.history.replace({
      pathname: '/hospital/chat',
      search: `?inquiryNo=${inquiryNo}&inquiryId=${id}&thirdDoctorId=${inquiryThirdRelation.thirdDoctorId}`
    })
  }, []);

  const toCheck = useCallback(() => {
    props.history.replace({
      pathname: "/hospital/evaluatecheck",
      state: state
    })
  }, []);

  return <div className='evaluatesuccess_page'>
    <Background />
    <Card prefixCls='evaluate'>
      <SvgIcon type='img' src={evaluate_success_icon} />
      <p className='text'>感谢您的评价</p>
      <div className='button_wrapper'>
        <div className="btn" onClick={() => toMyInquiry()}>{type === 'video' ? '返回首页' : '返回'}</div>
        {type !== 'video' ? <div className="btn" onClick={() => toCheck()}>查看评价</div> : null}
      </div>
    </Card>
  </div>
}

export default EvaluateSuccess;
