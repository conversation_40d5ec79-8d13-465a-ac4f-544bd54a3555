@import "src/style/index";

.ExaminationBodyType {
  &-modal {
    &.za-modal .za-popup {
      overflow: initial;
    }

    .za-modal__body {
      padding: 0;
    }
  }

  &-head {
    width: r(77);
    height: r(77);
    background: #fff;
    box-shadow: 0 r(2) r(4) 0 #d4d4d4;
    border-radius: 50%;
    margin-top: -20%;
    text-align: center;
    transform: translateX(-50%);
    margin-left: 50%;

    img {
      width: 50%;
      margin-top: 25%;
    }
  }

  &-title {
    font-size: r(17);
    font-family: PingFangSC-Medium, PingFang SC;
    font-weight: 500;
    color: #333;
    text-align: center;
    margin-top: r(12);
  }

  &-content {
    padding: r(16) 0 r(30);

    &__indicator {
      display: flex;
      flex-flow: row wrap;
      justify-content: space-between;
      width: r(212);
      margin: 0 auto;
      position: relative;
    }

    &__item {
      width: r(70);
      height: r(70);
      text-align: center;
      position: relative;
      padding: 0;
      padding-top: r(8);

      img {
        width: r(32);
      }

      p {
        font-size: r(12);
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #7d7d7e;
        margin-top: r(6);
      }

      &:nth-child(1) {
        .ExaminationBodyType-content__vertical {
          display: block;
          background: #e8672f;
        }
      }

      &:nth-child(4) {
        .ExaminationBodyType-content__vertical {
          display: block;
          background: #89da4f;

          &::after {
            display: block;
            content: "脂肪率";
            position: absolute;
            left: r(-7);
            writing-mode: tb-rl;
            transform: translate(-100%, 50%);
            font-size: r(12);
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: #000;
          }
        }
      }

      &:nth-child(7) {
        .ExaminationBodyType-content__vertical {
          display: block;
          background: #448bf7;
          height: r(60);
        }

        .ExaminationBodyType-content__line {
          display: block;
          background: #448bf7;
          width: r(60);
        }
      }

      &:nth-child(8) {
        .ExaminationBodyType-content__line {
          display: block;
          background: #89da4f;

          &::after {
            display: block;
            content: "肌肉率";
            position: absolute;
            top: r(7);
            left: 50%;
            transform: translateX(-50%);
            font-size: r(12);
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: #000;
            min-width: 4em;
          }
        }
      }

      &:nth-child(9) {
        .ExaminationBodyType-content__line {
          display: block;
          background: #e8672f;
        }
      }
    }

    &__line {
      position: absolute;
      display: none;
      width: r(68);
      border-radius: r(3);
      height: r(3);
      bottom: 0;
      right: 0;
    }

    &__vertical {
      display: none;
      position: absolute;
      height: r(68);
      border-radius: r(3);
      width: r(3);
      top: 0;
    }

    &__footer {
      text-align: center;
      margin-top: r(20);

      img {
        width: r(212);
      }
    }
  }
}
