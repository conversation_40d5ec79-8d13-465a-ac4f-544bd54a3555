/*
 * @Author: <EMAIL> <EMAIL>
 * @Date: 2022-11-10 16:50:36
 * @LastEditTime: 2022-11-15 15:46:56
 * @LastEditors: <EMAIL> <EMAIL>
 * @FilePath: /za-asclepius-patient-h5/src/pages/examination/components/BodyType/index.tsx
 * @Description: 身材类型分类
 */
import React, { forwardRef, useImperativeHandle, useState } from 'react';
import { Modal } from 'zarm';
import './index.scss';

export interface ExaminationBodyTypeHandle {
    open: Function
}

export interface ExaminationBodyProps {

}

const bodyTypes = ['隐形胖', '偏胖型', '肌肉偏胖型', '缺乏锻炼型', '标准型', '标准肌肉型', '偏瘦型', '偏瘦肌肉型', '肌肉发达型']

const ExaminationBodyType = (props, ref) => {
    const [visible, setVisible] = useState(false);

    useImperativeHandle(ref, () => ({
        open: toggle
    }))

    const toggle = () => setVisible(!visible);

    return (
        <div className='ExaminationBodyType'>
            <Modal
                visible={visible}
                className="ExaminationBodyType-modal"
                title={
                    <div className='ExaminationBodyType-head'>
                        <img src={require('../../images/report/bodytype.png')}></img>
                    </div>
                }
                width="85%"
                closable
                onCancel={() => toggle()}>
                <div className="ExaminationBodyType-title">
                    身材类型分类
                </div>
                <div className="ExaminationBodyType-content">
                    <div className="ExaminationBodyType-content__indicator">
                        {
                            bodyTypes.map((item, idx) => (
                                <div key={idx} className="ExaminationBodyType-content__item">
                                    <div className="ExaminationBodyType-content__line"></div>
                                    <div className="ExaminationBodyType-content__vertical"></div>
                                    <img src={require('../../images/report/bodynormal.png')} alt="" />
                                    <p>{item}</p>
                                </div>
                            ))
                        }
                    </div>
                    <div className="ExaminationBodyType-content__footer">
                        <img src={require('../../images/report/point.png')} alt="" />
                    </div>
                </div>
            </Modal>
        </div>
    )
}

export default forwardRef<ExaminationBodyTypeHandle, ExaminationBodyProps>(ExaminationBodyType);