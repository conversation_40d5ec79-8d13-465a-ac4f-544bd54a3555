/*
 * @Description: 头部注释
 * @Autor: hou
 * @Date: 2022-11-18 17:35:48
 * @LastEditors: hou
 * @LastEditTime: 2022-11-30 17:16:58
 */
import React, { forwardRef, useLayoutEffect } from 'react';
import * as echarts from 'echarts';
import './index.scss';
import _ from 'lodash'

// type EChartsOption = echarts.EChartsOption;

export interface ExaminationEcgHandle {
  open: Function
}

export interface ExaminationEcgProps {
  value?: any
}

const ExaminationEcg = (props) => {
    useLayoutEffect(() => {
      // const beatStatic = '125,121,122,132,151,169,179,177,167,125,84,75,76,89,105,113,113,119,122,125,124,126,127,129,131,125,121,122,132,151,169,179,177,167';
      const beatStatic = '';
      const data = props.value??beatStatic;
      const result = data.split(',').map((item,index)=> [index * 1, Number(item)])??[];
      const yData = data.split(',').map((item,index)=> Number(item))??[];
      result.length >= 100 && (result.length = 100);
      yData.length >= 100 && (yData.length = 100);
      const chartDom = document.getElementById('ExaminationEcg')!;
      const myChart = echarts.init(chartDom);
      var min = _.min(yData);
      var max = _.max(yData);

    const option = {
      grid: {
        left: 0,
        top: 0,
        right: 0,
        bottom: 0
      },
      color: ['black'],
      tooltip: {
        trigger: 'axis',
        axisPointer: { // 坐标轴指示器，坐标轴触发有效
          type: 'shadow' // 默认为直线，可选为：'line' | 'shadow'
        }
      },
      xAxis: [{
        axisLine: {
          show: false
        },
        axisLabel: {
          show: false
        },
        axisTick: {
          inside: true,
          lineStyle: {
            color: "#DEA9AA"
          }
        },
        minorTick: {
          show: false,
          lineStyle: {
            color: "#DEA9AA"
          }
        },
        minorSplitLine: {
          show: true,
          lineStyle: {
            color: "#DEA9AA"
          }
        },
        type: 'category',
        data: result.map(item => item[0]),
        splitLine: {
          show: true,
          lineStyle: {
            color: '#DEA9AA',
            width: 0.5
          }
        },
      }],
      yAxis: [{
        type: 'value',
        min: min - 20,
        max: max + 20,
        axisLabel: {
          show: false
        },
        axisTick: {
          inside: true,
          length: 315,
          lineStyle: {
            color: "#DEA9AA"
          }
        },
        minorSplitLine: {
          show: true,
          lineStyle: {
            color: "#DEA9AA"
          }
        },
        splitLine: {
          show: true,
          lineStyle: {
            color: '#DEA9AA',
            width: 0.5
          }
        },
        axisLine: {
          lineStyle: {
            color: '#DEA9AA'
          }
        }
      }],
      series: [{
        data: result.map(item => item[1]),
        symbol: "none",
        type: 'line'
      }]
    }
    // 使用刚指定的配置项和数据显示图表。
    myChart.setOption(option);
  }, [])

  return (
    <div id='ExaminationEcg'> </div>
  )
}

export default forwardRef<ExaminationEcgHandle, ExaminationEcgProps>(ExaminationEcg);
