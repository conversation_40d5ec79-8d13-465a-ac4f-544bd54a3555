@import "src/style/index";

.userInput {
  overflow: hidden;

  .za-date-select--arrow .za-date-select__input:after {
    display: none;
  }

  .za-cell:after {
    display: none;
  }

  &-content {
    margin: r(10) r(15) 0;
    height: calc(82vh);
    background: #fff;
    border-radius: r(7.5);

    .title {
      border-radius: r(7.5);
      font-family: PingFangSC-Semibold, PingFang SC;
      font-size: r(16);
      font-weight: 600;
      color: #1e1e1e;
      height: r(52);
      line-height: r(52);
      padding: 0 r(16);
    }

    .za-cell__inner {
      padding: 0;
    }

    .item,
    .item1 {
      margin: 0 r(16);
      border-bottom: 1px solid #e6e6e6;
    }

    .item1 {
      border: none;

      .za-cell__inner {
        background: rgba(0, 0, 0, 0.03);
        padding: 0 r(16);
        border-radius: r(6);
      }
    }
  }

  &-footer {
    position: fixed;
    bottom: 0;
    width: 100%;
    // background: #fff;
    // box-shadow: 0 -30px 37px 0 rgba(0, 0, 0, 0.02);
    border-radius: r(10) r(10) 0 0;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: r(14) r(15);

    &__btn {
      width: 80%;
      background: #00bc70;
      border-radius: r(22);
      font-size: r(16);
      font-family: PingFangSC-Semibold, PingFang SC;
      font-weight: 600;
      color: #fff;
      text-align: center;
      padding: r(11) r(43);
    }
  }
}
