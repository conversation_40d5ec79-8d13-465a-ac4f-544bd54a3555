/*
 * @Description: 头部注释
 * @Autor: hou
 * @Date: 2022-12-09 14:40:16
 * @LastEditors: hou
 * @LastEditTime: 2022-12-13 17:57:20
 * @FilePath: /za-asclepius-patient-h5/src/pages/examination/components/userInput/index.tsx
 */
import React, { useState, forwardRef, useImperativeHandle } from 'react';
import { Cell, DateSelect,Radio} from 'zarm';
import format from 'src/utils/format';
import { StaticToast } from 'src/components/common';
import './index.scss'

const UserInput = (prpos,ref) => {
  const [formValue,setformValue] = useState<any>({});

  const startTest = () => {
    const { unifiedOrder } = prpos;
    if(!formValue.gender){
      return StaticToast.error('请选择性别')
    }
    if(!formValue.birthday){
      return StaticToast.error('请选择出生日期')
    }
    formValue.birthday = format.date(formValue.birthday,'yyyy-MM-dd');
    unifiedOrder(formValue);
  }
  
  useImperativeHandle(ref, () => ({
    unifiedOrder: startTest
  }))

  return (
    <div className='userInput'>
      <div className={`userInput-content`}>
        <div className='title'> 请选择性别： </div>
        <Cell className="cell item" title="">
          <Radio.Group value={formValue.gender} onChange={(value) => { setformValue({...formValue,gender:value}); }}>
            <Radio value="M">男</Radio>
            <Radio value="F">女</Radio>
          </Radio.Group>
        </Cell>
        <div className='title'> 请选择出生日期: </div>
        <Cell className="cell item1" title="" hasArrow>
          <DateSelect
            mode="date"
            min="1900-01-01"
            max={format.date(new Date(), 'yyyy-MM-dd')}
            value={formValue.birthday}
            wheelDefaultValue="1998-01-01"
            onOk={(value) => { setformValue({...formValue,birthday:value});}}
          />
        </Cell>
      </div>
      {/* <div className="userInput-footer">
        <div className="userInput-footer__btn" onClick={startTest}> 开始检测 </div>
      </div> */}
    </div>
  )
}

export default forwardRef(UserInput);