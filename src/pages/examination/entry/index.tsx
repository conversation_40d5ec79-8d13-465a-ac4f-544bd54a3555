/*
 * @Author: sen.lv <EMAIL>
 * @Date: 2022-11-16 13:58:34
 * @LastEditTime: 2022-12-12 15:04:26
 * @LastEditors: hou
 * @FilePath: /za-asclepius-patient-h5/src/pages/examination/entry/index.tsx
 * @Description: 
 */
import React, { useEffect, useState } from 'react';
import { Deserialize, fetchJson } from 'src/utils';
import ExaminationStatus from './status';
import './index.scss'

const ExaminationEntry = (props) => {
  const [doing, setDoing] = useState(false);
  const [loading, setLoading] = useState(true);
  const { code, orderNo } = Deserialize(props.location.search);

  useEffect(() => {
    orderNo ? queryStatus(undefined) : fetchRecordByNo();
  }, []);

  const fetchRecordByNo = async () => {
    const res = await fetchJson({
      type: 'POST',
      url: '/api/api/v1/magic/mirror/queryMagicMirrorQrcodeRecordByNo',
      data: {
        codeNo: code,
      },
      isloading: false,
    });
    if (res && res.code === '0') {
      queryStatus(res.result.codeNo);
    }
  }

  const queryStatus = (codeNo) => {
    fetchJson({
      url: '/api/api/v1/magic/mirror/queryDetectionStatus',
      type: 'POST',
      data: {
        qrcodeNo: codeNo,
        orderNo,
      },
      isloading: false,
    })
      .then(res => {
        if(res.code === '0') {
          setLoading(false);
          const { detectionStatus } = res.result;
          if(['PADDING', 'SHOW', 'NOT_SHOW', 'TO_CREATE_REPORT', 'REPORT_CREATED'].includes(detectionStatus)) {
            return setDoing(true);
          }

          if(detectionStatus === 'NOT_PAY') {
            return props.history.push({
              pathname: '/hospital/myorder',
              search: `orderStatus=1`
            })
          }

          if(detectionStatus == 'SCANQR')
            props.history.push({
              pathname: '/hospital/examination/order',
              search: `code=${code}`
            })
            return;
        }
      })
  }

  return (
    <div className='ExaminationEntry'>
      {
        !loading && doing ?
        <ExaminationStatus {...props}></ExaminationStatus> : null
      }
    </div>
  )
}

export default ExaminationEntry;