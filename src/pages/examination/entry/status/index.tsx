/*
 * @Author: <EMAIL> <EMAIL>
 * @Date: 2022-11-14 11:17:26
 * @LastEditTime: 2023-01-10 16:32:27
 * @LastEditors: <EMAIL> <EMAIL>
 * @FilePath: /za-asclepius-patient-h5/src/pages/examination/entry/status/index.tsx
 * @Description:
 */

import React, { useState } from 'react';
import { Deserialize, fetchJson } from 'src/utils';
import { getMJURL } from 'src/utils/env';
import {useInterval} from 'src/utils/hooks';
import './index.scss';

const ExaminationStatus = ({ history, location }) => {
  const [status, setStatus] = useState('');
  const[orderNo, setOrderNo] = useState('');
  const[orderType, setOrderType] = useState('');
  const[thirdOrderId, setThirdOrderId] = useState('');
  const[delay, setDelay] = useState<number | null>(2000);
  const { code, orderNo: orderNoForSearch } = Deserialize(location.search);

  const queryStatus = () => {
    fetchJson({
      url: '/api/api/v1/magic/mirror/queryDetectionStatus',
      type: 'POST',
      data: {
        qrcodeNo: code,
        orderNo: orderNoForSearch,
      },
      isloading: false,
    })
      .then((res) => {
        if(res.code === '0') {
          setStatus(res.result.detectionStatus);
          if (res.result.detectionStatus === 'REPORT_CREATED') {
            setOrderNo(res.result.orderNo);
            setOrderType(res.result.orderType);
            setThirdOrderId(res.result.thirdOrderId);
            setDelay(null);
          }
        }
      });
  };

  useInterval(() => {
    queryStatus();
  }, delay, { immediate: true });

  const goReport = () => {
    if(orderType === 'skinTextureTest') {
      window.location.href = `${getMJURL}/magicmirror/skinReport?dataId=${thirdOrderId}&channel=9999`;
      return;
    }
    history.push({
      pathname: '/hospital/examination/report',
      search: `orderNo=${orderNo}`,
    });
  };

  const statusInfo = {
    REPORT_CREATED: (
      <div className='ExaminationStatus-complete'>
        <img className='ExaminationStatus-complete__img' src={require('../../images/complete.png')} alt='' />
        <p className='ExaminationStatus-complete__desc'>指标检测完成，请查看指标结果</p>
        <div className='ExaminationStatus-complete__btn' onClick={goReport}>
            查看检测报告
        </div>
      </div>
    ),
    DETECTION_EXCEPTION: (
      <div className='ExaminationStatus-complete'>
        <img className='ExaminationStatus-complete__img' src={require('../../images/device-warn.png')} alt='' />
        <p className='ExaminationStatus-complete__desc'>指标检测出现异常</p>
      </div>
    ),
  }[status] || (
    <div className='ExaminationStatus-complete'>
      <img className='ExaminationStatus-complete__img' src={require('../../images/device-doing.png')} alt='' />
      <p className='ExaminationStatus-complete__desc'>指标检测中，请按照设备提示完成检测…</p>
    </div>
  );

  return (
    <div className='ExaminationStatus'>
      {statusInfo}
    </div>
  );
};

export default ExaminationStatus;
