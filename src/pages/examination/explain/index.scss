@import "src/style/index";

.ExaminationExplain {
  min-height: 100vh;
  font-family: PingFangSC-Regular, PingFang SC;
  padding-top: r(42.5);
  padding-bottom: calc(100vh - r(350));

  .topTabs {
    height: 44px;
    background: #f5f5f5;
    display: flex;
    justify-content: left;
    align-items: center;
    font-family: PingFangSC-Medium, PingFang SC;
    border-top: 1px solid #e1e1e1;
    border-bottom: 1px solid #e1e1e1;
    overflow-x: scroll;
    scrollbar-width: none; /* firefox */
    -ms-overflow-style: none; /* IE 10+ */
    position: fixed;
    top: 0;
    width: 100%;
    z-index: 1000;
    padding-right: 20px;

    .item {
      font-size: r(15);
      color: #767676;
      margin: 0 r(13.5);
      height: calc(100% - r(2));
      display: flex;
      align-items: center;
      white-space: nowrap;
      position: relative;
    }

    .active {
      color: #e96e39;
      border-bottom: r(2) solid #e96e39;
      margin-bottom: r(-2);
    }

    .active::after {
      content: '';
      width: 0;
      height: 0;
      border: r(4) solid transparent;
      border-bottom-color: #e96e39;
      position: absolute;
      bottom: r(-1);
      left: calc(50% - r(4));
    }
  }

  .topTabs::-webkit-scrollbar {
    display: none; /* Chrome Safari */
  }

  .tabContent {
    min-height: r(286);
    background: #fff;
    padding: r(26);
    margin-bottom: r(10);

    .topbox {
      border-bottom: r(1) solid #e1e1e1;
      padding-bottom: r(18);
      text-align: center;

      .emptyState {
        width: r(100);
      }

      .itemNumber {
        height: r(53);
        text-align: center;
        overflow: hidden;

        .number {
          font-size: r(37.5);
        }

        .unit {
          margin-left: r(5);
          font-size: r(17);
        }
      }

      .placeholder {
        text-align: center;
        color: #999;
        font-size: r(13);
      }
    }

    .result {
      text-align: center;
      margin: r(5) 0 r(20) 0;
      color: #e97745;
    }

    .ruler {
      display: flex;
      text-align: center;
      justify-content: space-between;

      .Thin,
      .normal,
      .Overweight,
      .fat {
        width: r(77.5);
        font-size: r(14);
        color: #7a7a7c;
        position: relative;

        .bar {
          width: 100%;
          height: r(4);
        }

        .left {
          border-radius: r(8) 0 0 r(8);
        }

        .right {
          border-radius: 0 r(8) r(8) 0;
        }

        .ruler-text {
          margin-top: r(6);
        }
      }

      .Thin .bar {
        background: #448bf7;
      }

      .normal .bar {
        background: #8fdc58;
      }

      .Overweight .bar {
        background: #e97745;
      }

      .fat .bar {
        background: #ea4731;
      }

      .active::after {
        content: '';
        width: 0;
        height: 0;
        border: r(5) solid transparent;
        border-bottom-color: #e96e39;
        position: absolute;
        top: r(-12);
        left: r(5);
        transform: rotate(180deg);
      }
    }

    .title {
      color: #000;
      font-size: r(16);
      margin: r(19.5) 0 r(9) 0;
      display: flex;

      &-icon {
        width: r(21);
        height: r(21);
        border-radius: 50%;
        margin-right: r(6);
      }

      .height {
        background: #52b0cf;
      }

      &-text {
        line-height: r(24);
      }
    }

    .desc {
      font-family: PingFangSC-Regular, PingFang SC;
      font-size: r(13);
      color: #666;
      white-space: pre-line;
    }
  }
}
