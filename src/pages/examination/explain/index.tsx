/*
 * @Description: 头部注释
 * @Autor: hou
 * @Date: 2022-11-09 18:07:37
 * @LastEditors: hou
 * @LastEditTime: 2023-04-23 10:59:18
 */
import _ from 'lodash';
import React, { useState, useEffect } from 'react';
import { Deserialize, fetchJson } from 'src/utils';
import EchartsEcg from '../components/EchartsEcg';
import './index.scss';
import { useThrottleFn } from 'src/utils/hooks';


const cdnPrefix = 'https://cdn-qcloud.zhongan.com/doctor/examinationexplain/';
const emptyState = () => (
  <>
    <img className='emptyState' src={cdnPrefix + 'emptyState.png'} alt='' />
    <div className='placeholder'>还没有这项指标的检测数据哦~</div>
  </>
);
const weightRender = (item) => (
  <>
    <div className='result'>{item.propertyLevelText}</div>
    <div className='ruler'>
      <div className='Thin'>
        <div className={item.propertyLevelText == '偏瘦' ? 'bar left active' : 'bar left'}></div>
        <div className='ruler-text'>偏瘦</div>
      </div>
      <div className='normal'>
        <div className={item.propertyLevelText == '标准' ? 'bar active' : 'bar'}></div>
        <div className='ruler-text'>标准</div>
      </div>
      <div className='Overweight'>
        <div className={item.propertyLevelText == '偏胖' ? 'bar active' : 'bar'}></div>
        <div className='ruler-text'>偏胖</div>
      </div>
      <div className='fat'>
        <div className={item.propertyLevelText == '肥胖' ? 'bar right active' : 'bar right'}></div>
        <div className='ruler-text'>肥胖</div>
      </div>
    </div>
  </>
);

const tabHeight = 44;

const slideById = (id, str?: any) => {
  const target = document.getElementById(id)?.offsetTop;
  target && window.scrollTo({
    top: target - tabHeight + 5,
    behavior: str || 'smooth',
  });
};


const ExaminationExplain = (props) => {
  const [indicatorTypeList, setTypelist] = useState<any[]>([]);
  const [dataList, setData] = useState<any[]>([]);

  const tabClick = (i) => {
    const result = indicatorTypeList.map((item, index) => {
      // if(item.active) item.active = false;
      if (index === i) {
        slideById(item.propertyCode);
        // item.active = true;
      }
      return item;
    });
    setTypelist(result);
  };

  useEffect(() => {
    const { orderNo } = Deserialize(props.location.search);
    const { propertyCode } = Deserialize(props.location.search);
    fetchJson({
      url: '/api/api/v1/magic/mirror/queryDetectionInfoByOrderNo',
      type: 'POST',
      data: {
        orderNo,
      },
      isloading: false,
    }).then((res) => {
      if (res.code === '0') {
        const data = (res.result.detailList || []).filter((item) => item.icon && item.info);
        const typeList = data.map((item, index) => ({
          id: item.id,
          active: (item.propertyCode == propertyCode || (!propertyCode && index === 0)) ? true : false,
          selfId: `${item.propertyCode}${item.id}`,
          name: item.propertyName,
          propertyCode: item.propertyCode,
        }));
        setTypelist(typeList);
        setData(data);
        setTimeout(() => {
          slideById(propertyCode, 'auto');
          const selfId = typeList.find((ele) => ele.propertyCode === propertyCode)?.selfId;
          const offsetLeft = document.getElementById(selfId)?.offsetLeft || 10;
          document.getElementById('topTabs')?.scrollTo(offsetLeft - 10, 0);
        }, 50);
      }
    });
  }, []);

  const { run: handleScroll } = useThrottleFn(
    () => {
      let currentKey = (indicatorTypeList[0] || {}).propertyCode;

      for (const item of indicatorTypeList) {
        const element = document.getElementById(`${item.propertyCode}`);
        if (!element) {
          continue;
        }
        const rect = element.getBoundingClientRect();
        if (rect.top <= tabHeight) {
          currentKey = item.propertyCode;
        } else {
          break;
        }
      }

      const result = indicatorTypeList.map((item, index) => {
        if (item.active) {
          item.active = false;
        }
        if (item.propertyCode === currentKey) {
          item.active = true;
          const selfId = item.selfId;
          const offsetLeft = document.getElementById(selfId)?.offsetLeft || 10;
          document.getElementById('topTabs')?.scrollTo(offsetLeft - 120, 0);
        }
        return item;
      });
      setTypelist(result);
    },
    {
      leading: true,
      trailing: true,
      wait: 10,
    }
  );

  useEffect(() => {
    window.addEventListener('scroll', handleScroll);
    return () => {
      window.removeEventListener('scroll', handleScroll);
    };
  }, []);

  return (
    <div className='ExaminationExplain'>
      <div className='topTabs' id='topTabs'>
        {indicatorTypeList.map((item, index) => (
          <span
            key={index}
            id={`${item.selfId}`}
            className={item.active ? 'item active' : 'item'}
            onClick={() => tabClick(index)}>
            {item.name}
          </span>
        ))}
      </div>
      {dataList.map((item, index) => (
        item.icon && item.info &&
        <div className='tabContent' key={index} id={`${item.propertyCode}`}>
          <div className='topbox'>
            {(item.propertyValue && item.propertyCode !== 'ecg') ? (
              <div className='itemNumber'>
                <span className='number'>{item.propertyValue}</span>
                <span className='unit'>{item.propertyUnit}</span>
              </div>
            ) : item.propertyCode === 'ecg' ? null : emptyState()}
            {item.propertyCode == 'weight' && weightRender(item)}
            {item.propertyCode == 'ecg' && <EchartsEcg value={item.propertyValue} />}
          </div>
          <div className='title'>
            <img className='title-icon' src={`${cdnPrefix}${item.icon}`} alt='' />
            <div className='title-text'>{`关于${item.propertyName}`}</div>
          </div>
          <object className='desc'> {item.info} </object>
        </div>
      ))}
    </div>
  );
};

export default ExaminationExplain;
