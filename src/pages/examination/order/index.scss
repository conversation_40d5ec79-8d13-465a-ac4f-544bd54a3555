@import "src/style/index";

.ExaminationOrder {
  min-height: 100vh;
  background: #f5f5f5;

  &-patient {
    background: #fff;
    border-radius: r(7.5);
    margin: r(15) r(10);
    padding: r(18) r(15);

    &__title {
      font-size: r(16);
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 500;
      color: #000;
    }

    &__tag {
      margin-top: r(20);
      display: flex;
      flex-flow: row wrap;
    }

    &__item {
      background: rgba(0, 0, 0, 0.04);
      border: 1px solid rgba(0, 0, 0, 0.1);
      border-radius: r(28);
      font-size: r(14);
      padding: r(10) 0;
      text-align: center;
      min-width: r(86);
      margin-right: r(10);
      margin-top: r(10);

      &.active {
        background: rgba(236, 145, 49, 0.06);
        color: #ec9131;
        border: 1px solid rgba(236, 145, 49, 0.32);
      }
    }
  }

  &-equity {
    padding: 0 r(30);
    margin-top: r(30);
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: r(15);
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: rgba(0, 0, 0, 0.4);
  }

  &-footer {
    position: fixed;
    bottom: 0;
    width: 100%;
    background: #fff;
    box-shadow: 0 -30px 37px 0 rgba(0, 0, 0, 0.02);
    border-radius: r(10) r(10) 0 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: r(14) r(15);

    &__total {
      font-size: r(15);
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: rgba(0, 0, 0, 0.5);

      span {
        font-size: r(21);
        font-weight: 500;
        color: #ff5050;
        margin-left: r(6);
      }
    }

    &__discount {
      font-size: r(12);
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: rgba(0, 0, 0, 0.5);
      margin-top: r(1);
    }

    &__btn {
      background: #00bc70;
      border-radius: r(22);
      font-size: r(16);
      font-family: PingFangSC-Semibold, PingFang SC;
      font-weight: 600;
      color: #fff;
      padding: r(11) r(43);
    }
  }
}
