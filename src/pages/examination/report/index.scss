@import "src/style/index";

.ExaminationReport {
  min-height: 100vh;
  padding-bottom: r(82);
  background-color: #f8f8f8;

  &-header {
    width: 100vw;
    height: r(240);
    background: url(https://cdn-qcloud.zhongan.com/doctor/report/bg.png) no-repeat 100%;
    border-radius: 0 0 r(30);
    position: relative;

    &__container {
      position: absolute;
      width: calc(100vw - r(30));
      left: r(15);
      top: r(60);
      background-color: #fff;
      box-shadow: 0 -18px 32px 0 rgba(0, 0, 0, 0.04);
      border-radius: r(6);
      padding-bottom: r(15);
    }

    &__intro {
      margin-top: r(-35);
      text-align: center;
    }

    &__avatar {
      width: r(67);
      height: r(67);
      margin: 0 auto;

      img {
        background-color: #f0f0f0;
        border: 1px solid #efeef3;
        border-radius: 50%;
        height: 100%;
        width: 100%;
      }
    }

    &__person {
      font-size: r(13);
      font-family: PingFangSC-Regular, PingFang SC;
      color: #333;
      margin-top: r(5);
    }

    &__info {
      margin-top: r(15);
      display: flex;
      justify-content: space-around;
    }

    &__line {
      width: 1px;
      background: #e5e5e5;
    }

    &__item {
      font-size: r(23);
      font-family: PingFangSC-Semibold, PingFang SC;
      font-weight: 600;
      color: #333;

      &:nth-child(1) {
        margin-bottom: r(9);
      }
    }

    &__unit {
      font-size: r(13);
      font-weight: 400;
      color: #666;
    }

    &__design {
      font-size: r(13);
      font-family: PingFangSC-Regular, PingFang SC;
      color: #888;
      display: block;
      font-weight: 400;
    }
  }

  &-content {
    margin-top: r(55);
    padding: 0 15px;
  }

  &-probably {
    &__item {
      margin-top: r(30);
    }

    &__header {
      display: flex;
      justify-content: space-between;
    }

    &__title {
      font-size: r(18);
      font-family: PingFangSC-Semibold, PingFang SC;
      font-weight: 600;
      color: #333;
    }

    &__label {
      background: #e5f6ef;
      border-radius: r(8);
      font-size: r(14);
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 500;
      color: #00a864;
      margin-left: r(6);
      padding: r(1) r(10);
    }

    &__explain {
      font-size: r(14);
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #999;
      display: flex;
      align-items: center;
      justify-content: right;

      img {
        width: r(14);
        margin-left: 2px;
      }
    }

    &__desc {
      margin-top: r(12);
      font-size: r(14);
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #555;
    }

    &__score {
      font-size: r(25);
      font-family: PingFangSC-Semibold, PingFang SC;
      font-weight: 600;
      color: #00a864;
      margin-left: r(11);

      span {
        font-size: r(14);
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #999;
      }
    }

    &__plan {
      position: relative;
      margin-top: r(33);
    }

    &__indicator {
      width: r(12);
      position: absolute;
      top: r(-5);
      transform: translate(-100%, -100%);

      img {
        width: 100%;
      }
    }

    &__line {
      width: 100%;
      height: 5px;
      background: linear-gradient(90deg, rgba(238, 238, 238, 0) 0%, rgba(255, 202, 130, 0.56) 19%, rgba(113, 241, 187, 0.78) 65%, #9ee4ed 100%);
      border-radius: r(2.5);
    }

    &__scale {
      display: flex;
      justify-content: space-between;
      margin-top: r(4);

      span {
        font-size: r(12);
        font-family: PingFangSC-Regular, PingFang SC;
        color: #888;
      }
    }

    &__age {
      margin-top: r(20);
      display: flex;
      justify-content: space-between;

      &--item {
        display: flex;
        align-items: center;
        background: #fff;
        border-radius: r(5);
        padding: r(15) r(23);

        img {
          border-radius: 50%;
          width: r(25);
        }

        p {
          margin-left: r(8);
          font-size: r(14);
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          color: #464646;
        }

        span {
          font-size: r(18);
          font-weight: 600;
        }
      }
    }
  }

  &-norm {
    padding: 0 2px;

    &__title {
      font-size: r(15);
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 500;
      color: #666;
      margin-top: r(15);
    }

    &__detail {
      display: flex;
      justify-content: space-between;
      flex-flow: row wrap;

      &:after {
        content: '';
        width: 32%;
      }
    }

    &__item {
      background: #fff;
      box-shadow: 0 r(6) r(12) 0 rgba(0, 0, 0, 0.04);
      border-radius: r(6);
      border: 1px solid #e6e6e6;
      display: flex;
      align-items: center;
      flex-direction: column;
      margin-top: r(10);
      width: 32%;
      padding: r(22) 0 r(27);

      img {
        width: r(30);
      }

      p {
        font-size: r(14);
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #333;
        margin-top: r(10);
      }
    }

    &__state {
      font-size: r(11);
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      margin-top: r(1);
      color: #00a864;

      &.warn {
        color: #e64848;
      }
    }

    &__num {
      font-size: r(18);
      font-family: PingFangSC-Semibold, PingFang SC;
      font-weight: 600;
      color: #333;
      margin-top: r(1);

      &.none {
        font-size: r(12);
        font-weight: 400;
        margin-top: r(7.5);
      }

      span {
        font-size: r(12);
        font-weight: 400;
      }
    }

    &__ecg {
      display: flex;
      align-items: center;
      justify-content: space-between;
      background: #fff;
      box-shadow: 0 r(6) r(12) 0 rgba(0, 0, 0, 0.04);
      border-radius: r(6);
      border: 1px solid #e6e6e6;
      padding: r(20) 0;
      margin-top: r(10);
      width: 100%;

      .ExaminationEcg {
        width: 64%;
        text-align: center;
      }

      &--label {
        text-align: center;
        width: 32%;

        img {
          width: r(30);
        }

        p {
          font-size: r(14);
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          color: #333;
          margin-top: r(8);
        }
      }

      &--img {
        width: r(152);
        margin-right: r(37);
      }
    }
  }
}
