import React, { useEffect, useRef, useState } from 'react';
import './index.scss';
import classnames from 'classnames';
import _ from 'lodash';
import { useSelector } from 'react-redux';
import { ApplicationState } from 'src/store';
import { Deserialize, fetchJson } from 'src/utils';
import ExaminationBodyType, { ExaminationBodyTypeHandle } from '../components/BodyType';

const shapesSort = ['height', 'weight', 'bmi', 'bfr', 'tfr', 'bm', 'uvi', 'mr', 'pr', 'smr', 'bpm'];
const signsSort = ['temperature', 'heartbeat', 'breath', 'bloodOxygen', 'bloodPressure'];

const cdnPrefix = 'https://cdn-qcloud.zhongan.com/doctor/report/';

const midMan = require('../images/report/mid-man.png');
const midWoman = require('../images/report/mid-woman.png');

const ExaminationReport = (props) => {
  const examinationBodyType = useRef<ExaminationBodyTypeHandle>(Object.create(null));
  const [shapes, setShapes] = useState<any[]>([]);
  const [signs, setSigns] = useState<any[]>([]);
  const [data, setData] = useState<any>({});
  const { orderNo } = Deserialize(props.location.search);

  const { GENDER_OBJ } = useSelector((state: ApplicationState) => {
    const { GENDER_OBJ = {} } = state.dictionary;
    return {
      GENDER_OBJ,
    };
  });

  useEffect(() => {
    fetchJson({
      url: '/api/api/v1/magic/mirror/queryDetectionInfoByOrderNo',
      type: 'POST',
      data: {
        orderNo,
      },
      isloading: false,
    }).then((res) => {
      if (res.code === '0') {
        const data = _.keyBy(res.result.detailList, 'propertyCode');
        data.patientName = res.result.patientName;
        setData(data);
        const shapes: any[] = [];
        const signs: any[] = [];
        (res.result.detailList || []).forEach((item) => {
          if (shapesSort.includes(item.propertyCode)) {
            shapes.push(item);
          }
          if (signsSort.includes(item.propertyCode)) {
            signs.push(item);
          }
        });
        setSigns(signsSort.map((item) => _.find(signs, ['propertyCode', item]) || {
          temperature: {
            icon: 'wendu.png',
            propertyValue: '',
            propertyName: '体温',
          },
          heartbeat: {
            icon: 'xinlv.png',
            propertyValue: '',
            propertyName: '心率',
          },
          breath: {
            icon: 'huxi.png',
            propertyValue: '',
            propertyName: '呼吸',
          },
          bloodOxygen: {
            icon: 'xueyang.png',
            propertyValue: '',
            propertyName: '血氧',
          },
          bloodPressure: {
            icon: 'xueya.png',
            propertyValue: '',
            propertyName: '血压',
          },
        }[item]));
        setShapes(shapesSort.map((item) => _.find(shapes, ['propertyCode', item])));
      }
    });
  }, []);

  return (
    <div className='ExaminationReport'>
      <div className='ExaminationReport-header'>
        <div className='ExaminationReport-header__container'>
          <div className='ExaminationReport-header__intro'>
            <div className='ExaminationReport-header__avatar'>
              <img src={_.get(data, 'gender.propertyValue') === 'M' ? midMan : midWoman} alt='' />
            </div>
            <div className='ExaminationReport-header__person'>
              <span>{data.patientName}</span>&nbsp;&nbsp;<span>{GENDER_OBJ[_.get(data, 'gender.propertyValue')]}</span>&nbsp;&nbsp;<span>{_.get(data, 'age.propertyValue')}岁</span>
            </div>
          </div>
          <div className='ExaminationReport-header__info'>
            <div className='ExaminationReport-header__detail'>
              <div className='ExaminationReport-header__item'>
                <span>{_.get(data, 'weight.propertyValue')}</span>
                <span className='ExaminationReport-header__unit'>kg</span>
                <span className='ExaminationReport-header__design'>体重</span>
              </div>
              <div className='ExaminationReport-header__item'>
                <span>{_.get(data, 'height.propertyValue')}</span>
                <span className='ExaminationReport-header__unit'>cm</span>
                <span className='ExaminationReport-header__design'>身高</span>
              </div>
            </div>
            <div className='ExaminationReport-header__line'></div>
            <div className='ExaminationReport-header__detail'>
              <div className='ExaminationReport-header__item'>
                <span>{_.get(data, 'bmi.propertyValue')}</span><span className='ExaminationReport-header__design'>BMI</span>
              </div>
              <div className='ExaminationReport-header__item'>
                <span>{_.get(data, 'mr.propertyValue')}</span>
                <span className='ExaminationReport-header__unit'>%</span>
                <span className='ExaminationReport-header__design'>肌肉率</span>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div className='ExaminationReport-content'>
        <div className='ExaminationReport-probably'>
          <div className='ExaminationReport-probably__item'>
            <div className='ExaminationReport-probably__header'>
              <div className='ExaminationReport-probably__title'>
                健康评分
                <span className='ExaminationReport-probably__score'>{_.get(data, 'totalScore.propertyValue')}<span>分</span></span>
              </div>
            </div>
            <div className='ExaminationReport-probably__plan'>
              <div className='ExaminationReport-probably__indicator' style={{ left: _.get(data, 'totalScore.propertyValue') + '%' }}>
                <img src={require('../images/report/gps.png')} alt='' />
              </div>
              <div className='ExaminationReport-probably__line'></div>
              <div className='ExaminationReport-probably__scale'>
                {
                  [0, 20, 40, 60, 80, 100].map((item) => (
                    <span key={item}>{item}</span>
                  ))
                }
              </div>
            </div>
          </div>
          {/* <div className="ExaminationReport-probably__item">
            <div className="ExaminationReport-probably__header">
              <div className='ExaminationReport-probably__title'>
                体测年龄
              </div>
            </div>
            <div className="ExaminationReport-probably__desc">
              您比同龄人看上去更年轻，更健康。
            </div>
            <div className="ExaminationReport-probably__age">
              <div className="ExaminationReport-probably__age--item">
                <img src={cdnPrefix + '/age.png'} alt="" />
                <p>身体年龄<span>{String(Math.floor(_.get(data, 'monthAge.propertyValue') / 12))}</span>岁</p>
              </div>
              <div className="ExaminationReport-probably__age--item">
                <img src={cdnPrefix + '/zhenshi.png'} alt="" />
                <p>实际年龄<span>{_.get(data, 'age.propertyValue')}</span>岁</p>
              </div>
            </div>
          </div> */}
          <div className='ExaminationReport-probably__item'>
            <div className='ExaminationReport-probably__header'>
              <div className='ExaminationReport-probably__title'>
                详细指标
              </div>
              <div className='ExaminationReport-probably__explain' onClick={() => props.history.push({
                pathname: '/hospital/examination/explain',
                search: `orderNo=${orderNo}`,
              })}>
                <span>更多详情</span>
                <img src={require('../images/icon_arrow_right.svg')} />
              </div>
            </div>
            <div className='ExaminationReport-norm'>
              <div className='ExaminationReport-norm__title'>身体形态指标</div>
              <div className='ExaminationReport-norm__detail'>
                {
                  !!shapes.length && shapes.map((item, idx) => (
                    <div key={idx} className='ExaminationReport-norm__item' onClick={() => props.history.push({
                      pathname: '/hospital/examination/explain',
                      search: `orderNo=${orderNo}&propertyCode=${item.propertyCode}`,
                    })}>
                      <img src={cdnPrefix + item.icon} alt='' />
                      <p>{item.propertyName}</p>
                      {
                        item.propertyValue ?
                          <React.Fragment>
                            <div className='ExaminationReport-norm__num'>
                              {item.propertyValue}<span>{item.propertyUnit}</span>
                            </div>
                            <div className={classnames('ExaminationReport-norm__state', {
                              warn: !item.isNormal,
                            })}>
                              {item.propertyLevelText}
                            </div>
                          </React.Fragment> : <span className='ExaminationReport-norm__num none'>无</span>
                      }
                    </div>
                  ))
                }
              </div>
            </div>
            <div className='ExaminationReport-norm'>
              <div className='ExaminationReport-norm__title'>基本特征指标</div>
              <div className='ExaminationReport-norm__ecg' onClick={() => props.history.push({
                pathname: '/hospital/examination/explain',
                search: `orderNo=${orderNo}&propertyCode=ecg`,
              })}>
                <div className='ExaminationReport-norm__ecg--label'>
                  <img src={cdnPrefix + _.get(data, 'ecg.icon')} alt='' />
                  <p>心电</p>
                </div>
                <img className='ExaminationReport-norm__ecg--img' src={require('../images/report/heartbeat.png')}></img>
              </div>
              <div className='ExaminationReport-norm__detail'>
                {
                  !!signs.length && signs.map((item, idx) => (
                    <div key={idx} className='ExaminationReport-norm__item' onClick={() => props.history.push({
                      pathname: '/hospital/examination/explain',
                      search: `orderNo=${orderNo}&propertyCode=${item.propertyCode}`,
                    })}>
                      <img src={cdnPrefix + item.icon} alt='' />
                      <p>{item.propertyName}</p>
                      {
                        item.propertyValue ?
                          <React.Fragment>
                            <div className='ExaminationReport-norm__num'>
                              {item.propertyValue}<span>{item.propertyUnit}</span>
                            </div>
                            <div className={classnames('ExaminationReport-norm__state', {
                              warn: !item.isNormal,
                            })}>
                              {item.propertyLevelText}
                            </div>
                          </React.Fragment> : <span className='ExaminationReport-norm__num none'>无</span>
                      }
                    </div>
                  ))
                }
              </div>
            </div>
          </div>
        </div>
      </div>
      <ExaminationBodyType ref={examinationBodyType}></ExaminationBodyType>
    </div>
  );
};

export default ExaminationReport;
