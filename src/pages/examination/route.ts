/*
 * @Author: sen.lv <EMAIL>
 * @Date: 2022-11-16 13:58:34
 * @LastEditTime: 2022-12-13 17:16:46
 * @LastEditors: hou
 * @FilePath: /za-asclepius-patient-h5/src/pages/examination/route.ts
 * @Description:
 */
/*
 * @description：  魔镜体检
 */
const route = [
  {
    path: '/hospital/examination/report',
    name: 'ExaminationReport',
    component: () => import(/* webpackPrefetchPlaceHolder */ './report'),
    auth: false,
    exact: true,
    title: '健康报告',
  },
  {
    path: '/hospital/examination/entry',
    name: 'ExaminationEntry',
    component: () => import(/* webpackPrefetchPlaceHolder */ './entry'),
    auth: false,
    exact: true,
    title: '检测',
  },
  {
    path: '/hospital/examination/order',
    name: 'ExaminationOrder',
    component: () => import(/* webpackPrefetchPlaceHolder */ './order'),
    auth: false,
    exact: true,
    title: '下单',
  },
  {
    path: '/hospital/examination/explain',
    name: 'ExaminationExplain',
    component: () => import(/* webpackPrefetchPlaceHolder */ './explain'),
    auth: false,
    exact: true,
    title: '身体形态指标',
  },
];
export default route;

