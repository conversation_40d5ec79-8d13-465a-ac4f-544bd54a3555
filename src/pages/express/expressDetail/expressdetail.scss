@import "src/style/index";

.expressdetail_page {
  .head-cont {
    background-color: #fff;
    padding: r(20) r(15);
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: r(16);

    .express-company {
      margin-bottom: r(5);
      color: #1e1e1e;
      font-weight: bold;
    }

    .express-no {
      font-size: r(15);
    }

    .express-status {
      font-size: r(15);
    }
  }

  .head {
    @include display-flex;
    @include align-items(center);

    padding: r(15);

    .photo {
      width: r(80);
      height: r(80);
      min-width: r(80);
      min-height: r(80);
      background: #e6e6e6;
      border-radius: r(5);
      margin-right: r(20);
    }

    .text {
      font-size: r(14);
      color: #464646;
      padding: r(2) 0;

      .left {
        color: #9b9b9b;
        padding-right: r(2);
      }
    }
  }

  .process {
    position: relative;
    padding: r(20) r(15);

    .inquiry-item {
      min-height: r(60);
      padding-bottom: r(10);
      @include display-flex;

      position: relative;

      .pot {
        position: absolute;
        border-radius: 50%;
        background-color: #e6e6e6;
        width: r(6);
        height: r(6);
        left: r(3);
        top: r(6);
        z-index: 2;
      }

      .time_wrapper {
        min-width: r(24);
      }

      .inquiry_info {
        @include flex;

        color: #999;
        font-size: r(13);
        word-break: break-all;

        .head {
          padding: 0;
          font-size: r(15);
          line-height: r(21);
          margin-bottom: r(5);
        }

        .date {
          color: #b2b2b2;
          margin-top: r(5);
          margin-right: r(5);
        }
      }

      &.start {
        .inquiry_info {
          .head {
            color: var(--theme-primary);
          }

          .body {
            color: #666;
          }
        }

        .pot {
          position: absolute;
          border-radius: 50%;
          background-color: #fff;
          width: r(10);
          height: r(10);
          border: r(3) solid var(--theme-primary);
          left: r(1);
          top: r(6);
          z-index: 2;
        }

        &::after {
          content: '';
          position: absolute;
          height: 100%;
          // height: inherit;
          width: r(1);
          background: var(--theme-primary);
          left: r(5.5);
          top: r(8);
        }
      }

      &.progress {
        &::after {
          content: '';
          position: absolute;
          height: 100%;
          width: r(1);
          background: #e6e6e6;
          left: r(5.5);
          top: r(6);
        }
      }

      &.end {
        &::after {
          display: none;
        }
      }

      &.one {
        .pot {
          position: absolute;
          border-radius: 50%;
          background-color: #fff;
          width: r(13);
          height: r(13);
          border: r(3) solid var(--theme-primary);
          box-shadow: 0 2px 3px 0 rgba(140, 230, 200, 1);
          left: r(1);
          top: 0;
          z-index: 2;
        }

        &::after {
          content: none;
        }
      }
    }
  }

  .no-data {
    text-align: center;
  }

  .no-data-img {
    width: r(115);
    height: r(115);
    margin: r(33) auto 0;
  }

  .tips {
    font-size: r(14);
    margin-top: r(10);
    color: #666;
  }
}
