import classnames from 'classnames';
import React, { useState } from 'react';
import { shallowEqual, useSelector } from 'react-redux';
import { Card } from 'src/components/common';
import { ApplicationState } from 'src/store';
import './expressdetail.scss';

const ExpressDetail = (props) => {
  const { location: { state = {} } } = props;
  const [orderType] = useState(state.orderType);
  const [expressId] = useState(state.expressId);
  const { express } = useSelector((state: ApplicationState) => {
    const { detail: { exprssLists = [] } = {} } = state.order;

    return {
      express: exprssLists.find((e) => e.id === expressId) || {},
    };
  }, shallowEqual);

  const { EXPRESSSTATUS_OBJ }: any = useSelector((state: ApplicationState) => ({
    EXPRESSSTATUS_OBJ: state.dictionary.EXPRESSSTATUS_OBJ,
  }));

  const { expressLogisticsList = [] } = express || {};

  return <div className='expressdetail_page'>
    <div className='head-cont'>
      <div className='left-cont'>
        <div className='express-company'>{express.expressCompanyName || express.expressCompany}</div>
        {(orderType != 'serviceOrder' && orderType != 'sellingServpack') && <div className='express-no'>{express.expressNo}</div>}
      </div>
      {
        (orderType != 'serviceOrder' && orderType != 'sellingServpack' && express.expressStatus) && <div className='express-status'>{EXPRESSSTATUS_OBJ[express.expressStatus]}</div>
      }
      {
        (orderType == 'serviceOrder' || orderType == 'sellingServpack') && <div className='express-no'>{express.expressNo}</div>
      }

    </div>
    {
      expressLogisticsList.length ? (
        <Card prefixCls='process'>
          {expressLogisticsList.map((item: any, index: number) => <div className={classnames(
            'inquiry-item',
            { progress: index != 0 && index != (expressLogisticsList.length - 1) },
            { start: index == 0 },
            { end: index == expressLogisticsList.length - 1 },
            { one: expressLogisticsList.length == 1 },
          )} key={`inquiry-item-${index}`}>
            <div className='pot'></div>
            <div className='time_wrapper'></div>
            <div className='inquiry_info'>
              <div className='head'>{item.channelLogisticsName}</div>
              <div className='body'>{item.logisticsContext}</div>
              <div className='date'>{item.receiptTime}</div>
              {/* <div className='time'>{format.date(item.receiptTime, 'hh:mm')}</div> */}
            </div>
          </div>)
          }
        </Card>
      ) : (
        <div className='no-data'>
          <div className='no-data-img'>
            <img src={require('./images/no-data.png')} alt='' />
          </div>
          <div className='tips'>
            如需查看物流过程，请到对应的物流公司查询
          </div>

        </div>
      )
    }


  </div>;
};

export default ExpressDetail;
