@import "src/style/index";

.expresslist_page {
  .express {
    padding: r(15);

    .head {
      position: relative;
      @include display-flex;
      @include align-items(center);
      @include justify-content(space-between);

      padding-bottom: r(15);
      border-bottom: r(1) solid #e6e6e6;

      & > p:first-of-type {
        color: #464646;
        font-size: r(15);
      }

      & > p:last-of-type {
        color: #9b9b9b;
        font-size: r(14);
        padding-right: r(10);

        &::after {
          position: absolute;
          content: '';
          right: 0;
          top: r(6);
          width: r(8);
          height: r(8);
          border-top: r(2) solid #9b9b9b;
          border-right: r(2) solid #9b9b9b;
          transform: rotate(45deg);
        }
      }
    }

    .photo_wrapper {
      padding-top: r(10);

      .good_photo {
        background: #e6e6e6;
        width: r(80);
        height: r(80);
        min-width: r(80);
        min-height: r(80);
        border-radius: r(5);
        margin: r(10);
      }
    }
  }
}

.not-expresslist {
  text-align: center;
  padding-top: r(100);
  line-height: r(26);
  font-size: r(15);

  .sprite-icon--img {
    width: 80px;
  }
}
