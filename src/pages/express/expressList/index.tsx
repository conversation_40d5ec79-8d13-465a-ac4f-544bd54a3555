import React, { useCallback, useEffect } from 'react';
import { shallowEqual, useSelector, useDispatch } from 'react-redux';
import { Card, SvgIcon } from 'src/components/common';
import { ApplicationState } from 'src/store';
import { fetch_order_detail } from 'src/store/order/action';
import { Deserialize } from 'src/utils/serialization';
import { NoticeBar } from 'zarm';
import './expresslist.scss';

const ExpressList = (props) => {
  const { location: { search = '' } } = props;
  const { orderId = '', from = '', type = '' } = Deserialize(search);

  const { orderDetail, express } = useSelector((state: ApplicationState) => {
    const { detail = {} } = state.order;
    return {
      orderDetail: detail,
      express: detail.exprssLists || [],
    };
  }, shallowEqual);

  const { EXPRESSSTATUS_OBJ }: any = useSelector((state: ApplicationState) => ({
    EXPRESSSTATUS_OBJ: state.dictionary.EXPRESSSTATUS_OBJ,
  }));

  const dispatch = useDispatch();
  const fetchOrderDetail = useCallback((options: any, onSuccess?: any) => dispatch(fetch_order_detail(options, onSuccess)), [dispatch]);

  useEffect(() => {
    if (orderDetail.id != orderId || from !== 'otp') {
      fetchOrderDetail({
        orderId,
        option: {
          isQueryExpressLogistics: true,
          isQueryDeliveryAddress: true,
          isQueryGoods: true,
        },
      }, (o) => {
        const express = o.exprssLists;
        if(express && express.length === 1){
          const { expressGoodsList = [] } = express[0];
          const { orderGoods = [] } = orderDetail;
          const _goodsList: any[] = [];
          expressGoodsList.length && expressGoodsList.forEach((good) => {
            const item = orderGoods.find((i) => (good.goodsCode && i.goodsCode === good.goodsCode)) || {};
            _goodsList.push(item);
          });
          toExpressDetail(express[0], _goodsList, 'replace');
        }
      });
    }
  }, []);

  const toExpressDetail = useCallback((express, _goodsList, navigatorType) => {
    if (type === 'video') {
      props.history[navigatorType]({
        pathname: '/hospital/expressdetail',
        state: {
          expressId: express.id,
          photo:  _goodsList && _goodsList.length > 0  && _goodsList[0].goodsEntry.drugsPictureList.length && _goodsList[0].goodsEntry.drugsPictureList[0].attachment.thirdAttachmentDownloadUrl || '',
        },
      });
    } else {
      props.history[navigatorType]({
        pathname: '/hospital/expressdetail',
        state: {
          orderType: orderDetail.orderType, // 服务包和服务订单物流详情页面显示不一样
          expressId: express.id,
          photo: `${ _goodsList  && _goodsList.length > 0  && _goodsList[0].goodsEntry.drugsPictureList.length && _goodsList[0].goodsEntry.drugsPictureList[0].attachment.attachmentDownloadUrl}` || '',
        },
      });
    }
  }, [orderDetail]);

  if (!orderDetail.orderStatus || !express.length) {
    return (<div className='not-expresslist'>
      <SvgIcon type='img' src={require('./images/express-icon.svg')} />
      <p>暂无物流信息</p>
    </div>);
  }

  return <div className='expresslist_page'>
    <div className='head'>
      <NoticeBar>{express.length}个包裹已发出</NoticeBar>
      {express.map((express) => {
        const { expressGoodsList = [] } = express;
        const { orderGoods = [] } = orderDetail;
        const _goodsList: any[] = [];
        expressGoodsList.length && expressGoodsList.forEach((good) => {
          const item = orderGoods.find((i) => (good.goodsCode && i.goodsCode === good.goodsCode)) || {};
          _goodsList.push(item);
        });

        return <Card prefixCls='express' key={`express_${express.id}`} onClick={() => toExpressDetail(express, _goodsList, 'push')}>
          <div className='head'>
            {express.expressStatus ? <div className='div'>
              <p>{EXPRESSSTATUS_OBJ[express.expressStatus]}</p>
            </div> : null}
            <p>{express.expressCompany} {express.expressNo}</p>
          </div>
          <div className='photo_wrapper'>
            {_goodsList.map((good) => {
              const { goodsEntry: { drugsPictureList = [] } = {} } = good || {};
              let thumbnail = '';
              if (type === 'video') {
                thumbnail = (!!drugsPictureList.length && drugsPictureList[0].attachment && drugsPictureList[0].attachment.thirdAttachmentDownloadUrl) || '';
                return thumbnail ? <SvgIcon type='img' key={`good_${good.id}`} className='good_photo' src={thumbnail} /> : <SvgIcon className='good_photo' src={require('src/svgs/sprite-icon-drug.svg')} />;
              } else {
                thumbnail = !!drugsPictureList.length && drugsPictureList[0].attachment && `${drugsPictureList[0].attachment.attachmentDownloadUrl}` || '';
                return <SvgIcon type='img' key={`good_${good.id}`} className='good_photo' src={thumbnail} />;
              }
              // return <img className='good_photo' key={`good_${good.id}`} src={`${good && good.goodsEntry && good.goodsEntry.drugsPictureList.length && good.goodsEntry.drugsPictureList[0].attachment.attachmentDownloadUrl}`} alt="" />
            })}
          </div>
        </Card>;
      })}
    </div>
  </div>;
};

export default ExpressList;
