/*
 * @Author: <EMAIL> <EMAIL>
 * @Date: 2021-10-29 10:40:47
 * @LastEditTime: 2023-03-03 13:57:51
 * @LastEditors: <EMAIL> <EMAIL>
 * @FilePath: /za-asclepius-patient-h5/src/pages/express/route.tsx
 * @Description:
 */
/*
 * @authors :Bin <PERSON>
 * @description：  患者管理模块路由
 */
const route = [
  {
    path: '/hospital/expresslist',
    name: 'ExpressList',
    component: () => import(/* webpackPrefetchPlaceHolder */ './expressList'),
    auth: false,
    exact: true,
    title: '物流信息',
  }, {
    path: '/hospital/expressdetail',
    name: 'ExpressDetail',
    component: () => import(/* webpackPrefetchPlaceHolder */ './expressDetail'),
    auth: false,
    exact: true,
    title: '物流详情',
  },
];
export default route;

