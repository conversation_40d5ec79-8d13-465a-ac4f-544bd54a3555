import React, { useCallback, useState } from 'react';
import { SvgIcon } from 'src/components/common';
import { questionsList, questionIdMap } from '../../staticData';
import { CDN_PREFIX } from 'src/utils/staticData';
import './questions.scss';

const prefixCls = 'comp_questions';

const Index = (props) => {

  const [openMap, setOpenMap]: any = useState(questionIdMap);
  const [allOpen, setAllOpen]: any = useState(false);

  const toogle = useCallback((id) => {
    const obj = {
      ...openMap, 
      [id]: !openMap[id]
    };
    setOpenMap(obj);
    //判断是否全部关闭或者全部打开，然后setAllOpen
    let isAllOpen = true, isAllClosed = true;
    for (const i in obj) {
      if (obj[i]) {
        isAllClosed = false;
      } else {
        isAllOpen = false;
      }
    };
    isAllOpen && setAllOpen(true);
    isAllClosed && setAllOpen(false);
  }, [openMap]);

  const toogleAll = () => {
    
    setAllOpen(!allOpen);
    // const obj = {};
    // for (const i in questionIdMap) {
    //   obj[i] = !allOpen
    // }
    // setOpenMap(obj);  
  }
  return (
    <div className={`${prefixCls}`}>
      <img className="imgs" src={`${CDN_PREFIX}images/cjwt.png`} />
      {
        questionsList.map(({ id, q, a }) => {
          return <div className={`question_item ${openMap[id] ? 'open' : ''} ${(!allOpen && id == 4) ? 'hide':''}`} key={`question${id}`} >
            <SvgIcon className='question_icon' type='img' src={require('src/svgs/icon_question.svg')} />
            <p className='question_q' onClick={() => toogle(id)}>
              {q}
              <SvgIcon className='icon_arrow' src={require('src/svgs/sprite-icon_arrow.svg')} />
            </p>
            <p className='question_a'>{a}</p>
          </div>
        })
      }
      <div className={`btn_all_toogle ${allOpen ? 'open' : ''}`} onClick={toogleAll}>
        <span>{allOpen ? '收起全部' : '展开全部'}</span>
        <SvgIcon className='icon_all_arrow ' src={require('src/svgs/sprite-icon_arrow.svg')} />
      </div>
    </div>
  );
}

export default Index;