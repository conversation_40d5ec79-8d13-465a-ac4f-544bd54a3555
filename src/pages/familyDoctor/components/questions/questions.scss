@import "src/style/index";
$prefixCls: 'comp_questions';

.#{$prefixCls} {
  background: #fff;
  box-shadow: 0 r(6) r(9) r(1) rgba(0, 0, 0, 0.05);
  border-radius: r(8);
  padding: r(20) 0;
  margin: 0 r(10);

  .imgs {
    padding-bottom: r(20);
  }

  .question_item {
    position: relative;
    margin-bottom: r(10);
    padding: 0 r(15) 0 r(43);
    font-size: r(14);
    color: #999;

    .question_icon {
      position: absolute;
      width: r(24);
      height: r(21);
      left: r(13);
    }

    .question_q {
      position: relative;
      padding-right: r(15);
      font-size: r(14);
      font-weight: bold;
      color: #333;
    }

    .icon_arrow {
      position: absolute;
      width: r(5);
      height: r(10);
      right: r(0);
      font-size: r(12);
      top: r(4);
      transform: rotate(90deg);
      transition: all 0.3s;

      --arrow-color: #d6d6d6;
    }

    .question_a {
      display: none;
      padding: r(5) 0 0;
    }

    .hide {
      display: none;
    }

    &.open {
      margin-bottom: r(10);

      .icon_arrow {
        transform: rotate(-90deg);
      }

      .question_a {
        display: block;
      }
    }
  }

  .btn_all_toogle {
    width: r(80);
    margin: 0 auto;
    color: var(--theme-primary);
    font-size: r(14);

    .icon_all_arrow {
      width: r(5);
      height: r(10);
      margin-left: r(3);
      font-size: r(12);
      transform: rotate(90deg);
      transition: all 0.3s;

      --arrow-color: var(--theme-primary);
    }

    &.open {
      .icon_all_arrow {
        transform: rotate(-90deg) translateX(r(2));
      }
    }
  }
}
