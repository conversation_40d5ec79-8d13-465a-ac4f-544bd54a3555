@import "src/style/index";
$prefixCls: 'family-doctor-detail-pages';

.#{$prefixCls} {
  min-height: 100vh;
  padding-bottom: r(80);
  background: #f5f5f5;

  .header {
    height: r(288);
    position: relative;
    font-size: 0;

    .life_photo {
      width: 100%;
      height: 100%;
    }

    .header_content {
      position: relative;
      bottom: r(84);
      height: r(84);
      margin: 0 r(15);
      padding: 0 r(20) 0 r(27);
      border-radius: r(8) r(8) 0 0;
      background: rgba(0, 0, 0, 0.59);
      border: r(1) solid rgba(255, 245, 227, 0.8);
      border-bottom: none;
      color: rgba(255, 255, 255, 0.7);
      font-size: r(12);
      @include display-flex;
      @include align-items(center);
      @include justify-content(space-between);

      &::before,
      &::after {
        content: '';
        position: absolute;
        height: r(77);
        width: r(1);
        top: r(6);
        background: linear-gradient(180deg, rgb(255, 245, 227, 0), rgba(0, 0, 0, 0.8));
      }

      &::before {
        left: r(-1);
      }

      &::after {
        right: r(-1);
      }

      .title {
        font-size: r(16);
        font-weight: bold;
        color: rgba(255, 232, 190, 0.7);
        margin-bottom: r(5);
      }

      .doctor_name {
        font-weight: 600;
        font-size: r(21);
        margin-right: r(5);
        color: #ffe8be;
      }

      .icon_badge {
        width: r(74);
        height: r(72);
      }
    }
  }

  .intro_wrap {
    width: r(345);
    border-radius: r(8);
    margin: r(10) auto 0;
    font-size: r(13);
    color: #666;

    .title {
      font-size: r(16);
      font-weight: bold;
      color: #000;
    }

    &.doctor_intro {
      padding: r(18) r(15);

      .intro_item {
        position: relative;
        padding-left: r(25);
        margin-top: r(16);
        word-break: break-all;
      }

      .intro_item_img {
        position: absolute;
        width: r(18);
        height: r(18);
        left: 0;
        top: r(2);
      }

      .intro_title {
        font-size: r(15);
        font-weight: bold;
        margin-bottom: r(5);
        color: #333;
      }

      .noellipse_hide {
        position: absolute;
        top: 0;
        z-index: -1;
        opacity: 0;
      }

      .ellipse {
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 3;
        -webkit-box-orient: vertical;
      }
    }

    .btn_more_wrap {
      text-align: center;
    }

    .btn_more {
      position: relative;
      display: inline-block;
      padding-right: r(13);
      margin-top: r(20);

      &::before,
      &::after {
        content: '';
        position: absolute;
        border-left: 2px solid #bbb;
        border-bottom: 2px solid #bbb;
        transform: rotate(-45deg) scale(0.8);
        height: r(9);
        width: r(9);
        right: r(0);
        top: r(1);
      }

      &::after {
        opacity: 0.5;
        top: r(7);
      }
    }
  }

  .footer {
    height: r(91);
    line-height: r(91);
    text-align: center;
    @include iphone-bottom-fixed;

    .img_footer {
      width: r(216);
    }
  }

  .btn_wrap {
    position: fixed;
    background: #fff;
    left: 0;
    right: 0;
    bottom: 0;
    padding: r(10) r(15) r(25);

    .btns_div {
      overflow: hidden;
      border-radius: r(22);
      border: 1px solid var(--theme-primary);

      @include display-flex;
    }

    .btn_choose,
    .btn_sign {
      font-size: r(17);
      border-radius: 0;
      border: none;
      color: var(--text-base-color);

      @include flex(1);
    }

    .btn_sign,
    .btn_chat {
      font-weight: bold;
      color: #fff;
    }

    .icon_chat {
      width: r(17);
      height: r(16);
      margin-right: r(5);
      vertical-align: r(-2);
    }
  }

  &__modal {
    .text_content {
      font-size: r(16);
      font-weight: bold;
      padding: 0 r(20);
      color: #1e1e1e;
    }

    .doctor_name {
      color: var(--text-base-color);
    }
  }
}
