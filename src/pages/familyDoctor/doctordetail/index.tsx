import React, { useCallback, useEffect, useLayoutEffect, useRef, useState } from 'react';
import { useSelector, shallowEqual, useDispatch } from 'react-redux';
import { BrandSlogan, Card, StaticToast } from 'src/components/common';
import { ApplicationState } from 'src/store';
import { fetchJson } from 'src/utils/fetch';
import { Deserialize } from 'src/utils/serialization';
import './detail.scss';
import { Button, Modal } from 'zarm';
import { fetch_family_doctor_list, fetch_family_doctor_signDoctor } from 'src/store/familydoctor/action';
import { docorInfoMap } from 'src/pages/doctor/doctorIntro/staticData';

const prefixCls = 'family-doctor-detail-pages';

const DoctorDetail = (props) => {
  // const cdnDoctorQrcode: string = 'http://cdn-qcloud.zhongan.com/a00000/za-asclepius/enterprise-wechat/6662.png';
  const { location: { search = '' } } = props;
  const { staffNo = '' } = Deserialize(search);
  const [showMore, setShowMore] = useState(false);
  const [doctorInfo, setDoctorInfo]: any = useState({});

  const ellipseRef: any = useRef(null);
  const noEllipseRef: any = useRef(null);

  const { PROFESSIONALTITLE_OBJ, doctorList, isBindDoctor }: any = useSelector((state: ApplicationState) => {
    return {
      PROFESSIONALTITLE_OBJ: state.dictionary.PROFESSIONALTITLE_OBJ,
      ...state.familyDoctor
    }
  }, shallowEqual);

  const dispatch = useDispatch();
  const fetchFamilyDoctorList = (onSuccess?: any) => dispatch(fetch_family_doctor_list(onSuccess));
  const fetchFamilyDoctorSignDoctor = (data, onSuccess?: any) => dispatch(fetch_family_doctor_signDoctor(data, onSuccess));

  useEffect(() => {
    if (staffNo) {
      fetchJson({
        url: '/api/api/v1/patient/doctor/detail',
        type: 'POST',
        data: {
          staffNo: staffNo,
          option: {
            isQueryHeadPortrait: true,
            isQueryAttachment: true,
          },
        },
        isloading: false,
      }).then((res) => {
        if (res && res.code === '0') {
          dealDoctorInfo(res.result || {});
        }
      });
    } else {
      if (doctorList.length) {
        dealDoctorInfo(doctorList[0]);
      } else {
        fetchFamilyDoctorList((res) => {
          dealDoctorInfo(res.result[0] || {});
        });
      }
    }

  }, []);

  const dealDoctorInfo = useCallback((doctorInfo = {}) => {
    const { attachmentList = [] } = doctorInfo;
    const info: any = {};
    attachmentList.map(({ attachmentType, attachmentDownloadUrl }) => {
      info[attachmentType] = attachmentDownloadUrl;
    })
    setDoctorInfo({
      ...doctorInfo,
      ...info,
    });
  }, []);

  // const toChat = useCallback(() => {
  //   props.history.push({
  //     pathname: "/hospital/chatmedicalmanage",
  //   });
  // }, []);

  const moreInfo = docorInfoMap[staffNo] || {};

  useLayoutEffect(() => {
    if (doctorInfo.staffName) {
      console.log('-----------', noEllipseRef.current.clientHeight, ellipseRef.current.clientHeight);
      if (noEllipseRef.current.clientHeight > ellipseRef.current.clientHeight) {
        setShowMore(false);
      } else {
        setShowMore(true);
      }
    }
  }, [doctorInfo]);

  const signedDoctor = useCallback(() => {
    Modal.confirm({
      className: `${prefixCls}__modal`,
      width: '80%',
      content: <p className='text_content'>确认签约<span className='doctor_name'>{doctorInfo.staffName}医生</span>为您的家庭医生吗？</p>,
      cancelText: '取消',
      okText: '确认',
      onCancel: () => {
        console.log('点击cancel');
      },
      onOk: () => {
        if (!doctorInfo.id) {
          StaticToast.error('请先选择医生');
          return
        }
        fetchFamilyDoctorSignDoctor({
          doctorId: doctorInfo.id
        }, () => {
          StaticToast.success('签约成功');
        })
      },
    });
  }, [doctorInfo]);

  const chooseOtherDoctor = useCallback(() => {
    props.history.replace({
      pathname: '/hospital/familydoctor/list',
    })
  }, []);

  const toChat = useCallback(() => {
    props.history.push({
      pathname: '/hospital/chatfamilydoctor',
    })
  }, []);

  const { staffName = '', staffProfessionalTitle = '', workSeniority = '', staffSkills = '', staffIntroduction = '', lifePhoto = '' } = doctorInfo;

  console.log('--moreInfo--', moreInfo);
  return (
    <div className={`${prefixCls}`}>

      <header className="header">
        <img className="life_photo" src={`${lifePhoto}`} />
        <div className="header_content">
          <div>
            <p className="title"><span className="doctor_name">{staffName}</span>家庭医生</p>
            <p>{PROFESSIONALTITLE_OBJ[staffProfessionalTitle]}｜从医{workSeniority}年</p>
          </div>
          <img className="icon_badge" src={require('src/images/icon_family_doctor_badge.png')} />
        </div>
      </header>
      <Card prefixCls="intro_wrap doctor_intro">
        <p className="title">医生介绍</p>
        <div className="intro_item">
          <img className="intro_item_img" src={require('src/images/icon_doctor_professional.png')} />
          <p className="intro_title">专业资质</p>
          <p>{moreInfo.professional || '全科金牌医生'}</p>
        </div>
        <div className="intro_item">
          <img className="intro_item_img" src={require('src/images/icon_doctor_field.png')} />
          <p className="intro_title">擅长领域</p>
          <p>{moreInfo.staffSkills || staffSkills}</p>
        </div>
        <div className="intro_item">
          <img className="intro_item_img" src={require('src/images/icon_doctor_experience.png')} />
          <p className="intro_title">执业经历</p>
          <p className={`${showMore ? '' : 'ellipse'}`} ref={ellipseRef}>{moreInfo.experience || staffIntroduction}</p>
          <p className="noellipse_hide" ref={noEllipseRef}>{moreInfo.experience || staffIntroduction}</p>
        </div>
        {
          !showMore && doctorInfo.staffName && <div className='btn_more_wrap'>
            <p className="btn_more" onClick={() => {
              setShowMore(true);
            }}>查看更多</p>
          </div>
        }
      </Card>
      <BrandSlogan />
      <div className='btn_wrap'>
        {
          isBindDoctor ?
            <Button block theme='primary' shape='round' onClick={toChat} className='btn_chat'>
              <img className='icon_chat' src={require('src/svgs/icon_chat.svg')} />
              问医生
            </Button>
            : staffNo ?
              <Button block theme='primary' shape='round' onClick={signedDoctor} className='btn_chat'>
                <img className='icon_chat' src={require('src/svgs/icon_chat.svg')} />
                签约并使用服务
              </Button>
              : <div className="btns_div">
                <Button className='btn_choose' onClick={chooseOtherDoctor}>选择其他医生</Button>
                <Button className='btn_sign' theme='primary' onClick={signedDoctor}>签约并使用服务</Button>
              </div>
        }
      </div>
    </div>
  );
}

export default DoctorDetail;
