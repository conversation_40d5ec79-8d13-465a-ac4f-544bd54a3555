import React, { useCallback, useEffect } from 'react';
import { shallowEqual, useDispatch, useSelector } from 'react-redux';
import { docorInfoMap } from 'src/pages/doctor/doctorIntro/staticData';
import { ApplicationState } from 'src/store';
import { fetch_family_doctor_list } from 'src/store/familydoctor/action';
// import { fetchJson } from 'src/utils';
import './list.scss';

const prefixCls = 'family-doctor-list-pages';

const List = (props) => {

  const { PROFESSIONALTITLE_OBJ, DEPARTMENT_OBJ, doctorList }: any = useSelector((state: ApplicationState) => {
    return {
      PROFESSIONALTITLE_OBJ: state.dictionary.PROFESSIONALTITLE_OBJ,
      DEPARTMENT_OBJ: state.department.obj,
      ...state.familyDoctor
    }
  }, shallowEqual);

  const dispatch = useDispatch();
  const fetchFamilyDoctorList = (onSuccess?: any) => dispatch(fetch_family_doctor_list(onSuccess));


  useEffect(() => {
    !doctorList.length && fetchFamilyDoctorList();
  }, []);

  const toDetail = useCallback((staffNo) => {
    props.history.push({
      pathname: '/hospital/familydoctor/detail',
      search: `staffNo=${staffNo}`
    })
  }, []);

  return (
    <div className={`${prefixCls}`}>
      <div className={`${prefixCls}__list`}>
        <p className="title">请选择1位医生呵护您的健康</p>
        {
          doctorList.map(({ id, staffName, staffNo, firstPageHeadPortrait, staffProfessionalTitle, firstWorkOrgName, workDepartment, staffSkills }) => {
            const moreInfo = docorInfoMap[staffNo] || {};
            return <div className="doctor_item" key={`doctor${id}`} onClick={() => toDetail(staffNo)}>
              <img className='doctor_avator' src={firstPageHeadPortrait} />
              <p className='doctor_name'>{staffName}<span className='doctor_title'>{PROFESSIONALTITLE_OBJ[staffProfessionalTitle]}</span></p>
              <p>{firstWorkOrgName}｜{DEPARTMENT_OBJ[workDepartment]}</p>
              <p className='doctor_skill'>擅长：{moreInfo.staffSkills || staffSkills}</p>
            </div>
          })
        }
      </div>
      <div className="footer">没有更多了</div>
    </div>
  );
}

export default List;