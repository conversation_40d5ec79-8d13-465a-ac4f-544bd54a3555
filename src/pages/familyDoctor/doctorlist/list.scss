@import "src/style/index";
$prefixCls: 'family-doctor-list-pages';

.#{$prefixCls} {
  min-height: 100vh;
  padding-bottom: r(70);
  background: #f5f5f5;

  &__list {
    background: #fff;
  }

  .title {
    margin: 0 r(15);
    padding: r(20) 0 r(10);
    font-size: r(17);
    font-weight: bold;
    border-bottom: 1px solid #e6e6e6;
  }

  .doctor_item {
    position: relative;
    padding: r(15) r(15) r(15) 0;
    margin-left: r(77);
    border-bottom: 1px solid #e6e6e6;
    font-size: r(13);
    color: #333;

    .doctor_avator {
      position: absolute;
      top: r(18);
      left: r(-62);
      width: r(52);
      height: r(52);
      border-radius: r(4);
    }

    .doctor_name {
      margin-bottom: r(4);
      font-size: r(16);
      font-weight: bold;
    }

    .doctor_title {
      display: inline-block;
      margin-left: r(5);
      padding: r(1) r(4);
      vertical-align: r(2);
      border-radius: r(2);
      font-size: r(11);
      color: #ec9131;
      border: 1px solid #ec9131;
    }

    .doctor_skill {
      margin-top: r(4);
      font-size: r(12);
      color: #999;
      overflow: hidden;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
    }
  }

  .footer {
    padding: r(27) 0;
    text-align: center;
    font-size: r(12);
    color: #b2b2b2;
  }
}
