import React, { useEffect, useState, useMemo, useReducer } from "react";
import { fetchJson } from "src/utils";
import { FormatPrice, StaticToast, CouponPicker } from "src/components/common";
import { Deserialize } from 'src/utils/serialization';
import { Button, Modal } from "zarm";
import UseCouponCalculate from 'src/hooks/useCouponCalculate';
import IntegerPointPrice from 'src/components/common/integerPointPrice';
import { CouponTypeCalc } from 'src/components/common/couponPicker';
import session from "src/utils/session";
import { commonPay } from "src/utils/pay";

import './intro.scss';

const _ORDER_REDUCER = (state, action) => {
  switch (action.type) {
    case 'edit':
      const { key = '', value } = action;
      return {
        ...state,
        [key]: value,
      };
    case 'reset':
      return {
        ...state,
      };
    default:
      return {
        ...state,
      };
  }
};
const FamilyDoctorBuy = (props) => {
  const { location: { search = '' } } = props;
  const { addressId, time = 0 } = Deserialize(search);
  //存放提交数据
  const [order, setOrder]: any = useReducer(_ORDER_REDUCER, {});
  const [servpackInfo, setServpackInfo]: any = useState({});

  const [selectedCoupon, setSelectedCoupon] = useState<CouponTypeCalc | undefined>();

  const goodsInfoMemo = useMemo(() => ({ goodsId: servpackInfo.servpackCode, goodsRealPrice: servpackInfo.servpackPrice, goodsPrice: servpackInfo.servpackPrice, relatedBizNo: servpackInfo.servpackCode, orderType: 'sellingServpack' }), [servpackInfo]);
  const { discountFee, orderRealAmount, couponList, orderAmount, orderPreferenceList } = UseCouponCalculate({ selectedCoupon, goodsInfo: goodsInfoMemo });

  useEffect(() => {
    fetchJson({
      url: `/api/api/v1/patient/servpack/list`,
      type: 'POST',
      data: {
        status: 1,
        servpackBizType: 3,
      },
      success: (res) => {
        const { code, result = [] } = res || {};
        if (code === '0') {
          setServpackInfo(result[0] || {});
        }
      },
    });

  }, []);

  const unifiedOrder = () => {
    const { servpackPrice, servpackCode, servpackName, id, isContainsPhysicalGoods, status } = servpackInfo;
    const { deliveryAddressId } = order;
    if (!deliveryAddressId && isContainsPhysicalGoods === 'Y') {
      StaticToast.warning('请选择您的地址');
      return;
    }
    if (status != 1) {
      StaticToast.warning('商品尚未上架');
      return;
    }
    if (!servpackCode) {
      return;
    }

    fetchJson({
      url: `/api/api/v1/patient/order/unifiedOrder`,
      type: 'POST',
      data: {
        bizType: 'sellingServpack',
        orderType: 'sellingServpack',
        orderGoods: [{
          goodsCode: servpackCode,
          goodsName: servpackName,
          goodsId: id,
          goodsNum: 1,
          goodsPrice: servpackPrice,
          goodsRealPrice: servpackPrice
        }],
        relatedBizNo: servpackCode,
        orderAmount,
        orderRealAmount: +time > 0 ? Number(((+orderRealAmount || 0) * 0.8).toFixed(2)) : orderRealAmount,
        orderExtraInfo: +time > 0 ? JSON.stringify({ "isRenew": "Y" }) : '',
        deliveryAddressId,
        orderPreferenceList
      },
      needToast: false,
    }).then((res) => {
      const { result = {}, code } = res || {};
      const {orderRealAmount, orderTime = '', orderNo, id, orderType, relatedBizNo, businessEntry: { servpackBizType = '' } = {}} = result;
      if (code === '0') {
        // 需要透传的活动referrer，不然支付成功页面会丢失
        const referrer = session.getSessionStorage('referrer');
        commonPay({
          orderRealAmount,
          orderTime,
          orderNo,
          id,
          orderType,
          servpackBizType,
          relatedBizNo,
          returnUrl: `${location.origin}/hospital/familydoctor/rights?referrer=${referrer}`,
          canBack: true
        })

      } else {
        if (code === '300006') {
          Modal.alert({
            content: <p>{res.message}</p>,
            cancelText: '去支付',
            onCancel: () => {
              props.history.push({
                pathname: '/hospital/myorder',
                search: `orderStatus=1`
              })
            },
          });
        } else {
          StaticToast.error(res.message);
        }

      }
    });
  };
  useEffect(() => {
    if (addressId) {
      setOrder({ type: 'edit', key: 'deliveryAddressId', value: addressId });
    }
  }, [addressId])

  return <div className='page_one_serv_pack_buy'>
    <div className="product_wrap">
      <p className="product_title">商品详情</p>
      <div className='product_info'>
        <p>{servpackInfo.servpackName}</p>
        <p className='product_price'><FormatPrice price={servpackInfo.servpackPrice || 0} /></p>
      </div>
    </div>

    <div className="product_summary">
      <div className="product_summary_line">
        共1件商品 小计：
        <b>
          <IntegerPointPrice value={orderAmount} color="#FF5050" integerSize={14} pointSize={10} unitSize={10}></IntegerPointPrice>
        </b>
      </div>
    </div>
    <CouponPicker disableCoupon={+time > 0} value={selectedCoupon} discountFee={discountFee} couponList={couponList} onChange={(value) => setSelectedCoupon(value)}></CouponPicker>


    <div className='fixed_footer'>
      <div>
        <p>
          <span className="footer_text">合计：</span>
          <span className="footer_price">
            <FormatPrice price={+(orderRealAmount || 0) * ((+time > 0) ? 0.8 : 1)} />
          </span>
        </p>
        {!!discountFee && (
          <p className="footer_discount">
            已优惠 <IntegerPointPrice value={discountFee} color="#FF5050" integerSize={12} pointSize={12} unitSize={12}></IntegerPointPrice>
          </p>
        )}
      </div>
      <Button className='btn_buy' theme='primary' shape='round' onClick={unifiedOrder}>立即支付</Button>
    </div>
  </div>
};

export default FamilyDoctorBuy;
