import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { SvgIcon } from 'src/components/common';
import { ApplicationState } from 'src/store';
import { fetch_family_doctor_banner, fetch_family_doctor_service } from 'src/store/familydoctor/action';
import { fetchJson, validate } from 'src/utils';
import format from 'src/utils/format';
import { CDN_PREFIX } from 'src/utils/staticData';
import useInterval from 'src/utils/useInterval';
import { Button } from 'zarm';
import { Questions } from '../components';
import './intro.scss';
import { xflowPushEvent } from 'src/utils/pageTrack';

const prefixCls = 'family-doctor-intro-pages';

// servpackBizType 服务包业务类型,1-普通 2-家医体验版 3-家医正式版")
// const RefundRules = [
//   { title: '未使用服务:',subTitle1: 'a）超过有效期未使用任何服务，不予退款。', subTitle2:'b）有效期内未使用服务，可联系客服进行全额退款。' },
//   { title: '已使用任一服务，不支持退款。' },
//   { title: '非本人付费，如他人转赠或企业集采类用户，不支持退款。' },
// ]

const Index = (props) => {

  const [servpackInfo, setServpackInfo]: any = useState({});

  const { favourCountdownTime, bannerList = [] }: any = useSelector((state: ApplicationState) => ({
    ...state.familyDoctor,
  }));

  const [time, setTime] = useState(favourCountdownTime);

  const hour = format.zeroPad(Math.floor(time / 60 / 60));
  const minute = format.zeroPad(Math.floor((time / 60) % 60));
  const seconds = format.zeroPad(Math.floor(time % 60));

  const dispatch = useDispatch();
  const fetchFamilyDoctorService = (onSuccess?: any) => dispatch(fetch_family_doctor_service(onSuccess));
  const fetchFamilyDoctorBanner = (onSuccess?: any) => dispatch(fetch_family_doctor_banner(onSuccess));

  useInterval(() => {
    setTime(time - 1);
  }, time > 0 ? 1000 : null);

  useEffect(() => {
    if (favourCountdownTime) {
      const now = Date.now();
      const endDate = new Date(favourCountdownTime.replace(/-/g, '/'));
      const end = endDate.getTime();
      setTime((end - now) / 1000);
    }
  }, [favourCountdownTime]);

  useEffect(() => {
    if (!bannerList.length) {
      fetchFamilyDoctorBanner();
    }
    fetchFamilyDoctorService();
    fetchJson({
      url: '/api/api/v1/patient/servpack/list',
      type: 'POST',
      data: {
        status: 1,
        servpackBizType: 3,
      },
      success: (res) => {
        const { code, result = [] } = res || {};
        if (code === '0') {
          setServpackInfo(result[0] || {});
        }
      },
    });
  }, []);
  const tobuy = () => {
    xflowPushEvent(['click', 'ZAHLWYY_JTYSGRB_XQY', '家庭医生个人版_详情页', { ZAHLWYY_CLICK_CONTENT: '家庭医生个人版_立即开通按钮' }]);
    props.history.push({
      pathname: '/hospital/familydoctor/buy',
      search: `time=${time}`,
      state: { servpackInfo },
    });
  };


  const toChat = () => {
    window.location.href = validate.isAlipayApplet() ? 'https://webchat-sh.clink.cn/chat.html?accessId=1936f9be-d24f-46cf-b0d1-09d34b8bef9d&language=zh_CN' : 'https://webchat-sh.clink.cn/chat.html?accessId=1936f9be-d24f-46cf-b0d1-09d34b8bef9d&language=zh_CN';
  };
  const { servpackPrice } = servpackInfo;

  const [currentTab, setCurrentTab] = useState(0);

  return (
    <div className={`${prefixCls}`}>
      {/* {
        !!bannerList.length && <React.Fragment>
          <Swiper bannerList={bannerList} />
          <p className='main_title'>家庭医生个人版</p>
        </React.Fragment>
      } */}
      <div className={`${prefixCls}__main`}>
        <img className='imgs' src={`${CDN_PREFIX}images/fdnew/fd_new_intro1.png`} />
        <img className='imgs' src={`${CDN_PREFIX}images/fdnew/fd_new_intro2.png`} />
        <img className='imgs' src={`${CDN_PREFIX}images/fdnew/fd_new_intro3.png`} />
        <img className='imgs' src={`${CDN_PREFIX}images/fdnew/fd_new_new_intro4.png`} />
        <img className='imgs' src={`${CDN_PREFIX}images/fdnew/fd_new_intro5.png`} />
        <img className='imgs' src={`${CDN_PREFIX}images/fdnew/fd_new_new_intro6.png`} />


        <Questions />
        <div className='rules_wrap'>
          <img className='img_tab' src={require(`./images/tab${currentTab}.png`)} />
          <div className='tab_wrap'>
            {
              [0, 1].map((item) => <p className='tab_item' key={`tab_item_${item}`} onClick={() => {
                setCurrentTab(item);
              }}>tab{item}</p>)
            }
          </div>
          <div className='rules_panel' style={{ display: currentTab === 0 ? 'block' : 'none' }}>
            <img className='mt30 mb20' src={require('./images/fwlc.png')} alt='' />
          </div>
          <div className='rules_panel' style={{ display: currentTab === 1 ? 'block' : 'none' }}>
            <p className='rule_title'>服务产品有效期：购买之日起1年，</p>
            <p className='rule_title'>退款规则：自购买之日起有效期内</p>
            <p className='rule_item'><span className='item_num'>1</span>未使用服务：</p>
            <div className='rule_item_detail'>
              <p>a)超过有效期未使用任何服务，不予退款。</p>
              <p>b)有效期内未使用服务，可联系客服进行全额退款</p>
            </div>
            <p className='rule_item'><span className='item_num'>2</span>已使用任一服务，不支持退款。</p>
            <p className='rule_item'><span className='item_num'>3</span>非本人付费，如他人转赠或企业集采类用户，不支持退款。</p>
          </div>
        </div>
      </div>
      <div className='mt40'>
        <div className={`${prefixCls}__footer ${validate.isFromMiniApplet() ? '' : 'in_h5'}`}>
          {
            time > 0 ? <Button className='btn_buy_discount' theme='primary' shape='round' onClick={tobuy}>
              <div className='discount_info'>
                <div className='left'>
                  <p>¥{Number(((servpackPrice || 0) * 0.8).toFixed(2))}/年<span className='old_price'>¥{servpackPrice}</span></p>
                  <p className='time'>限时折扣 {hour}:{minute}:{seconds}</p>
                </div>
                <div className='right'>立即开通</div>
              </div>
            </Button>
              : <div className='btn'>
                <Button className='goConsult' onClick={toChat}><SvgIcon type='img' className='icon_earphone' src={`${CDN_PREFIX}images/fdnew/icon.svg`} />在线客服</Button>
                <Button className='goBuy' onClick={tobuy}>¥{servpackPrice}/年 立即开通</Button>
              </div>

          }
        </div>
      </div>
    </div>
  );
};

export default Index;
