@import "src/style/index";
$prefixCls: 'family-doctor-intro-pages';

.#{$prefixCls} {
  min-height: 100vh;
  padding-bottom: r(65);
  background: #fcf9f6;

  .main_title {
    padding: r(5) r(15) r(20);
    font-size: r(16);
    font-weight: bold;
    color: #1e1e1e;
  }

  &__main {
    .imgs {
      display: block;
      width: 100%;
    }

    .img_footer {
      margin: r(20) r(10);
    }
  }

  &__footer {
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
    padding: r(18) r(15);
    background: #fff;
    z-index: 1000;

    .za-button {
      font-size: r(17);
      font-weight: bold;
    }

    .price {
      color: #fffacc;
    }

    .btn_buy_discount {
      height: r(55);
      width: 100%;
      padding: 0;
      border: none;

      .discount_info {
        @include display-flex;
      }

      .left {
        padding: r(4) 0 0 r(31);
        text-align: left;
        height: r(55);
        width: 66%;
        box-sizing: border-box;
        background: linear-gradient(320deg, #ffa11f 0%, #ffc174 100%);
      }

      .time {
        font-size: r(14);
        line-height: 1.2;
        font-weight: normal;
      }

      .old_price {
        margin-left: r(4);
        font-size: r(14);
        font-weight: normal;
        color: #fffacc;
        text-decoration: line-through;
        vertical-align: r(1);
      }

      .right {
        position: relative;
        height: r(55);
        line-height: r(55);
        width: 34%;

        &::after {
          content: '';
          position: absolute;
          height: r(55);
          width: r(20);
          left: r(-10);
          top: 0;
          transform: skew(-20deg);
          background: var(--theme-primary);
        }
      }
    }
  }

  .za-tabs__header {
    background: linear-gradient(360deg, #f0f0f0 0%, #fff 100%);
    font-size: r(16);
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #666;

    &:after {
      border: none;
    }
  }

  .za-tabs__tab {
    overflow: visible;
    font-size: r(16);
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
  }

  .za-tabs--horizontal .za-tabs__line {
    height: 0;
  }

  .mt40 {
    margin-top: r(30);
  }

  .btn {
    margin: 0;
    display: flex;
    justify-content: space-between;
    text-align: center;
    line-height: r(44);

    .goConsult {
      width: r(118);
      height: r(44);
      display: block;
      background: url('./images/btnbg1.png') no-repeat;
      background-size: contain;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 500;
      color: #9f5a09;
      border: none;
      font-size: r(16);
      padding: 0;
    }

    .icon_earphone {
      width: r(16);
      height: r(16);
      margin-right: r(4);
    }

    .goBuy {
      width: r(233);
      height: r(44);
      display: block;
      background: url('./images/btnbg2.png') no-repeat;
      background-size: contain;
      font-family: PingFangSC-Semibold, PingFang SC;
      font-weight: 600;
      color: #fff;
      border: none;
      font-size: r(18);
    }
  }
}

.page_one_serv_pack_buy {
  .header_address {
    position: relative;
    min-height: r(74);
    padding: r(15) r(15) r(20);
    background: #fff;
    @include display-flex;
    @include align-items(center);

    color: #333;

    .icon_address {
      width: r(18);
      height: r(18);
      margin-right: r(4);
    }

    .bottom_line {
      position: absolute;
      bottom: 0;
      left: 0;
      right: 0;
    }
  }

  .address_info {
    position: relative;
    padding-right: r(50);
    font-size: r(16);
    font-weight: bold;
    @include flex(1);

    .address_text {
      margin-top: r(5);
      font-size: r(15);
      color: #666;
      font-weight: normal;
    }

    .icon_narrow {
      position: absolute;
      right: r(0);
      top: 50%;
      transform: translateY(-50%);
      font-size: r(15);
      color: #d8d8d8;
    }
  }

  .product_wrap {
    margin-top: r(10);
    background: #fff;

    .product_title {
      padding: r(12) 0 r(10) r(15);
      font-size: r(13);
      font-weight: bold;
      color: #333;
    }

    .product_info {
      padding: r(17) r(15);
      background: #fbfbfb;
      font-size: r(15);
      color: #1e1e1e;
      @include display-flex;
      @include justify-content(space-between);
    }

    .product_price {
      margin-left: r(45);
      font-size: r(12);
      font-weight: bold;
      color: #1e1e1e;
    }

    .express_price {
      padding: r(15) r(15) r(10);
      font-size: r(14);
      color: #666;
      @include display-flex;
      @include justify-content(space-between);
    }
  }

  .fixed_footer {
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100vw;
    padding: r(12) r(15) r(27);
    font-size: r(14);
    color: #333;
    background: #fff;
    @include display-flex;
    @include align-items(center);
    @include justify-content(space-between);

    .footer_text {
      vertical-align: 2px;
    }

    .footer_price {
      font-weight: bold;

      .sign,
      .integer,
      .point {
        font-size: r(16);
        color: #ff5050;
      }

      .integer {
        font-size: r(21);
      }
    }

    .btn_buy {
      height: r(40);
      width: r(114);
      box-sizing: border-box;
      font-size: r(17);
      font-weight: bold;

      &.btn_cancel {
        margin-right: r(12);
        color: var(--text-base-color);
        border: 1px solid var(--theme-primary);
      }
    }

    .btn_pay,
    .btn_cancel {
      height: r(32);
      width: r(94);
      font-size: r(14);
    }
  }

  .product_summary {
    padding-left: r(15);
    background: #fff;

    &_line {
      display: flex;
      justify-content: flex-end;
      align-items: center;
      padding-right: r(15);
      height: r(52);
      border-top: 1px solid #e6e6e6;
    }
  }

  .footer_discount {
    font-size: r(12);
    color: #ff5050;
  }
}

.rules_wrap {
  position: relative;
  margin: r(10) r(10) 0;
  background: #fff;
  border-radius: r(10) r(10) r(8) r(8);
  overflow: hidden;
  padding: r(15);
  box-shadow: 0 r(1) r(7) 0 #efece9;

  .img_tab {
    width: 100%;
    height: r(44);
  }

  .tab_wrap {
    position: absolute;
    left: 0;
    right: 0;
    top: 0;
    height: r(44);
    opacity: 0;

    @include display-flex;
  }

  .mt30 {
    margin-top: r(20);
  }

  .mb20 {
    margin-bottom: r(14);
  }

  .tab_item {
    height: 100%;
    @include flex(1);
  }

  .rules_panel {
    padding: r(10) r(15) r(15);
    border-bottom: solid 1px #fbe6c3;
    border-right: solid 1px #fbe6c3;
    border-left: solid 1px #fbe6c3;
    border-radius: 0 0 r(8) r(8);
    background-color: #fffcf7;

    .rule_title,
    .rule_item {
      font-size: r(14);
      font-weight: 500;
      color: #bb691f;
      margin-top: r(5);
      font-family: PingFangSC-Medium, PingFang SC;
    }

    .rule_item {
      position: relative;
      margin-top: r(5);
      padding-left: r(20);
    }

    .item_num {
      position: absolute;
      width: r(15);
      height: r(15);
      left: 0;
      top: r(2);
      font-size: r(12);
      border-radius: 50%;
      line-height: r(15);
      text-align: center;
      color: #fff;
      background: linear-gradient(158deg, #eaaf89 0%, #d98f64 100%);
    }

    .rule_item_detail {
      font-size: r(12);
      color: #bb691f;
      line-height: r(17);
      opacity: 0.8;
      padding-left: r(20);
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
    }
  }
}
