import React, { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { ApplicationState } from 'src/store';
import { fetch_family_doctor_service } from 'src/store/familydoctor/action';

const PreAuth = (props) => {

  const { isBuyedServpack }: any = useSelector((state: ApplicationState) => {
    return {
      ...state.familyDoctor
    }
  });

  const dispatch = useDispatch();
  const fetchFamilyDoctorService = (onSuccess?: any) => dispatch(fetch_family_doctor_service(onSuccess));

  useEffect(() => {
    if (isBuyedServpack) {
      props.history.replace({
        pathname: isBuyedServpack === 'Y' ? '/hospital/familydoctor/rights' : '/hospital/familydoctor/intro',
      });
    } else {
      fetchFamilyDoctorService((res) => {
        let { userServpackDomain = {} } = res.result || {};
        const isBuyedServpack = Object.keys(userServpackDomain).length ? 'Y' : 'N';
        props.history.replace({
          pathname: isBuyedServpack === 'Y' ? '/hospital/familydoctor/rights' : '/hospital/familydoctor/intro',
        });
      })
    }
  }, []);

  return <div>
  </div>;
};

export default PreAuth;
