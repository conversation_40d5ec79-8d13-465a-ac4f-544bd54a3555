@import "src/style/index";
$prefixCls: 'family-doctor-rights-pages';

.#{$prefixCls} {
  min-height: 100vh;
  padding-bottom: r(65);
  background: #f5f5f5;

  .main_title {
    padding: r(5) r(15) r(10);
    font-size: r(16);
    font-weight: bold;
    color: #1e1e1e;
  }

  &__header {
    position: relative;
    // height: r(230);

    .header_img {
      display: block;
      width: 100%;

      &.img_bottom {
        position: relative;
        z-index: 8;
        margin-top: r(-70);
      }
    }

    .header_content {
      position: absolute;
      width: 89.3%;
      left: 5.3%;
      top: r(56);
      z-index: 1;
      border-radius: r(8);
      overflow: hidden;

      .doctor_info {
        position: absolute;
        left: 0;
        padding: r(35) 0 0 r(20);
        z-index: 2;
        font-size: r(14);
        color: #d1a37b;
      }

      .doctor_title {
        font-size: r(18);
        font-weight: bold;
        color: #555353;
        margin-bottom: r(3);

        .sign_tips {
          display: inline-block;
          padding: r(2) r(6);
          margin-left: r(5);
          font-size: r(11);
          font-weight: normal;
          color: #fff;
          background: linear-gradient(90deg, #ff7240 0%, #ffce46 100%);
          border-radius: 0 r(9) r(9) r(9);
          vertical-align: r(2);
          line-height: 1.4;
        }
      }

      .doctor_default_img {
        float: right;
        width: r(165);
        height: r(150);
      }

      .doctor_img {
        float: right;
        margin: r(20) r(20) 0 0;
        width: r(120);
      }
    }

    .btn_sign {
      position: absolute;
      width: r(192);
      height: r(62);
      padding: 0;
      left: 50%;
      transform: translateX(-50%);
      bottom: r(20);
      border: none;
      background: url('../imgs/btn_sign.png') 100%/100%;
      z-index: 9;

      &.binded {
        background: url('../imgs/btn_chat.png') 100%/100%;
      }
    }
  }

  .qrcode_wrap {
    position: relative;
    margin: r(-15) r(15) r(30);
    height: r(96);
    background: #fff;
    border-radius: r(8);
    padding: 0 r(15);
    font-size: r(12);
    color: #666;
    z-index: 9;

    @include display-flex;
    @include align-items(center);
    @include justify-content(space-between);

    .qrcode_title {
      font-size: r(16);
      font-weight: bold;
      color: #1e1e1e;
      margin-bottom: r(5);
    }

    .qrcode_img {
      width: r(60);
      height: r(60);
      border-radius: r(6);
      overflow: hidden;
    }
  }

  &__rights_coupon {
    position: relative;
    margin-top: r(-30);
    padding: 0 r(15);
    z-index: 10;

    .title {
      padding-top: r(10);
      font-size: r(16);
      font-weight: bold;
      color: #1e1e1e;
    }

    .rights_item {
      height: r(84);
      background: #fff;
      border-radius: r(8);
      margin-top: r(10);
      padding: 0 r(15);
      @include display-flex;
      @include align-items(center);

      font-size: r(12);
      color: #999;

      .icon_right {
        width: r(44);
        height: r(44);
        margin-right: r(10);
      }

      .rights_content {
        @include flex(1);

        overflow: hidden;
        font-size: r(13);

        .rights_name {
          margin-bottom: r(8);
          color: var(--text-special-color);
          font-size: r(17);
          line-height: 1em;
          font-weight: bold;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
      }

      .times_wrap {
        width: r(90);
        text-align: center;

        .times {
          font-size: r(13);

          .left_times {
            color: #ff7240;
            font-weight: bold;
          }
        }

        .btn_use {
          margin-top: r(7);
          height: r(20);
          line-height: r(18);
          padding: 0 r(6);
          font-size: r(12);
          font-weight: bold;
          color: var(--theme-primary);
          border-radius: r(10);
          background: rgba(0, 188, 112, 0.05);
          border: r(1) solid rgba(0, 188, 112, 0.5);

          .icon_arrow {
            width: r(6);
            height: r(12);
            margin-left: r(2);
            font-size: r(6);

            --arrow-color: var(--theme-primary);
          }
        }
      }
    }
  }

  .img_footer {
    width: 100%;
  }

  &__footer {
    position: fixed;
    left: 0;
    width: 100%;
    bottom: 0;
    padding: r(10) r(15) r(25);
    background: #fff;
    z-index: 1000;

    .btn_chat {
      font-size: r(17);
      font-weight: bold;
    }

    .icon_chat {
      width: r(17);
      height: r(16);
      margin-right: r(5);
      vertical-align: r(-2);
    }
  }

  .za-tabs__header {
    height: r(44);
    background: linear-gradient(360deg, #f0f0f0 0%, #fff 100%);
    font-size: r(16);
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #666;
  }

  .za-tabs__tab {
    overflow: visible;
    font-size: r(16);
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
  }

  .za-tabs--horizontal .za-tabs__line {
    height: 0;
  }

  .activeBg1 {
    width: r(220);
    height: r(44);
    background: url(../imgs/tabbg1.png) no-repeat;
    background-size: 100% 100%;
    font-size: r(18);
    font-family: PingFangSC-Semibold, PingFang SC;
    font-weight: 600;
    color: #a85f1f;
  }

  .activeBg2 {
    width: r(220);
    height: r(44);
    background: url(../imgs/tabbg2.png) no-repeat;
    background-size: 100% 100%;
    font-size: r(18);
    font-family: PingFangSC-Semibold, PingFang SC;
    font-weight: 600;
    color: #a85f1f;
  }
}
