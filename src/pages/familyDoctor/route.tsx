const prefix = '/hospital/familydoctor/';
const route = [
  {
    path: `${prefix}pre`, // mother -> 母婴，andrology -> 专科
    name: 'FamilyDoctorPre',
    component: () => import(/* webpackPrefetchPlaceHolder */ './pre'),
    auth: true,
    exact: true,
    realAuth: false,
    title: '众安家庭医生',
  },
  {
    path: `${prefix}intro`, // mother -> 母婴，andrology -> 专科
    name: 'FamilyDoctorIntro',
    component: () => import(/* webpackPrefetchPlaceHolder */ './intro'),
    auth: true,
    exact: true,
    realAuth: false,
    title: '众安家庭医生',
  },
  {
    path: `${prefix}rights`,
    name: 'FamilyDoctorRights',
    component: () => import(/* webpackPrefetchPlaceHolder */ './rights'),
    auth: true,
    exact: true,
    realAuth: true,
    title: '众安家庭医生',
  },
  {
    path: `${prefix}list`,
    name: 'FamilyDoctorList',
    component: () => import(/* webpackPrefetchPlaceHolder */ './doctorlist'),
    auth: true,
    exact: true,
    realAuth: true,
    title: '选择医生',
  },
  {
    path: `${prefix}detail`,
    name: 'FamilyDoctorDetail',
    component: () => import(/* webpackPrefetchPlaceHolder */ './doctordetail'),
    auth: true,
    exact: true,
    realAuth: true,
    title: '签约家庭医生',
  },
  {
    path: `${prefix}buy`,
    name: 'FamilyDoctorBuy',
    component: () => import(/* webpackPrefetchPlaceHolder */ './intro/buy'),
    auth: true,
    exact: true,
    realAuth: true,
    title: '确认订单',
  },
];

export default route;
