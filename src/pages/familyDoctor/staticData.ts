export const questionsList = [{
  id: 0,
  q: '众安家庭医生有哪些科室的医生？',
  a: '众安家庭医生团队是由内科、外科、妇科、儿科、中医科、口腔科、皮肤科七大科室的医生和健康管家共同组成，同时配备心理咨询师、药剂师，为您提供全面的健康服务支持。',
}, {
  id: 1,
  q: '众安家庭医生服务有效期是多长时间？',
  a: '体验版有效期自购买之日起30天内有效，正式版有效期自购买之日起1年内有效。'
}, {
  id: 2,
  q: '众安家庭医生是否可以退款？',
  a: '服务自购买起30日内无理由退款，超过30天的用户需要按整年扣费，如果用户使用门诊绿通或重疾绿通服务，则按实际费用进行收费。'
}, {
  id: 3,
  q: '众安家庭医生服务到期后有续费优惠吗？',
  a: '服务到期后，老用户可享受续费8折优惠；用户退款后，3个月内不可再购买服务，3个月后可按原价进行购买。'
}, {
  id: 4,
  q: '我可以给家人购买众安家庭医生服务吗？',
  a: '本服务仅限本人使用，若您要给家人购买服务，可使用家人手机号注册众安互联网医院小程序后购买本项服务。'
}];

export const questionIdMap = (()=>{
  const obj = {};
  questionsList.map(({ id }) => {
    obj[id] = false;
  });
  return obj;
})();

