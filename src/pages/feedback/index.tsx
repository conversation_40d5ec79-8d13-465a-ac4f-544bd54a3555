import React, { useState } from 'react';
import { StaticToast } from 'src/components/common';
import { fetchJson } from 'src/utils/fetch';
import { Button, Input } from 'zarm';
import './feedback.scss';

const Feedback = () => {
  const [value, setValue] = useState('');
  const [hasSubmit, setHasSubmit] = useState(false);


  const submit = () => {
    if(!value) {
      StaticToast.success('请输入你遇到的问题或意见');
      return
    }
    fetchJson({
      url: '/api/api/v1/patient/user/sumbitSuggestion',
      type: 'POST',
      data: {
        suggestionDesc: value
      },
      isloading: true,
    }).then((res) => {
      if (res && res.code === '0') {
        StaticToast.success('提交成功');
        setHasSubmit(true);
      }
    })
  }

  return (
    <div className="page_feedback">
      <div className="input_wrap">
        <Input
          autoHeight
          showLength
          maxLength={500}
          type="text"
          rows={5}
          disabled={hasSubmit}
          placeholder="请告诉我们你遇到的问题或意见"
          value={value}
          onChange={(val) => setValue(val)}
        />
      </div>
      <div className="btn_submit_wrap">
        <Button className={`btn_submit ${!value ? 'btn_disabled' : ''}`} block theme="primary" shape="round" disabled={!value || hasSubmit} onClick={submit}>确认提交</Button>
      </div>
    </div>
  )
};

export default Feedback;
