// import React, { useEffect, useState, useMemo, useCallback } from 'react';
// import { useSelector, shallowEqual, useDispatch } from 'react-redux';
// import { fetch_sugarblood_data } from 'src/store/sugarblood/action';
// import SugarRecommand from '../recommand';
// import { Progress, Modal, Button, Icon } from 'zarm';
// import { ApplicationState } from 'src/store';
// import format from 'src/utils/format';
// import classnames from 'classnames';
// import echarts from 'echarts';
// import './sugarmanage.scss';

// export const judgeSugarValue = (item) => {
//   const { rangeTimeType: type, recordValue: value } = item;
//   let result: string = ''
//   if (type === 'BEFORE_BREAKFAST' || type === 'BEFORE_LUNCH' || type === 'BEFORE_DINNER') {
//     if (value <= 3.8) {
//       result = '偏低';
//     } else if (value >= 3.9 && value <= 6.1) {
//       result = '正常';
//     } else {
//       result = '偏高';
//     }
//   } else if (type === 'AFTER_BREAKFAST' || type === 'AFTER_LUNCH' || type === 'AFTER_DINNER' || type === 'BEFORE_SLEEP') {
//     if (value <= 3.8) {
//       result = '偏低';
//     } else if (value >= 3.9 && value <= 7.8) {
//       result = '正常';
//     } else {
//       result = '偏高';
//     }
//   }
//   return result;
// }

// //这产品一定要在用户没数据的时候弄一个默认假数据的示意图
// const DEFAULT_CHART = [{
//   rangeTimeType: "BEFORE_BREAKFAST",
//   recordValue: "6"
// }, {
//   rangeTimeType: "BEFORE_BREAKFAST",
//   recordValue: "9"
// }, {
//   rangeTimeType: "BEFORE_BREAKFAST",
//   recordValue: "2"
// }, {
//   rangeTimeType: "BEFORE_BREAKFAST",
//   recordValue: "6"
// }, {
//   rangeTimeType: "BEFORE_BREAKFAST",
//   recordValue: "6"
// }, {
//   rangeTimeType: "BEFORE_BREAKFAST",
//   recordValue: "2.8"
// }, {
//   rangeTimeType: "BEFORE_BREAKFAST",
//   recordValue: "6.1"
// }]

// const SugarManage = (props) => {
//   const [selected, setSelected] = useState(0);
//   const [modalVisible, setModalVisible] = useState(false);
//   const { patientslist, sugarBloodData } = useSelector((state: ApplicationState) => {
//     return {
//       patientslist: state.patients.patientsList || [],
//       sugarBloodData: state.sugarblood.list || []
//     };
//   }, shallowEqual);

//   const { BLOODGROUP_OBJ }: any = useSelector((state: ApplicationState) => {
//     return {
//       BLOODGROUP_OBJ: state.dictionary.BLOODGROUP_OBJ,
//     }
//   });

//   const dispatch = useDispatch();
//   const fetchSugarbloodData = (init: boolean, options?: any) => dispatch(fetch_sugarblood_data(init, options));

//   useEffect(() => {
//   }, []);

//   const recommandDoctorClick = useCallback((item) => {
//     props.history.push({
//       pathname: '/hospital/doctordetail',
//       search: `staffId=${item.id}`
//     })
//   }, []);

//   const renderSugarBloodData = useMemo(() => {
//     const id = patientslist.length && patientslist[selected].id;
//     return sugarBloodData && sugarBloodData[id] || [];
//   }, [sugarBloodData, selected, patientslist]);

//   const percent = useMemo(() => {
//     if (renderSugarBloodData.length) {
//       return Math.floor(renderSugarBloodData[0].recordValue / 32 * 100);
//     }
//     return 0;
//   }, [renderSugarBloodData]);

//   const echartsSeries = useMemo(() => {
//     let data: any[] = [];
//     if (!renderSugarBloodData.length) {
//       DEFAULT_CHART.forEach(element => {
//         const status = judgeSugarValue(element);
//         data.push({
//           value: element.recordValue,
//           itemStyle: {
//             color: status === '偏高' ? '#FF9C82' : status === '偏低' ? '#FFCE74' : '#00BC70',
//             borderColor: "#fff",
//             borderWidth: 3
//           }
//         })
//       });
//     } else {
//       renderSugarBloodData.forEach(element => {
//         const status = judgeSugarValue(element);
//         data.push({
//           value: element.recordValue,
//           itemStyle: {
//             color: status === '偏高' ? '#FF9C82' : status === '偏低' ? '#FFCE74' : '#00BC70',
//             borderColor: "#fff",
//             borderWidth: 3
//           }
//         })
//       });
//     }
//     return data;
//   }, [renderSugarBloodData]);

//   const patientClick = useCallback((item, index) => {
//     setSelected(index);
//     fetchSugarbloodData(true, { patientId: item.id })
//   }, []);

//   const toSugarRecord = useCallback(() => {
//     if (patientslist.length) {
//       window._XFLOW_.pushEvent(['click', 'ZAHLWYY_JKGLJLY', '健康管理记录页', { ZAHLWYY_CLICK_CONTENT: '健康管理_记录_手动记录' }]);
//       props.history.push({
//         pathname: "/hospital/sugarrecord",
//         state: {
//           patientId: patientslist.length && patientslist[selected].id
//         }
//       });
//     } else {
//       setModalVisible(true);
//     }
//   }, [patientslist, selected]);

//   const toSugarRecent = useCallback(() => {
//     props.history.push({
//       pathname: "/hospital/sugarcharts",
//       state: {
//         patientId: patientslist.length && patientslist[selected].id
//       }
//     });
//   }, [patientslist, selected]);

//   useEffect(() => {
//     setTimeout(() => {
//       var myChart = echarts.init(document.getElementById('echarts'));
//       myChart.setOption({
//         tooltip: {
//           trigger: 'item',
//           formatter: "{c}",
//           backgroundColor: '#ffffff',
//           textStyle: {
//             color: '#00BC70',
//             fontWeight: 'bold',
//           },
//           extraCssText: 'box-shadow: 0px 6px 10px 0px rgba(224,230,253,1);  border-radius: 50%'
//         },
//         grid: {
//           top: 'middle',
//           left: '3%',
//           right: '4%',
//           bottom: '0%',
//           height: '90%',
//           containLabel: true
//         },
//         xAxis: {
//           type: 'category',
//           data: [1, 2, 3, 4, 5, 6, 7],
//           boundaryGap: true,
//           axisLine: {
//             show: false
//           },
//           axisTick: {
//             show: false
//           },
//           axisLabel: {
//             inside: true,
//             color: '#D4D8F2',
//             fontSize: 20,
//             margin: 1,
//           },
//           splitArea: {//背景条纹
//             show: true,
//             areaStyle: {
//               color: [
//                 '#F6F8FF',
//                 '#FFFFFF'
//               ]
//             }
//           }
//         },
//         yAxis: {
//           type: 'value',
//           splitNumber: 2,
//           splitLine: {
//             show: false
//           },
//           axisTick: {
//             show: false
//           },
//           axisLine: {
//             show: false,
//           },
//           axisLabel: {
//             color: '#999999',
//             fontSize: 14
//           },
//           splitArea: {
//             show: false,
//           }
//         },
//         series: [
//           {
//             name: '血糖值',
//             type: 'line',
//             data: echartsSeries,
//             color: "#F58080",
//             symbol: 'circle',     //折点设定为实心点
//             symbolSize: 12,   //设定实心点的大小
//             lineStyle: {
//               normal: {
//                 width: 4,
//                 color: {
//                   type: 'linear',
//                   colorStops: [{
//                     offset: 0,
//                     color: '#1EC5C4' // 0% 处的颜色
//                   }, {
//                     offset: 1,
//                     color: '#A7E99D' // 100% 处的颜色
//                   }],
//                   globalCoord: false // 缺省为 false
//                 }
//               }
//             },
//             areaStyle: {
//               color: {
//                 type: 'linear',
//                 x: 0,
//                 y: 0,
//                 x2: 0,
//                 y2: 1,
//                 colorStops: [{
//                   offset: 0, color: 'rgba(84,211,209,0.3)' // 0% 处的颜色
//                 }, {
//                   offset: 1, color: 'rgba(61,205,203,0)' // 100% 处的颜色
//                 }],
//                 global: false // 缺省为 false
//               }
//             },
//             smooth: true
//           }
//         ]
//       });
//     }, 200);
//   }, [echartsSeries]);

//   return (
//     <div className="sugarmanage_page">
//       <div className='patients_wrapper'>
//         <div className="patients_list">
//           <div className="inner">
//             {patientslist.map((item, index) => <div className={classnames("patient_item", { active: selected === index })} key={`patient_item_${item.id}`} onClick={() => patientClick(item, index)}>{item.patientName}</div>)}
//           </div>
//         </div>
//         <div className="progress_wrapper">
//           <div className='first_pot'></div>
//           <div className='second_pot'></div>
//           <div className='third_pot'></div>
//           <Progress
//             className='progress'
//             percent={percent}
//             theme='primary'
//             shape='circle'
//           // weight='thin'
//           >
//             {renderSugarBloodData.length ? <div className="progress_content">
//               <p className="progress_text_date">{format.date(renderSugarBloodData[0].recordTime, 'MM-dd')}</p>
//               <p className="progress_text_time">{format.date(renderSugarBloodData[0].recordTime, 'hh:mm')} | {BLOODGROUP_OBJ[renderSugarBloodData[0].rangeTimeType]} </p>
//               <p className="progress_text_status">{judgeSugarValue(renderSugarBloodData[0])}</p>
//               <p className="progress_text_record">{renderSugarBloodData[0].recordValue}</p>
//               <p className="progress_text_unit">mmol/L</p>
//             </div> : <div className="progress_content">
//                 <span className="no_progress_text">当前无记录哦</span>
//               </div>}
//           </Progress>
//         </div>
//         <div className='record_wrapper' onClick={() => toSugarRecord()}>
//           <div className='rocord'>
//             <img src={require('src/images/icon_record.png')} alt="" />
//           </div>
//           手动记录
//         </div>
//       </div>

//       <div className='sugar_blood_wrapper'>
//         <div className='head'>
//           <p className='title'>最近7次血糖趋势图</p>
//           <div className='more_btn' onClick={() => toSugarRecent()}>更多</div>
//         </div>
//         <div className='echarts_wrapper'>
//           {!renderSugarBloodData.length ? <p className='default'>示意图</p> : null}
//           <div id='echarts' className='echarts'></div>
//           <div className='info'>
//             <p className='unit'>单位 mmol/L</p>
//             <div className='dot_wrapper'>
//               <div className='dot_height dot'>偏高</div>
//               <div className='dot_low dot'>偏低</div>
//               <div className='dot_normal dot'>正常</div>
//             </div>
//           </div>
//         </div>
//       </div>

//       <div className='sugar_recommond_wrapper'>
//         <p className="title">相关医生推荐</p>
//         <SugarRecommand recommandClick={recommandDoctorClick} />
//       </div>

//       <Modal
//         className='sugar_no_patient_modal'
//         visible={modalVisible}
//         maskClosable
//         onCancel={() => setModalVisible(false)}
//         footer={
//           <Button
//             block
//             theme="primary"
//             onClick={() => {
//               setModalVisible(false);
//               props.history.push({ pathname: "/hospital/editpatient/baseinfo" })
//             }}
//           >立即添加</Button>
//         }
//       >
//         <Icon className='sugar_modal_close_button' type="wrong" onClick={() => setModalVisible(false)} />
//         您还未添加需要记录的患者，请先添加之后再记录
//         </Modal>
//     </div>
//   );
// }

// export default SugarManage;