// import React, { useEffect, useState, useMemo, useCallback } from 'react';
// import { useSelector, shallowEqual, useDispatch } from 'react-redux';
// import { fetch_sugarblood_recent, fetch_sugarblood_data } from 'src/store/sugarblood/action';
// import { ApplicationState } from 'src/store';
// import { judgeSugarValue } from './index';
// import Tabs from 'antd-mobile/lib/tabs';
// import 'antd-mobile/lib/tabs/style/css';
// import format from 'src/utils/format';
// import echarts from 'echarts';
// import './sugarmanage.scss';

// // const tabs = [
// //   { title: '全部', key: 'ALL' },
// //   { title: '早餐前', key: 'BEFORE_BREAKFAST' },
// //   { title: '早餐后', key: 'AFTER_BREAKFAST' },
// //   { title: '午餐前', key: 'BEFORE_LUNCH' },
// //   { title: '午餐后', key: 'AFTER_LUNCH' },
// //   { title: '晚餐前', key: 'BEFORE_DINNER' },
// //   { title: '晚餐后', key: 'AFTER_DINNER' },
// //   { title: '睡前', key: 'BEFORE_SLEEP' },
// // ];

// //将血糖时间枚举渲染成首页tab需要的数据格式
// export const TransToTabData = (list: [], title = 'value', key = 'label') => {
//   const res: Object[] = []
//   return list.reduce((prev, elem) => {
//     prev.push({ title: elem[title], key: elem[key] });
//     return prev;
//   }, res);
// };

// const SugarData = (props) => {
//   //选中的类型 默认选择第一个
//   const { location: { state } } = props;
//   const [patientId] = useState(state && state.patientId || '');
//   const { sugarBloodData, sugarBloodRecent } = useSelector((state: ApplicationState) => {
//     return {
//       sugarBloodData: state.sugarblood.list,
//       sugarBloodRecent: state.sugarblood.recent
//     };
//   }, shallowEqual);

//   const { RANGETIMETYPE }: any = useSelector((state: ApplicationState) => {
//     const t = TransToTabData(state.dictionary.RANGETIMETYPE, 'resName', 'resValue');
//     t.unshift({ title: '全部', key: 'ALL' });
//     return {
//       RANGETIMETYPE: t,
//     }
//   });

//   //dispatch 修改患者信息 
//   const dispatch = useDispatch();
//   const dispatchFetchSugarbloodRecent = (patientId: string) => dispatch(fetch_sugarblood_recent(patientId));
//   const dispatchFetchSugarbloodData = (init: boolean, options?: any) => dispatch(fetch_sugarblood_data(init, options))


//   useEffect(() => {
//     dispatchFetchSugarbloodData(true, { patientId })
//   }, []);

//   useEffect(() => {
//     patientId && dispatchFetchSugarbloodRecent(patientId);
//   }, [patientId]);

//   const renderSugarBloodData = useMemo(() => {
//     return sugarBloodData && sugarBloodData[patientId] || [];
//   }, [sugarBloodData, patientId]);

//   const echartsSeries = useMemo(() => {
//     const dataSource = sugarBloodData && sugarBloodData[patientId] || [];
//     let data: any[] = [];
//     if (!dataSource.length) {
//       return [];
//     }
//     dataSource.forEach(element => {
//       const status = judgeSugarValue(element);
//       data.push({
//         value: element.recordValue,
//         itemStyle: {
//           color: status === '偏高' ? '#FF9C82' : status === '偏低' ? '#FFCE74' : '#00BC70',
//           borderColor: "#fff",
//           borderWidth: 3
//         }
//       })
//     });
//     return data;
//   }, [sugarBloodData])

//   const echartsCircleSeries = useMemo(() => {
//     let data: any[] = [];
//     let lowTime: number = 0;
//     let highTime: number = 0;
//     let normalTime: number = 0;
//     if (!sugarBloodRecent.length) {
//       return data;
//     }
//     sugarBloodRecent.forEach(element => {
//       const status = judgeSugarValue(element);
//       status === '偏高' ? highTime++ : status === '偏低' ? lowTime++ : normalTime++
//     });

//     [normalTime, highTime, lowTime].forEach(element => {
//       data.push({
//         value: element,
//         percent: Math.floor(element / sugarBloodRecent.length * 100)
//       })
//     })
//     return data;
//   }, [sugarBloodRecent])

//   const itemClick = useCallback((selected) => {
//     if (selected === 'ALL') {
//       dispatchFetchSugarbloodData(true, { patientId })
//     }
//     dispatchFetchSugarbloodData(false, {
//       patientId,
//       rangeTimeType: selected
//     })
//   }, [patientId]);

//   const toSugarList = useCallback(() => {
//     window._XFLOW_.pushEvent(['click', 'ZAHLWYY_JKGLJLY', '健康管理记录页', { ZAHLWYY_CLICK_CONTENT: '健康管理_记录_更多' }]);
//     props.history.push({
//       pathname: "/hospital/sugarlist",
//       state: {
//         patientId
//       }
//     })
//   }, [patientId]);

//   useEffect(() => {
//     setTimeout(() => {
//       if (renderSugarBloodData.length) {
//         const circleChart = echarts.init(document.getElementById('echarts-circle'));
//         circleChart.setOption({
//           title: {
//             text: `${format.date(format.timeForMat(30), 'yyyy/MM/dd')}\n - \n${format.date(Date.now(), 'yyyy/MM/dd')}`,
//             x: 'center',
//             y: 'center',
//             textStyle: {
//               fontSize: 10,
//               fontWeight: 'normal',
//               color: ['#333']
//             }
//           },
//           legend: {
//             show: false
//           },
//           tooltip: {
//             show: false,
//           },
//           color: ['#00BC70', '#FF9C82', '#FFCE74'],
//           series: [
//             {
//               name: '访问来源',
//               type: 'pie',
//               hoverAnimation: false,
//               radius: [40, 75],
//               avoidLabelOverlap: false,
//               labelLine: {
//                 normal: {
//                   show: false,
//                   smooth: 0.2,
//                   length: 10,
//                   length2: 20
//                 }
//               },
//               itemStyle: {
//                 normal: {
//                   // shadowBlur: 10,
//                   // shadowColor: 'rgba(0, 0, 0, 0.5)'
//                 }
//               },
//               roseType: 'radius',
//               data: echartsCircleSeries,
//               animationType: 'scale',
//               animationEasing: 'elasticOut',
//               animationDelay: function (idx) {
//                 return Math.random() * 200;
//               }
//             }
//           ]
//         })
//       } else {
//         // document.querySelector('#echarts-circle') && document.removeChild(document.querySelector('#echarts-circle')!);
//       }
//       const myChart = echarts.init(document.getElementById('echarts'));

//       myChart.setOption({
//         tooltip: {
//           trigger: 'item',
//           formatter: "{a} <br/>{c}"
//         },
//         grid: {
//           top: 'middle',
//           left: '3%',
//           right: '4%',
//           bottom: '0%',
//           height: '90%',
//           containLabel: true
//         },
//         xAxis: {
//           type: 'category',
//           data: [1, 2, 3, 4, 5, 6, 7],
//           boundaryGap: true,
//           axisLine: {
//             show: false
//           },
//           axisTick: {
//             show: false
//           },
//           axisLabel: {
//             inside: true,
//             color: '#D4D8F2',
//             fontSize: 20,
//             margin: 1,
//           },
//           splitArea: {//背景条纹
//             show: true,
//             areaStyle: {
//               color: [
//                 '#F6F8FF',
//                 '#FFFFFF'
//               ]
//             }
//           }
//         },
//         yAxis: {
//           type: 'value',
//           splitNumber: 2,
//           splitLine: {
//             show: false
//           },
//           axisTick: {
//             show: false
//           },
//           axisLine: {
//             show: false,
//           },
//           axisLabel: {
//             color: '#999999',
//             fontSize: 14
//           },
//           splitArea: {
//             show: false,
//           }
//         },
//         series: [
//           {
//             name: '血糖值',
//             type: 'line',
//             data: echartsSeries,
//             color: "#F58080",
//             symbol: 'circle',     //折点设定为实心点
//             symbolSize: 12,   //设定实心点的大小
//             lineStyle: {
//               normal: {
//                 width: 4,
//                 color: {
//                   type: 'linear',
//                   colorStops: [{
//                     offset: 0,
//                     color: '#1EC5C4' // 0% 处的颜色
//                   }, {
//                     offset: 1,
//                     color: '#A7E99D' // 100% 处的颜色
//                   }],
//                   globalCoord: false // 缺省为 false
//                 }
//               }
//             },
//             areaStyle: {
//               color: {
//                 type: 'linear',
//                 x: 0,
//                 y: 0,
//                 x2: 0,
//                 y2: 1,
//                 colorStops: [{
//                   offset: 0, color: 'rgba(84,211,209,0.3)' // 0% 处的颜色
//                 }, {
//                   offset: 1, color: 'rgba(61,205,203,0)' // 100% 处的颜色
//                 }],
//                 global: false // 缺省为 false
//               }
//             },
//             smooth: true
//           }
//         ]
//       });
//     }, 200);
//   }, [echartsSeries, echartsCircleSeries])

//   return (
//     <div className="sugarmanage_page">
//       <div className='sugar_blood_wrapper'>
//         <div className='head'>
//           <p className='title'>最近7次血糖趋势图</p>
//         </div>
//         <Tabs
//           prefixCls='record_type_wrapper'
//           tabBarActiveTextColor='#333333'
//           tabBarUnderlineStyle={{ borderColor: '#00BC70', borderWidth: 1.5 }}
//           tabs={RANGETIMETYPE}
//           onChange={(tab) => itemClick(tab.key)}>
//         </Tabs>
//         <div className='echarts_wrapper'>
//           <div id='echarts' className='echarts'></div>
//           <div className='info'>
//             <p className='unit'>单位 mmol/L</p>
//             <div className='dot_wrapper'>
//               <div className='dot_height dot'>偏高</div>
//               <div className='dot_low dot'>偏低</div>
//               <div className='dot_normal dot'>正常</div>
//             </div>
//           </div>
//         </div>
//       </div>

//       <div className='recent_data_wrapper'>
//         <div className='head'>
//           <p className='title'>最近30天血糖统计图</p>
//           <div className='more_btn' onClick={() => toSugarList()}>更多</div>
//         </div>

//         {sugarBloodRecent.length ? <div className='echarts_wrapper'>
//           <div id='echarts-circle' className='echarts_circle'></div>
//           <div className='info'>
//             <div className='dot_wrapper'>
//               <div className='dot_height dot'>偏高</div>
//               {echartsCircleSeries.length && <p>{echartsCircleSeries[1].percent}%  {echartsCircleSeries[1].value}次</p>}
//               <div className='dot_low dot'>偏低</div>
//               {echartsCircleSeries.length && <p>{echartsCircleSeries[2].percent}%  {echartsCircleSeries[2].value}次</p>}
//               <div className='dot_normal dot'>正常</div>
//               {echartsCircleSeries.length && <p>{echartsCircleSeries[0].percent}%  {echartsCircleSeries[0].value}次</p>}
//             </div>
//           </div>
//         </div> :
//           <div className='no_record_wrapper'>
//             <div id='echarts-circle' className='echarts_circle'></div>
//             <img className='no_record_cover' src={require('src/images/icon_no_sugar_record.png')} alt="" />
//             <p>最近30天没有测量数据</p>
//           </div>
//         }
        
//       </div>
//     </div>
//   );
// }

// export default SugarData;