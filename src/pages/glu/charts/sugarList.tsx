// import { fetch_sugarblood_history } from 'src/store/sugarblood/action';
// import { useSelector, shallowEqual, useDispatch } from 'react-redux';
// import React, { useState, useCallback } from 'react';
// import { StaticToast } from 'src/components/common';
// import { ApplicationState } from 'src/store';
// import { judgeSugarValue } from './index';
// import format from 'src/utils/format';
// import { DatePicker } from 'zarm';
// import './sugarmanage.scss';

// const SugarList = (props) => {
//   const { location: { state } } = props;
//   const [patientId] = useState(state && state.patientId || '');
//   const [startPickerVisible, setStartPickerVisible] = useState(false);
//   const [endPickerVisible, setEndPickerVisible] = useState(false);
//   const [startDate, setStartDate] = useState();
//   const [endDate, setEndDate] = useState();

//   const { historyData } = useSelector((state: ApplicationState) => {
//     return {
//       historyData: state.sugarblood.history,
//     };
//   }, shallowEqual);

//   const { RANGETIMETYPE_OBJ }: any = useSelector((state: ApplicationState) => {
//     return {
//       RANGETIMETYPE_OBJ: state.dictionary.RANGETIMETYPE_OBJ,
//     }
//   });

//   const dispatch = useDispatch();
//   const fetchHistory = useCallback((options) => dispatch(fetch_sugarblood_history(options)), [dispatch]);

//   const search = useCallback(() => {
//     if (!patientId) {
//       StaticToast.warning('您还未添加患者，无法查询到血糖数据');
//       return
//     }
//     if (!startDate || !endDate) {
//       StaticToast.warning('请选择时间范围');
//       return
//     }
//     fetchHistory({
//       patientId,
//       recordEndTime: `${format.date(endDate, 'yyyy-MM-dd')} 23:59:59`,
//       recordStartTime: `${format.date(startDate, 'yyyy-MM-dd')} 00:00:00`,
//     })
//   }, [patientId, startDate, endDate]);

//   return (
//     <div className="sugarlist_page">
//       <div className="select">
//         <div className='start' onClick={() => setStartPickerVisible(true)}>{startDate}</div>
//         <span>至</span>
//         <div className='end' onClick={() => setEndPickerVisible(true)}>{endDate}</div>
//         <div className='search_btn' onClick={() => search()}>查询</div>
//       </div>
//       <div className='data_wrapper'>
//         {Object.keys(historyData).map(element => {
//           return <div className='element_wrapper' key={`element_wrapper_${element}`}>
//             <p className='title'>{element}</p>
//             <div className='item_wrapper'>
//               {historyData[element].map(item => <div className='item' key={`item_${item.id}`}>
//                 <p className='type'>{RANGETIMETYPE_OBJ[item.rangeTimeType]}</p>
//                 <p className='time'>{format.date(item.recordTime, 'hh:mm')}</p>
//                 <p className='value'>{item.recordValue} mmol/L</p>
//                 <p className='status'>{judgeSugarValue(item)}</p>
//               </div>)}
//             </div>
//           </div>
//         })}
//       </div>
//       <DatePicker
//         visible={startPickerVisible}
//         mode="date"
//         value={startDate}
//         max={endDate}
//         onOk={(value) => {
//           setStartDate(format.date(value, 'yyyy-MM-dd'));
//           setStartPickerVisible(false);
//         }}
//         onCancel={() => setStartPickerVisible(false)}
//       />
//       <DatePicker
//         visible={endPickerVisible}
//         mode="date"
//         min={startDate}
//         value={endDate}
//         onOk={(value) => {
//           setEndDate(format.date(value, 'yyyy-MM-dd'));
//           setEndPickerVisible(false);
//         }}
//         onCancel={() => setEndPickerVisible(false)}
//       />
//     </div>
//   );
// }

// export default SugarList;