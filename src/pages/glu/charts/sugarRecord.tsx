// import React, { useState, useCallback, useEffect } from 'react';
// import { useDispatch } from 'react-redux';
// import { Ruler, FixedButton, StaticToast } from 'src/components/common';
// import { save_sugarblood } from 'src/store/sugarblood/action';
// import { Cell, DateSelect } from 'zarm';
// import format from 'src/utils/format';
// import classnames from 'classnames';
// import './sugarmanage.scss';

// const tabs = [
//   { title: '早餐前', key: 'BEFORE_BREAKFAST' },
//   { title: '早餐后', key: 'AFTER_BREAKFAST' },
//   { title: '午餐前', key: 'BEFORE_LUNCH' },
//   { title: '午餐后', key: 'AFTER_LUNCH' },
//   { title: '晚餐前', key: 'BEFORE_DINNER' },
//   { title: '晚餐后', key: 'AFTER_DINNER' },
//   { title: '睡前', key: 'BEFORE_SLEEP' },
// ];

// const SugarRecord = (props) => {
//   const { location: { state } } = props;
//   const [patientId] = useState(state && state.patientId || '');
//   const [rangeTimeType, setRangeTimeType] = useState('BEFORE_BREAKFAST');
//   const [recordValue, setRecordValue] = useState(6.0);
//   const [recordDate, setRecordDate]: [any, any] = useState();
//   const [recordTime, setRecordTime]: [any, any] = useState();

//   useEffect(() => {
//     setRecordDate(new Date());
//     setRecordTime(new Date());
//   }, []);

//   const dispatch = useDispatch();
//   const saveBlood = (options, onSuccess) => dispatch(save_sugarblood(options, onSuccess));

//   const save = useCallback(() => {
//     if (!patientId) {
//       StaticToast.warning('您还未添加患者，无法记录血糖数据');
//       return
//     }
//     if (!recordDate || !recordTime) {
//       StaticToast.warning('请选择血糖的测量时间');
//       return
//     }

//     saveBlood({
//       patientId,
//       rangeTimeType,
//       recordValue,
//       recordTime: `${format.date(recordDate, 'yyyy-MM-dd')} ${format.date(new Date(recordTime), 'hh:mm:ss')}`
//     }, () => {
//       props.history.replace('/hospital/sugarbloodmanage');
//     })
//   }, [rangeTimeType, recordValue, recordDate, recordTime]);

//   const rulerRef = useCallback((node) => {
//     setTimeout(() => {
//       return new Ruler(
//         {
//           el: node,
//           maxValue: 32,
//           heightDecimal: 30,
//           minValue: 0,
//           fontSize: 12,
//           fontColor: '#999999',
//           colorDecimal: '#DDDDDD',
//           colorDigit: '#DDDDDD',
//           currentValue: recordValue,
//           handleValue: value => setRecordValue(value),
//           precision: 0.1
//         }
//       );
//     }, 200);
//   }, []);

//   const rangeClick = useCallback((tab) => {
//     setRangeTimeType(tab.key);
//   }, []);

//   return (
//     <div className="sugarecord_page">
//       <div className='record_type_select'>
//         {tabs.map(item => <div className={classnames('record_type', { active: rangeTimeType === item.key })} key={`record_type_${item.key}`} onClick={() => rangeClick(item)}>
//           {item.title}
//         </div>)}
//       </div>
//       <div className='record_input_wrapper'>
//         <p className="title">血糖值</p>
//         <p className='value'>{recordValue}</p>
//         <div ref={rulerRef} ></div>
//       </div>
//       <div className='record_info_wrapper'>
//         <Cell className='edit_cell select' title="测量日期" hasArrow>
//           <DateSelect
//             hasArrow={false}
//             title="测量日期"
//             placeholder="请选择日期"
//             mode="date"
//             min="1974-05-16"
//             max="2027-05-15"
//             value={recordDate}
//             onOk={(value) => setRecordDate(value)}
//           />
//         </Cell>
//         <Cell className='edit_cell select' title="测量时间" hasArrow>
//           <DateSelect
//             hasArrow={false}
//             title="测量时间"
//             placeholder="请选择时间"
//             mode="time"
//             value={recordTime}
//             onOk={(value) => setRecordTime(value)}
//           />
//         </Cell>
//       </div>
//       <FixedButton buttonClick={() => save()} text='保存' />
//     </div>
//   );
// }

// export default SugarRecord;