// @import "src/style/index";

// .sugarmanage_page {
//   background: #eff0fa;

//   .patients_wrapper {
//     width: 100%;
//     height: r(270);
//     position: relative;
//     background: linear-gradient(135deg, #92dabd 0%, #00bc70 100%);
//     padding-top: r(24);

//     .patients_list {
//       width: 100%;
//       height: r(34);
//       overflow: hidden;

//       .inner {
//         height: 120%;
//         overflow-x: auto;
//         white-space: nowrap;

//         &::-webkit-scrollbar {
//           display: none;
//         }

//         .patient_item {
//           display: inline-block;
//           padding: 0 r(22);
//           height: r(34);
//           border-radius: r(17);
//           line-height: r(34);
//           font-size: r(15);
//           font-weight: 600;
//           color: #fff;
//           border: r(1) solid rgba(255, 255, 255, 1);
//           text-align: center;
//           margin-right: r(20);

//           &.active {
//             background: #fff;
//             color: #00bc70;
//           }

//           &:first-of-type {
//             margin-left: r(30);
//           }
//         }
//       }
//     }

//     .progress_wrapper {
//       position: relative;
//       margin-top: r(30);

//       .first_pot {
//         position: absolute;
//         top: r(30);
//         left: r(90);
//         border-radius: 50%;
//         height: r(15);
//         width: r(15);
//         background: #fff;
//       }

//       .second_pot {
//         position: absolute;
//         top: r(16);
//         left: r(113);
//         border-radius: 50%;
//         width: r(7);
//         height: r(7);
//         background: rgba(255, 255, 255, .5);
//       }

//       .third_pot {
//         position: absolute;
//         bottom: r(20);
//         right: r(113);
//         border-radius: 50%;
//         width: r(7);
//         height: r(7);
//         background: hsla(0, 0%, 100%, 0.3);
//       }

//       .progress {
//         display: block;
//         margin: 0 auto;

//         .progress_content {
//           .progress_text_date,
//           .progress_text_time,
//           .progress_text_unit {
//             font-size: r(12);
//             color: rgba(255, 255, 255, .5);
//             margin-bottom: r(8);
//           }

//           .progress_text_status {
//             color: #fff;
//             font-size: r(19);
//             font-weight: 600;
//             margin-bottom: r(5);
//           }

//           .progress_text_record {
//             color: #fff;
//             font-size: r(34);
//             font-weight: 600;
//           }

//           .no_progress_text {
//             color: #fff;
//             font-size: r(19);
//           }
//         }
//       }

//       .za-progress__track {
//         stroke: #93ecc7;
//       }

//       .za-progress__thumb {
//         stroke: rgba(255, 255, 255, .8);
//       }
//     }

//     .record_wrapper {
//       position: absolute;
//       right: r(20);
//       bottom: r(10);
//       @include display-flex;
//       @include align-items(center);

//       flex-direction: column;
//       color: #fff;
//       font-size: r(12);

//       .rocord {
//         width: r(45);
//         height: r(45);
//         position: relative;
//         border-radius: 50%;
//         background: rgba(255, 255, 255, .5);
//         margin-bottom: r(6);
//         @include display-flex;
//         @include align-items(center);
//         @include justify-content(center);

//         img {
//           display: block;
//           height: r(22);
//           width: r(18);
//         }
//       }
//     }
//   }

//   .sugar_blood_wrapper {
//     width: 100%;
//     background: #fbfbfb;

//     .head {
//       padding: r(15);
//       @include display-flex;
//       @include align-items(center);
//       @include justify-content(space-between);

//       border-bottom: r(1) solid #f0f0f0;

//       .title {
//         color: $base-text-color;
//         font-size: r(16);
//         font-weight: 600;
//       }

//       .more_btn {
//         position: relative;
//         width: r(55);
//         height: r(22);
//         line-height: r(20);
//         border-radius: r(11);
//         padding-left: r(10);
//         border: r(1) solid #ccc;
//         color: #999;

//         &::after {
//           content: '';
//           position: absolute;
//           right: r(7);
//           top: r(6);
//           width: r(8);
//           height: r(8);
//           border-top: r(2) solid #ccc;
//           border-right: r(2) solid #ccc;
//           transform: rotate(45deg);
//         }
//       }
//     }

//     .record_type_wrapper {
//       height: r(45);
//       width: 100%;
//       overflow: hidden;
//       line-height: r(45);
//       position: relative;

//       .inner {
//         height: 110%;
//         overflow-x: auto;
//         white-space: nowrap;

//         &::-webkit-scrollbar {
//           display: none;
//         }

//         .item {
//           display: inline-block;
//           margin-right: r(26);
//           color: #333;
//           font-size: r(13);

//           &:first-of-type {
//             margin-left: r(15);
//           }

//           &.active {
//             font-weight: 600;
//           }
//         }
//       }
//     }

//     .echarts_wrapper {
//       width: 100%;
//       position: relative;

//       .default {
//         position: absolute;
//         color: #999;
//         font-size: r(12);
//         right: r(30);
//         top: r(8);
//         z-index: 10;
//       }

//       .echarts {
//         display: block;
//         margin: r(15) auto r(8);
//         width: r(345);
//         height: r(160);
//       }

//       .info {
//         padding: 0 r(20) r(10) r(50);
//         @include display-flex;
//         @include align-items(center);
//         @include justify-content(space-between);

//         color: #999;
//         font-size: r(12);

//         .dot_wrapper {
//           @include display-flex;
//           @include align-items(center);

//           .dot {
//             padding: 0 r(16);
//             position: relative;

//             &.dot_height {
//               &::before {
//                 content: '';
//                 position: absolute;
//                 left: r(2);
//                 top: r(2);
//                 width: r(10);
//                 height: r(10);
//                 border: r(3) solid #ff9c82;
//                 border-radius: 50%;
//                 background: #fff;
//               }
//             }

//             &.dot_low {
//               &::before {
//                 content: '';
//                 position: absolute;
//                 left: r(2);
//                 top: r(2);
//                 width: r(10);
//                 height: r(10);
//                 border: r(3) solid #ffce74;
//                 border-radius: 50%;
//                 background: #fff;
//               }
//             }

//             &.dot_normal {
//               &::before {
//                 content: '';
//                 position: absolute;
//                 left: r(2);
//                 top: r(2);
//                 width: r(10);
//                 height: r(10);
//                 border: r(3) solid #00bc70;
//                 border-radius: 50%;
//                 background: #fff;
//               }
//             }
//           }
//         }
//       }
//     }
//   }

//   .recent_data_wrapper {
//     width: 100%;
//     background: #fff;
//     margin-top: r(10);

//     .head {
//       padding: r(15);
//       @include display-flex;
//       @include align-items(center);
//       @include justify-content(space-between);

//       border-bottom: r(1) solid #f0f0f0;

//       .title {
//         color: $base-text-color;
//         font-size: r(16);
//         font-weight: 600;
//       }

//       .more_btn {
//         position: relative;
//         width: r(55);
//         height: r(22);
//         line-height: r(20);
//         border-radius: r(11);
//         padding-left: r(10);
//         border: r(1) solid #ccc;
//         color: #999;

//         &::after {
//           content: '';
//           position: absolute;
//           right: r(7);
//           top: r(6);
//           width: r(8);
//           height: r(8);
//           border-top: r(2) solid #ccc;
//           border-right: r(2) solid #ccc;
//           transform: rotate(45deg);
//         }
//       }
//     }

//     .no_record_wrapper {
//       @include flex;
//       @include display-flex;
//       @include align-items(center);

//       flex-direction: column;
//       padding: r(19) r(40) r(25);

//       img {
//         margin-top: r(15);
//         height: r(78);
//         width: r(65);
//       }

//       p {
//         padding-top: r(15);
//         color: #999;
//         font-size: r(12);
//       }
//     }

//     .echarts_wrapper {
//       padding: r(19) r(40) r(25);
//       @include display-flex;
//       @include align-items(center);
//       @include justify-content(space-between);

//       .echarts_circle {
//         width: r(160);
//         height: r(160);
//       }

//       .info {
//         .dot_wrapper {
//           p {
//             padding-left: r(16);
//             color: #333;
//             font-size: r(14);
//             font-weight: 600;
//           }
//         }

//         .dot {
//           padding: 0 r(16);
//           position: relative;

//           &.dot_height {
//             &::before {
//               content: '';
//               position: absolute;
//               left: r(2);
//               top: r(5);
//               width: r(8);
//               height: r(8);
//               border-radius: 50%;
//               background: #ff9c82;
//             }
//           }

//           &.dot_low {
//             &::before {
//               content: '';
//               position: absolute;
//               left: r(2);
//               top: r(5);
//               width: r(8);
//               height: r(8);
//               border-radius: 50%;
//               background: #ffce74;
//             }
//           }

//           &.dot_normal {
//             &::before {
//               content: '';
//               position: absolute;
//               left: r(2);
//               top: r(5);
//               width: r(8);
//               height: r(8);
//               border-radius: 50%;
//               background: #00bc70;
//             }
//           }
//         }
//       }
//     }
//   }

//   .sugar_recommond_wrapper {
//     margin-top: r(10);
//     padding: r(15);
//     background: #fbfbfb;

//     .title {
//       color: $base-text-color;
//       font-size: r(16);
//       font-weight: 600;
//     }

//     .doctor_item {
//       margin: 0;
//     }
//   }
// }

// .sugarecord_page {
//   .record_type_select {
//     background: #fff;
//     // margin-bottom: r(10);

//     @include display-flex;
//     @include justify-content(flex-start);

//     flex-wrap: wrap;
//     // border-radius: 0px 0px 20px 20px;
//     padding: r(10) 0;

//     .record_type {
//       width: r(74);
//       height: r(34);
//       border-radius: r(17);
//       border: r(1) solid #eee;
//       line-height: r(34);
//       text-align: center;
//       font-size: r(15);
//       color: #333;
//       margin: r(5) r(9.5);

//       &.active {
//         background: linear-gradient(231deg, rgba(20, 207, 141, 1) 0%, rgba(0, 188, 112, 1) 100%);
//         border: none;
//         color: #fff;
//         font-weight: 600;
//       }
//     }
//   }

//   .record_input_wrapper {
//     position: relative;
//     background: #fff;
//     box-shadow: inset 0 r(10) r(13) 0 #f5f7fe;

//     .title {
//       color: $base-text-color;
//       font-size: r(14);
//       font-weight: 600;
//       padding: r(23) 0 0 r(15);
//     }

//     .value {
//       color: $base-text-color;
//       text-align: center;
//       font-size: r(44);
//       font-weight: bold;
//     }
//   }

//   .record_info_wrapper {
//     margin-top: r(10);
//     background: #fff;

//     .edit_cell {
//       .za-date-select__input,
//       .za-cell__title.za-cell__title--label,
//       .za-select__input,
//       .za-input {
//         font-size: r(15);
//         @include placeholder;
//       }

//       .za-date-select__input {
//         text-align: right;
//       }

//       &:first-child {
//         &::after {
//           display: none;
//         }
//       }

//       .za-cell__footer {
//         color: #464646;
//         font-size: r(15);
//       }

//       .za-cell__title.za-cell__title--label {
//         width: r(90);
//       }

//       &.custom {
//         p {
//           @include line(1);

//           font-size: r(15);
//           color: $zarm-placeholder;
//           height: inherit;
//           max-width: r(200);

//           &.text {
//             color: $base-text-color;
//           }
//         }
//       }
//     }
//   }
// }

// .sugarlist_page {
//   padding-bottom: r(20);

//   .select {
//     @include display-flex;
//     @include align-items(center);

//     padding: r(10) r(15);
//     border-bottom: r(1) solid #eee;

//     .start,
//     .end {
//       width: r(106);
//       height: r(30);
//       line-height: r(28);
//       text-align: center;
//       border: r(1) solid #eee;
//       border-radius: r(4);
//       background: #fff;
//     }

//     span {
//       color: #999;
//       font-size: r(14);
//       padding: 0 r(6);
//     }

//     .search_btn {
//       width: r(79);
//       height: r(28);
//       line-height: r(28);
//       background: linear-gradient(231deg, #14cf8d 0%, #00bc70 100%);
//       border-radius: r(4);
//       margin-left: r(30);
//       text-align: center;
//       color: #fff;
//       font-size: r(14);
//     }
//   }

//   .data_wrapper {
//     .title {
//       color: #333;
//       font-size: r(15);
//       padding: r(15);
//       font-weight: 600;
//     }

//     .item_wrapper {
//       margin: 0 auto;
//       width: r(345);
//       background: #fff;
//       border-radius: r(4);
//       box-shadow: 0 r(6) r(6) #f0f0f0;

//       .item {
//         height: r(52);
//         line-height: r(52);
//         padding: 0 r(15);
//         border-bottom: r(1) solid #eee;
//         @include display-flex;
//         @include align-items(center);
//         @include justify-content(space-between);

//         .type,
//         .time,
//         .value,
//         .status {
//           font-size: r(14);
//         }

//         .type {
//           color: #999;
//           width: r(80);
//         }

//         .time {
//           @include flex;

//           color: #999;
//         }

//         .value {
//           @include flex;

//           color: #333;
//         }

//         &:last-of-type {
//           border: none;
//         }
//       }
//     }
//   }
// }

// .sugar_no_patient_modal {
//   padding-top: r(12);

//   .sugar_modal_close_button {
//     position: absolute;
//     right: r(15);
//     top: r(10);
//     width: r(15);
//     height: r(15);
//   }
// }
