// import { Card, AbbText, SvgIcon } from 'src/components/common';
// import React, { useCallback, useEffect } from 'react';
// import SugarRecommand from '../recommand';
// import './sugarblood.scss';

// import sugarblood_disease_svg from 'src/svgs/sugarblood-disease-intro.svg';
// import book_svg from 'src/svgs/book.svg';

// const SugarbloodIntro = (props) => {
//   useEffect(() => {
//     pageScrollTop();
//   }, []);

//   const recommandDoctorClick = useCallback((item) => {
//     const { id = '', staffName = '' } = item;
//     window._XFLOW_.pushEvent(['click', 'ZAHLWYY_ZBZX_TNB', '专病中心_糖尿病', { ZAHLWYY_CLICK_CONTENT: `专病中心_糖尿病咨询_${staffName}` }]);
//     props.history.push({
//       pathname: '/hospital/doctordetail',
//       search: `staffId=${id}`
//     })
//   }, []);

//   const toSugarOne = useCallback(() => {
//     props.history.push({
//       pathname: '/hospital/sugarblood/one'
//     })
//   }, []);

//   const toSugarTwo = useCallback(() => {
//     props.history.push({
//       pathname: '/hospital/sugarblood/two'
//     })
//   }, []);

//   const pageScrollTop = useCallback(() => {
//     window.scroll({ top: 0, behavior: "smooth" });
//   }, []);

//   return (
//     <div className='sugarblood_page'>
//       <div className='background'></div>
//       <Card prefixCls='head'>
//         <SvgIcon type='img' className='bg_intro' src={sugarblood_disease_svg} />
//         <div className='text'>一种以血糖增高为特征的代谢性疾病，如得不到良好控制，到导致血管、精神广泛损害。目前没有根治方式，尽量控制疾病发展，预防并发症是治疗的目标。</div>
//         <div className='from'>
//           <SvgIcon type='img' src={book_svg} />
//           张俊清主任医生编审，北京大学第一医院
//         </div>
//       </Card>
//       <div className="title">
//         <SvgIcon className='icon' src={require('src/svgs/sprite-recommand-doctor.svg')} />
//         病种分类
//       </div>
//       <Card prefixCls='sugar_classify_wrapper'>
//         <div className="item">
//           <div className="item_head">
//             <img src={require('src/images/icon_disease_one.png')} alt="" />
//             1型糖尿病
//             <div className="item_btn" onClick={() => toSugarOne()}>查看</div>
//           </div>
//           <div className="item_text">
//             糖尿病患者中胰岛β细胞数量少或遭到破坏，导致胰岛素分泌绝对缺乏的被称为1型糖尿病。
//           </div>
//           <div className="text"></div>
//         </div>
//         <div className="item">
//           <div className="item_head">
//             <img src={require('src/images/icon_disease_two.png')} alt="" />
//             2型糖尿病
//               <div className="item_btn" onClick={() => toSugarTwo()}>查看</div>
//           </div>
//           <div className="item_text">
//             全身组织器官出现胰岛素抵抗，胰岛素分泌相对缺 乏的被称为2型糖尿病。
//           </div>
//           <div className="text"></div>
//         </div>
//       </Card>
//       <div className="title">
//         <SvgIcon type='img' className='icon icon_sugar_department' src={require('src/images/icon_sugar_department.png')} />
//         就诊科室
//       </div>
//       <Card prefixCls='sugar_department_wrapper'>
//         内科/内分泌科
//       </Card>
//       <div className="title">
//         <SvgIcon type='img' className='icon icon_sugar_classify' src={require('src/images/icon_sugar_detail.png')} />
//         病种详情
//       </div>
//       <Card prefixCls='sugar_detail_wrapper'>
//         <div className='item'>
//           <div className="item_title">病因</div>
//           <AbbText
//             showPart={<p>
//               正常情况下，食物消化得到的葡萄糖进入血液，胰岛素可以帮助葡萄糖从血液进入身体细胞来为细胞供能，从而使血液中的血糖降低。当胰岛素无法有效降低人体的血糖时，人就会患上糖尿病。针对不同的糖尿病分型，具体的发病原理也有所不同。
//               <br />1型糖尿病
//               <br />患者胰腺上的β细胞团会由于遗传等原因而减少，或出现原因不明确的炎症，使得能够分泌胰岛素的β细胞数量，只有正常人的1/10，从而胰岛素分泌“绝对”不足。
//               <br />糖尿病患者存在家族聚集性发病的倾向，也可以理解成为具有遗传易感性的特点。比如一个人直系亲属里有糖尿病患者，那么这个人的遗传基因情况决定了，他比别人更容易得糖尿病。
//               <br />还有一种情况，部分人会因为免疫系统有问题，患上1型糖尿病。当人在感染了某些病毒（如柯萨奇病毒、风疹病毒、腮腺病毒等）之后，免疫系统可能会攻击病毒同时，还错误攻击了β细胞，导致β细胞大量死亡，胰岛素的分泌量大幅下降。
//               <br />2型糖尿病
//               <br />与1型糖尿病不同，患者的胰岛往往淀粉样变性，虽然分泌胰岛素的β细胞并没有明显减少，但身体各组织器官对胰岛素的指令反应力下降，让胰岛素出现“相对”不足的状况。
//               <br />2型糖尿病患者，同样也具有家族聚集性发病的倾向性，这种遗传易感性的特点类似于1型糖尿病，只不过不同类型糖尿病遗传的基因有所区分。
//               <br />另外，不良的生活习惯也是2型糖尿病发生的重要原因。如果一个人进食过多而体力活动又减少，则容易导致肥胖，加上其本身如果具有2型糖尿病的遗传易感性，那么就更容易患上糖尿病。
//               </p>}
//           />
//         </div>
//         <div className='item'>
//           <div className="item_title">发病部位</div>
//           <AbbText
//             showPart={<p>
//               人体内部有一套精密调整血糖含量的系统，这个系统的主要工作者就是胰腺分泌出来的两种物质：胰岛素和胰高血糖素。
//               <br />空腹或餐后，血液中血糖水平过高的时候，胰腺上一种名字叫做β细胞的细胞团，开始分泌胰岛素，胰岛素激活能够像银行一样储蓄血糖的细胞，比如肌肉细胞和脂肪细胞。
//               <br />过高的血糖被这些肌肉和脂肪细胞吸收，并且被储存起来，从而使得高血糖回归到正常范围之内，人体也可以接受这样的正常血糖。
//               <br />如果胰腺里的β细胞因为一些原因死亡，或者其中一部分β细胞无法正常地工作，从而极大地扰乱了胰岛素的工作秩序，此时不管空腹的或者用餐后的血糖如何飙高，胰岛素的量总是很低，甚至完全都没有
//               <br />而肌肉细胞、脂肪细胞如果因为某种原因根本不听从胰岛素的指令，也不去储存葡萄糖，不管来多少数量的胰岛素催促它们，它们就是不接受储蓄血糖的任务。因此，当这些环节部位发生了问题，人就会得糖尿病。
//               </p>}
//           />
//         </div>
//         <div className='item'>
//           <div className="item_title">预后影响</div>
//           <AbbText
//             showPart={<p>
//               糖尿病早期开始有效治疗的话，预后会比较好。60岁以后发现的患者，其预后比较差。
//               <br />糖尿病如果得不到有效控制，会给全身广泛组织和器官带来损害，如引发糖尿病肾病、视网膜病变、心脏及血管疾病、周围神经病变等。
//               <br />目前还没有能够根治糖尿病的方式，对确诊糖尿病的患者而言，所要做的是尽量控制糖尿病的发展，保持血糖水平稳定在一个合理的范围，预防并发症。
//               </p>}
//           />
//         </div>
//       </Card>
//       <div className="title">
//         <SvgIcon className='icon' src={require('src/svgs/sprite-recommand-doctor.svg')} />
//         在线名医推荐
//       </div>
//       <div className='sugar_recommond'>
//         <SugarRecommand recommandClick={recommandDoctorClick} />
//       </div>
//     </div>
//   );
// }

// export default SugarbloodIntro;