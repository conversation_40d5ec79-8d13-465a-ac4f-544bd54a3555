// import React, { useEffect, useCallback, useState, useMemo } from 'react';
// import { AbbText, SvgIcon } from 'src/components/common';
// import SugarRecommand from '../recommand'
// import classnames from 'classnames';
// import './sugarblood.scss';

// import sugarblood_disease_svg from 'src/svgs/sugarblood-disease-one.svg';
// import book_svg from 'src/svgs/book.svg';
// // declare const wx: any;

// const SugarbloodOne = (props) => {
//   const [topOffset, setTopOffset] = useState(20);
//   const [scrollTop, setScrollTop] = useState(0);
//   const [innerDomList, setInnerDomList]: [any, any] = useState([]);
//   const [alpha, setAlpha] = useState(0);
//   const [bravo, setBravo] = useState(0);
//   const [charlie, setC<PERSON><PERSON>] = useState(0);
//   const [delta, setDelta] = useState(0);
//   const [echo, setEcho] = useState(0);

//   const [menuHeight, setMenuHeight] = useState(0);

//   useEffect(() => {
//     document.addEventListener('scroll', scroll);
//     return () => document.removeEventListener('scroll', scroll);
//   }, []);

//   const scroll = () => {
//     let scrollTop = document.documentElement.scrollTop || window.pageYOffset || document.body.scrollTop;
//     setScrollTop(scrollTop);
//   }

//   const menuRef = useCallback(node => {
//     setTimeout(() => {
//       if (node !== null) {
//         setMenuHeight(node.getBoundingClientRect().height);
//       }
//     }, 200);
//   }, []);

//   const measuredRef = useCallback(node => {
//     setTimeout(() => {
//       if (node !== null) {
//         setTopOffset(node.getBoundingClientRect().height);
//       }
//     }, 200);
//   }, []);

//   const wrapperRef = useCallback(node => {
//     setTimeout(() => {
//       if (node !== null) {
//         const attrs = node.attributes;
//         const value = node.offsetTop - menuHeight;
//         switch (attrs['data-order'].value) {
//           case 'alpha':
//             setAlpha(value);
//             break;
//           case 'bravo':
//             setBravo(value);
//             break;
//           case 'charlie':
//             setCharlie(value);
//             break;
//           case 'delta':
//             setDelta(value);
//             break;
//           case 'echo':
//             setEcho(value);
//             break;
//           default:
//             break;
//         }
//       }
//     }, 200);
//   }, [innerDomList]);

//   const innerDomChange = useCallback((index, value) => {
//     const data = innerDomList.concat([]);
//     data[index] = value || 0;
//     setInnerDomList(data);
//   }, [innerDomList]);

//   const recommandDoctorClick = useCallback((item) => {
//     props.history.push({
//       pathname: '/hospital/doctordetail',
//       search: `staffId=${item.id}`
//     });
//   }, []);

//   const scrollTo = useCallback((key) => {
//     switch (key) {
//       case 'alpha':
//         window.scroll({
//           top: alpha + 1,
//           behavior: "smooth"
//         });
//         break;
//       case 'bravo':
//         window.scroll({
//           top: bravo + 1,
//           behavior: "smooth"
//         });
//         break;
//       case 'charlie':
//         window.scroll({
//           top: charlie + 1,
//           behavior: "smooth"
//         });
//         break;
//       case 'delta':
//         window.scroll({
//           top: delta + 1,
//           behavior: "smooth"
//         });
//         break;
//       case 'echo':
//         window.scroll({
//           top: echo + 1,
//           behavior: "smooth"
//         });
//         break;
//       default:
//         break;
//     }
//   }, [alpha, bravo, charlie, delta, echo]);

//   const isFixed = useMemo(() => {
//     return scrollTop >= topOffset || false;
//   }, [topOffset, scrollTop]);

//   const isActive = useMemo(() => {
//     if (scrollTop >= alpha && scrollTop < bravo) {
//       return 'alpha'
//     } else if (scrollTop >= bravo && scrollTop < charlie) {
//       return 'bravo'
//     } else if (scrollTop >= charlie && scrollTop < delta) {
//       return 'charlie'
//     } else if (scrollTop >= delta && scrollTop < echo) {
//       return 'delta'
//     } else if (scrollTop >= echo) {
//       return 'echo'
//     }
//     return 'normal'
//   }, [scrollTop, alpha, bravo, charlie, delta, echo]);

//   return (
//     <div className='sugardetail_page'>
//       <div ref={measuredRef}>
//         <div className='head'>
//           <div className="text_wrapper">
//             <p className='title'>1型糖尿病</p>
//             <p className='sub_title'>Type 1 diabetes mellitus</p>
//             <p className='text'>1型糖尿病多为年轻人发病，是一种因体内缺少胰岛素导致血糖升高的疾病。高血糖升高会对人体各种组织器官造成伤害，引发一系列并发症。</p>
//           </div>
//           <SvgIcon type='img' src={sugarblood_disease_svg} />
//         </div>
//         <div className='from'>
//           <SvgIcon type='img' src={book_svg} />
//           张俊清主任医生编审，北京大学第一医院
//       </div>
//       </div>
//       <div ref={menuRef} className={classnames('menu_bar', { isFixed })}>
//         <div className='menu_item' onClick={() => scrollTo('alpha')}>
//           <img className={classnames({ highlight: isActive === 'alpha' || isActive === 'normal' })} src={require('src/images/icon_tab_intro.png')} />
//           概要
//         </div>
//         <div className='menu_item' onClick={() => scrollTo('bravo')}>
//           <img className={classnames({ highlight: isActive === 'bravo' || isActive === 'normal' })} src={require('src/images/icon_tab_diagnose.png')} />
//           诊断
//         </div>
//         <div className='menu_item' onClick={() => scrollTo('charlie')}>
//           <img className={classnames({ highlight: isActive === 'charlie' || isActive === 'normal' })} src={require('src/images/icon_tab_cure.png')} />
//           治疗
//         </div>
//         <div className='menu_item' onClick={() => scrollTo('delta')}>
//           <img className={classnames({ highlight: isActive === 'delta' || isActive === 'normal' })} src={require('src/images/icon_tab_inquiry.png')} />
//           就诊
//         </div>
//         <div className='menu_item' onClick={() => scrollTo('echo')}>
//           <img className={classnames({ highlight: isActive === 'echo' || isActive === 'normal' })} src={require('src/images/icon_tab_care.png')} />
//           <div className="title">护理</div>
//         </div>
//       </div>
//       {isFixed && <div className="menu_cover"></div>}

//       <div ref={wrapperRef} data-order='alpha' className='info_wrapper'>
//         <p className="title">疾病概要</p>

//         <div className="item_title">就诊科室</div>
//         <div className='text'>糖尿病专区</div>

//         <div className="item_title">疾病简介</div>
//         <AbbText
//           domChange={innerDomChange}
//           index={0}
//           showPart={
//             <p>
//               1型糖尿病，又被称为胰岛素依赖型糖尿病、青年发病型糖尿病，大部分患者常在35岁以前发病，起病比较突然，是一种因为体内缺乏胰岛素，导致血糖持续升高的疾病。如果血糖长时间得不到有效控制，就会对患者的全身各个组织器官造成损害，尤其是引发一系列的并发症。
//               <br />由于患者体内胰腺中的胰岛罢工，使胰岛素细胞彻底损坏，无法再生产胰岛素，使得1型糖尿病患者体内的胰岛素绝对不足，所以患者需要终身进行胰岛素替代治疗。
//             </p>
//           }
//         />

//         <div className="item_title">高危人群</div>
//         <AbbText
//           domChange={innerDomChange}
//           index={1}
//           showPart={
//             <p>
//               家族中有1型糖尿病患者的人，特别是青年人，是1型糖尿病的主力军，由于从父母那里继承了1型糖尿病的易感基因，加上后天环境不良因素的刺激，如受到病毒感染等，则患病的风险会比家族中没有患者的人要高。
//               <br />另外，如过于肥胖或太瘦的人、长期使用一些影响糖代谢药物（如糖皮质激素、利尿药等）的人、出生时体重低或婴儿期体重比一般小孩轻的人、怀孕时被诊断过糖尿病的女性、生育8斤以上巨大儿的女性等。
//               <br />此外，有些人受到某些病毒感染后，免疫系统功能发生异常，错误攻击人体自身的胰岛细胞，导致胰岛细胞大量死亡，无法生产足够胰岛素时，也会患上1型糖尿病。
//             </p>
//           }
//         />

//         <div className="item_title">病因</div>
//         <AbbText
//           domChange={innerDomChange}
//           index={2}
//           showPart={
//             <p>
//               1型糖尿病的发生，主要与先天的遗传因素和后天的环境因素有关。
//               <br />一般情况下，如果家族人员中有患1型糖尿病的，自己就有可能从上一辈那里继承这种易感基因，之后在一些后天环境因素的刺激下，如感染柯萨奇B病毒、 腮腺炎病毒、风疹病毒等，两者协同作用下，损伤、破坏胰岛细胞功能，使其无法生产胰岛素，以致血糖飙升。
//               <br />还有一种情况是，一些人家族中虽无1型糖尿病患者，但由于后天感染病毒，加上不良的生活、饮食习惯等原因，体内的免疫系统对胰岛无法准确地进行识别，将其当作“异物”，并对其进行攻击，于是产生胰岛炎，使胰岛细胞遭到破坏，不能再生产胰岛素，导致血糖持续升高。
//             </p>
//           }
//         />

//         <div className="item_title">预防</div>
//         <AbbText
//           domChange={innerDomChange}
//           index={3}
//           showPart={
//             <p>
//               1型糖尿病的预防，主要包括以下“三级”。
//               <br />一级预防：通过控制各种危险因素，中止自身免疫的启动，预防1型糖尿病的发生，降低其发病率。首先，对公众进行健康教育，提高全社会对糖尿病危害的认识，改变不良的生活方式，要戒烟、限酒。其次，通过筛查易感基因、自身抗体、代谢指标联合检测等，预测1型糖尿病高危人群，这些人群尤其应注意尽早开始关注自己的血糖，预防或延迟1型糖尿病的发生。
//               <br />二级预防：对于已有免疫学指标异常但尚未发病的人群，通过阻止其自身免疫介导的β细胞损害的进展，防止其发病。
//               <br />三级预防：对于已患病的人，通过规范的胰岛素治疗、良好的临床自我管理、定期的并发症筛查等措施，保护患者残存的胰岛β细胞，加强血糖控制，以防止或减少并发症的发生，降低致残率和死亡率。
//             </p>
//           }
//         />

//         <div className="item_title">并发症</div>
//         <AbbText
//           domChange={innerDomChange}
//           index={4}
//           showPart={
//             <p>
//               1型糖尿病本身并不可怕，可怕的是，因血糖没有控制好，并发症陆陆续续地找上门来。此种疾病不仅容易出现并发症，而且数量还不少。
//               <br />首先，最早、最容易出现的并发症是糖尿病酮症酸中毒，一旦患者体内胰岛素不够了，就会产生大量的酮体，酮体会消耗过多的碱，于是就会出现酮症酸中毒。
//               <br />其次，是血管和神经并发症，血糖升高，体内工作系统被扰乱、打破，影响到血管和神经时，就有可能出现相关并发症，比如脑血栓、脑溢血、冠心病、心肌梗死、手脚麻木、糖尿病足、勃起功能障碍、排尿困难、视力下降、结膜出血、失明，等等。
//               <br />最严重、危害最大的并发症，是肾脏方面的并发症，也就是糖尿病肾病，一旦发病，病情会逐渐恶化，最终出现尿毒症。
//             </p>
//           }
//         />
//       </div>
//       <div ref={wrapperRef} data-order='bravo' className='info_wrapper'>
//         <p className="title">诊断</p>

//         <div className="item_title">临床总体症状</div>
//         <AbbText
//           domChange={innerDomChange}
//           index={5}
//           showPart={
//             <p>
//               多数1型糖尿病患者常突然发病，且多出现“三多一少”的四种典型症状，即吃的多、喝的多、尿的多，体重急剧下降，甚至有的比较严重的患者，一开始就出现了酮症酸中毒，也就是除了血糖升高以外，还出现了恶心、呕吐、腹痛、关节或肌肉疼痛、皮肤黏膜干燥、血压下降、体温不升、呼气中带有酮味、总喜欢睡觉等高血酮和酸中毒现象。
//               <br />由于患者体内胰岛素不足，无法帮助身体各组织器官从血液中获取食物转换而成的葡萄糖，以提供能量，也就是说，吃进去的东西没有利用好，以致掌管“饥饿”的神经中枢得不到足够的营养，让人感受到饥饿，不断地想吃东西。
//               <br />吃得越多，血液中的葡萄糖就越多，当过量时，就会漏到尿液当中，引起尿糖升高，尿液中的糖增多时，尿渗透压会升高，于是大量的尿液往外排，引起多尿，多尿导致血容量减少，使血浆晶体渗透压升高，刺激口渴中枢，让人想喝更多的水。因此，患者会出现“三多一少”的典型症状。
//               <br />另外，当体内胰岛素严重不足时，血液中的酮体浓度会增高，而酮体中的乙酰乙酸和β-羟丁酸都是酸性物质，一旦这两种酸性物质在血液中过多积蓄，血液会变酸，引起酸中毒。所以患者会出现酮症酸中毒。
//             </p>
//           }
//         />

//         <div className="item_title">常见症状分布</div>
//         <div className='chart'>
//           <div className="chart_bar one">
//             <div className="percent">
//               <p>34.0%</p>
//               <p>多饮</p>
//             </div>
//           </div>
//           <div className="chart_bar two">
//             <div className="percent">
//               <p>25.0%</p>
//               <p>多尿</p>
//             </div>
//           </div>
//           <div className="chart_bar three">
//             <div className="percent">
//               <p>25.0%</p>
//               <p>口干</p>
//             </div>
//           </div>
//           <div className="chart_bar four">
//             <div className="percent">
//               <p>20.0%</p>
//               <p>血糖升高</p>
//             </div>
//           </div>
//         </div>

//         <div className="item_title">常用检验检查</div>
//         <AbbText
//           domChange={innerDomChange}
//           index={6}
//           showPart={
//             <p>
//               常用的检查包括尿糖测定、β细胞功能测定、口服葡萄糖耐量试验及血糖、糖化血红蛋白检查。
//               <br />检测尿糖时，若结果显示为阳性，则有可能患1型糖尿病，但不能百分百确诊。要想确诊，还需进一步检测血糖及胰岛β细胞功能。血糖的测定，一般包括空腹血糖和餐后2小时血糖，如果空腹血糖≥7.0mmol/L，餐后2小时血糖≥11.1mmol/L，即可确诊。
//               <br />有时测完空腹血糖后，发现血糖正常或只是轻度升高者，可直接做一个口服葡萄糖耐量试验，即晚上10点后停止进食进水8～10小时，第二天早7～9点空腹到医院，抽取静脉血测量血糖，此后在5分钟内喝完混合好的葡萄糖水，从喝糖水的第一口开始计算时间，分别在服糖水后半小时、1小时、2小时和3小时，抽取静脉血测量血糖，若口服葡萄糖耐量试验2小时血浆葡萄糖≥11.1mmol/L，可确诊。
//               <br />胰岛β细胞功能测定，可与糖耐量试验测血糖同时进行，主要检测各时相的胰岛素及C-肽水平，若胰岛素及C-肽水平很低，可确诊。
//               <br />此外，还有糖化血红蛋白检查，它主要可以判断患者血糖控制效果如何；糖尿病并发症检查，比如怀疑并发糖尿病酮症酸中毒时，除测定血糖外，还应测定生化检查，以及血酮、尿酮、血渗透压、血气、酸碱平衡等检查。
//             </p>
//           }
//         />
//       </div>
//       <div ref={wrapperRef} data-order='charlie' className='info_wrapper'>
//         <p className="title">治疗</p>

//         <div className="item_title">治疗方案</div>
//         <AbbText
//           domChange={innerDomChange}
//           index={7}
//           showPart={
//             <p>
//               1型糖尿病的治疗以注射胰岛素为主，辅以运动治疗。
//               <br />1.胰岛素治疗
//               <br />根据胰岛素的种类及作用时间，可将其分为短效胰岛素、中效珠蛋白胰岛素、长效鱼精蛋白胰岛素。短效胰岛素，皮下注射后吸收快，作用迅速、持续时间短、便于调整；中效珠蛋白胰岛素，起作用时间较慢、维持时间较长，主要用于病情稳定的糖尿病患者，可减少注射次数；长效鱼精蛋白胰岛素，吸收速度更慢，作用时间更长，主要用于提供基础胰岛素的需要量，降低夜间血糖和空腹血糖。
//               <br />注射胰岛素时，可选上臂前外侧、大腿前部、腰部、臀部，每针间隔至少1厘米，并且要有计划地轮换部位，避免多次注射同一部位，以防注射部位发生硬结、吸收不良等现象。
//               <br />治疗方法上，根据病情的严重程度，三种胰岛素可以单独使用，也可搭配使用。一般情况下，1型糖尿病初始患者，先用短效胰岛素治疗，每天剂量分4次，分别于早、中、晚餐前20～30分钟皮下注射，睡前再注射一次。病情控制后，睡前的一次注射可取消。
//               <br />搭配使用时，一般适用于病程较长的患者，可中效配短效，先抽取短效胰岛素，再抽取中效胰岛素，分早、晚两次注射，早餐前注射量占总量的2/3，晚餐前注射量占总量的1/3，若午餐前血糖经常≥11.2mmol/L，可在午餐前加用2～4单位短效胰岛素；也可短效配长效，即在每天注射3～4次短效胰岛素的基础上，在早餐前或晚餐前加入长效胰岛素混合注射。
//               <br />此外，还要注意根据血糖监测结果，按需调胰岛素剂量。
//               <br />2.运动治疗
//               <br />患者通过运动，有利于增加肌肉组织对葡萄糖的利用，有助于降低血糖。患者可以在血糖控制良好的情况下，根据年龄、运动能力安排适当的项目，如快走、慢跑、骑自行车、游泳、健身操等，每天定时定量进行运动。
//               <br />运动治疗的同时，要注意调整饮食，控制好胰岛素的用量，或运动前加餐，以防止低血糖的发生。需要强调的是，在酮症酸中毒时不宜进行任何运动。
//             </p>
//           }
//         />

//         <div className="item_title">常用药品</div>
//         <AbbText
//           domChange={innerDomChange}
//           index={8}
//           showPart={
//             <p>
//               1型糖尿病患者常用的药物是胰岛素。胰岛素主要是为了纠正患者的代谢紊乱，消除症状。
//               <br />胰岛素主要是皮下注射。按药效时间长短，胰岛素分为短效、中效、长效，其中短效胰岛素吸收最快，发挥药效也最快，但持续时间短；中效胰岛素主要用于病情稳定的患者，发挥药效较慢，但维持时间较长；长效胰岛素则维持时间最长，但吸收速度很慢，主要是提供基础胰岛素的需要，降低夜间血糖和空腹血糖。
//             </p>
//           }
//         />
//       </div>
//       <div ref={wrapperRef} data-order='delta' className='info_wrapper'>
//         <p className="title">治疗平台</p>
//         <div className="item_title">权威机构</div>
//         <div className='hospital'>
//           <div className='item'>
//             <p className='bold'>上海交通大学附属</p>
//             <p className='bold'>瑞金医院</p>
//             <p>内分泌科</p>
//             <p>三级甲等 综合</p>
//           </div>
//           <div className='item'>
//             <p className='bold'>复旦大学附属中山</p>
//             <p className='bold'>医院</p>
//             <p>内分泌科</p>
//             <p>三级甲等 综合</p>
//           </div>
//         </div>
//         <div className="item_title">医生推荐</div>
//         <SugarRecommand recommandClick={recommandDoctorClick} />
//       </div>
//       <div ref={wrapperRef} data-order='echo' className='info_wrapper'>
//         <p className="title">护理</p>

//         <div className="item_title">日常护理</div>
//         <AbbText
//           domChange={innerDomChange}
//           index={9}
//           needSlice={false}
//           showPart={
//             <p>
//               注射胰岛素的护理
//               <br />除了要每天按时注射以外，还需密切观察和预防胰岛素的不良反应。低血糖是患者最常出现的不良反应，轻者感到疲乏、头昏、心悸、出汗、饥饿等，重则导致昏迷。其实这与没有掌握好胰岛素的使用剂量、饮食失调或运动过量有关，只有每天定时定量进食，适当运动，在此基础上监测血糖，调整胰岛素的使用剂量，才能有效预防低血糖的发生。另外，糖尿病患者平时最好将一些糖类食物如糖果、饼干等，带在身上，当发生低血糖时，可以救急。
//               <br />日常生活护理
//               <br />适当进行一些运动，有助于血糖的控制，但注意不要空腹进行；勤洗头、洗澡，勤换内衣，保持床铺清洁平展，以防发生疖、痈等软组织感染；保持心情愉快，避免情绪激动、焦虑、紧张，有利于长期有效地控制病情；每天用40℃左右的水泡脚、洗脚，做好足部卫生，如发现足部有皮肤干裂、湿冷、水肿、肤色变暗、趾甲变形、局部红肿等情况，应及时就医。
//             </p>
//           }
//         />

//         <div className="item_title">营养饮食</div>
//         <AbbText
//           domChange={innerDomChange}
//           index={10}
//           needSlice={false}
//           showPart={
//             <p>
//               由于血糖会受到食物的影响，而且进食对血糖的影响幅度是巨大的，因此，1型糖尿病患者对饮食需要进行控制。
//               <br />根据理想体重合理控制热量的摄入。合理配餐，控制主食，饮食宜多样化，注意粗粮细粮搭配、荤素搭配、不挑食、不偏食。并且进食时间和数量应相对固定，做到定时定量，少食多餐。同时要注意，除低血糖的情况下，最好不要吃含糖的甜食，若想解解馋，可以甜味葡萄糖精、木糖醇等代替。
//             </p>
//           }
//         />
//       </div>
//     </div>
//   );
// }

// export default SugarbloodOne;
