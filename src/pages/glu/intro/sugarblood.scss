// @import "src/style/index";

// .sugarblood_page {
//   padding-bottom: r(60);

//   .sugar_recommond {
//     padding: 0 r(15);
//   }

//   .background {
//     position: absolute;
//     top: 0;
//     left: 0;
//     width: 100%;
//     height: r(170);
//     background: linear-gradient(230deg, #3e4565 0%, #323953 100%);
//     z-index: -1;
//   }

//   .head {
//     margin: r(17) auto;
//     width: r(345);
//     height: r(285);
//     padding-top: r(10);
//     box-shadow: 0 r(10) r(13) 0 rgba(221, 227, 244, 0.2);

//     .bg_intro {
//       margin: 0 auto r(15);
//       display: block;
//       width: r(323);
//       height: r(132);
//       border-radius: r(2);
//       overflow: hidden;
//     }

//     .text {
//       margin: 0 auto r(15);
//       width: r(315);
//       font-size: r(13);
//       line-height: r(21);
//       color: $base-text-color;
//     }

//     .from {
//       margin: 0 auto;
//       width: r(315);
//       height: r(30.5);
//       background: rgba($color: #f0f2f8, $alpha: 0.5);
//       border-radius: r(2);
//       @include display-flex;
//       @include align-items(center);
//       @include justify-content(center);

//       color: #41c670;
//       font-size: r(13);

//       img {
//         display: block;
//         height: r(18);
//         width: r(16);
//         margin-right: r(12);
//       }
//     }
//   }

//   .title {
//     @include display-flex;
//     @include align-items(center);

//     padding: 0 r(17);
//     color: $base-text-color;
//     font-size: r(16);
//     font-weight: 600;
//     margin: r(25) 0 r(10);

//     .icon {
//       display: block;
//       width: r(20);
//       height: r(20);
//       margin-right: r(10);
//     }

//     .icon_doctor {
//       width: r(23);
//       height: r(17);
//     }

//     .icon_sugar_department {
//       width: r(18);
//       height: r(16);
//     }

//     .icon_sugar_classify {
//       width: r(16);
//       height: r(18);
//     }
//   }

//   .sugar_classify_wrapper {
//     position: relative;
//     margin: 0 auto;
//     width: r(345);
//     height: r(218);
//     box-shadow: 0 r(10) r(13) 0 rgba(221, 227, 244, 0.2);

//     .item {
//       width: 100%;
//       height: 50%;
//       padding: r(17);
//       position: relative;

//       &:first-child {
//         border-bottom: r(1) solid #eee;
//       }

//       .item_head {
//         @include display-flex;
//         @include align-items(center);
//         @include justify-content(space-between);

//         padding-left: r(40);
//         color: $base-text-color;
//         font-size: r(15);
//         font-weight: 600;

//         img {
//           display: block;
//           position: absolute;
//           left: r(6);
//           top: r(13);
//           width: r(50);
//           height: r(50);
//         }

//         .item_btn {
//           width: r(74);
//           height: r(28);
//           border-radius: r(17);
//           border: r(1) solid $dark-green;
//           color: $dark-green;
//           font-size: r(14);
//           line-height: r(28);
//           text-align: center;

//           &:active {
//             background: $dark-green;
//             color: #fff;
//           }
//         }
//       }

//       .item_text {
//         margin-top: r(12);
//         color: #999;
//         font-size: r(14);
//       }
//     }
//   }

//   .sugar_department_wrapper {
//     position: relative;
//     margin: 0 auto;
//     width: r(345);
//     height: r(52);
//     line-height: r(52);
//     box-shadow: 0 r(10) r(13) 0 rgba(221, 227, 244, 0.2);
//     padding-left: r(13);
//     color: $base-text-color;
//     font-size: r(15);
//     font-weight: 600;
//   }

//   .sugar_detail_wrapper {
//     padding: r(15);
//     margin: 0 auto;
//     width: r(345);
//     box-shadow: 0 r(10) r(13) 0 rgba(221, 227, 244, 0.2);

//     .item {
//       position: relative;

//       .item_title {
//         color: $base-text-color;
//         font-size: r(15);
//         font-weight: 600;
//         padding-left: r(11);

//         &::before {
//           content: '';
//           position: absolute;
//           left: 0;
//           top: r(8);
//           height: r(6);
//           width: r(6);
//           background: #11cc89;
//           border-radius: 50%;
//         }
//       }

//       .item_text {
//         p {
//           position: relative;
//           color: #999;
//           font-size: r(13);
//           line-height: r(21);

//           span {
//             position: absolute;
//             display: inline-block;
//             background: #fff;
//             right: r(15);
//             bottom: 0;
//             color: #11cd89;
//           }
//         }

//         margin: r(10) 0;
//       }
//     }
//   }
// }

// .sugardetail_page {
//   .head {
//     @include display-flex;
//     @include align-items(center);
//     @include justify-content(space-between);

//     padding: r(26) r(32) r(15) r(20);

//     .text_wrapper {
//       .title {
//         color: #333;
//         font-size: r(27);
//         font-weight: 600;
//         line-height: r(38);
//       }

//       .sub_title {
//         color: #999;
//         font-size: r(14);
//         line-height: r(17);
//         padding-bottom: r(15);
//       }

//       .text {
//         color: #909090;
//         font-size: r(13);
//         line-height: r(21);
//         max-width: r(209);
//       }
//     }

//     img {
//       display: block;
//       width: r(100);
//       height: r(150);
//     }

//     .bg_sugar_two {
//       width: r(96);
//       height: r(134);
//     }
//   }

//   .from {
//     margin: 0 auto r(20);
//     width: r(315);
//     height: r(30.5);
//     background: rgba($color: #f0f2f8, $alpha: 0.5);
//     border-radius: r(2);
//     @include display-flex;
//     @include align-items(center);
//     @include justify-content(center);

//     color: #41c670;
//     font-size: r(13);

//     img {
//       display: block;
//       height: r(18);
//       width: r(16);
//       margin-right: r(12);
//     }
//   }

//   .menu_bar {
//     width: 100%;
//     height: r(103);
//     background: #fff;
//     box-shadow: 0 r(10) r(13) rgba(221, 227, 244, 0.2);
//     margin-bottom: r(10);

//     &.isFixed {
//       position: fixed;
//       top: 0;
//       left: 0;
//       z-index: 10;
//     }

//     @include display-flex;
//     @include align-items(center);

//     .menu_item {
//       @include flex;

//       text-align: center;
//       color: #333;
//       font-size: r(14);
//       font-weight: bold;

//       img {
//         display: block;
//         height: r(29);
//         width: r(29);
//         margin: 0 auto r(7);
//         opacity: 0.2;
//         // opacity: ;
//       }

//       .highlight {
//         opacity: 1;
//       }
//     }
//   }

//   .menu_cover {
//     width: 100%;
//     height: r(103);
//     background: #fff;
//     box-shadow: 0 r(10) r(13) rgba(221, 227, 244, 0.2);
//     margin-bottom: r(10);
//   }

//   .info_wrapper {
//     background: #fff;
//     padding: r(15);
//     position: relative;
//     margin-bottom: r(10);

//     .title {
//       color: #333;
//       font-size: r(16);
//       font-weight: 600;
//       padding: r(10) 0 r(10) r(10);
//       position: relative;

//       &::before {
//         content: '';
//         position: absolute;
//         left: r(0);
//         top: r(14);
//         width: r(5);
//         height: r(15);
//         background: #10cc89;
//       }
//     }

//     .item_title {
//       font-size: r(13);
//       font-weight: bold;
//     }

//     .text {
//       font-size: r(13);
//       color: #999;
//       margin: r(10) 0;
//     }

//     .chart {
//       @include display-flex;
//       @include align-items(center);
//       @include justify-content(space-between);

//       margin: r(15) 0;

//       .chart_bar {
//         position: relative;
//         width: r(80);
//         height: r(86);
//         background: linear-gradient(180deg, #f6f8ff 0%, #e2e6f3 100%);

//         .percent {
//           @include display-flex;
//           @include align-items(center);
//           @include justify-content(center);

//           flex-direction: column;
//           color: #fff;
//           font-size: r(14);
//           position: absolute;
//           width: r(80);
//           background: linear-gradient(138deg, #8be4dd 0%, #00bcbe 100%);
//           bottom: 0;
//           left: 0;
//         }

//         &.one > .percent {
//           height: r(63);
//         }

//         &.two > .percent {
//           height: r(52);
//         }

//         &.three > .percent {
//           height: r(52);
//         }

//         &.four > .percent {
//           height: r(43);
//         }
//       }
//     }

//     .hospital {
//       @include display-flex;
//       @include align-items(center);
//       @include justify-content(space-between);

//       margin: r(15) 0;

//       .item {
//         @include display-flex;
//         @include align-items(center);
//         @include justify-content(center);

//         flex-direction: column;
//         width: r(165);
//         height: r(95);
//         background: #fff;
//         box-shadow: 0 r(10) r(13)  rgba(221, 227, 244, 0.2);
//         border: r(1) solid rgba(238, 238, 238, 1);

//         p {
//           font-size: r(12);
//           color: #999;

//           &:nth-child(2) {
//             margin-bottom: r(4);
//           }

//           &.bold {
//             font-weight: bold;
//             color: $base-text-color;
//           }
//         }
//       }
//     }
//   }
// }
