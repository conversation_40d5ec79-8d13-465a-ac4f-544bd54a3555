// import React, { useEffect, useCallback, useState, useMemo } from 'react';
// import { AbbText, SvgIcon } from 'src/components/common';
// import SugarRecommand from '../recommand';
// import classnames from 'classnames';
// import './sugarblood.scss';

// import sugarblood_disease_svg from 'src/svgs/sugarblood-disease-two.svg';
// import book_svg from 'src/svgs/book.svg';

// /* tslint-disable */
// const SugarbloodOne = (props) => {
//   const [topOffset, setTopOffset] = useState(20);
//   const [scrollTop, setScrollTop] = useState(0);
//   const [innerDomList, setInnerDomList]: [any, any] = useState([]);

//   const [alpha, setAlpha] = useState(0);
//   const [bravo, setBravo] = useState(0);
//   const [charlie, setCharlie] = useState(0);
//   const [delta, setDelta] = useState(0);
//   const [echo, setEcho] = useState(0);

//   const [menuHeight, setMenuHeight] = useState(0);

//   useEffect(() => {
//     document.addEventListener('scroll', scroll);
//     return () => document.removeEventListener('scroll', scroll);
//   }, []);

//   const scroll = () => {
//     let scrollTop = document.documentElement.scrollTop || window.pageYOffset || document.body.scrollTop;
//     setScrollTop(scrollTop);
//   }

//   const menuRef = useCallback(node => {
//     setTimeout(() => {
//       if (node !== null) {
//         setMenuHeight(node.getBoundingClientRect().height);
//       }
//     }, 200);
//   }, []);

//   const measuredRef = useCallback(node => {
//     setTimeout(() => {
//       if (node !== null) {
//         setTopOffset(node.getBoundingClientRect().height);
//       }
//     }, 200);
//   }, []);

//   const wrapperRef = useCallback(node => {
//     setTimeout(() => {
//       if (node !== null) {
//         const attrs = node.attributes;
//         const value = node.offsetTop - menuHeight;
//         switch (attrs['data-order'].value) {
//           case 'alpha':
//             setAlpha(value);
//             break;
//           case 'bravo':
//             setBravo(value);
//             break;
//           case 'charlie':
//             setCharlie(value);
//             break;
//           case 'delta':
//             setDelta(value);
//             break;
//           case 'echo':
//             setEcho(value);
//             break;
//           default:
//             break;
//         }
//       }
//     }, 200);
//   }, [innerDomList]);

//   const recommandDoctorClick = useCallback((item) => {
//     props.history.push({
//       pathname: '/hospital/doctordetail',
//       search: `staffId=${item.id}`
//     });
//   }, []);

//   const scrollTo = useCallback((key) => {
//     switch (key) {
//       case 'alpha':
//         window.scroll({
//           top: alpha + 1,
//           behavior: "smooth"
//         });
//         break;
//       case 'bravo':
//         window.scroll({
//           top: bravo + 1,
//           behavior: "smooth"
//         });
//         break;
//       case 'charlie':
//         window.scroll({
//           top: charlie + 1,
//           behavior: "smooth"
//         });
//         break;
//       case 'delta':
//         window.scroll({
//           top: delta + 1,
//           behavior: "smooth"
//         });
//         break;
//       case 'echo':
//         window.scroll({
//           top: echo + 1,
//           behavior: "smooth"
//         });
//         break;
//       default:
//         break;
//     }
//   }, [alpha, bravo, charlie, delta, echo]);

//   const innerDomChange = useCallback((index, value) => {
//     const data = innerDomList.concat([]);
//     data[index] = value || 0;
//     setInnerDomList(data);
//   }, [innerDomList]);

//   const isFixed = useMemo(() => {
//     return scrollTop >= topOffset || false;
//   }, [topOffset, scrollTop]);

//   const isActive = useMemo(() => {
//     if (scrollTop >= alpha && scrollTop < bravo) {
//       return 'alpha'
//     } else if (scrollTop >= bravo && scrollTop < charlie) {
//       return 'bravo'
//     } else if (scrollTop >= charlie && scrollTop < delta) {
//       return 'charlie'
//     } else if (scrollTop >= delta && scrollTop < echo) {
//       return 'delta'
//     } else if (scrollTop >= echo) {
//       return 'echo'
//     }
//     return 'normal'
//   }, [scrollTop, alpha, bravo, charlie, delta, echo]);

//   return (
//     <div className='sugardetail_page'>
//       <div ref={measuredRef}>
//         <div className='head'>
//           <div className="text_wrapper">
//             <p className='title'>2型糖尿病</p>
//             <p className='sub_title'>Type 2 diabetes mellitus</p>
//             <p className='text'>2型糖尿病原名叫成人发病型糖尿病，患者体内产生胰岛素的能力并非完全丧失，有的患者体内胰岛素甚至产生过多，但胰岛素的作用效果较差，因此患者体内的胰岛素是一种相对缺乏。</p>
//           </div>
//           <SvgIcon type='img' src={sugarblood_disease_svg}/>
//         </div>
//         <div className='from'>
//         <SvgIcon type='img' src={book_svg} />
//           张俊清主任医生编审，北京大学第一医院
//       </div>
//       </div>
//       <div ref={menuRef} className={classnames('menu_bar', { isFixed })}>
//         <div className='menu_item' onClick={() => scrollTo('alpha')}>
//           <img className={classnames({ highlight: isActive === 'alpha' || isActive === 'normal' })} src={require('src/images/icon_tab_intro.png')} />
//           概要
//         </div>
//         <div className='menu_item' onClick={() => scrollTo('bravo')}>
//           <img className={classnames({ highlight: isActive === 'bravo' || isActive === 'normal' })} src={require('src/images/icon_tab_diagnose.png')} />
//           诊断
//         </div>
//         <div className='menu_item' onClick={() => scrollTo('charlie')}>
//           <img className={classnames({ highlight: isActive === 'charlie' || isActive === 'normal' })} src={require('src/images/icon_tab_cure.png')} />
//           治疗
//         </div>
//         <div className='menu_item' onClick={() => scrollTo('delta')}>
//           <img className={classnames({ highlight: isActive === 'delta' || isActive === 'normal' })} src={require('src/images/icon_tab_inquiry.png')} />
//           就诊
//         </div>
//         <div className='menu_item' onClick={() => scrollTo('echo')}>
//           <img className={classnames({ highlight: isActive === 'echo' || isActive === 'normal' })} src={require('src/images/icon_tab_care.png')} />
//           <div className="title">护理</div>
//         </div>
//       </div>
//       {isFixed && <div className="menu_cover"></div>}

//       <div ref={wrapperRef} data-order='alpha' className='info_wrapper'>
//         <p className="title">疾病概要</p>

//         <div className="item_title">就诊科室</div>
//         <div className='text'>糖尿病专区</div>

//         <div className="item_title">疾病简介</div>
//         <AbbText
//           domChange={innerDomChange}
//           index={0}
//           showPart={
//             <p>
//               2型糖尿病，又称为成人发病型糖尿病，大多数患者在35岁之后发病，是一种患者体内的胰岛素相对缺乏，导致血糖升高的一种疾病。高血糖，会对人体的大小血管、神经都造成伤害，并引发一系列并发症。
//               <br />2型糖尿病患者的胰岛素体内工厂——胰岛可能功能正常也可能轻微受损，产生胰岛素的能力仍然存续，有的人体内胰岛素甚至产生过多。但胰岛素在发挥作用时出现了问题，身体各种组织器官对胰岛素不敏感（胰岛素抵抗），因此，胰岛素的作用效果大打折扣，所以说，2型糖尿病患者体内的胰岛素只是相对缺乏，并不是绝对量不足。
//             </p>
//           }
//         />

//         <div className="item_title">高危人群</div>
//         <AbbText
//           domChange={innerDomChange}
//           index={1}
//           showPart={
//             <p>
//               2型糖尿病主要有下面高危人群，应该注意。
//               <br />1.家族中有2型糖尿病患者的人
//               <br />家族成员中有2型糖尿病患者的人，其患病率会比家族中没有患者的人高。因此，如果家族中有2型糖尿病患者，从20岁左右就应开始关注血糖水平。这一特性除了遗传方面的家族聚集性因素外，研究还发现，在同一家族成员中，彼此生活方式往往比较相近。比如都有喜欢高糖饮食、肥胖、较少运动等习惯，这也是2型糖尿病在家族中有聚集性的原因之一。
//               <br />2.超重或体重过轻的人
//               <br />超重的人患上2型糖尿病的几率更高，但2型糖尿病患者并不都是胖子，一些瘦子也可能是患者。在没有刻意节食的状态下，一个月内体重减轻超过2公斤，即便体型偏瘦，也需要格外留意，要到医院检查血糖。
//               <br />3.高血压、高血脂及冠心病患者
//               <br />患有高血压、高血脂、冠心病的人，再患上2型糖尿病的几率更高。
//               <br />4.妊娠期出现糖尿病的女性、高出生体重婴儿的母亲，以及多次流产者
//               <br />对于女性而言，如果在怀孕期间曾被诊断为妊娠期糖尿病，或生出的宝宝超过8斤的，或者有多次流产，患上2型糖尿病的几率更高。
//             </p>
//           }
//         />

//         <div className="item_title">家族遗传性</div>
//         <AbbText
//           domChange={innerDomChange}
//           index={2}
//           showPart={
//             <p>
//               2型糖尿病具有一定的家族聚集性，如家族中有人患病，则患病率会比家族中没有患者的人高。
//             </p>
//           }
//         />

//         <div className="item_title">病因</div>
//         <AbbText
//           domChange={innerDomChange}
//           index={3}
//           showPart={
//             <p>
//               糖尿病的病因和发病机制，并不十分清楚，但总的来说，糖尿病的发病都有遗传因素和环境因素的作用。
//               <br />就遗传因素而言，主要影响胰岛细胞本身的功能基础，但起病和病情进程状况则受环境因素的影响而变异甚大。环境因素包括年龄、生活方式及饮食习惯、应激、接触化学毒物等。
//             </p>
//           }
//         />

//         <div className="item_title">预防</div>
//         <AbbText
//           domChange={innerDomChange}
//           index={4}
//           showPart={
//             <p>
//               选择健康生活方式，定期测量血糖，积极预防并发症；糖尿病的预防应构筑三道防线，医学上称之为“三级预防”。
//               <br />一级预防： 是树立正确的饮食观和采取合理的生活方式，以最大限度地减少糖尿病的发生率。要杜绝和戒掉一切不良嗜好，要戒烟少饮酒。属于高危人群者，如双亲中有患糖尿病，而本人又肥胖多食、血糖偏高、缺乏运动者，尤应注意预防。
//               <br />二级预防： 是定期测量血糖，以尽早发现无症状性糖尿病。应该将血糖测定列入中老年常规的体检项目，即使一次正常者，仍要定期测定。凡有糖尿病蛛丝马迹可循者，如有皮肤感觉异常、性功能减退、视力不佳、多尿、白内障等，更要及时去测定和仔细鉴别，以期尽早诊断，争得早期治疗的可贵时间。
//               <br />三级预防： 目的是预防或延缓糖尿病慢性合并症的发生和发展，减少其伤残和死亡率。糖尿病人很容易并发其他慢性病，且容易因并发症而危及生命。因此，要对糖尿病慢性合并症加强监测，做到早期发现、早期预防是其要点，晚期疗效不佳。早期诊断和早期治疗常可预防并发症的发生，使患者能长期过上接近正常人的生活。
//             </p>
//           }
//         />

//         <div className="item_title">并发症</div>
//         <AbbText
//           domChange={innerDomChange}
//           index={5}
//           showPart={
//             <p>
//               生病最怕的就是病还没好利落，并发症却接踵登门造访。糖尿病就是一种很容易有并发症的疾病，而且糖尿病的并发症数量还不少。
//               <br />糖尿病对身体最大的影响，是其在全身造成的种类繁多的并发症。根据糖尿病并发症发病的急缓，以及病理上的差异，可将其分为急性和慢性两大类。
//               <br />主要的并发症，是血管并发症。血糖升高，身体代谢不正常工作了，可能引起各种各样的血管并发症。咱们主要归纳为两点，一个大血管并发症；一个微血管并发症。大血管并发症包括了冠心病、脑血管病，患者有可能发生心梗，有可能发生脑血管意外。
//               <br />微血管并发症，即身体里很小的血管发生问题，主要的病变，一个是肾脏的病变，一个是眼睛的病变，还有些患者有神经的病变，比方说感觉到手麻、脚麻，勃起功能障碍及消化、排尿等方面的问题。
//               <br/>这里面危害最大的当属肾脏病变，糖尿病肾病最终发展为肾功能不全，肾功能不全以后，多米诺骨牌效应样出现尿毒症。
//               <br />许多糖尿病患者，不可避免地发生某种形式的眼部疾病（如视网膜病变），造成视力下降，结膜出血，甚至失明。
//               <br />糖尿病并发症的治愈率也是比较低的。
//             </p>
//           }
//         />
//       </div>
//       <div ref={wrapperRef} data-order='bravo' className='info_wrapper'>
//         <p className="title">诊断</p>

//         <div className="item_title">临床总体症状</div>
//         <AbbText
//           domChange={innerDomChange}
//           index={6}
//           showPart={
//             <p>
//               糖尿病有四种“典型”的症状，也就是三多一少：吃的多，喝的多，尿的多，体重减轻。
//               <br />人体内的每个细胞都依靠能量才能存活。我们可借由将吃入的食物转换成脂肪和糖类（葡萄糖），获取能量。食物消化所得到的葡萄糖由血液负责传送，胰岛所分泌的胰岛素负责帮助身体各部位组织器官细胞从血液中取得葡萄糖，作为细胞能量物质使用。
//               <br />2型糖尿病患者的细胞对胰岛素不敏感，胰岛素无法有效将血液中的葡萄糖搬进细胞，一方面导致血液中的葡萄糖含量升高；一方面细胞却处在"饥饿"状态。细胞的饥饿信号传递给中枢神经，大脑会释放出赶快补充进食的信号，于是，真正的饥饿感出现了，恶性循环开始：身体得到更多葡萄糖，血糖升高，胰岛拼命释放胰岛素，但胰岛素无所作为，血糖无法进入细胞，糖更多留存在血液中，血糖进一步升高，细胞却仍旧饥饿，细胞又向大脑索取更多糖……
//               <br />-血液中的葡萄糖太多时，就会"漏"到尿液当中，也就是所谓的“尿糖”了。糖尿病患者尿液中的糖会大幅度提高尿液渗透压，尿液就会疯狂吸收水分来稀释，患者产生大量的尿液，会让人觉得口渴，想要喝入更多的水。
//               <br/>所以，糖尿病患者会出现四种典型的“糖尿病症状”：尽管食量增加，体重仍然减轻，且会喝多、尿多。
//             </p>
//           }
//         />

//         <div className="item_title">常见症状分布</div>
//         <div className='chart'>
//           <div className="chart_bar one">
//             <div className="percent">
//               <p>24.0%</p>
//               <p>血糖升高</p>
//             </div>
//           </div>
//           <div className="chart_bar two">
//             <div className="percent">
//               <p>15.0%</p>
//               <p>多饮</p>
//             </div>
//           </div>
//           <div className="chart_bar three">
//             <div className="percent">
//               <p>25.0%</p>
//               <p>口干</p>
//             </div>
//           </div>
//           <div className="chart_bar four">
//             <div className="percent">
//               <p>9.0%</p>
//               <p>多尿</p>
//             </div>
//           </div>
//         </div>

//         <div className="item_title">常用检验检查</div>
//         <AbbText
//           domChange={innerDomChange}
//           index={7}
//           showPart={
//             <p>
//               常用的检查包括血糖检测，以及糖耐量测试。
//               <br />血糖稍高不一定就是糖尿病。体检时发现血糖数值，比正常参考值上限就高那么一点，在6.2~7.2mmol/L，不需要很担心是不是患了糖尿病啦。
//               <br />单次测量血糖稍高大可不必太担心，如果想要确诊到底得没得糖尿病，可以再检查一下糖耐量试验。如果糖耐量正常，就可以放心了。
//               <br />要确诊糖尿病，需要任意时间点（不论何时进餐，是否运动等）的血糖≥11.1mmol/L或空腹血糖≥7.0mmol/L，或者75g口服葡萄糖耐量试验2小时血浆葡萄糖≥11.1mmol/L。
//               <br />关于糖耐量测试，医生一般会进行“口服葡萄糖耐量试验”。需要晚上10时后停止进食进水8～10小时，第二天上午7～9时空腹到医院，抽取静脉血测量血糖。此后医生会要求被检测者在5分钟内喝完混合好的葡萄糖水，从喝糖水的第一口开始计算时间，分别在口服糖水后半小时、1小时、2小时和3小时，抽取静脉血测量血糖。
//               <br/>此外，医生还可能会通过抽取静脉血来进行胰岛功能检测。
//             </p>
//           }
//         />
//       </div>
//       <div ref={wrapperRef} data-order='charlie' className='info_wrapper'>
//         <p className="title">治疗</p>

//         <div className="item_title">治疗方案</div>
//         <AbbText
//           domChange={innerDomChange}
//           index={8}
//           showPart={
//             <p>
//               首先改变不良生活习惯，口服降糖药是首选方案，需要时注射胰岛素治疗。
//               <br />1.首选药物：二甲双胍
//               <br />如果患者没有二甲双胍的禁忌证，该药物应该一直保留在糖尿病的治疗方案中，贯穿始终。
//               <br />2.α-糖苷酶抑制剂：阿卡波糖、伏格列波糖、米格列糖
//               <br />这是一类可以延缓肠道葡萄糖吸收的口服降糖药，竞争性抑制位于小肠的各种葡萄糖苷酶，使碳水化合物分解为葡萄糖的速度减慢。
//               <br />3.磺脲类：格列美脲、格列喹酮、格列吡嗪、格列齐特
//               <br />磺脲类是口服的促胰岛素分泌剂，是许多国家和国际组织控制2型糖尿病的主要用药，通过刺激β细胞分泌胰岛素实现降糖。
//               <br />4.格列奈类：瑞格列奈、那格列奈
//               <br />格列奈类是口服的促胰岛素分泌剂，作用机制与磺脲类药物类似。格列奈类吸收快、起效快、作用时间短，调节餐后血糖效果好。
//               <br />5.噻唑烷二酮类：罗格列酮、吡格列酮
//               <br/>噻唑烷二酮类药物是一类增敏剂，主要的降糖机制是增加肝脏、肌肉和脂肪组织对于胰岛素的敏感性。部分药物有增加心力衰竭的风险，需要和医生协调使用。
//               <br/>6.DPP-4：新型胰岛素促泌剂，适用于不适合促胰岛素分泌类药物及α-糖苷酶抑制剂药物的患者。
//               <br />7.GLP-1：新型胰岛素促泌剂，注射用于两种口服药联用无法有效控制血糖的患者人群。
//               <br />8.胰岛素类药物：若是通过改变生活方式和使用口服降糖药仍然不能很好地控制住血糖，或者服用其他药物会给您带来不良影响时，医生可能就会建议您使用胰岛素。目前，胰岛素不能口服，只能利用注射器或胰岛素笔等装置通过皮下注射。
//               <br />不同胰岛素制剂的起效时间和作用持续时间也不同。患者需要在医生的指导下，选用适合自身当前病情的胰岛素类型，并制定适当的胰岛素注射时间。
//             </p>
//           }
//         />

//       </div>
//       <div ref={wrapperRef} data-order='delta' className='info_wrapper'>
//         <p className="title">治疗平台</p>
//         <div className="item_title">权威机构</div>
//         <div className='hospital'>
//           <div className='item'>
//             <p className='bold'>上海交通大学附属</p>
//             <p className='bold'>瑞金医院</p>
//             <p>内分泌科</p>
//             <p>三级甲等 综合</p>
//           </div>
//           <div className='item'>
//             <p className='bold'>复旦大学附属中山</p>
//             <p className='bold'>医院</p>
//             <p>内分泌科</p>
//             <p>三级甲等 综合</p>
//           </div>
//         </div>
//         <div className="item_title">医生推荐</div>
//           <SugarRecommand recommandClick={recommandDoctorClick}/>
//       </div>
//       <div ref={wrapperRef} data-order='echo' className='info_wrapper'>
//         <p className="title">护理</p>

//         <div className="item_title">日常护理</div>
//         <AbbText
//           domChange={innerDomChange}
//           index={9}
//           needSlice={false}
//           showPart={
//             <p>
//               糖尿病患者是一个需要特殊关爱的群体，他们的身体比较脆弱，生活需要细心照顾。所以，如果家里有糖尿病患者的话，每个家庭成员都应该牢记三件事情：防脱水、管理血糖、足部护理，以便更好地照顾患者，不让其发生意外
//               <br />关于脱水
//               <br />夏季闷热，出汗多，而高血糖会引起脱水，患者喝进去的水如果不能很快补偿出汗所缺失的水分，很有可能出现脱水状况。尤其中暑感冒的时候，患者发烧出汗增多，经常小量地补充水分非常重要。当血糖高的时候，不能用果汁、饮料、糖水等代替白开水。
//               <br />关于血糖，我们都知道将血糖控制在正常范围内是最好的，可有的时候达到标准是一件很困难的事情，我们要面对患者的年龄、运动习惯和生活作息等诸多因素的难题。
//               <br/>糖尿病患者以中老年人偏多，有时候他们对身体状况描述不是很清楚，家人无法判断是否病情复发。比如低血糖，症状是心慌、头晕等，但大多数患者不会联想到低血糖，认为自己血糖高了，要求家人给自己吃降糖药，结果造成病情加剧。
//               <br />除此之外，还有很多因素会导致血糖升高。比如，降糖药的吸收速度、情绪的变化、食物的不同及运动强度，等等。因此，每天监督患者测血糖非常重要。当血糖波动特别大，且身体有不良反应的时候，患者应立即就医。
//               <br />再强调一次，2型糖尿病患者还需要格外关注低血糖。一次低血糖，很可能造成晕厥或跌倒，甚至会带来生命危险。外出时，患者应随身携带写卡片，上面写清楚姓名、家属联系方式、所患疾病、现服药物及低血糖自救措施的卡片，以应对突发状况。在随身口袋里，时刻备一些含糖食品，便于在出现低血糖时采取自救和获得他救时提供有效帮助。
//               <br />关于足部护理，每天用30℃左右温水洗脚，绝对不能用热水、烫水泡脚。洗干净后，用干毛巾轻轻擦干，包括趾缝均需要擦洗干净。涂抹护肤霜等，保护脚的皮肤不发生皴裂。每天还要检查脚跟、脚底、脚趾有没有溃破，有没有裂口、水疱等，经常仔细观察足部皮肤，妥善处理，切不可贻误了治疗时机。如果足部有脚气、鸡眼、厚茧子，不能自己拿剪刀剪割，随意处理。
//             </p>
//           }
//         />

//         <div className="item_title">营养饮食</div>
//         <AbbText
//           domChange={innerDomChange}
//           index={10}
//           needSlice={false}
//           showPart={
//             <p>
//               由于血糖会受到食物的影响，而且进食对血糖的影响幅度是巨大的，因此，2型糖尿病患者对饮食需要进行控制。
//               <br />1.应根据理想体重合理控制总热能，每周称体重1次，根据体重不断调整食物摄入量和运动量，直至实际体重恢复至接近理想体重。要培养良好的饮食习惯，保持饮食均衡，应该遵循少食多餐的原则以保持血糖的平衡。主食提倡采用粗制米、面和杂粮，限食各种糖果、甜糕点、饼干、冰激凌、含糖饮料等。
//               <br/>2.提倡多食用绿叶蔬菜、豆类、根茎类、粗谷物、含糖成分低的水果等，少吃肥肉、花生、瓜子等含脂肪丰富的食物及动物内脏、奶油、香肠等含胆固醇高的食物。多选用鱼、禽、奶、豆类等优质蛋白质，伴有肝肾疾病者减少蛋白质的摄入量。
//               <br/>3.进行适当的运动，因运动治疗亦是重要的基础治疗措施。
//               <br />4.根据年龄、健康状况、性别、病情及有无并发症，在医生指导下选择合适的运动方式和运动量，以不感到疲劳为宜。
//               <br />5.运动方式有步行、慢跑、骑自行车、健身操、太极拳、气功等，要循序渐进，开始运动不可间断。为了避免低血糖反应，运动最好选择在饭后0.5～1 小时较为合适，运动不宜在降糖药物作用最强的时间进行。
//               <br />6.注射胰岛素的患者不宜清晨空腹运动，尤其不宜在注射胰岛素后、吃饭前运动。外出运动时随身携带糖尿病卡和一些糖果、甜食，以防低血糖的发生，空腹食入较少时，尽量减少运动量。发生低血糖反应(头昏、心悸、多汗、饥饿等)，应立即停止运动，并及时服用随身携带的糖果、甜食。
//             </p>
//           }
//         />
//       </div>
//     </div>
//   );
// }

// export default SugarbloodOne;
