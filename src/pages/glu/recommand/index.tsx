// import React, { useEffect, useCallback } from 'react';
// import { shallowEqual, useSelector, useDispatch } from 'react-redux';
// import { fetch_doctor_recommand } from 'src/store/doctor/action';
// import { Card, Avatar } from 'src/components/common';
// import { ApplicationState } from 'src/store/index';
// import classnames from 'classnames';
// import './recommand.scss';

// interface RecommandProps {
//   prefixCls?: string;
//   recommandClick?: any;
// }


// const SugarRecommand = (props: RecommandProps) => {
//   const { prefixCls, recommandClick } = props;
//   const doctorRecommand = useSelector((state: ApplicationState) => {
//     const data = state.doctor.recommand.concat([]);
//     return data.splice(0, 3);
//   }, shallowEqual);

//   const { PROFESSIONALTITLE_OBJ, DEPARTMENT_OBJ }: any = useSelector((state: ApplicationState) => {
//     return {
//       PROFESSIONALTITLE_OBJ: state.dictionary.PROFESSIONALTITLE_OBJ,
//       DEPARTMENT_OBJ: state.department.obj
//     }
//   });

//   const dispatch = useDispatch();
//   const fetchRecommand = (options?: any) => dispatch(fetch_doctor_recommand(options));

//   useEffect(() => {
//     fetchRecommand({
//       option: {
//         doctorSortFactor: "HIGH_OPINION_RATE",
//         isQueryMedicalStaffService: true,
//         isQueryHeadPortrait: true
//       },
//       currentPage: 1,
//       pageSize: 3,
//       staffTypes: ["doctor"],
//       workDepartment: '03'
//     });
//   }, []);

//   const itemClick = useCallback((item) => {
//     typeof recommandClick === 'function' && recommandClick(item)
//   }, [recommandClick]);

//   return (
//     <div className={classnames('recommand_component', prefixCls)}>
//       {doctorRecommand.length ? doctorRecommand.map(item => {
//         return (
//           <Card prefixCls="recommand_doctor_item" onClick={() => itemClick(item)}  key={`doctor_item_${item.id}`}>
//             <div className="header">
//               <Avatar preUrl={item.avatar} />
//               <div className="doctor_info">
//                 <p className="top">
//                   <span className="name">{item.staffName}</span>
//                   <span className="staffType">{PROFESSIONALTITLE_OBJ[item.staffProfessionalTitle]}</span>
//                 </p>
//                 {/* <p className="bottom">{item.firstWorkOrgName} {DEPARTMENT_OBJ[item.workDepartment]}</p> */}
//                 <p className="bottom">{DEPARTMENT_OBJ[item.workDepartment]}</p>
//               </div>
//             </div>
//             <div className="skill">
//               {item.staffSkills}
//             </div>
//             <div>
//               {item.tags && item.tags.split(',').map((item: any, index: number) => {
//                 return <span className="tag_item" key={`tag_item_${index}`}>{item}</span>
//               })}
//             </div>
//           </Card>
//         )
//       }) : null
//       }
//     </div>
//   );
// }

// export default SugarRecommand;