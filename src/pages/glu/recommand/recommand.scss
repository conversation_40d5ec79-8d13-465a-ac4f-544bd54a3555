// @import "src/style/index";

// .recommand_component {
//   margin: r(15) 0 0;

//   .recommand_doctor_item {
//     padding: r(15) r(20);
//     border: 1px solid rgba(238, 238, 238, 1);
//     margin: 0;
//     margin-bottom: r(10);
//     box-shadow: r(3) r(13) r(18) 0 rgba(221, 227, 244, 0.3);

//     .header {
//       @include display-flex;
//       @include align-items(center);
//       @include justify-content(flex-start);

//       .doctor_info {
//         @include flex;

//         .top {
//           margin-bottom: r(4);

//           .name {
//             display: inline-block;
//             font-size: r(18);
//             font-weight: bold;
//             margin-left: r(10);
//             color: #000;
//           }

//           .staffType {
//             font-size: r(15);
//             font-weight: bold;
//             margin-left: r(8);
//             color: #464646;
//           }
//         }

//         .bottom {
//           color: #309eeb;
//           font-size: r(14);
//           margin-left: r(10);
//         }
//       }
//     }

//     .tag_item {
//       height: r(22);
//       line-height: r(22);
//       font-size: r(13);
//       padding: r(2) r(10);
//       background: $light-green;
//       border-radius: r(4);
//       color: #00bc7d;
//       margin-left: r(8);

//       &:first-of-type {
//         margin-left: 0;
//       }
//     }

//     .skill {
//       color: #909090;
//       font-size: r(12);
//       margin: r(10) 0;
//       line-height: r(18);
//       @include line(2);
//     }
//   }
// }
