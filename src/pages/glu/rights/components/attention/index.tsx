import React, { useState } from 'react';
import { Icon, Modal } from 'zarm';
import attentionConfig from './utils';

import './attention.scss';



const title = '注意事项';
const UniqueAttention = (props) => {

  const prefixCls = 'unique-extra-attention';
  const [visible, setVisible] = useState(false);
  const toogle = (value = false) => {
    return setVisible(value);
  }
  const { type = '1', } = props;
  const current = attentionConfig[type] || [];
  return (
    <>
      <p className={prefixCls} onClick={() => toogle(true)}>
        <Icon type="warning-round" />
        {title}
      </p>
      <Modal
        visible={visible}
        title={title}
        onCancel={() => toogle(false)}
        className={`${prefixCls}__modal-body`}
        footer={<p className={`${prefixCls}__modal-close`} onClick={() => toogle()}><Icon type="wrong-round" /></p>}
      >
        {current.map((k, index) => {
          return (<p className={`${prefixCls}__modal-row`} key={`row-${index}`}>{k}</p>)
        })}
      </Modal>
    </>
  );
}

export default UniqueAttention;