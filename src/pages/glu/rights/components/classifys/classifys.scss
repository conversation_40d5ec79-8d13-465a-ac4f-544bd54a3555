@import "src/style/index";
$prefixCls: 'classifys-card';

.#{$prefixCls} {
  padding: r(19) r(13) 0;

  &:last-child {
    padding-bottom: r(17);
  }

  &__title {
    line-height: r(24);
    margin-bottom: r(6);
    @include display-flex;
    @include justify-content(space-between);
    @include align-items(center);

    h3 {
      color: rgba(70, 70, 70, 1);
      font-size: r(16);
      font-weight: bold;
    }
  }

  &__fold {
    text-align: center;
    font-size: 12px;
    color: rgba(102, 102, 102, 1);
    position: relative;

    i {
      display: inline-block;
      position: relative;
      width: r(10);
      height: r(10);

      &::after,
      &::before {
        content: "";
        position: absolute;
        width: r(6);
        height: r(6);
        top: r(-1.8);
        border-width: 1px 1px 0 0;
        border-style: solid;
        border-color: #bbb #bbb transparent transparent;
        transform: rotate(135deg);
      }

      &::after {
        top: r(2);
        opacity: .7;
      }
    }

    &::before {
      content: "";
      position: absolute;
      height: r(40);
      left: 0;
      right: 0;
      top: r(-40);
      background: linear-gradient(360deg, #fff 0%, rgba(255, 255, 255, 0.93) 0%, rgba(255, 255, 255, 0) 100%);
    }
  }

  //文本类型
  &__children-text {
    line-height: r(20);
    font-size: r(13);
    color: #909090;
  }
  //流程类型
  &__children-flow {
    @include display-flex;

    .flow-item {
      position: relative;
      background: linear-gradient(360deg, #fafafa 0%, #f2f2f2 100%);
      border-radius: 8px;
      padding: r(9) r(3) r(6);
      color: #909090;
      line-height: r(20);
      font-size: r(12);
      @include flex;

      &:not(:last-child) {
        margin-right: r(16.5);

        &:after {
          position: absolute;
          content: "";
          right: -10px;
          top: 50%;
          width: 8px;
          height: 8px;
          border-color: rgba(230, 230, 230, 1);
          border-style: solid;
          border-width: 0 1px 1px 0;
          transform: translateY(-25%) rotate(-45deg);
        }
      }
    }

    .flow-title {
      text-align: center;
      font-weight: 600;
      color: #909090;
      font-size: r(14);
      margin-bottom: r(10);

      strong {
        color: rgba(188, 188, 188, 1);
        font-size: r(18);
        margin-right: 6px;
        // font-style: oblique;
        font-style: italic;
      }
    }
  }
  //卡片类型
  &__children-card {
    .card-row {
      padding: r(13.5) 0 r(15);
      font-size: r(13);

      &:not(:first-child) {
        @include borderTop($color: rgba(230, 230, 230, 1));
      }

      .card-hd {
        margin-bottom: r(10);
        font-weight: bold;
        color: #464646;
        @include display-flex;
        @include justify-content(space-between);
      }

      .card-tags {
        display: inline-block;
        border-radius: 19px;
        border: 1px solid #bbb;
        padding: 1px 4px;
        color: #bbb;
        font-size: 12px;
        transform: scale(.8) translateX(-2px);
      }

      .card-title {
        @include display-flex;
        @include align-items(center);

        font-size: r(14);
      }

      .card-content {
        line-height: r(20);
        color: rgba(144, 144, 144, 1);
      }
    }
  }
  //无序列表类型
  &__children-disorderly {
    line-height: r(20);
    color: rgba(144, 144, 144, 1);
    font-size: r(13);
  }
}
