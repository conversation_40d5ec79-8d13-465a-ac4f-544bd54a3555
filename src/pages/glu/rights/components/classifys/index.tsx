import React, { ReactNode, useState, useCallback, useEffect } from 'react';
import './classifys.scss';

const prefixCls = 'classifys-card';


interface ConfigProps {
  extra?: string | ReactNode;
  title?: string | ReactNode;
  type?: string;
  text?: string | ReactNode;
  list?: any[]
}
interface CardsProps {
  config: ConfigProps;
  children?: string | ReactNode;
  activeIndex?: number | string;

}
//文本类型
const TextChild = ({ text }) => {
  return (
    <p className={`${prefixCls}__children-text`}>
      {text}
    </p>
  )
}

//流程类型
const FlowChild = ({ list }) => {
  return (
    <ul className={`${prefixCls}__children-flow`}>
      {list.map((item, i) => {
        return (
          <li className="flow-item" key={`flow${i}`}>
            <p className="flow-title"><strong>{i + 1}</strong>{item.title}</p>
            <p className="desc">{item.desc}</p>
          </li>
        )
      })}
    </ul>
  )
}
// 卡片类型
const CardChild = ({ list, showMore }) => {
  return (
    <ul className={`${prefixCls}__children-card`}>
      {list.map((item, i) => (
        <li className="card-row" key={`row${i}`} style={{ display: !showMore && i > 0 ? 'none' : '' }}>
          <div className="card-hd">
            <p className="card-title">{item.title}{item.tag && <span className="card-tags">{item.tag}</span>}</p>
            <p className="card-extra">{item.extra}</p>
          </div>
          <div className="card-content">
            {item.children.map((k, v) => (<p key={`children${v}-${i}`} className="desc">{k}</p>))}
          </div>
        </li>
      ))}

    </ul>
  )
}

//无序列表类型
const DisorderlyChild = ({ list }) => {
  return (
    <div className={`${prefixCls}__children-disorderly`}>
      {list.map((k, v) => (<p key={`children${v}`} className="desc">· {k}</p>))}
    </div>
  )
}



const Classifys = (props: CardsProps) => {
  let [showMore, setShowMore] = useState(false);
  let { type = '', text = '', title = '服务内容', extra, list = [] } = props.config;
  const toogle = useCallback(() => {
    setShowMore(!showMore);
  }, [showMore])
  useEffect(() => {
    setShowMore(false);
  }, [props.activeIndex]);
  return (
    <div className={`${prefixCls}`}>
      <div className={`${prefixCls}__title`}>
        <h3>{title}</h3>
        <div className={`${prefixCls}__extra`}>{extra}</div>
      </div>
      <div className={`${prefixCls}__body`} >
        {type == 'text' && <TextChild text={text} />}
        {type == 'flow' && <FlowChild list={list} />}
        {type == 'card' && <CardChild list={list} showMore={showMore} />}
        {type == 'disorderly' && <DisorderlyChild list={list} />}

        {!type && props.children}
      </div>
      {type == 'card' && list.length > 1 && !showMore && <p className={`${prefixCls}__fold`} onClick={toogle}>查看更多<i></i></p>}
    </div >
  );
}

export default Classifys;