import React from 'react';
import { Feature } from '../../config';

const prefixCls = 'rights-headers';

export interface ConsultTheDoctorProp {
  name: string;
  serviceNum: string | number;
  desc: string;
  feature: Feature[];
}

export default function ConsultTheDoctor({
  name,
  serviceNum,
  desc,
  feature,
}: ConsultTheDoctorProp) {
  return (
    <div>
      <div className={`${prefixCls}`}>
        <div className={`${prefixCls}__base`}>
          <h3>
            <span className={`${prefixCls}__base-number-container`}>
              <span>{name}</span>
              {serviceNum && <span className='num'>{serviceNum}</span>}
            </span>
          </h3>
          <p>{desc}</p>
        </div>
        <ul className={`${prefixCls}__body`}>
          {feature.map(({ label, value }) => (
            <li className='feature-item' key={label}>
              <p className='label'>{label}</p>
              <p className='desc'>{value}</p>
            </li>
          ))}
        </ul>
      </div>
    </div>
  );
}
