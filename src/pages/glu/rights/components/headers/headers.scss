@import 'src/style/index';
$prefixCls: 'rights-headers';

.#{$prefixCls} {
  //margin: 0 r(10) r(10);
  //padding-bottom: r(5);
  //background-image: url(../../images/card/bg01.png);
  //background-size: 100% auto;
  //background-position: 0 0;
  //background-repeat: no-repeat;

  &__header {
    display: flex;
    align-items: center;
    justify-content: space-between;

    h3 {
      width: 60%;
    }
  }

  &__base {
    padding: r(6) 0 r(12);
    font-size: r(13);

    .right-services-container {
      width: 40%;
      text-align: center;
      justify-content: end;
    }

    &-number-container {
      position: relative;
      display: inline-block;
      line-height: 1.1;

      .num {
        position: absolute;
        left: 101%;
        top: 0;
        white-space: nowrap;
        font-size: r(12);
        font-weight: normal;
        padding: 4px 6px;
        line-height: 1;
        color: white;
        transform: scale(0.8);
        background-color: #ff7240;
        border-radius: 10px 10px 10px 0;
      }
    }

    h3 {
      flex: 1 1 auto;
      font-weight: 600;
      font-size: r(23);
      margin-bottom: 2px;
    }

    p {
      width: calc(100% - #{r(75)});
      color: #666;
    }
  }

  &__body {
    background: #fafafa;
    border-radius: r(8);
    @include display-flex;

    .feature-item {
      @include flex(1, 1, auto);

      padding: r(12) 0 r(12) r(12);
      font-size: r(12);
      color: #999;
      line-height: r(20);
      position: relative;

      &:not(:last-of-type) {
        &:after {
          content: '';
          position: absolute;
          top: 26%;
          right: r(-5);
          width: r(10);
          height: 50%;
          background: url('./assets/rotate-line.svg') center no-repeat;
          background-size: cover;
        }
      }

      /* .icon {
        width: r(45);
        height: r(45);
      } */

      .label {
        padding-top: r(5);
        font-size: r(13);
        color: #666;
        font-weight: bold;
      }
    }
  }
}
