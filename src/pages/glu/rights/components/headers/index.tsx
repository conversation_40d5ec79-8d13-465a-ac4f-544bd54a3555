import React, { useEffect, useState } from 'react';
import { fetchJson } from 'src/utils/fetch';
import { LinkToPDFPreview } from '../LinkToPDFPreview';
import { Policy } from '../sources';
import { RightServices } from '../sources/RightServices';
import './headers.scss';
import { SvgIcon } from 'src/components/common';

interface featureItem {
  icon: string | undefined;
  label?: string;
  value?: string;
  fileName?: string;
}

interface ConfigProps {
  name: string;
  serviceNum: string;
  desc: string;
  background: string;
  feature: featureItem[];
  vasCode: string;
  cards: any;
}

interface HeadersProps {
  polices: Policy[];
  config: ConfigProps;
  hasRight: boolean;
  cards: any;
  liftPolicyStatus(status: 'hasRight' | 'noRight' | 'invalidRight'): void;
  isGracePeriod(status): void;
  patientId?: string
  onSuccess?: Function
  policyNo?: string
  userRightsId?: string
}

const RightsDetail = ({ polices, config, cards, hasRight, liftPolicyStatus, patientId = '', isGracePeriod, onSuccess, policyNo, userRightsId }: HeadersProps) => {
  const prefixCls = 'rights-headers';
  const [numText, setNumText] = useState('');
  const [rightServicesVisible, setRightServicesVisible] = useState<boolean>(false);
  const { name, desc, feature = [], vasCode } = config || {};

  /** 获取权益列表（包含过期、未过期） */
  const getPolicyList = async () => fetchJson({
    type: 'POST',
    url: '/api/api/v1/patient/policy/queryPolicyList',
    data: {
      patientId,
      vasCode,
    },
    isloading: true,
  });

  useEffect(() => {
    if (!vasCode) {
      return;
    }
    setNumText('');
    fetchJson({
      type: 'POST',
      url: '/api/api/v1/patient/policy/queryPolicyVasInfo',
      data: {
        vasCode,
        patientId,
        sortBizNo: userRightsId || policyNo,
      },
      isloading: true,
    }).then(async (res) => {
      const { code = '', result = [] } = res;
      if (code === '0') {
        const right = result[0] || {};
        onSuccess && onSuccess(right);
        const { isSelected = false, totalCount = '', usedCount = '', isWaitPeriod, remainingWaitPeriod = '0', policyExpiryDate } = right;
        const period =  result.every((item) => item.isGracePeriod === 'Y') ? 'Y' : 'N';
        if (isSelected) {
          if(isWaitPeriod) {
            setNumText(`等待期${remainingWaitPeriod}天`);
          } else if (totalCount < 0) {
            setNumText('不限次');
          } else if ((totalCount - usedCount > 0) && (period !== 'Y')) {
            setNumText('可使用');
          }
        }
        if (typeof liftPolicyStatus === 'function') {
          let status =
            ['drugDiscount', 'onlineConsult', 'nucleicAcidTest'].includes(vasCode) || isSelected
              ? 'hasRight'
              : 'noRight';

          if (status !== 'hasRight') {
            const allRights = await getPolicyList();
            if (allRights.result.length > 0) {
              status = 'invalidRight';
            }
          }
          liftPolicyStatus(status as any);
        }
        isGracePeriod(period);
      }
    });
  }, [vasCode]);

  const { list = [] } = cards.find(({ type }) => type === 'card') || {};

  const hasDes = list.length > 0; // 是否有“服务项目”说明

  return (
    <div className={`${prefixCls}`}>
      <div className={`${prefixCls}__base`}>
        <header className={`${prefixCls}__header`}>
          <h3>
            <span className={`${prefixCls}__base-number-container`}>
              <span>{name}</span>
              {numText && <span className='num'>{numText}</span>}
            </span>
          </h3>
          {hasDes && (
            <div
              className='right-services-container'
              onClick={() => {
                setRightServicesVisible(true);
              }}
            >
              <SvgIcon className='explain__extra_icon' src={require('./../../images/nav-top-right/sprite-warn.svg')} />
              <span className='explain__extra'>服务项目</span>
            </div>
          )}
        </header>
        <p>{desc}</p>
      </div>
      <ul className={`${prefixCls}__body`}>
        {feature.map(({ label, value, fileName }, index) => (
          <li className='feature-item' key={label}>
            <p className='label'>{label}</p>
            <div className='desc'>
              {fileName ? (
                <LinkToPDFPreview fileName={fileName}>{value}</LinkToPDFPreview>
              ) : (
                value
              )}
            </div>
          </li>
        ))}
      </ul>
      <RightServices
        visible={rightServicesVisible}
        polices={polices}
        close={() => {
          setRightServicesVisible(false);
        }}
        code={vasCode}
        rightName={name}
        cards={cards}
        hasRight={hasRight}
      />
    </div>
  );
};

export default RightsDetail;
