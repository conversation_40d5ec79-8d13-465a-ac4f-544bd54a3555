import { ArrowRight } from '@zarm-design/icons';
import React, { useEffect, useState } from 'react';
import { withRouter } from 'react-router-dom';
import './index.scss';

function InvalidEnter(props) {
  const { history, patientId, vasCode, headers } = props;
  const { name } = headers;

  const handleInvalidRightList = () => {
    history.push({
      pathname: '/hospital/rights/invalidList',
      search: `patientId=${patientId}&vasCode=${vasCode}&rightName=${encodeURIComponent(name)}`,
    });
  };

  return (
    <div className='invalid-enter'>
      <span onClick={handleInvalidRightList}>已失效权益<ArrowRight className='invalid-enter__arrow'></ArrowRight></span>
    </div>
  );
}

export default withRouter(InvalidEnter);
