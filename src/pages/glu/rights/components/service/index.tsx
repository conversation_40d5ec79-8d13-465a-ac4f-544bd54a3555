import React from 'react';
import { Button } from 'zarm';
// import overview from './utils';
import validate from 'src/utils/validate';

import './service.scss';

const pdfPrefix = 'https://cdn-qcloud.zhongan.com/a00000/za_hospital/unique/';
const UniqueService = (props) => {
  const prefixCls = 'rights-services';
  const { config: current = {} } = props;
  const url = `${pdfPrefix}${current.pdf}`;
  const looks = () => {
    if (validate.isIos()) {
      window.location.href = url;
      return false;
    };
    return props.history.push(`/hospital/pdf?url=${encodeURIComponent(url)}`);
  }
  return (
    <div className={`${prefixCls} anchor-node`}>
      <img className={`${prefixCls}__icon`} src={current.icon} alt="" />
      <div className={`${prefixCls}__body`}>
        <h4 className={`${prefixCls}__title`}>{current.title}</h4>
        <p className={`${prefixCls}__desc`}>{current.desc ? <p className={`${prefixCls}__desc__detail`}>{current.desc}</p> : null}<Button theme="primary" ghost shape="round" size='xs' onClick={looks}>立即查看</Button>
        </p>
      </div>
      {!!current.tag && <i><img className={`${prefixCls}__tag`} src={require('../../images/service/service-icon-tag.png')} alt="" /></i>}
    </div>
  );
}

export default UniqueService;