import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { FETCH_POLICY_VAS_DETAIL } from 'src/store/right/action-types';
import { ApplicationState } from 'src/store';
import { Popup } from 'zarm';
import { Policy } from './index';

export interface RightServicesProps {
  rightName: string;
  polices: Array<Policy>;
  code: string;
  visible: boolean;
  close(): void;
  cards: any;
  hasRight: boolean;
}

function fixed(enable) {
  try {
    const fix = [document.querySelector('body'), document.querySelector('#app')] as Array<Element>;

    document.querySelectorAll('.rights-pages__container').forEach((element) => {
      fix.push(element);
    });

    fix.forEach((element) => {
      if (enable) {
        element.classList.add('fixed-overflow');
      } else {
        element.classList.remove('fixed-overflow');
      }
    });
  } catch (error) {
    console.log(error);
  }
}

export function RightServices({
  visible,
  polices,
  code,
  close,
  rightName,
  cards,
  hasRight,
}: RightServicesProps) {
  const dispatch = useDispatch();
  const policy = useSelector((state: ApplicationState) => state.policyVasDetail || []);

  const names = [...new Set(polices.map(({ name }) => name))];

  const [activeName, setActiveName] = useState<string>(names[0]);
  let p: any = {};

  try {
    p = polices.find(({ name }) => name === (activeName || names[0])) || {};
  } catch (error) {}

  const { policyNo: policyNumber = '' } = p;

  const services =
    (policy[policyNumber] || []).find(({ vasCode }) => vasCode === code)?.itemList || [];

  useEffect(() => {
    if (visible && !!policyNumber) {
      dispatch({ type: FETCH_POLICY_VAS_DETAIL, policyNumber });
    }
  }, [visible, policyNumber]);

  useEffect(() => {
    fixed(visible);
  }, [visible]);

  let cardsNodes: Array<JSX.Element> = [
    <li className='right-service-item' key='empty'>
      <div className='description'>暂无权益项目说明</div>
    </li>,
  ];

  const { list = [] } = cards.find(({ type }) => type === 'card') || {};

  try {
    if (!hasRight) {
      cardsNodes = list.map(({ title, children }) => (
        <li className='right-service-item' key={title}>
          <header>
            <div className='title'>{title}</div>
            <div>可用 0 次</div>
          </header>
          {children && <div className='description'>{children.join()}</div>}
        </li>
      ));
    }
  } catch (error) {
    console.log(error);
  }

  return (
    <Popup
      visible={visible}
      direction='bottom'
      onMaskClick={close}
      // afterOpen={() => console.log('打开')}
      // afterClose={() => console.log('关闭')}
      destroy={false}
    >
      <div className='popup-box'>
        <div className='right-service-container'>
          <header className='right-service-header'>
            {rightName} 服务项目
            <div className='btn-close' onClick={close}>
              <img src={require('./close.svg')} alt='' />
            </div>
          </header>
          {names.length > 0 && (
            <div style={{ padding: '1.1rem 0.93rem 0.6rem' }}>
              <div className='rights-sources__policy-names'>
                {names.map((name) => (
                  <span
                    key={name}
                    className={name === (activeName || names[0]) ? 'active' : ''}
                    onClick={() => setActiveName(name)}
                  >
                    {name}
                  </span>
                ))}
              </div>
            </div>
          )}
          <ul className='right-service-list'>
            {hasRight && services.length > 0
              ? services.map(({ itemCode, itemName, totalTimes, usedTimes = 0 }) => {
                  let des = null;
                  try {
                    let { children = [] } = list.find(({ title }) => title === itemName) || {};
                    des = children.join('');
                  } catch (error) {
                    console.log(error);
                  }

                  const time =
                    totalTimes >= 0 && usedTimes >= 0 ? `可用 ${totalTimes - usedTimes} ` : '不限';
                  return (
                    <li className='right-service-item' key={itemCode}>
                      <header>
                        <div className='title'>{itemName}</div>
                        <div>{time}次</div>
                      </header>
                      {des && <div className='description'>{des}</div>}
                    </li>
                  );
                })
              : cardsNodes}
          </ul>
        </div>
      </div>
    </Popup>
  );
}
