import React, { useEffect, useState } from 'react';
import { fetchJson } from 'src/utils/fetch';
import './sources.scss';

export interface Policy {
  policyNo: string;
  name: string;
  packageName: string;
  policyEffectiveDate: string;
  policyExpiryDate: string;
  policyVasInfos: []
  isWaitPeriod: boolean
  receiveTime: string;
}

function splitDate(str: string): string {
  return str.slice(0, 10).replace(/-/g, '.');
}

export interface RightsSourceProps {
  headers: any;
  selectPolicyNo?: string;
  onSuccess(polices: Policy[]): void;
  patientId?: string
}

const prefixCls = 'rights-sources';

export default function RightsSource(props: RightsSourceProps) {
  const [names, setNames] = useState<string[]>([]);
  const [activeName, setActiveName] = useState<string>(names[0]);
  const [polices, setPolices] = useState<Policy[]>([]);

  // eslint-disable-next-line @typescript-eslint/unbound-method
  const { headers: { vasCode = '' } = {}, onSuccess, selectPolicyNo, patientId = '' } = props;

  useEffect(() => {
    if (!vasCode) {
      return;
    }

    fetchJson({
      type: 'POST',
      url: '/api/api/v1/patient/policy/queryPolicyList',
      data: {
        patientId,
        vasCode,
      },
      isloading: true,
    }).then((res) => {
      const { code = '', result = [] } = res;
      if (code === '0') {
        let _names: string[] = [];
        const list: Policy[] = [];
        for (const policy of result) {
          const {
            policyNo,
            policyProduct,
            policyInsurant,
            policyEffectiveDate = '',
            policyExpiryDate = '',
            policyVasInfos = [],
            isWaitPeriod,
            receiveTime,
          } = policy;
          let packageName = '';
          let name = '';
          if(policyProduct){
            packageName = policyProduct.packageName;
          }
          if(policyInsurant){
            name = policyInsurant.name;
          }

          const vas = policyVasInfos.find((item) => (item.isSelected && item.vasCode === vasCode)) || {};
          if (
            vas
            && vas.vasCode
            && (Date.now() < new Date(policyExpiryDate).getTime() && Date.now() > new Date(policyEffectiveDate).getTime())
          ) {
            _names.push(name);
            list.push({
              policyNo,
              name,
              packageName,
              policyEffectiveDate: splitDate(policyEffectiveDate),
              policyExpiryDate: splitDate(policyExpiryDate),
              policyVasInfos,
              isWaitPeriod,
              receiveTime: receiveTime ? splitDate(receiveTime) : '',
            });
          }
        }
        _names = [...new Set(_names)];
        setNames(_names);
        setActiveName(_names[0]);
        setPolices(list);

        onSuccess(list);
      }
    });
  }, [vasCode]);

  if (polices.length === 0) {
    return null;
  }
  const filterPolices = () => {
    const newPolicies = polices.filter((policy) => activeName === policy.name);
    const findPolicy = newPolicies.filter((policy) => policy.policyNo === selectPolicyNo);
    // 如果携带policyNo，则会过滤对应保单，如果过滤不到，则表示权益不在此保单里，正常显示其他保单
    return findPolicy.length > 0  ? findPolicy : newPolicies;
  };

  return (
    <section className={`${prefixCls}__container`}>
      <header className={`${prefixCls}__header`}>
        <h3>权益来源</h3>
        <div className={`${prefixCls}__policy-names`}>
          {names.map((name) => (
            <span
              key={name}
              className={name === activeName ? 'active' : ''}
              onClick={() => setActiveName(name)}
            >
              {name}
            </span>
          ))}
        </div>
      </header>
      <ul className={`${prefixCls}__list`}>
        {filterPolices()
          .map((policy) => {
            const { policyNo, packageName, name, policyEffectiveDate, policyExpiryDate, receiveTime } = policy;

            return (
              <li className='source-item' key={policyNo}>
                <div className='source-item-body'>
                  <section className='source-item-base'>
                    <h4 className='source-item-title'>{packageName}</h4>
                    <p>受益人：{name} </p>
                    {
                      receiveTime && <p>领取时间：{receiveTime} </p>
                    }
                    <p>
                      有效期限：{policyEffectiveDate} - {policyExpiryDate}
                    </p>
                  </section>
                </div>
              </li>
            );
          })}
      </ul>
    </section>
  );
}
