@import "src/style/index";
$prefixCls: 'rights-sources';

.#{$prefixCls} {
  &__container {
    margin: r(20) r(-15) 0;
    padding: r(20) r(15) 0;
    border-top: 1px dotted #e6e6e6;
  }

  &__header {
    > h3 {
      margin-bottom: r(10);
      font-size: r(16);
      font-weight: bold;
    }

    @at-root .#{$prefixCls}__policy-names {
      font-size: r(12);
      color: #999;

      > span {
        margin-right: r(10);
        padding: r(4) r(13);
        background-color: #f5f5f5;
        border-radius: r(13);

        &.active {
          font-size: r(13);
          font-weight: bold;
          color: var(--text-base-color);
          background: var(--theme-secondary);
        }
      }
    }
  }

  &__icon {
    font-size: r(15);
    color: rgba(204, 204, 204, 1);
  }

  &__list {
    margin-top: r(10);
    padding: r(12);
    background: #fafafa;
    border-radius: 6px;

    .source-item {
      color: #999;

      &:not(:first-child) {
        margin-top: r(15);
      }

      &-body {
        // @include display-flex;
        // @include justify-content(space-between);
        // @include align-items(center);
      }

      &-base {
        line-height: r(24);
        font-size: r(14);
      }

      &-title {
        color: #666;
        font-size: r(14);
        font-weight: bold;
        white-space: nowrap;
      }

      &-extra {
        position: relative;
        text-align: center;
        min-width: r(55);
        font-size: r(12);

        .days {
          color: rgba(70, 70, 70, 1);
          font-size: r(12);

          strong {
            font-size: r(21);
          }
        }

        &:after {
          content: "";
          position: absolute;
          top: r(3);
          bottom: r(3);
          left: r(-15);
          width: 1px;
          transform: scaleX(0.5);
          background: rgba(86, 44, 22, .1);
        }
      }
    }
  }
}
