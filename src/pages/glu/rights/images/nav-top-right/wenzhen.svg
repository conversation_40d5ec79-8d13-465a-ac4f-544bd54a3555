<?xml version="1.0" encoding="UTF-8"?>
<svg width="209px" height="208px" viewBox="0 0 209 208" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>权益详情 / 开药问诊导诊 bg</title>
    <defs>
        <linearGradient x1="-14.0563965%" y1="76.8705357%" x2="100%" y2="7.93303571%" id="linearGradient-1">
            <stop stop-color="#F8FFFC" offset="0%"></stop>
            <stop stop-color="#EFFCF7" offset="100%"></stop>
        </linearGradient>
    </defs>
    <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="权益详情/医疗垫付/开药问诊导诊" transform="translate(-511.000000, -390.000000)">
            <g id="蒙版" transform="translate(30.000000, 390.000000)">
                <g id="编组-6" transform="translate(477.000000, -14.000000)">
                    <g id="编组-5">
                        <rect id="矩形备份" fill="url(#linearGradient-1)" transform="translate(107.000000, 107.000000) rotate(45.000000) translate(-107.000000, -107.000000) " x="32" y="32" width="150" height="150" rx="28.5714286"></rect>
                        <path d="M110,63 C111.656854,63 113,64.3431458 113,66 L113,101 L148,101 C149.656854,101 151,102.343146 151,104 L151,110 C151,111.656854 149.656854,113 148,113 L113,113 L113,148 C113,149.656854 111.656854,151 110,151 L104,151 C102.343146,151 101,149.656854 101,148 L101,113 L66,113 C64.3431458,113 63,111.656854 63,110 L63,104 C63,102.343146 64.3431458,101 66,101 L101,101 L101,66 C101,64.3431458 102.343146,63 104,63 L110,63 Z" id="形状结合" fill="#FFFFFF"></path>
                    </g>
                    <rect id="区域" stroke="#979797" fill="#D8D8D8" opacity="0" x="5.33436138" y="14.5" width="207" height="207"></rect>
                </g>
            </g>
        </g>
    </g>
</svg>