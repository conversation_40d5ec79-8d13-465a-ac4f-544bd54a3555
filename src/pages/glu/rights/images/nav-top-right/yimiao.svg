<?xml version="1.0" encoding="UTF-8"?>
<svg width="209px" height="208px" viewBox="0 0 209 208" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>权益详情 / 疫苗咨询 bg</title>
    <defs>
        <linearGradient x1="10.069005%" y1="76.8705357%" x2="81.1686242%" y2="7.93303571%" id="linearGradient-1">
            <stop stop-color="#F8FFFC" offset="0%"></stop>
            <stop stop-color="#EFFCF7" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="28.6794538%" y1="76.8705357%" x2="66.6420118%" y2="7.93303571%" id="linearGradient-2">
            <stop stop-color="#F8FFFC" offset="0%"></stop>
            <stop stop-color="#EFFCF7" offset="100%"></stop>
        </linearGradient>
    </defs>
    <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="权益详情/健康咨询/疫苗咨询" transform="translate(-511.000000, -390.000000)">
            <g id="蒙版" transform="translate(30.000000, 390.000000)">
                <g id="编组-6" transform="translate(481.834361, -11.000000)">
                    <g id="编组-5" transform="translate(112.665639, 96.000000) rotate(10.000000) translate(-112.665639, -96.000000) translate(45.165639, 11.000000)">
                        <path d="M5.8008048,95.3326579 C6.51424255,94.0969474 8.09433934,93.6735618 9.33004977,94.3869995 L9.33004977,94.3869995 L94.3532125,143.475145 C95.5889229,144.188583 96.0123086,145.76868 95.2988708,147.00439 L95.2988708,147.00439 L92.7152842,151.479294 C92.0018465,152.715004 90.4217497,153.13839 89.1860393,152.424952 L89.1860393,152.424952 L4.16287651,103.336806 C2.92716609,102.623368 2.50378043,101.043272 3.21721818,99.8075612 L3.21721818,99.8075612 Z M114.53998,1.39444012e-13 L123.489787,5.16717325 L110.571652,27.542 L117.284209,31.4170697 L105.658069,51.5541346 L98.9456518,47.679 L69.2344676,99.1401426 L60.284661,93.9729693 L89.9956518,42.512 L83.2835522,38.6362014 L94.909692,18.4991366 L101.621652,22.374 L114.53998,1.39444012e-13 Z" id="形状结合" fill="url(#linearGradient-1)"></path>
                        <path d="M95.7626038,24.9084004 C100.04323,24.9084004 103.513364,28.3785338 103.513364,32.6591603 L103.513364,125.668279 C103.513364,129.948905 100.04323,133.419039 95.7626038,133.419039 L95.7623515,148.9204 L103.513364,148.920558 C104.940239,148.920558 106.09695,150.07727 106.09695,151.504145 L106.09695,156.671318 C106.09695,158.098194 104.940239,159.254905 103.513364,159.254905 L31.1729381,159.254905 C29.7460626,159.254905 28.5893515,158.098194 28.5893515,156.671318 L28.5893515,151.504145 C28.5893515,150.07727 29.7460626,148.920558 31.1729381,148.920558 L38.9233515,148.9204 L38.923698,133.419039 C34.6430715,133.419039 31.1729381,129.948905 31.1729381,125.668279 L31.1729381,32.6591603 C31.1729381,28.3785338 34.6430715,24.9084004 38.923698,24.9084004 L95.7626038,24.9084004 Z" id="形状结合" fill="url(#linearGradient-2)" transform="translate(67.343151, 92.081653) scale(-1, 1) rotate(-30.000000) translate(-67.343151, -92.081653) "></path>
                        <rect id="矩形" fill-opacity="0.3" fill="#FFFFFF" transform="translate(75.233031, 47.412941) scale(-1, 1) rotate(-30.000000) translate(-75.233031, -47.412941) " x="54.5643376" y="43.5375606" width="41.337386" height="7.75075988"></rect>
                        <rect id="矩形" fill-opacity="0.6" fill="#FFFFFF" transform="translate(66.190477, 63.075102) scale(-1, 1) rotate(-30.000000) translate(-66.190477, -63.075102) " x="45.5217845" y="59.1997222" width="41.337386" height="7.75075988"></rect>
                        <rect id="矩形" fill-opacity="0.3" fill="#FFFFFF" transform="translate(57.147924, 78.737264) scale(-1, 1) rotate(-30.000000) translate(-57.147924, -78.737264) " x="36.4792313" y="74.8618837" width="41.337386" height="7.75075988"></rect>
                        <rect id="矩形" fill-opacity="0.6" fill="#FFFFFF" transform="translate(48.105371, 94.399425) scale(-1, 1) rotate(-30.000000) translate(-48.105371, -94.399425) " x="27.4366781" y="90.5240453" width="41.337386" height="7.75075988"></rect>
                    </g>
                    <rect id="区域" stroke="#979797" fill="#D8D8D8" opacity="0" x="0.5" y="11.5" width="207" height="207"></rect>
                </g>
            </g>
        </g>
    </g>
</svg>