import React, { useCallback, useEffect, useRef, useState } from 'react';
import { shallowEqual, useSelector, useDispatch } from 'react-redux';
import { ChatInput, StaticToast } from 'src/components/common';
import { ApplicationState } from 'src/store';
import { edit_inquiry } from 'src/store/inquiry/action';
// import { ApplicationState } from 'src/store';
import { pushEvent, validate } from 'src/utils';
import { Toast } from 'zarm';

const autoDescList: [string[], string[]] = [['感冒发烧', '咳嗽', '头晕头痛', '腹泻呕吐', '抑郁失眠', '高血压', '肾虚'], ['皮肤过敏', '糖尿病', '皮炎湿疹', '痤疮长痘', '祛湿', '妇科炎症']];

let shouldConditionDescValidate = true;
const setShouldConditionDescValidate = (v) => {
  shouldConditionDescValidate = v;
};
const AiConditionDescComponents = (props) => {
  const {
    prefixCls = '',
    onConfirm = null,
    popWrapperEl,
  } = props;
  const dispatch = useDispatch();
  const { illnessDescription = '' } = useSelector((state: ApplicationState) => {
    const { create = {} } = state.inquiry;
    return create;
  }, shallowEqual);
  const [sendVal, setSendVal] = useState(illnessDescription);
  const [autoDescIndex, setAutoDescIndex] = useState(0);

  const toastContainerRef = useRef(null);
  const isFocus = useRef(false);
  const editInquiry = (key: string, value: any, payload?: any) => dispatch(edit_inquiry(key, value, payload));

  /** ios键盘弹起后，页面整体上移。弹窗显示在页面中间根本看不到，重新设置弹窗位置 */
  const polyToast = (msg, stayTime = 2000) => {
    if (isFocus.current && validate.isIos()) {
      Toast.show({
        content: msg,
        stayTime,
        mask: true,
        mountContainer: toastContainerRef.current,
      });
    } else {
      StaticToast.error(msg, stayTime);
    }
  };

  const handleSumbit = useCallback(() => {
    pushEvent({eventTag: 'prescription_sub'});
    const value = (sendVal || '').trim();
    if (!value) {
      polyToast('请描述您的问题！');
      return;
    }
    if(validate.hasEmoji(value)) {
      polyToast('不支持输入表情');
      return;
    }
    editInquiry('illnessDescription', value);
    onConfirm && onConfirm(value);
  }, [sendVal]);

  // 键盘收缩页面回弹
  const windowScrollBottom = () => {
    isFocus.current = false;
    window.scroll({
      top: 0,
    });
    popWrapperEl.current && (popWrapperEl.current.style = {});
  };

  // 页面滑动到底部 / 这段逻辑不会生效的。popWrapperEl.current 容器并不可内部滚动
  const scrollBottom = useCallback((time = 100) => {
    setTimeout(() => {
      if (popWrapperEl.current) {
        popWrapperEl.current.scrollTo({
          top: 1000,
          behavior: 'smooth',
        });
      }
    }, time);
  }, []);

  useEffect(() => {
    // 监听安卓软键盘事件 处理聊天信息滚动
    // 获取原窗口的高度
    if (!validate.isIos()) {
      const originalHeight = document.documentElement.clientHeight || document.body.clientHeight;
      window.onresize = function() {
        // 键盘弹起与隐藏都会引起窗口的高度发生变化
        const resizeHeight = document.documentElement.clientHeight || document.body.clientHeight;
        if (resizeHeight - 0 < originalHeight - 0) {
          // 当软键盘弹起，在此处操作
          scrollBottom();
        } else {
          // 当软键盘收起，在此处操作
        }
      };
    }
    return () => {
      window.onresize = null;
      windowScrollBottom();
    };
  }, []);

  // Input focus
  const focusInput = useCallback(() => {
    isFocus.current = true;
    if (!validate.isIos()) {
      return;
    }
    // 处理ios系统软键盘不收起 消息记录少的时候 使消息可以看见的问题
    if (popWrapperEl.current) {
      setTimeout(() => {
        const height = document.documentElement.scrollTop || window.pageYOffset || document.body.scrollTop;
        if (height) {
          popWrapperEl.current.style.top = `${height - 180}px`;
        }
        scrollBottom(0);
      }, 300);
    }
  }, []);

  return (
    <div className={`${prefixCls}__conditiondesc`}>
      <div className='ios-toast__container' ref={toastContainerRef}></div>
      <div className={`${prefixCls}__conditiondesc-automatic`}>
        <p className='conditiondesc-automatic_title'>
          <span>猜你想问</span>
          <img onClick={() => {
            pushEvent({eventTag: 'prescription_choice_switch'});
            setAutoDescIndex(autoDescIndex === 0 ? 1 : 0);
          }} src={require('../../images/switch.png')} alt='' />
        </p>
        <div className='conditiondesc-automatic_tags'>
          {
            autoDescList[autoDescIndex].map((item, index) => (
              <div onClick={() => {
                pushEvent({eventTag: 'prescription_choice', text: item});
                setShouldConditionDescValidate(false);
                setSendVal(item);
              }}
              key={index}
              className='conditiondesc-automatic_tag'>
                {item}
              </div>
            ))
          }
        </div>
      </div>
      <ChatInput
        popWrapperEl={popWrapperEl}
        sendVal={sendVal}
        businessNo={''}
        handleChange={(e) => setSendVal(e.target.value)}
        handleFocus={focusInput}
        handleBlur={windowScrollBottom}
        handleSumbit={handleSumbit}
        chatSendMsg={null}
        platformCode={''}
        placeholder='描述越细，解答越准' />
    </div>
  );
};


export default AiConditionDescComponents;
