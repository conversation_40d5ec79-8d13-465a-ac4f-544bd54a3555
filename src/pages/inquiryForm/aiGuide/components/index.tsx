import React, { useEffect } from 'react';
import { Popup } from 'zarm';
import componentAggregation from './config';
import './aggregation.scss';

const prefixCls = 'aipopup-components';

const AiPopupComponents = (props) => {
  const {
    // location: { pathname = '', search = '' },
    currentItem = {}, onComponentConfirm = null,
    popupRef = null,
    ...other
  } = props;

  useEffect(() => {
  }, [])

  const CurrentComponent = componentAggregation[currentItem.componentType || 'default'];
  return (
    <Popup
      visible={true}
      direction="bottom"
      destroy={true}
      mask={false}
      className={`${prefixCls}__popup`}
    >
      <div className={`${prefixCls}__box`} key={`time${currentItem.time}`} ref={popupRef}>
        <CurrentComponent prefixCls={prefixCls} onConfirm={onComponentConfirm} currentItem={currentItem} key={`uid${currentItem.uid}`} {...other} />
      </div>
    </Popup>

  )
};


export default AiPopupComponents;
