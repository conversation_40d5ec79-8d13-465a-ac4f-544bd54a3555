import { dmEnv } from '@dm/utils';
import React, { useEffect, useReducer, useState, useCallback, useRef } from 'react';
import { shallowEqual, useSelector, useDispatch } from 'react-redux';
import { CheckInquiry, FixedButton } from 'src/components/common';
import { PhoneMessage } from 'src/pages/chatMedicalManage/components';
import { MessageBody } from 'src/pages/textInquiryChat/component';
import { ApplicationState } from 'src/store';
import { edit_inquiry } from 'src/store/inquiry/action';
import { Deserialize, pushEvent, validate } from 'src/utils';
import { fetchJson } from 'src/utils/fetch';
import format from 'src/utils/format';
import { CDN_PREFIX } from 'src/utils/staticData';
import { session } from 'src/utils/storage';
import './aiGuide.scss';
import {throttleArgs} from 'src/utils/throttle';
import { Button, Modal } from 'zarm';
import patientInquiryInfoInit from '../init';
import AiPopupComponents from './components';
import reducer, { itemType, initState, aiDoctorIssuseList, patientMsgBase } from './reducer';
import { Serialize } from 'src/utils/serialization';

const prefixCls = 'aiguide-page';

const AiGuideInquiry = (props) => {
  const {
    location: { pathname = '', search = '' },
  } = props;

  const popWrapperEl: any = useRef(null);
  const popupEl: any = useRef(null);
  const isVideo = /\/hospital\/videointro/ig.test(pathname);
  const dispatch = useDispatch();

  const { isChangeInquiry, ...othersSearch } =  Deserialize(search);

  const editInquiry = (key: string, value: any, payload?: any) => dispatch(edit_inquiry(key, value, payload));
  const { pageState = initState, lastImTtem = {} } = session.get('aiGuideInquiryData') || {};
  const [placeholderHieght, setPlaceholderHieght] = useState<Number | string>(0);
  const [currentImItem, setCurrentImItem] = useState<itemType>(lastImTtem);
  const [isBusy, setIsBusy] = useState(false);
  const [state, upDateDispatch] = useReducer(reducer, pageState);
  const [showChangeInquiry, setShowChangeInquiry] =  useState(isChangeInquiry === 'Y');

  const { create = {} } = useSelector((state: ApplicationState) => {
    const { create = {} } = state.inquiry;
    const { GENDER_OBJ = {} } = state.dictionary;
    return {
      create,
      GENDER_OBJ,
    };
  }, shallowEqual);

  useEffect(() => {
    setShowChangeInquiry(isChangeInquiry === 'Y');
  }, [location.search]);

  const checkIsDisabled = async () => {
    if(!isVideo) {
      return;
    }
    const res = await fetchJson({
      type: 'POST',
      url: '/api/api/v1/patient/inquiry/acceptTime/isVideoAcceptTime',
      data: {},
      isloading: false,
    });
    if (res.code === '0') {
      setIsBusy(!res.result || false);
    }
  };

  useEffect(() => {
    pushEvent({ eventTag: 'guide_page', text: '首页' });
    checkIsDisabled();
    const isAiGuideInquiryBack = session.get('isAiGuideInquiryBack') || 0;
    if (isAiGuideInquiryBack) {
      session.remove('isAiGuideInquiryBack');
      session.remove('aiGuideInquiryData');
      scrollBottom();
      return;
    }
    patientInquiryInfoInit(props, dispatch);
    if ((validate.isFromMiniApplet() || validate.isBestpay() || dmEnv.isApp() || !isVideo)) {
      const [item] = aiDoctorIssuseList;
      setCurrentImItem({
        ...item,
        index: 0,
      });
      upDateDispatch({ type: 'push', data: { list: [item] } });
    } else {
      setCurrentImItem({
        componentType: 'notSupport',
      });
    }
  }, []);
  const onChangePopupHeight = throttleArgs((cb) => {
    setTimeout(() => {
      if (popupEl.current) {
        const { height = 0 } = popupEl.current.getBoundingClientRect() || {};
        setPlaceholderHieght(height + 20);
        cb && cb();
      }
    }, 100);
  }, 600);
  // 页面滑动到底部
  const scrollBottom = useCallback((time = 100) => {
    onChangePopupHeight(() => {
      setTimeout(() => {
        if (popWrapperEl.current) {
          const { height = 0 } = popWrapperEl.current.getBoundingClientRect() || {};
          window.scrollTo({
            top: Number(height) + 500,
            behavior: 'smooth',
          });
        }
      }, time);
    });
  }, []);

  const { inquiryNo = '', patientGender = '', patientAge = '', attachmentList = [], cardHasPatient = false } = create;

  const onComponentConfirm = useCallback((content) => {
    console.log(content);
    const { index = 0, componentType = '', prevIndex = 0, issuseKey = '' } = currentImItem;

    let nextIndex = index + 1;
    const pushList: itemType[] = [];
    let nextQAMsgBufferObj = {
      isIssuePush: true,
      isAnswerPush: true,
      isUpdateCurrentImItem: true,
      patientAnswerMsg: { ...patientMsgBase, index, msgUid: format.guid(), componentType },
      nextIssueMsg: aiDoctorIssuseList[nextIndex] || {},
    };
    const guid = { uid: format.guid() };

    if (componentType === 'submit') { // 提交前缓存页面的对话数据
      const lastImTtem = {
        componentType: 'submit',
        ...guid,
        index: nextIndex,
      };
      session.set('isAiGuideInquiryBack', 1, 1.2);
      session.set('aiGuideInquiryData', { pageState: state, lastImTtem }, 1.2);
      return;
    }
    nextQAMsgBufferObj = {
      ...nextQAMsgBufferObj,
      patientAnswerMsg: { ...nextQAMsgBufferObj.patientAnswerMsg, customExts: { content } },
    };
    if (componentType === 'annex') { // 附件要特别处理
      if (attachmentList.length && !content) {
        nextQAMsgBufferObj.patientAnswerMsg.contentsType = 'IMAGE';
        nextQAMsgBufferObj.patientAnswerMsg.customExts = {
          ...nextQAMsgBufferObj.patientAnswerMsg.customExts,
          // eslint-disable-next-line @typescript-eslint/ban-ts-comment
          // @ts-ignore
          attachmentList,
        };
        upDateDispatch({ type: 'annex', data: { list: attachmentList } });
      }
    }
    // eslint-disable-next-line no-prototype-builtins
    const isHasPrevIndex = currentImItem.hasOwnProperty('prevIndex');
    // const isUnderageFemale = componentType === 'patient' && (patientGender !== 'F' || patientGender === 'F' && patientAge < 18);
    // if (!isHasPrevIndex && isUnderageFemale) { //非女性 或未成年女性 跳过询问 孕期 问题
    //   nextIndex += 1;
    //   nextQAMsgBufferObj.nextIssueMsg = aiDoctorIssuseList[nextIndex] || {};
    // }
    if (isHasPrevIndex) { // 编辑某问题待回答

      nextIndex = prevIndex;
      nextQAMsgBufferObj.nextIssueMsg = aiDoctorIssuseList[nextIndex] || {};
      nextQAMsgBufferObj.isIssuePush = false;
      const { list = [] } = state;
      const reviseWithBady = !currentImItem.msgUid && componentType === 'extraInfo' && issuseKey == 'withBady';
      let insertIndex = list.findIndex((k) => ((reviseWithBady ? k.uid == currentImItem.uid : k.msgUid == currentImItem.msgUid)));
      let cullNumber = 1;
      if (reviseWithBady) {
        cullNumber = 0;
        insertIndex += 1;
      }
      const reviseAnswerInsert = { ...nextQAMsgBufferObj.patientAnswerMsg, index, msgUid: currentImItem.msgUid || nextQAMsgBufferObj.patientAnswerMsg.msgUid };
      upDateDispatch({ type: 'reviseAnswer', data: { cullNumber, actionIndex: insertIndex, reviseAnswerInsert } });// 订正某问题答案
      nextQAMsgBufferObj.isAnswerPush = false;
    }


    if (nextIndex >= aiDoctorIssuseList.length) { // 最后一个问题，提交
      nextQAMsgBufferObj.isIssuePush = false;
      nextQAMsgBufferObj.nextIssueMsg = {
        componentType: 'submit',
        index: nextIndex,
      };
    }

    const { nextIssueMsg, patientAnswerMsg, isIssuePush, isAnswerPush, isUpdateCurrentImItem } = nextQAMsgBufferObj;
    // eslint-disable-next-line no-prototype-builtins
    if (!nextIssueMsg.hasOwnProperty('index')) {
      nextIssueMsg.index = nextIndex;
    }
    isAnswerPush && pushList.push(patientAnswerMsg);
    isIssuePush && pushList.push(nextIssueMsg);
    // ['description','annex','patient']
    isUpdateCurrentImItem && setCurrentImItem(nextIssueMsg);
    pushList.length && upDateDispatch({ type: 'push', data: { list: pushList } });
    scrollBottom();
  }, [currentImItem, patientGender, patientAge, attachmentList]);
  const issuseEditRevise = useCallback((msgItem) => {
    const { index: msgIndex = 0 } = msgItem;
    const { index = 0, prevIndex = '', msgUid = '' } = currentImItem;
    if (msgItem.msgUid !== msgUid) {
      // StaticToast.error('请先完成当前问题');
      const item = aiDoctorIssuseList[msgIndex] || {};
      const nextImItem = {
        ...item,
        ...msgItem,
        index: msgIndex,
        title: item.title || item.customExts?.content,
      };
      if (item.componentType == 'patient') { // 变更用户提示是否清空问题
        Modal.confirm({
          // mountContainer: modalRef.current,
          className: 'modal_ref_wrap',
          content: (
            <p className='danger_inquiry_alert_content center'>
              更改用户需重新回答，是否更换？
            </p>
          ),
          onOk: () => {
            upDateDispatch({ type: 'changeUser', data: { reviseAnswerInsert: nextImItem } });// 订正某问题答案
            editInquiry('illnessDescription', '', {
              attachmentList: [],
            });
            setCurrentImItem(nextImItem);
          },
        });
        return;
      }
      nextImItem.prevIndex = prevIndex || index;
      setCurrentImItem(nextImItem);
      scrollBottom();
    }
  }, [currentImItem]);

  const outExtraVisible = useCallback((item) => (!cardHasPatient || (cardHasPatient && item.componentType !== 'patient')), [cardHasPatient]);
  const textInquiryMode = validate.isFromMiniApplet() || validate.isBestpay() || dmEnv.isApp() || !isVideo;
  const qrcodeImg = `${CDN_PREFIX}static/inquiry/qrcode.png`;

  return (
    showChangeInquiry ? <div>
      <img style={{width: '100%'}} src='https://static.za-doctor.com/staticImages/aiGuid.jpg' alt='' />
      <FixedButton buttonShape='round'
        buttonClick={() => {
          // window.location.href = `/hospital/inquiryform?${Serialize(othersSearch)}`;
          props.history.push({
            pathname: '/hospital/inquiryform',
            search: Serialize(othersSearch),
          });
        }} text='立即问诊' />
    </div> :
      <>
        <div className={textInquiryMode ? 'aiguide-page text-mode' : 'aiguide-page'} ref={popWrapperEl}>
          <CheckInquiry position='inquiryForm' currentTab={1} create={create} />
          {
            !textInquiryMode ? (
              <div className='video-tips'>
                <div>本平台暂不支持视频问诊功能</div>
                <br />
                <div className='tips-text'>如需使用，请<span className='text-red'>截图保存二维码或微信扫码</span>打开对应小程序</div>
                <img className='tips-img' src={qrcodeImg} alt='' />
              </div>
            ) :
              <>
                {
                  isBusy?
                    <PhoneMessage doctorInfo={{}} msgInfo={{isNurseOnDuty: 'Y', isAllDoctorRest: 'Y', isVideo: true}} /> :
                    <MessageBody
                      imChatConfig={{ ...state, groupChat: state }}
                      outExtraVisible={outExtraVisible}
                      outExtra={<p className={`${prefixCls}__edifor-msg`}>点击修改</p>}
                      extraClick={issuseEditRevise} {...props}
                    />
                }
              </>
          }
        </div>
        <p style={{ height: `${placeholderHieght}px` }}></p>
        {inquiryNo && textInquiryMode && !isBusy &&
         <AiPopupComponents popupRef={popupEl}
           popWrapperEl={popWrapperEl}
           onChangePopupHeight={onChangePopupHeight}
           currentItem={currentImItem}
           onComponentConfirm={onComponentConfirm} {...props} />}
      </>
  );
};


export default AiGuideInquiry;
