@import "src/style/index";
$prefixCls: 'aiguide-page';

.#{$prefixCls} {
  background-color: transparent;
  position: relative;

  .aiguide-page-content {
    &__submit {
      padding: r(0) r(15);
      margin-top: r(15);
      margin-bottom: r(50);
    }

    &__tip {
      background: #f0f0f0;
      font-size: r(13);
      z-index: 0;
      color: #666;
      padding: r(12) r(10);

      span {
        color: #00bc70;
      }
    }
  }

  .text-mode {
    padding-bottom: 100px;
  }

  .video-tips {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding-top: r(45);
    font-size: 18px;
    height: 100vh;
    background-color: #fff;

    .tips-text {
      margin: 0 r(40) r(50);
      text-align: center;

      .text-red {
        color: rgba(217, 23, 23, 0.874);
      }
    }

    .tips-img {
      width: 60%;
    }
  }

  .out {
    .chat-content {
      position: relative;
      padding-bottom: r(22);

      .#{$prefixCls} {
        &__edifor-msg {
          position: absolute;
          right: 0;
          bottom: 0;
          color: #00bc70;
          font-size: 12px;
          min-width: 52px;
          text-align: right;
        }
      }
    }
  }
}
