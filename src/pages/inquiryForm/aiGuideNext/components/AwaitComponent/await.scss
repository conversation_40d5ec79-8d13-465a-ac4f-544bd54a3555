@import "src/style/index";
$prefixCls: 'await_component';

.#{$prefixCls} {
  background: #f5f5f5;

  &_content {
    background: #fff;
    border-radius: r(8);
    width: r(345);
    margin: r(10) auto;
  }

  &_window {
    position: relative;
    width: 100%;
    height: r(132);
    background-image: url('./images/window.png');
    background-size: 100%;
    background-repeat: no-repeat;
    overflow: hidden;

    p {
      text-align: center;
      padding-top: r(42.5);
      font-size: r(18);
      font-weight: 600;
      color: #00a864;
    }

    img {
      display: block;
      position: absolute;
      left: 50%;
      transform: translateX(-50%);
      bottom: 0;
      width: r(23);
      height: r(15);
    }

    &_scan {
      position: absolute;
      width: r(252);
      height: r(10);
      background: linear-gradient(rgba(#00a864, 0.1) 0%, rgba(#00a864, 0.4) 100%);
      animation-name: SCAN;
      animation-duration: 2s;
      animation-iteration-count: infinite;
      animation-timing-function: linear;
      left: 50%;
      transform: translateX(-50%);
    }
  }

  &_step {
    @include display-flex;

    margin-top: r(12);

    &_line {
      margin: r(18.5) r(10.5) 0 r(65);
      width: r(8);
      height: r(191);
    }

    &_lv {
      .item {
        margin-bottom: r(16);

        img {
          display: block;
          width: r(66.5);
        }

        p {
          margin-top: -1px;
          font-size: r(14);
          font-weight: 600;
          color: #333;
          line-height: r(20);
        }
      }
    }
  }

  &_tip {
    margin: 0 r(70) 0 r(70);
    font-size: r(11);
    color: #ff5050;
    font-weight: bold;
  }

  &-specific {
    &_btn {
      border-radius: 36px;
      background: #00bc70;
      color: #fff;
      display: inline-block;
      padding: r(7.5) r(37);
    }

    &_qrcode {
      display: block;
      margin: r(25) auto 0;
    }

    &_tip {
      padding-top: r(36) !important;
      font-size: r(16) !important;

      .sm {
        margin: auto;
        max-width: calc(230 / 375 * 100vw);
        font-size: r(13) !important;
      }
    }
  }
}

@keyframes SCAN {
  0% {
    top: 20%;
    opacity: 0;
  }

  25% {
    opacity: 0.4;
  }

  50% {
    top: 68%;
    opacity: 0.2;
  }

  51% {
    opacity: 0;
  }

  100% {
    opacity: 0;
  }
}
