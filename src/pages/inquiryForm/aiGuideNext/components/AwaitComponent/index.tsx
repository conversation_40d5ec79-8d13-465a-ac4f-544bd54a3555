import React, { useCallback, useEffect, useRef, useState } from 'react';
import { useDispatch } from 'react-redux';
import { timSdkLogout } from 'src/store/im/action';
import { fetchJson, validate } from 'src/utils';
import { jumpToAliAppletPublicMethod, jumpToAppletPublicMethod } from 'src/utils/auth';
import bridge from 'src/utils/bridge';
import { Serialize } from 'src/utils/serialization';
import { InquiryStatus, THIRD_PLATFORM_RESOURCECODE } from 'src/utils/staticData';
import storage from 'src/utils/storage';
import startZaAppOnlineVideoConsultation from 'src/utils/tim/open';
import { getInquiryOrMentalConsultListUrl, isDrugReplenishType } from 'src/utils/tool';
import PrescriptionRefuse from 'src/components/common/PrescriptionRefuse';
import useInterval from 'src/utils/useInterval';
import { Loading } from 'zarm-v3';
import './await.scss';
import { Modal } from 'zarm';

interface AwaitComponentProps {
  inquiryId: string;
  inquiryNo: string;
  onStatusChange?: (status: number) => void;
  onDetailChange?: (r: any) => void;
  onRedirect?: (path: string, search: string) => void;
  className?: string;
}

const prefixCls = 'await_component';
const inquiryingStatusList = [2, 3, 7];
const THIRD_CODE = [
  THIRD_PLATFORM_RESOURCECODE.LHYW,
  THIRD_PLATFORM_RESOURCECODE.MBJK,
  THIRD_PLATFORM_RESOURCECODE.KYUSHU_POP,
];

const AwaitComponent: React.FC<AwaitComponentProps> = ({
  inquiryId: propInquiryId = '',
  inquiryNo: propInquiryNo = '',
  onStatusChange,
  onDetailChange,
  onRedirect,
  className = '',
}) => {
  const [isRunning, setIsRunning] = useState(false);
  const [count, setCount] = useState(1);

  const platformCodeRef = useRef<string | null>(null);
  const timerRef = useRef<any>(null);

  const [platformCode, setPlatformCode] = useState('');
  const [inquiryId, setInquiryId] = useState(propInquiryId);
  const [inquiryNo, setInquiryNo] = useState(propInquiryNo);
  const [inquiryStatus, setInquiryStatus] = useState(0);
  const [inquiryType, setInquiryType] = useState('');
  const [inquiryTimingType, setInquiryTimingType] = useState('');
  const [isSpecificRights, setIsSpecificRights] = useState(false);
  const [specificRightsAttachmentUrl, setSpecificRightsAttachmentUrl] = useState('');
  /** 是否为商城的先方后药的问诊单 */
  const [isHyMallDrugReplenish, setIsHyMallDrugReplenish] = useState(false);
  /** 是否展示处方拒绝弹窗 */
  const [showPrescriptionRefuse, setShowPrescriptionRefuse] = useState(false);

  const dispatch = useDispatch();

  // 清理定时器
  const clearAwaitTimer = useCallback(() => {
    if (timerRef.current) {
      clearTimeout(timerRef.current);
      timerRef.current = null;
    }
  }, []);

  // 重定向方法
  const redirectTo = useCallback((pathname: string, search = '') => {
    if (onRedirect) {
      onRedirect(pathname, search);
    } else {
      window.location.replace(`${pathname}${search ? `?${search}` : ''}`);
    }
  }, [onRedirect]);

  // 重定向到过期列表
  const redirectExpiredList = useCallback((inquiryTimingType: string) => {
    const baseUrl = getInquiryOrMentalConsultListUrl(inquiryTimingType);
    redirectTo(`${baseUrl}/expired`, 'withStatus=expired');
  }, [redirectTo]);

  /**
   * @description 处理异常问诊状态
   */
  const handleUnusualInquiry = useCallback((status: number, inquiryDetail?: any) => {
    // fetchInquiryDetail 方法中的引用方式，导致当前函数执行时，inquiryTimingType、isHyMallDrugReplenish 都还没被react更新。
    // 通过inquiryDetail来补救
    let _inquiryTimingType = inquiryTimingType;
    let _isHyMallDrugReplenish = isHyMallDrugReplenish;

    if (inquiryDetail) {
      _inquiryTimingType = inquiryDetail.inquiryTimingType;
      _isHyMallDrugReplenish = isDrugReplenishType(inquiryDetail.inquiryTimingType) && !inquiryDetail.isYSB;
    }

    const endInquiryStatus = [4, 5]; // 已完成&&已评价

    if ([...endInquiryStatus, 6, 9].includes(status)) {
      setIsRunning(false);
    }

    if (status === 6) {
      Modal.alert({
        content: '超时医生未接诊，服务已取消',
        cancelText: '我知道了',
        onCancel: () => {
          redirectExpiredList(_inquiryTimingType);
        },
      });
    } else if (status === 9) {
      // 商城先方后药，在分配页中过期取消时，页面无需自动跳转到问诊记录列表页
      if (_isHyMallDrugReplenish) {
        setShowPrescriptionRefuse(true);
        return;
      }
      // 已取消
      redirectExpiredList(_inquiryTimingType);
    } else if (endInquiryStatus.includes(status)) {
      redirectTo('/hospital/chat', `inquiryId=${inquiryId}&inquiryNo=${inquiryNo}`);
    }
  }, [inquiryId, inquiryNo, inquiryTimingType, redirectExpiredList, redirectTo, isHyMallDrugReplenish]);

  // 跳转到视频页面
  const toVideoPage = useCallback((no = '', id = '', status: number) => {
    const query = {
      inquiryId: id,
      inquiryNo: no,
      inquiryStatus: status,
    };
    const nextSearch = Serialize(query);

    if (validate.isAlipayApplet()) {
      const path = `/pages/video/index?${nextSearch}`;
      jumpToAliAppletPublicMethod(path);
      return;
    }

    const path = `/pages/video/selfChat?${nextSearch}`;
    jumpToAppletPublicMethod(path);
  }, []);

  // 跳转到第三方视频页面
  const toThirdVideoPage = useCallback(async ({ inquiryNo = '', id = '', inquiryStatus, canRecall }: any) => {
    const query = {
      inquiryId: id,
      inquiryNo,
      inquiryStatus,
      canRecall,
    };
    const nextSearch = Serialize(query);

    const appVerIsCanOnlineVideo: any = await startZaAppOnlineVideoConsultation(id, inquiryNo);
    if (appVerIsCanOnlineVideo) {
      return;
    }

    if (validate.isFromMiniApplet()) {
      const path = `/pages/video/await?${nextSearch}`;
      jumpToAppletPublicMethod(path);
    }
  }, []);

  // 跳转到聊天页面
  const toChatPage = useCallback((no = '', id = '') => {
    clearAwaitTimer();
    const _search = Serialize({
      inquiryId: id || inquiryId,
      inquiryNo: no || inquiryNo,
    });

    redirectTo('/hospital/chat', _search);
  }, [ clearAwaitTimer, inquiryId, inquiryNo, redirectTo]);


  // 打开问诊聊天
  const openInquiryChat = useCallback(() => {
    fetchJson({
      url: '/api/api/v1/patient/video/inquiry/open',
      type: 'GET',
      data: {
        inquiryId,
        inquiryNo,
      },
      isloading: false,
      success: (res) => {
        if (res && res.code === '0') {
          setIsRunning(true);
        }
      },
    });
  }, [inquiryId, inquiryNo]);

  // 处理问诊
  const handleInquiry = useCallback((inquiryData: any) => {
    const {
      platformCode = '',
      id = '',
      inquiryNo = '',
      inquiryType = '',
      inquiryStatus = '',
      canRecall = '',
      inquiryThirdRelationList: [{ thirdInquiryId = '' } = {}] = [],
    } = inquiryData;

    if (platformCode === THIRD_PLATFORM_RESOURCECODE.ZAHY) {
      if (inquiryStatus === 3) {
        if (inquiryType === 'I') {
          toChatPage(inquiryNo, id);
        } else {
          toVideoPage(inquiryNo, id, inquiryStatus);
        }
      }
      if (inquiryStatus === 2) {
        clearAwaitTimer();
      }
    } else if (THIRD_CODE.includes(platformCode)) {
      if (inquiryType === 'I') {
        if (!thirdInquiryId || (canRecall == 'Y' && inquiryStatus == '3') || inquiryStatus == '2') {
          openInquiryChat();
        } else if (canRecall == 'N' && inquiryStatus == '3') {
          toChatPage(inquiryNo, id);
        }
      } else {
        toThirdVideoPage({ inquiryNo, id, inquiryStatus, canRecall });
      }
    }
  }, [clearAwaitTimer, openInquiryChat, toChatPage, toThirdVideoPage, toVideoPage]);

  // 轮询问诊状态
  const fetchPoll = useCallback(() => {
    const currentPlatformCode = platformCodeRef.current || '';

    if (THIRD_CODE.includes(currentPlatformCode)) {
      fetchJson({
        url: '/api/api/v1/patient/video/inquiry/poll',
        type: 'GET',
        data: {
          inquiryId,
          inquiryNo,
        },
        success: (res) => {
          const { code = '0', result: { thirdDoctorId = '' } = {} } = res;

          if (code === '0' && thirdDoctorId) {
            setIsRunning(false);
            if (inquiryType === 'I') {
              toChatPage(inquiryNo, inquiryId);
            } else {
              toThirdVideoPage({ inquiryNo, id: inquiryId });
            }
          }
        },
        error: () => {
          setIsRunning(false);
        },
      });
    }

    if (currentPlatformCode === THIRD_PLATFORM_RESOURCECODE.ZAHY || !currentPlatformCode) {
      fetchJson({
        type: 'POST',
        url: '/api/api/v1/patient/inquiry/detail',
        data: {
          inquiryId,
          inquiryNo,
        },
        success: (res) => {
          const { code = '0', result: { inquiryType = '', inquiryStatus = '', platformCode = '' } = {} } = res;
          if (code === '0') {
            setInquiryStatus(inquiryStatus);
            onStatusChange?.(inquiryStatus);
            setPlatformCode(platformCode);
            platformCodeRef.current = platformCode;

            if (platformCode && inquiryingStatusList.includes(inquiryStatus)) {
              handleInquiry(res.result);
            }

            if ((inquiryStatus === 3 || (THIRD_CODE.includes(platformCode) && inquiryType === 'V'))) {
              setIsRunning(false);
            }

            handleUnusualInquiry(inquiryStatus);
          }
        },
        error: () => {
          setIsRunning(false);
        },
      });
    }
  }, [inquiryId, inquiryNo, inquiryType, handleInquiry, handleUnusualInquiry, onStatusChange, toChatPage, toThirdVideoPage]);

  // 初始化
  useEffect(() => {
    dispatch(timSdkLogout());
    storage.remove('ispick');

    const fetchInquiryDetail = async () => {
      const res = await fetchJson({
        type: 'POST',
        url: '/api/api/v1/patient/inquiry/detail',
        isloading: false,
        data: {
          inquiryId,
          inquiryNo,
          option: {
            needThirdRelation: true,
            needRightsBaseInfo: true,
            needPatient: true,
            needQueryPrescription: true,
            needMedical: true,
          },
        },
      });

      const { code = '', result = {} } = res || {};
      if (code === '0') {
        const {
          platformCode = '',
          id = '',
          inquiryNo = '',
          inquiryStatus,
          inquiryType = '',
          inquiryTimingType = '',
          rightsBasicInfoDomain = {},
          isYSB
        } = result;

        setIsSpecificRights(rightsBasicInfoDomain?.specificRights === 'Y');
        setSpecificRightsAttachmentUrl(rightsBasicInfoDomain?.specificRightsAttachment?.attachmentDownloadUrl);
        setPlatformCode(platformCode);
        setInquiryStatus(inquiryStatus);
        onStatusChange?.(inquiryStatus);
        platformCodeRef.current = platformCode;
        setInquiryNo(inquiryNo);
        inquiryTimingType && setInquiryTimingType(inquiryTimingType);
        setInquiryId(id);
        setInquiryType(inquiryType);
        setIsHyMallDrugReplenish(isDrugReplenishType(inquiryTimingType) && !isYSB);

        if (platformCode) {
          if (inquiryingStatusList.includes(inquiryStatus)) {
            handleInquiry(res.result);
          }
          if (platformCode === THIRD_PLATFORM_RESOURCECODE.ZAHY && inquiryStatus !== 3) {
            setIsRunning(true);
          }
        } else {
          setIsRunning(true);
        }

        handleUnusualInquiry(inquiryStatus, result);
        onDetailChange?.(result);
      }
    };

    if (inquiryId || inquiryNo) {
      fetchInquiryDetail();
    }

    // 设置一分钟超时
    clearAwaitTimer();
    timerRef.current = setTimeout(() => {
      setIsRunning(false);
      // redirectTo('/hospital/paySuccess/await', `inquiryNo=${inquiryNo}&inquiryId=${inquiryId}`);
    }, 1000 * 60 * 20);

    return () => {
      clearAwaitTimer();
    };
  }, []);

  // 设置标题
  useEffect(() => {
    bridge.setTitle(inquiryStatus === 2 ? '医生待回复' : '正在分配医生');
  }, [inquiryStatus]);

  // 轮询间隔
  useInterval(
    () => {
      setCount(count+1);
      fetchPoll();
    },
    isRunning && inquiryNo && inquiryId && inquiryType ? 1000 : null,
  );

  // 处理倒计时结束
  useEffect(() => {
    if (count === 0) {
      setIsRunning(false);
      redirectTo(getInquiryOrMentalConsultListUrl(inquiryTimingType), '');
    }
  }, [count, inquiryTimingType, redirectTo]);

  /** 是否即将跳转聊天页面 (对于不需停留的问诊状态，不再展示页面信息) */
  const isDirectToChat = [InquiryStatus.INQUIRY_ING, InquiryStatus.INQUIRY_END, InquiryStatus.INQUIRY_EVALUATED].includes(inquiryStatus);

  // 加载中状态
  if (inquiryStatus === 0 || isDirectToChat) {
    return <div style={{ textAlign: 'center', marginTop: '10px' }}><Loading /></div>;
  }

  // 渲染等待提示
  const renderPendingStatusTip = () => {
    if (isSpecificRights) {
      return (
        <div>
          <p className={`${prefixCls}-specific_tip`}>
            专家临床问诊中，线上接诊需等待
            <br />
            <div className='sm'>您可扫码添加导诊助手，为您协调专家时间，获取就医指导等福利权益</div>
          </p>
        </div>
      );
    }

    if (inquiryStatus === InquiryStatus.INQUIRY_WAIT_ASSIGN) {
      return (
        <p>
          您的信息已收到
          <br />
          正在为您分配医生&nbsp;{count}S
        </p>
      )
    }

    if (inquiryStatus === InquiryStatus.INQUIRY_AWAIT) {
      return (
        <p>
          已经为您分配医生<br />
          请等待医生回复···
        </p>
      )
    }

    return null;
  };

  // 渲染步骤
  const renderStep = () => {
    if (isSpecificRights && specificRightsAttachmentUrl) {
      return (
        <img className={`${prefixCls}-specific_qrcode`} src={specificRightsAttachmentUrl} alt='' />
      );
    }
    return null;
  };

  if (showPrescriptionRefuse) {
    return <PrescriptionRefuse inquiryNo={inquiryNo} rejectReason='问诊单超时未开方，您的开方申请已被医生拒绝' />
  }

  return (
    <div className={`${prefixCls} ${className}`}>
      <div className={`${prefixCls}_content`}>
        <div className={`${prefixCls}_window`}
          style={{
            backgroundSize: inquiryStatus === 6 ? '100% 115%' : '100%',
          }}>
          {renderPendingStatusTip()}
          <div className={`${prefixCls}_window_scan`}></div>
        </div>
        {renderStep()}
      </div>
    </div>
  );
};

export default AwaitComponent;
