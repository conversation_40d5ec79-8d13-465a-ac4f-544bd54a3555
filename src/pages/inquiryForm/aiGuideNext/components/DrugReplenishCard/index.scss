@import "src/style/index";
$prefixCls: 'aiguide-page-content';

.#{$prefixCls} {
  &__drug-replenish {
    font-family: PingFang SC;

    &__guide {
      margin: r(15);
    }

    &__card {
      margin: r(15);
      padding: r(15);
      border-radius: 8px;
      background: linear-gradient(180deg, #e3fff9 0%, #fff 18%);
      box-shadow: inset 0 0.5px 0 0 #fff;
    }

    &__title {
      display: flex;
      align-items: center;
      margin-bottom: r(13);
      font-size: r(16);
      font-weight: 600;
      line-height: r(21);
      letter-spacing: normal;
      color: #333;

      img {
        width: r(24);
        height: r(24);
        margin-right: r(5);
      }
    }

    &__patient {
      margin-bottom: r(10);
      @include onepx(bottom, #EAEAEA);
    }

    &__item {
      display: flex;
      justify-content: flex-start;
      padding-bottom: r(10);

      .label {
        font-size: r(14);
        font-weight: normal;
        line-height: r(21);
        letter-spacing: normal;
        color: #999;
      }

      .desc {
        flex: 1;

        &-flex {
          display: flex;
          justify-content: space-between;

          &__right {
            flex: 1;
            margin-left: r(5);
            text-align: right;
          }
        }
      }
    }

    &__alert {
      display: flex;
      align-items: center;

      img {
        width: r(13);
        height: r(13);
        margin-right: r(4);
      }

      .text {
        font-size: r(12);
        font-weight: normal;
        line-height: r(21);
        letter-spacing: normal;
        color: #999;
      }
    }
  }
}
