import React, { useEffect, useState } from 'react';
import { fetchJson } from 'src/utils';

import './index.scss';

import Drug_guide from './images/drug_guide.webp';
import Info_icon from './images/info_icon.webp';
import Guard_icon from './images/guard_icon.webp';

const $prefix = 'aiguide-page-content';

interface IPrescriptionMedicineItem {
  drugSkuCode: string;
  drugName: string;
  dosageTotal: number;
}

/**
 * 药品补充卡片组件
 *
 * 该组件用于显示药品补充相关的问诊信息，包括用户信息等内容。
 *
 * @param {Object} props - 组件属性
 * @param {Object} props.patient - 患者信息对象
 * @returns {JSX.Element} 药品补充卡片组件
 */
export default function DrugReplenishCard(props) {
  const {
    patient: { patientName = '', patientAge = '', patientGender = '', patientHealth = {} },
    illnessDescription = '',
    inquiryNo,
    id,
  } = props;

  const [prescriptionMedicineList, setPrescriptionMedicineList] = useState<IPrescriptionMedicineItem[]>();
  const { patientAllergicHistory } = patientHealth;
  const patientAllergicHistoryList = patientAllergicHistory ? JSON.parse(patientAllergicHistory) : [];
  const patientAllergicHistoryTags = patientAllergicHistoryList.map((item) => {
    const { additional, tags } = item;
    return tags.join('|') + (additional? `|${additional}` : '');
  }).join(',');

  /**
   * 获取患者问诊处方补充缓存数据
   *
   * @returns {void}
   */
  const getPrescriptionList = () => {
    fetchJson({
      type: 'GET',
      url: '/api/api/v1/patient/inquiry/prescription/replenish/cache',
      data: {
        inquiryId: id,
        inquiryNo,
      },
      success: (res) => {
        const { code = '0', result = [] } = res;
        if (code === '0') {
          setPrescriptionMedicineList(
            result.reduce((acc, item) => {
              acc.push(
                ...item.prescriptionMedicineList.map((medicine) => ({
                  drugSkuCode: medicine.drugSkuCode,
                  drugName: medicine.drugName,
                  dosageTotal: medicine.dosageTotal,
                })),
              );
              return acc;
            }, []),
          );
        }
      },
    });
  };

  useEffect(() => {
    getPrescriptionList();
  }, []);

  const sex = { M: '男', F: '女' }[patientGender] || '未知';

  return (
    <div className={`${$prefix}__drug-replenish`}>
      <div className={`${$prefix}__drug-replenish__guide`}>
        <img src={Drug_guide} alt='' />
      </div>
      <div className={`${$prefix}__drug-replenish__card`}>
        <div className={`${$prefix}__drug-replenish__title`}>
          <img src={Info_icon} alt='' />
          <div className='text'>问诊信息</div>
        </div>
        <div className={`${$prefix}__drug-replenish__patient ${$prefix}__drug-replenish__item`}>
          <div className='label'>用户信息：</div>
          <div className='desc'>
            {patientName} {sex} {patientAge}岁 复诊/
            {patientAllergicHistoryTags ? `有过敏史：${patientAllergicHistoryTags}` : '无过敏史'}
          </div>
        </div>
        <div className={`${$prefix}__drug-replenish__item`}>
          <div className='label'>病情描述：</div>
          <div className='desc'>{illnessDescription}</div>
        </div>
        <div className={`${$prefix}__drug-replenish__item`}>
          <div className='label'>药品需求：</div>
          <div className='desc'>
            {prescriptionMedicineList?.map((item, index) => (
              <div className='desc-flex' key={item.drugSkuCode}>
                <div>{item.drugName}</div>
                <div className='desc-flex__right'>x{item.dosageTotal}</div>
              </div>
            ))}
          </div>
        </div>
        <div className={`${$prefix}__drug-replenish__alert`}>
          <img src={Guard_icon} alt='' />
          <div className='text'>互联网医院将保证您的隐私安全，请放心与医生问诊</div>
        </div>
      </div>
    </div>
  );
}
