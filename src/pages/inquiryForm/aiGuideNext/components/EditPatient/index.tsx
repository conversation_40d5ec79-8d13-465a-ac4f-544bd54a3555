import classnames from 'classnames';
import React, { useState, useCallback, useEffect } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { StaticToast, SvgIcon, Card } from 'src/components/common';
import { useIOSInputScroll } from 'src/hooks/useIOSInputScroll';
import { ApplicationState } from 'src/store';
import { edit_patient_info, save_patient, clear_patient, edit_patient } from 'src/store/patients/action';
import { fetchJson } from 'src/utils/fetch';
import format from 'src/utils/format';
import { TransformEnumArray } from 'src/utils/serialization';
import validate from 'src/utils/validate';
import { Popup, Button } from 'zarm';
import { Cell, Input, Select, DatePicker } from 'zarm';

import './editpatient.scss';

interface EditPatientPopupProps {
  visible: boolean;
  onClose: () => void;
  onSuccess?: () => void;
  setVisible?: (r: boolean) => void;
  patientId?: string;
  hasSelf?: boolean;
}

const EditPatientPopup: React.FC<EditPatientPopupProps> = ({
  visible,
  onClose,
  onSuccess,
  hasSelf = true,
}) => {
  const [blurName, setBlurName] = useState('');
  const [IdNumberDisabled, setIdNumberDisabled] = useState(false);
  const [birthdayVisiblePicker, setBirthdayVisiblePicker] = useState(false);

  const dispatch = useDispatch();
  const editPatientData = useSelector((state: ApplicationState) => state.patients.editPatient || {});
  const [inputRefs, setInputRefs] = useState<HTMLInputElement[]>();

  useIOSInputScroll(inputRefs);

  const {
    GENDER,
    CERTTYPE,
  }: any = useSelector((state: ApplicationState) => {
    const {
      CERTTYPE = [],
      GENDER = [],
    } = state.dictionary;
    return {
      GENDER: TransformEnumArray(GENDER, 'resValue', 'resName'),
      CERTTYPE: TransformEnumArray(CERTTYPE, 'resCode', 'resName'),
    };
  });

  const {
    patientName = '',
    patientCertType = 'I',
    patientCertNo = '',
    patientGender = '',
    patientBirthday = '',
    isRelative = false,
    patientRelation,
  } = editPatientData;

  const dispatchEditPatientInfo = (key: string, value: any) => dispatch(edit_patient_info(key, value));
  const dispatchEditPatient = (value: any) => dispatch(edit_patient(value));
  const savePatient = (callback) => dispatch(save_patient(callback));

  useEffect(() => {
    dispatchEditPatient({
      patientRelation: hasSelf ? 5 : 1,
      patientCertType,
    });
  }, [hasSelf]);

  React.useEffect(() => {
    if (patientCertType === 'I' && validate.isIdCard(patientCertNo)) {
      const value: any = validate.isIdCard(patientCertNo);
      setIdNumberDisabled(true);
      dispatchEditPatientInfo('patientBirthday', value.birthday);
      dispatchEditPatientInfo('patientGender', value.sex);
    } else {
      setIdNumberDisabled(false);
      dispatchEditPatientInfo('patientBirthday', '');
      dispatchEditPatientInfo('patientGender', '');
    }
  }, [patientCertType, patientCertNo]);

  const patientInfoChange = useCallback(
    (key: string, value: any, args?: any) => {
      if (IdNumberDisabled && key === 'patientGender') {
        return;
      }
      if (key === 'patientDistrictCode') {
        dispatchEditPatientInfo('patientDistrictName', args);
      }
      dispatchEditPatientInfo(key, value);
    },
    [IdNumberDisabled],
  );

  const validata = (editPatientData: any) => {
    const {
      patientName = '',
      patientCertType = '',
      patientCertNo = '',
      patientCertNoCOVER = '',
      patientBirthday = '',
    } = editPatientData;

    if (patientName.trim() === '') {
      StaticToast.warning('您还未输入患者的姓名');
      return false;
    } else if (!validate.isUsername(patientName)) {
      StaticToast.warning('您输入的患者姓名格式错误');
      return false;
    } else if (patientCertNo.trim() === '') {
      StaticToast.warning('您还未输入患者的证件号码');
      return false;
    } else if (patientCertType === 'I' && !validate.isIdCard(patientCertNo)) {
      StaticToast.warning('您输入的患者证件号码格式错误');
      return false;
    } else if(patientCertType !== 'I' && !validate.isNumberAndWord(patientCertNo)) {
      StaticToast.warning('您输入的患者证件号码格式错误');
      return false;
    } else if(patientCertType !== 'I' && !patientGender.trim()) {
      StaticToast.warning('您还未选择患者性别');
      return false;
    } else if(patientCertType !== 'I' && !patientBirthday.trim()) {
      StaticToast.warning('您还未输入患者出生日期');
      return false;
    }
    return true;
  };

  const savePatientClick = useCallback(() => {
    if (validata(editPatientData)) {
      savePatient(() => {
        dispatchEditPatient({
          patientRelation: hasSelf ? 5 : 1,
          patientCertType,
        });
        onClose();
        onSuccess && onSuccess();
      });
    }
  }, [editPatientData, isRelative]);

  const nameBlur = useCallback(() => {
    if (patientName === blurName) {
      return;
    }
    fetchJson({
      type: 'POST',
      url: '/api/api/v1/patient/patient/getPatientRelativesCertNo',
      data: { patientName },
      isloading: false,
    }).then((res) => {
      if (res && res.code === '0') {
        const { result: { relativesCertNo = '', relativesId = '' } = {} } = res;
        if (relativesCertNo) {
          patientInfoChange('isRelative', true);
          patientInfoChange('patientCertNo', relativesCertNo);
          patientInfoChange('patientCertNoCOVER', relativesCertNo);
          patientInfoChange('relativesId', relativesId);
        } else {
          patientInfoChange('isRelative', false);
          patientInfoChange('relativesId', '');
        }
      }
    });
  }, [patientName, blurName]);

  const goToPrivacy = (pathname, search = '') => {
    if(pathname) {
      window.reactHistory.push({
        pathname,
        search,
      });
    }
  };

  useEffect(() => {
    if (visible) {
      setInputRefs([
        document.querySelector(`.${prefixCls}__input-name input`) as HTMLInputElement,
        document.querySelector(`.${prefixCls}__input-certNo input`) as HTMLInputElement,
      ]);
    }
  }, [visible, patientCertType]);

  const prefixCls = 'editpatient_page_popup';

  return (
    <Popup
      visible={visible}
      direction='bottom'
      onMaskClick={onClose}
      destroy={false}
    >
      <div className={`${prefixCls}`} style={{ maxHeight: '80vh', overflowY: 'auto' }}>
        <div className={`${prefixCls}__guide`}>
          <h3>新建用户</h3>
          <img src={require('./images/close.svg')} alt='' onClick={onClose}/>
        </div>
        <div className={`${prefixCls}__tip`}><img src={require('./images/tip.svg')} alt='' />为保证您获取完整保单权益数据，请进行实名</div>
        <Card prefixCls={`${prefixCls}__card`}>
          <Cell className={`input ${prefixCls}__cell`} title='姓名'>
            <Input
              className={`${prefixCls}__input-name`}
              type='text'
              placeholder='请输入真实姓名'
              onChange={(value) => {
                patientInfoChange('patientName', value);
              }}
              value={patientName}
              maxLength={15}
              onBlur={() => nameBlur()}
            />
          </Cell>
          <Cell className={`input ${prefixCls}__cell`} title='证件类型' hasArrow>
            <Select
              hasArrow={false}
              dataSource={CERTTYPE} placeholder='应监管要求录入'
              onOk={
                (selected: Array<{value: string, label: string}> = []) => {
                  patientInfoChange('patientCertType', (selected[0] || {}).value);
                }
              }
              value={patientCertType}
            />
          </Cell>
          <Cell className={`input ${prefixCls}__cell`} title='证件号码'>
            <Input
              className={`${prefixCls}__input-certNo`}
              placeholder={patientCertType === 'Q' ? '婴幼儿可填写出生日期，格式19990101' : '应监管要求录入证件号码'}
              maxLength={patientCertType ==='I' ? 18 : 30}
              onChange={(value) => {
                patientInfoChange('patientCertNo', value);
              }}
              value={patientCertNo}
            />
          </Cell>
          {
            patientCertType !== 'I' &&
            <>
              <Cell className={`input ${prefixCls}__cell`} title='性别'>
                <div className={`${prefixCls}__cell-relation`}>
                  {GENDER.map((k, i) => (
                    <div
                      onClick={() => patientInfoChange('patientGender', k.value)}
                      className={classnames('gender button', {
                        'not-selected': patientGender !== k.value,
                        'disabled': IdNumberDisabled,
                      })}
                      key={`k${i}${k.value}`}
                    >
                      <SvgIcon width={15}
                        height={15}
                        className='svg'
                        src={require(`./images/sprite-icon-${k.value == 'M' ? 'boy' : 'girl'}.svg`)} />
                      {k.label}
                    </div>
                  ))}
                </div>
              </Cell>
              <Cell
                className={`input ${prefixCls}__cell`}
                title='出生日期'
                hasArrow
                onClick={() => setBirthdayVisiblePicker(true)}>
                <div className='za-select__value'>{patientBirthday}</div>
                <DatePicker
                  title='出生日期'
                  max={new Date()}
                  visible={birthdayVisiblePicker}
                  value={patientBirthday ? new Date(patientBirthday) : null}
                  onOk={(value) => {
                    setBirthdayVisiblePicker(false);
                    patientInfoChange('patientBirthday', format.date(value, 'yyyy-MM-dd'));
                  }}
                  onCancel={() => setBirthdayVisiblePicker(false)}
                />
              </Cell>
            </>
          }
        </Card>

        <div className={`${prefixCls}-footer`}>
          <div className={`${prefixCls}-footer__privacy`}>我已阅读并同意<span onClick={() => {
            goToPrivacy('/hospital/agreement');
          }}>《用户协议》</span>
          <span onClick={() => {
            goToPrivacy('/hospital/private');
          }}>《隐私政策》</span>和
          <span onClick={() => {
            goToPrivacy('/hospital/pdf', `url=${encodeURIComponent('https://static.za-doctor.com/pdf/riskinfo.pdf')}&t=${Date.now()}`);
          }}>《风险告知及患者知情同意书》</span></div>
          <div className={`${prefixCls}-footer__btn`} onClick={savePatientClick}>确认提交</div>
        </div>
      </div>
    </Popup>
  );
};

export default EditPatientPopup;
