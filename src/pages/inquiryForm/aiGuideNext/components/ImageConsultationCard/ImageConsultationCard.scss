@import "src/style/index";

.consultation-container {
  display: flex;
  flex-direction: column;
  border-radius: r(8);
  background: linear-gradient(180deg, #f0fcf7 0%, #fff 100%);
  box-shadow: inset 0 1px 0 0 #fff;
  margin-top: r(15);
  padding-bottom: r(15);

  .top-section {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    padding: r(15) r(10);
    background-repeat: no-repeat;
    background-size: contain;
    background-position: 80%;

    .icon-text-group {
      display: flex;
      align-items: center;

      .icon-box {
        margin-right: 1rem;

        img {
          width: r(44);
        }
      }

      .text-group {
        .title {
          font-size: 1.125rem;
          font-weight: 600;
          margin-bottom: 0.25rem;
        }

        .desc {
          font-size: 0.875rem;
          color: #6c757d;
        }
      }
    }

    .consult-right {
      text-align: right;

      span {
        font-size: r(14);
        font-variation-settings: "opsz" auto;
        color: #fa0000;
        display: inline-block;
        margin-right: 3px;
        margin-bottom: 5px;
        font-weight: bold;
      }
    }

    .consult-btn {
      padding: r(7.5) r(25);
      background: #00bc70;
      color: white;
      border: none;
      border-radius: 100px;
      font-size: r(15);
      cursor: pointer;

      &.disabled {
        opacity: 0.5;
      }

      &:hover {
        opacity: 0.8;
      }
    }
  }

  .bottom-link {
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: r(13);
    cursor: pointer;
    border-top: 1px solid #e7e7e7;
    margin: 0 r(10);
    padding-top: r(15);
    color: #666;

    span {
      color: #2ecc71;
    }

    svg {
      margin-left: 0.25rem;
      width: 1rem;
      height: 1rem;
    }
  }
}
