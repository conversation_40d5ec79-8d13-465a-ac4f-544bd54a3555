import classnames from 'classnames';
import React, { useCallback, useState, useEffect } from 'react';
import { useSelector, shallowEqual, useDispatch } from 'react-redux';
import { RightsAndDiscount } from 'src/components/common';
import { useOverflowHidden } from 'src/components/hooks';
import ConfirmPop from 'src/pages/inquiryForm/conforpop';
import { autoUnifiedOrder, unifiedOrderProcessPay } from 'src/pages/inquiryForm/utils';
import { ApplicationState } from 'src/store';
import { clear_inquiry, edit_inquiry, submit_inquiry } from 'src/store/inquiry/action';
import { Deserialize, getIsBlackUser, storage } from 'src/utils';
import { fetchJson } from 'src/utils/fetch';
import './ImageConsultationCard.scss';
import format from 'src/utils/format';
import { pushEvent, sendMonitorEvent } from 'src/utils/pageTrack';

interface ImageConsultationCardProps {
  onConsultClick?: () => void;
  onClear?: () => void;
  className?: string;
  isVideo?: boolean;
  showTip?: boolean;
}

const ImageConsultationCard: React.FC<ImageConsultationCardProps> = ({
  onConsultClick,
  className = '',
  isVideo = false,
  showTip,
}) => {
  // 从Redux获取问诊单数据
  const { create = {} } = useSelector((state: ApplicationState) => ({
    create: state.inquiry.create,
  }), shallowEqual);

  const {
    patientId = '',
    patientName = '',
    inquiryNo,
    inquiryType,
    orderAmount = 0,
    orderRealAmount = 0,
  } = create;

  const { businessType, onlyRights } =  Deserialize(window.location.search);

  const isCTAI = businessType === 'CTAI';

  const dispatch = useDispatch();
  const editInquiry = (key: string, value: any, payload?: any) => dispatch(edit_inquiry(key, value, payload));
  const clearInquiry = () => dispatch(clear_inquiry());
  const submitInquiry = (onSuccess: any) => dispatch(submit_inquiry(onSuccess, { isClear: true }));

  // 权益和优惠券状态
  const [vasDiscountPop, setVasDiscountPop] = useState(false);
  const [couponList, setCouponList] = useState<any[]>([]);
  const [vasList, setVasList] = useState<any[]>([]);
  const [checkedCoupon, setCheckedCoupon] = useState<any>({});
  const [checkedVas, setCheckedVas]: any = useState({});
  const [isCalculatePrice, setIsCalculatePrice] = useState(false);
  const [popVisible, setPopVisible] = useState(false);

  useOverflowHidden(vasDiscountPop);

  const hasNoChoice = (!couponList.length && !vasList.length);

  // 计算价格的函数，从ConditionSubmit中提取
  const calculatePrice = useCallback((create, select?) => {
    setIsCalculatePrice(true);
    const {
      orderGoods = [], platformCode = '',
      useRightsAndDiscount, policyNo,
      userRightsId,
      fromSelfChannel,
      inquiryTimingType,
      cardNo,
      staffId,
      rightsId,
      isChangeInquiry,
    } = create;

    const data: any = {
      orderGoods: orderGoods || [],
      platformCode: platformCode || '',
      patientId,
      orderType: 'inquiry',
      inquiryType,
      orderPreferenceList: [],
      returnValidPreference: true,
      relatedBizNo: inquiryNo,
      inquiryTimingType,
      policyNo,
      cardNo,
      staffId,
      fromSelfChannel,
      rightsId,
      userRightsId,
      isChangeInquiry,
    };
    const { userPreferenceObjectId = '' } = useRightsAndDiscount || {};
    if (userPreferenceObjectId) {
      data.orderPreferenceList.push(useRightsAndDiscount);
    }
    if(userRightsId && !userPreferenceObjectId && !select) {
      data.orderPreferenceList.push({
        preferenceObjectType: '3',
        userPreferenceObjectId: userRightsId,
        isPolicyRights: false,
      });
    }
    fetchJson({
      url: '/api/api/v1/patient/preference/calculate',
      type: 'POST',
      data: {
        ...data,
      },
      needLoading: true,
      success: (res) => {
        const { code = '0', result: { couponList = [], rightsList = [], order: { orderGoods = [], platformCode = '', orderAmount = '', orderRealAmount = '', orderPreferenceList = [] } = {} } = {} } = res || {};
        if (code === '0') {
          // 问诊可以用的优惠券
          const coupon: any[] = couponList.map((element) => ({
            ...element.coupon,
            couponEffectiveTime: (element.couponEffectiveTime || '').replace(/-/g, '/'),
            couponExpiryTime: (element.couponExpiryTime || '').replace(/-/g, '/'),
            id: element.id,
            userCouponStatus: element.userCouponStatus,
          }));
          setCouponList(coupon);
          setVasList(rightsList);
          const { userPreferenceObjectId, preferenceObjectType = '' } = orderPreferenceList[0] || {};
          if (preferenceObjectType == '1') {
            const checkedCoupon = coupon.find(({ id }) => id == userPreferenceObjectId) || {};
            setCheckedCoupon(checkedCoupon);
            setCheckedVas({});
          } else if (preferenceObjectType == '3-1') {
            const checkedVas = rightsList.find(({ id }) => id == userPreferenceObjectId) || {};
            setCheckedCoupon({});
            setCheckedVas(checkedVas);
          } else {
            setCheckedCoupon({});
            setCheckedVas({});
          }
          if (orderPreferenceList[0]) {
            const findItem = rightsList.find((item) => item.id === orderPreferenceList[0].userPreferenceObjectId);
            if (findItem) {
              orderPreferenceList[0].drawVasCode = findItem.drawVasCode;
            }
          }
          editInquiry('useRightsAndDiscount', orderPreferenceList[0] || {}, {
            orderGoods,
            orderPreferenceList,
            platformCode,
            orderRealAmount,
            orderAmount,
          });
          setIsCalculatePrice(false);
        }
      },
    });
  }, [patientId, inquiryNo, inquiryType]);

  // 初始化时获取权益和优惠券数据
  useEffect(() => {
    if (patientId) {
      calculatePrice(create);
    }
  }, [patientId]);

  // 点击优惠券
  const couponClick = useCallback((item: any) => {
    if (!item.id || item.id !== checkedCoupon.id) {
      const param = item.id ? {
        userPreferenceObjectId: item.id,
        preferenceObjectType: '1',
      } : {};
      editInquiry('useRightsAndDiscount', param);
      calculatePrice({ ...create, useRightsAndDiscount: param }, true);
    }
  }, [checkedCoupon, create]);

  // 点击权益
  const vasClick = useCallback((item) => {
    if (!item.id || item.id !== checkedVas.id) {
      const param = item.id ? {
        userPreferenceObjectId: item.id,
        preferenceObjectType: '3',
        isPolicyRights: item.isPolicyRights,
      } : {};
      editInquiry('useRightsAndDiscount', param);
      calculatePrice({ ...create, useRightsAndDiscount: param }, true);
    }
  }, [checkedVas, create]);

  // 从选中的权益获取名称
  const { id: couponId , couponName } = checkedCoupon;
  const { id: vasId, rightsBasicInfoDomain = {} } = checkedVas;
  const vasName = rightsBasicInfoDomain.rightsName;

  // 显示的权益名称：如果有选中的权益，优先显示选中的权益名称
  const displayRightName = vasId ? vasName : couponId ? couponName : '';

  // 可用的优惠券
  const ableCouple = couponList.filter(({ userCouponStatus }) => userCouponStatus === 1);

  const handleConsultClick = useCallback((e) => {
    if(getIsBlackUser()) {
      return;
    }
    e.stopPropagation();
    showTip ? setPopVisible(true) : agreeConfirm();
  }, [onConsultClick]);

  // 确认风险告知书之后创建问诊单的数据，处理为后端需要的格式
  const agreeConfirm = useCallback(() => {
    pushEvent({eventTag: 'inquiry_sub_ct_end'});
    if(create.isChangeInquiry) {
      pushEvent({eventTag: 'inquiry_changeInquiry_sub_end'});
    }
    setPopVisible(false);
    try {
      submitInquiry((result) => {
        // 删除ai智能导诊页的缓存数据
        storage.remove('isAiGuideInquiryBack');
        storage.remove('aiGuideInquiryData');
        storage.remove('aiGuideInquiryThyroidData');
        unifiedOrderProcessPay(result, true);
        if(result.orderRealAmount !== 0) {
          // onClear && onClear();
        }
      });
    } catch(e) {
      sendMonitorEvent(
        'inquiry_sub_ct_end_error',
        'error',
        'inquiry_sub_ct_end报错',
        {
          error: e,
        }
      );
    }
  }, []);

  return (
    <div className={`consultation-container ${className}`}>
      <div className='top-section' style={{
        backgroundImage: `url('${require('./images/bg.png')}')`,
      }}>
        <div className='icon-text-group'>
          <div className='icon-box'>
            <img src={require('./images/consult.png')} alt='' />
          </div>
          <div className='text-group'>
            <h3 className='title'>{isVideo ? '视频问诊权益' : '图文问诊权益'}</h3>
            <p className='desc'>24小时健康守护不打烊</p>
          </div>
        </div>
        <div className='consult-right'>
          {!!orderRealAmount && orderAmount !== orderRealAmount && <span style={{
            fontSize: 12,
            textDecoration: 'line-through',
            color: '#D7D7D7',
          }}>¥{orderAmount}</span>}&nbsp;
          {!!orderRealAmount && <span>¥{orderRealAmount}</span>}
          <div className={classnames('consult-btn', {
            disabled: getIsBlackUser(),
          })} onClick={handleConsultClick}>{orderRealAmount === 0 ? '去问诊' :  '去支付'}</div>
        </div>
      </div>
      {
        hasNoChoice ?
          <div className='bottom-link'>
            <div style={{color: '#8E8E8E'}}>暂无可用优惠券</div>
            <svg width='12' height='12' viewBox='0 0 12 12' fill='none' xmlns='http://www.w3.org/2000/svg'>
              <path d='M4 2L8 6L4 10' stroke='#8E8E8E' strokeWidth='1.5' strokeLinecap='round' strokeLinejoin='round' />
            </svg>
          </div> :
          <div className='bottom-link'>
            <RightsAndDiscount
              className={'consultation-container__wrapper'}
              checkedVas={checkedVas}
              vasList={vasList}
              vasClick={vasClick}
              patientName={patientName}
              ableCouple={ableCouple}
              checkedCoupon={checkedCoupon}
              couponClick={couponClick}
              disabled={isCTAI || onlyRights === '1'}
            >
              {
                displayRightName ? <div>已选<span>{displayRightName}</span>权益</div> : <span>优惠权益待使用</span>
              }
            </RightsAndDiscount>
            <svg width='12' height='12' viewBox='0 0 12 12' fill='none' xmlns='http://www.w3.org/2000/svg'>
              <path d='M4 2L8 6L4 10' stroke='#2ECC71' strokeWidth='1.5' strokeLinecap='round' strokeLinejoin='round' />
            </svg>
          </div>
      }
      <ConfirmPop popVisible={popVisible} agreeConfirm={agreeConfirm} closePop={() => setPopVisible(false)} />
    </div>
  );
};

export default ImageConsultationCard;
