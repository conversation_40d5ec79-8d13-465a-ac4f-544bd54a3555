import React from'react';

import './ImageConsultationHead.scss';
interface ImageConsultationHeadProps {
  isVideo: boolean
}

const ImageConsultationHead: React.FC<ImageConsultationHeadProps> = ({ isVideo = false }) => {

  const renderRightsInfo = () => {
    return <span className='ImageConsultationHead-rights__current-type'>{isVideo ? '视频问诊' : '图文问诊'}</span>
  }

  return (
    <div
      className='ImageConsultationHead'
    >
      <img src={require('./images/head.webp')} alt='' />
      <div className='ImageConsultationHead-rights'>
        <img className='ImageConsultationHead-rights__bg' src={require('./images/rights-bg.webp')} alt='' />
        <div className='ImageConsultationHead-rights__current'>
          <span className='ImageConsultationHead-rights__current-my'>我的当前权益：</span>
          {renderRightsInfo()}
        </div>
        <div
          className='ImageConsultationHead-rights__more' 
          onClick={() => {
            window.reactHistory.push({
              pathname: '/hospital/myrights',
            });
          }}
        >
          查看其他权益<img className='ImageConsultationHead-rights__more-arrow' src={require('./images/arrow.png')} alt='' />
        </div>
      </div>
    </div>
  );
};

export default ImageConsultationHead;
