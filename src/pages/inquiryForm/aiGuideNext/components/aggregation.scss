@import "src/style/index";
$prefixCls: 'aipopup-components';
@import "./patientAnnex/patientAnnex.scss";

.#{$prefixCls} {
  &__popup {
    .za-popup {
      z-index: 999;
    }
  }

  &__box {
    background-color: #fff;
    border-radius: r(8) r(8) 0 0;
    padding-bottom: r(26);

    .confirm-btn {
      padding: r(12) r(15) 0;
    }
  }

  &__title {
    height: r(49);
    color: #1e1e1e;
    line-height: r(49);
    padding: 0 r(15);
    border-bottom: r(1) solid#e6e6e6;
    @include display-flex;
    @include align-items(center);
    @include justify-content(space-between);

    h3 {
      font-size: r(16);
      font-weight: 600;
    }

    .tip {
      font-weight: normal;
      font-size: r(13);
      color: #b2b2b2;
    }
  }

  &__add {
    font-family: PingFang SC;
    font-size: r(14);
    font-variation-settings: "opsz" auto;
    color: #00bc70;
  }
  //患者选择
  &__patient {
    .patient-component {
      padding-left: r(15);
    }

    .no_patient {
      background-color: #fffced;
      padding: r(10) 0;
      margin-bottom: r(-26);
    }

    .line-first {
      text-align: center;
      font-size: 14px;
      color: #ec9131;
      margin-bottom: r(2.5);
    }

    .line-second {
      text-align: center;
      font-size: 12px;
      color: #ec9131;
    }
  }

  //问题描述
  &__conditiondesc {
    form {
      padding-bottom: 0;
      padding-left: 0;
    }
  }
  //扩展信息
  &__extraInfo {
    padding: r(10) r(15) 0;

    .confirm-btn {
      padding: r(12) 0 0;
    }

    &-title {
      color: #ccc;
      font-size: r(13);
      text-align: center;
      padding-bottom: r(6);
    }

    .inquiry-other {
      &__tags {
        .SelectButton-item {
          padding: r(4) r(15);
          font-size: r(12);
          margin-bottom: r(6);
        }
      }

      &__issues {
        margin: 0;
        padding: r(10);
      }

      &__more {
        padding: 0;
      }
    }

    &-checkbox {
      text-align: center;
      width: 100%;

      .za-checkbox {
        width: 30%;

        &:last-child {
          margin-right: 0;
        }
      }
    }

    .za-radio-group,
    .za-checkbox-group {
      &__inner {
        margin: 0;
      }

      &--button {
        .za-radio,
        .za-checkbox {
          background: rgba(0, 0, 0, 0.04);
          border-radius: 20px;
          height: r(38);

          &.za-radio--checked,
          &.za-checkbox--checked,
          &.za-checkbox--checked:hover &.za-radio--checked:hover {
            background: rgba(236, 145, 49, 0.06) !important;
            border: 1px solid rgba(236, 145, 49, 0.32) !important;
            color: #ec9131 !important;
          }
        }
      }
    }
  }

  //不支持的服务
  &__notSupport {
    padding: r(10) r(15) 0;

    .text {
      margin: 0 auto;
      background: #fff7cb;
      width: 100%;
      color: #ec9131;
      font-size: r(13);
      text-align: center;
      line-height: r(19);
      border-radius: r(6);
    }
  }
}

.conditiondesc-automatic {
  &_title {
    font-size: r(15);
    color: #212121;
    padding: r(12.5) r(15);
    border-bottom: 1px solid #e7e7e7;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  &_label {
    font-size: r(16);
    font-weight: 600;
  }

  &_switch {
    font-size: r(14);
    color: #00bc70;
    display: flex;
    align-items: center;

    img {
      width: r(13.5);
      margin-right: r(5);
    }
  }

  &_tags {
    display: flex;
    flex-wrap: wrap;
  }

  &_tag {
    border: 1px solid #dedede;
    background: #f5f5f5;
    border-radius: 25.5px;
    padding: r(5) r(10);
    margin-left: r(15);
    font-size: r(13);
    margin-top: r(12);
  }

  &_tag.active {
    border: 1px solid #fcd8b7;
  }
}

//患者选择
.modal_ref_wrap {
  .za-popup--center {
    width: 80% !important;
  }

  .za-confirm__button {
    padding: 0;
    font-size: r(16);
  }
}

.ios-toast__container {
  .za-mask,
  .za-popup__wrapper {
    top: auto;
    bottom: 100%;
  }
}
