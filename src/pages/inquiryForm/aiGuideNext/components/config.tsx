import SelectPatients from './selectPatient';
import ConditionDesc from './conditionDesc';
import PatientExtraInfo from './patientExtraInfo';
import NotSupportService from './notSupport';
import AiSubmit from './submit';
import PatientAnnex from './patientAnnex';

// 组件聚合
const ComponentAggregation = {
  patient: SelectPatients, //选择患者
  description: ConditionDesc, //问题描述
  extraInfo: PatientExtraInfo, //问诊扩展信息
  annex: PatientAnnex, //上传附件
  notSupport: NotSupportService, //不支持服务的提示
  submit: AiSubmit, //最后信息提交
  default: () => (null), // 空消息 or 未知类型
};

export default ComponentAggregation;
