import React, { useEffect, useRef, useState } from 'react';
import { Popup } from 'zarm';
import componentAggregation from './config';
import './aggregation.scss';

const prefixCls = 'aipopup-components';

const AiPopupComponents = (props) => {
  const {
    // location: { pathname = '', search = '' },
    currentItem = {},
    onComponentConfirm = null,
    mountContainer,
    popupRef = null,
    ...other
  } = props;

  const [visible, setVisible] = useState(false);

  useEffect(() => {
    console.log(window.location.pathname);
    setVisible(true);
  }, []);

  const CurrentComponent = componentAggregation[currentItem.componentType || 'default'];

  return (
    <Popup
      visible={visible}
      direction='bottom'
      destroy={true}
      mask={false}
      mountContainer={() => mountContainer.current!}
      className={`${prefixCls}__popup`}
    >
      <div className={`${prefixCls}__box`} key={`time${currentItem.time}`} ref={popupRef}>
        <CurrentComponent prefixCls={prefixCls} onConfirm={onComponentConfirm} currentItem={currentItem} key={`uid${currentItem.uid}`} {...other} />
      </div>
    </Popup>
  );
};


export default AiPopupComponents;
