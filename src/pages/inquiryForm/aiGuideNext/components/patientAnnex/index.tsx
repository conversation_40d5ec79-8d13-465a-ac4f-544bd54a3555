import React, { useCallback, useEffect, useState } from 'react';
import { shallowEqual, useDispatch, useSelector } from 'react-redux';
import { CompatibleFileInput, StaticToast, SvgIcon } from 'src/components/common';
import { ApplicationState } from 'src/store';
import { edit_inquiry, edit_inquiry_addFile } from 'src/store/inquiry/action';
import { pushEvent } from 'src/utils';
import { Icon, Modal, Carousel, Button } from 'zarm';

const maxAttachmentTotal = 9;

const AiPatientAnnexComponents = (props) => {
  const {
    prefixCls = '',
    onConfirm = null,
    onChangePopupHeight = null,
    showJump = true,
  } = props;
  const [showImageCarousel, setShowImageCarousel] = useState(false);
  const [imageCarouselActive, setImageCarouselActive] = useState(0);

  const { create = {} } = useSelector((state: ApplicationState) => state.inquiry, shallowEqual);
  const { SYSTEMMODE = [] }: any = useSelector((state: ApplicationState) => state.dictionary, shallowEqual);
  const supervisionModel = (SYSTEMMODE[0] || {}).resCode;
  const { attachmentList = [], inquiryNo } = create;
  const dispatch = useDispatch();
  const editInquiry = (key: string, value: any, payload?: any) => dispatch(edit_inquiry(key, value, payload));

  useEffect(() => {
    if (attachmentList.length % 3 == 0) {
      onChangePopupHeight && onChangePopupHeight();
    }
  }, [attachmentList]);
  // 上传附件前的验证
  const onUploadBeforeValidate = useCallback((e) => {
    if (!inquiryNo) {
      StaticToast.warning('没有生成问诊单编号，图片无法上传，请重新进入诊单填写页');
      return false;
    }
    return true;
  }, [inquiryNo]);

  // 上传附件
  const uploadFileClick = useCallback((data) => {
    dispatch(edit_inquiry_addFile({ ...data, url: data.localLink }));
  }, [inquiryNo, attachmentList]);

  // 删除附件
  const deleteFileClick = useCallback((id: number) => {
    editInquiry('attachmentList', [
      ...attachmentList.filter((i) => i.id !== id),
    ]);
  }, [attachmentList]);

  // 点击图片放大展示
  const photoClick = useCallback((item) => {
    const index = attachmentList.findIndex((k) => k.id == item.id && k.attachmentDownloadUrl === item.attachmentDownloadUrl);
    if (index >= 0) {
      setShowImageCarousel(true);
      setImageCarouselActive(index);
    }
  }, [attachmentList]);
  const handleSumbit = useCallback(() => {
    pushEvent({eventTag: 'picture_sub'});
    if (!attachmentList.length) {
      if (supervisionModel === 'supervision') {
        StaticToast.warning('复诊材料不可为空');
        return;
      }
      jumpOverStep();
      return;
    }
    onConfirm && onConfirm('', create);
  }, [attachmentList]);
  const jumpOverStep = () => {
    editInquiry('attachmentList', []);
    onConfirm && onConfirm('暂无图片');
  };
  return (
    <>
      <div className={`${prefixCls}__annex`}>
        <div className={`${prefixCls}__title`}>
          <h3>上传图片{supervisionModel === 'supervision' ? '（必填）' : '（选填）'}</h3>
          {supervisionModel !== 'supervision' && showJump && <p className='tip' onClick={jumpOverStep}>跳过</p>}
        </div>
        <div className={`${prefixCls}__upload`}>
          <p className={`${prefixCls}__tips`}>检查单/病历/患处图片/处方单图片（最多9张）</p>
          <div className={`${prefixCls}__photo`}>
            {attachmentList.length
              ? attachmentList.map((item: any, index: number) => (
                <div className='upload-item' key={`upload-photo-${index}`}>
                  <img className='upload-photo' src={`${item.attachmentDownloadUrl}`} alt='' onClick={() => photoClick(item)} />
                  <Icon className='delete-icon' theme='danger' type='minus-round-fill' onClick={() => deleteFileClick(item.id)} />
                </div>
              ))
              : null}
            {attachmentList.length < maxAttachmentTotal && (
              <div className='upload-item'>
                <div className='upload-button'>
                  <CompatibleFileInput
                    id='upload_file'
                    className='upload-input'
                    maxAttachmentTotal={maxAttachmentTotal}
                    hasAttachmentTotal={attachmentList.length}
                    businessNo={inquiryNo}
                    attachmentType='inquiryVoucher'
                    onUploadBeforeValidate={onUploadBeforeValidate}
                    onUploadSuccess={uploadFileClick}
                  />
                  <label htmlFor='upload_file'>
                    <SvgIcon className='upload-cover' type='img' src={require('../../../images/add_photo.png')} />
                    <p className='upload-text'>{attachmentList.length ? `${attachmentList.length}/${maxAttachmentTotal}` : '添加图片'}</p>
                  </label>
                </div>
              </div>
            )}
          </div>
        </div>
        {
          showJump &&
          <p className='confirm-btn'>
            <Button onClick={handleSumbit} theme='primary' shape='round' size='md' block>确定</Button>
          </p>
        }
      </div>
      <Modal
        className='previewimage-component__imagemodal condition_description_modal'
        visible={showImageCarousel}
        maskClosable
        onCancel={() => {
          setShowImageCarousel(false);
        }}
      >
        <div
          onClick={() => {
            setShowImageCarousel(false);
          }}
        >
          <Carousel className='image_modal_carousel' activeIndex={imageCarouselActive} showPagination={false} onChange={(activeIndex) => {
            setImageCarouselActive(activeIndex!);
          }}>
            {attachmentList.map((photo, index) => (
              <div className='carousel__item__pic' key={+index}>
                <img className='carousel__item__img' src={`${photo.attachmentDownloadUrl}`} alt='' draggable={false} />
              </div>
            ))}
          </Carousel>
          <p className='condition_description_img_num'>{imageCarouselActive + 1} / {attachmentList.length}</p>
        </div>
      </Modal>
    </>
  );
};

export default AiPatientAnnexComponents;
