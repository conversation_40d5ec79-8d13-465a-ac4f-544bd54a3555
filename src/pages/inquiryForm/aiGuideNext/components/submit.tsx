/*
 * @Author: <EMAIL> <EMAIL>
 * @Date: 2022-09-28 18:31:52
 * @LastEditTime: 2023-03-21 10:42:20
 * @LastEditors: <EMAIL> <EMAIL>
 * @FilePath: /za-asclepius-patient-h5/src/pages/inquiryForm/aiGuide/components/submit.tsx
 * @Description:
 */
import React, { useCallback, useState } from 'react';
import { shallowEqual, useSelector } from 'react-redux';
import { StaticToast } from 'src/components/common';
import { ApplicationState } from 'src/store';
import { Deserialize, pushEvent } from 'src/utils';
import { sendMonitorEvent } from 'src/utils/pageTrack';
import { Button } from 'zarm';
import ConfirmPop from '../../conforpop';
import { autoUnifiedOrder, unifiedOrderProcessPay } from '../../utils';

const AiPatientSubmitComponents = (props) => {
  const [popVisible, setPopVisible] = useState(false);

  const { isSelectDepartmentOpen, create } = useSelector((state: ApplicationState) => {
    const { create: { isSelectDepartmentOpen, ...otherCreate } = {} } = state.inquiry;
    return {
      isSelectDepartmentOpen,
      create: otherCreate,
    };
  }, shallowEqual);

  const {
    prefixCls = '',
    onConfirm = null,
    location: { search = '' },
  } = props;

  // 确认风险告知书之后创建问诊单的数据，处理为后端需要的格式
  const agreeConfirm = useCallback(async () => {
    pushEvent({eventTag: 'inquiry_sub_ct_end'});
    setPopVisible(false);
    try {
      const r = await autoUnifiedOrder(create);
      unifiedOrderProcessPay(r);
    } catch(e) {
      sendMonitorEvent(
        'inquiry_sub_ct_end_error',
        'error',
        'inquiry_sub_ct_end报错',
        {
          error: e,
        }
      );
    }
  }, []);

  const { businessType, userRightsId } =  Deserialize(search);

  const isCTAI = businessType === 'CTAI';

  const handleSumbit = useCallback(() => {
    pushEvent({eventTag: 'submit'});
    onConfirm && onConfirm();

    if(isCTAI) {
      if(!userRightsId) {
        return StaticToast.error('未查询到有效的权益');
      }
      setPopVisible(true);
      return;
    }

    if(isSelectDepartmentOpen) {
      return props.history.push({
        pathname: '/hospital/inquiry/office',
      });
    }

    props.history.push({
      pathname: '/hospital/inquiry/submit',
    });
  }, []);
  return (
    <div className={`${prefixCls}__extraInfo`}>
      <Button theme='primary' shape='round' size='md' block onClick={handleSumbit}>{isCTAI ? '发起问诊' : '提交'}</Button>
      <ConfirmPop popVisible={popVisible} agreeConfirm={agreeConfirm} closePop={() => setPopVisible(false)} />
    </div>
  );
};


export default AiPatientSubmitComponents;
