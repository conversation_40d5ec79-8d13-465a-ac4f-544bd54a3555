import { dmEnv } from '@dm/utils';
import React, { useEffect, useReducer, useState, useCallback, useRef, useLayoutEffect } from 'react';
import { shallowEqual, useSelector, useDispatch } from 'react-redux';
import { CheckInquiry, FixedButton } from 'src/components/common';
import { PhoneMessage } from 'src/pages/chatMedicalManage/components';
import { MessageBody } from 'src/pages/textInquiryChat/component';
import { ApplicationState } from 'src/store';
import { clear_inquiry, edit_inquiry } from 'src/store/inquiry/action';
import { fetch_patients_list } from 'src/store/patients/action';
import { Deserialize, pushEvent, validate, storage } from 'src/utils';
import { fetchJson } from 'src/utils/fetch';
import format from 'src/utils/format';
import { CDN_PREFIX, InquiryStatus } from 'src/utils/staticData';
import { session } from 'src/utils/storage';
import './aiGuide.scss';
import {throttleArgs} from 'src/utils/throttle';
import { isDrugReplenishType, rasSecretKeyEncrypt } from 'src/utils/tool';
import { Modal } from 'zarm';
import { Skeleton } from 'zarm-v3';
import patientInquiryInfoInit from '../init';
import AiPopupComponents from './components';
import AwaitComponent from './components/AwaitComponent';
import DrugReplenishCard from './components/DrugReplenishCard';
import ImageConsultationCard from './components/ImageConsultationCard';
import ImageConsultationHead from './components/ImageConsultationHead';
import reducer, { itemType, initState, aiDoctorIssuseList, patientMsgBase, doctorMsgBase } from './reducer';
import { Serialize } from 'src/utils/serialization';
import { useActivate, useUnactivate } from 'react-activation';

const prefixCls = 'aiguide-page';

const AiGuideInquiry = (props) => {
  const {
    location: { pathname = '', search = '' },
  } = props;

  const popWrapperEl: any = useRef(null);
  const popupEl: any = useRef(null);
  const isVideo = /\/hospital\/videointro/ig.test(pathname);
  const dispatch = useDispatch();

  const {
    isChangeInquiry,
    inquiryId = '',
    inquiryNo: searchInquiryNo = '',
    awaitPage = '',
    returnUrl = '', // 商城先方后药流程-开方成功
    failUrl = '', // 商城先方后药流程-开方驳回
    successUrl = '', // 商城先方后药流程-查看详情-非必要
    ...othersSearch
  } = Deserialize(search);

  const editInquiry = (key: string, value: any, payload?: any) => dispatch(edit_inquiry(key, value, payload));
  const { pageState = initState, lastImTtem = {} } = session.get('aiGuideInquiryData') || {};
  const [placeholderHieght, setPlaceholderHieght] = useState<Number | string>(0);
  const [currentImItem, setCurrentImItem] = useState<itemType>(lastImTtem);
  const [isBusy, setIsBusy] = useState(false);
  const [state, upDateDispatch] = useReducer(reducer, pageState);
  const [showChangeInquiry, setShowChangeInquiry] =  useState(isChangeInquiry === 'Y');
  const [inquiryType, setInquiryType] =  useState('');
  const [isAwait, setIsAwait] = useState(/\/hospital\/await/ig.test(pathname) || awaitPage === '1');
  const checkInquiryRef = useRef<any>(null);
  /** 是否为商城的先方后药的问诊单 */
  const [isHyMallDrugReplenish, setIsHyMallDrugReplenish] = useState(false);
  // 问诊单详情，使用前必须判空（导诊和轮训同共用同一套页面逻辑，导诊链路时，这里值为空）
  const [inquiryDetail, setInquiryDetail] = useState<any>();

  const clearInquiry = () => dispatch(clear_inquiry());

  const { create = {} } = useSelector((state: ApplicationState) => {
    const { create = {} } = state.inquiry;
    return {
      create,
    };
  }, shallowEqual);

  useEffect(() => {
    setShowChangeInquiry(isChangeInquiry === 'Y');
    setIsAwait(/\/hospital\/await/ig.test(pathname) || awaitPage === '1');
  }, [search]);

  const checkIsDisabled = async () => {
    if(!isVideo) {
      return;
    }
    const res = await fetchJson({
      type: 'POST',
      url: '/api/api/v1/patient/inquiry/acceptTime/isVideoAcceptTime',
      data: {},
      isloading: false,
    });
    if (res.code === '0') {
      setIsBusy(!res.result || false);
    }
  };

  const fetchPatientsList = (onSuccess?) => dispatch(fetch_patients_list({
    inquiryType,
    sensitiveKeyEncry: rasSecretKeyEncrypt(),
    option: {
      needHealthInfo: true,
      needSensitiveEncrypt: !!validate.isBestpay(),
    },
  }, onSuccess));


  const { SYSTEMMODE = [] }: any = useSelector((state: ApplicationState) => state.dictionary, shallowEqual);
  const supervisionModel = (SYSTEMMODE[0] || {}).resCode;

  useLayoutEffect(() => {
    fetchPatientsList();
  }, []);

  const init = () => {
    if(isAwait || !supervisionModel) {
      return;
    }

    // 根据模式决定使用哪个列表
    if (supervisionModel === 'supervision') {

      // 检查是否已经添加了isRevisit问题
      const hasRevisitQuestion = aiDoctorIssuseList.some((item) => item.issuseKey === 'isRevisit');

      // 检查是否已经添加了annex组件
      const hasAnnexComponent = aiDoctorIssuseList.some((item) => item.componentType === 'annex');

      // 只有在没有添加过的情况下才添加
      if (!hasRevisitQuestion) {
        aiDoctorIssuseList.push({
          ...doctorMsgBase,
          customExts: { content: '您是否为复诊？' },
          componentType: 'extraInfo',
          uid: format.guid(),
          issuseKey: 'isRevisit',
        });
      }

      if (!hasAnnexComponent) {
        aiDoctorIssuseList.push({
          ...doctorMsgBase,
          customExts: { content: '请上传能反应病情的图片或者相关检查资料，资料仅自己和医生可见' },
          componentType: 'annex',
          uid: format.guid(),
        });
      }
    }
    if ((validate.isFromMiniApplet() || validate.isBestpay() || dmEnv.isApp() || !isVideo)) {
      const [item] = aiDoctorIssuseList;
      setCurrentImItem({
        ...item,
        index: 0,
      });
      upDateDispatch({ type: 'push', data: { list: [item] } });
    } else {
      setCurrentImItem({
        componentType: 'notSupport',
      });
    }
    patientInquiryInfoInit(props, dispatch);
  };

  const pageShowFunc = (e) => {
    if(e.persisted){
      // if (checkInquiryRef.current) {
      //   const result = checkInquiryRef.current.checkHasInquiry();
      // }
      location.reload();
    }
  };

  useEffect(() => {
    pushEvent({ eventTag: 'guide_page', text: '首页' });
    checkIsDisabled();
    init();
    window.addEventListener('pageshow', pageShowFunc);

    return () => {
      window.removeEventListener('pageshow',pageShowFunc);
    };
  }, [supervisionModel]);

  useEffect(() => {
    if (returnUrl && failUrl && searchInquiryNo) {
      // 每次更新两个链接的有效期都为 1 天
      storage.set(`prescriptionJumpLinkInfo_${searchInquiryNo}`, JSON.stringify({ failUrl, returnUrl, successUrl }), 24 * 60);
    }
  }, []);

  useActivate(() => {
    fetchPatientsList();
    scrollBottom(null, true);
    const l = document.getElementsByClassName('za-popup-container') as never as HTMLDivElement[];
    for(const i of l) {
      i.style.display = 'block';
    }
  });

  useUnactivate(() => {
    const l = document.getElementsByClassName('za-popup-container') as never as HTMLDivElement[];
    for(const i of l) {
      i.style.display = 'none';
    }
  });

  const onChangePopupHeight = throttleArgs((cb) => {
    setTimeout(() => {
      if (popupEl.current) {
        const { height = 0 } = popupEl.current.getBoundingClientRect() || {};
        setPlaceholderHieght(height + 20);
        cb && cb();
      }
    }, 100);
  }, 600);
  // 页面滑动到底部
  const scrollBottom = useCallback((time = 100, direct = false) => {
    if(direct) {
      console.log(popWrapperEl.current);
      setTimeout(() => {
        if (popWrapperEl.current) {
          const { height = 0 } = popWrapperEl.current.getBoundingClientRect() || {};
          window.scrollTo({
            top: Number(height) + 500,
            behavior: 'smooth',
          });
        }
      }, time);
      return;
    }

    onChangePopupHeight(() => {
      setTimeout(() => {
        if (popWrapperEl.current) {
          const { height = 0 } = popWrapperEl.current.getBoundingClientRect() || {};
          console.log(height,1);
          window.scrollTo({
            top: Number(height) + 500,
            behavior: 'smooth',
          });
        }
      }, time);
    });
  }, []);

  const { inquiryNo = '', patientGender = '', patientAge = '', attachmentList = [], cardHasPatient = false } = create;

  const onComponentConfirm = useCallback((content) => {
    const { index = 0, componentType = '', prevIndex = 0, issuseKey = '' } = currentImItem;

    let nextIndex = index + 1;
    const pushList: itemType[] = [];
    let nextQAMsgBufferObj = {
      isIssuePush: true,
      isAnswerPush: true,
      isUpdateCurrentImItem: true,
      patientAnswerMsg: { ...patientMsgBase, index, msgUid: format.guid(), componentType },
      nextIssueMsg: aiDoctorIssuseList[nextIndex] || {},
    };

    nextQAMsgBufferObj = {
      ...nextQAMsgBufferObj,
      patientAnswerMsg: { ...nextQAMsgBufferObj.patientAnswerMsg, customExts: { content } },
    };
    if (componentType === 'annex') { // 附件要特别处理
      if (attachmentList.length && !content) {
        nextQAMsgBufferObj.patientAnswerMsg.contentsType = 'IMAGE';
        nextQAMsgBufferObj.patientAnswerMsg.customExts = {
          ...nextQAMsgBufferObj.patientAnswerMsg.customExts,
          // eslint-disable-next-line @typescript-eslint/ban-ts-comment
          // @ts-ignore
          attachmentList,
        };
        upDateDispatch({ type: 'annex', data: { list: attachmentList } });
      }
    }
    // eslint-disable-next-line no-prototype-builtins
    const isHasPrevIndex = currentImItem.hasOwnProperty('prevIndex');
    if (isHasPrevIndex) { // 编辑某问题待回答

      nextIndex = prevIndex;
      nextQAMsgBufferObj.nextIssueMsg = aiDoctorIssuseList[nextIndex] || {};
      nextQAMsgBufferObj.isIssuePush = false;
      const { list = [] } = state;
      const reviseWithBady = !currentImItem.msgUid && componentType === 'extraInfo' && issuseKey == 'withBady';
      let insertIndex = list.findIndex((k) => ((reviseWithBady ? k.uid == currentImItem.uid : k.msgUid == currentImItem.msgUid)));
      let cullNumber = 1;
      if (reviseWithBady) {
        cullNumber = 0;
        insertIndex += 1;
      }
      const reviseAnswerInsert = { ...nextQAMsgBufferObj.patientAnswerMsg, index, msgUid: currentImItem.msgUid || nextQAMsgBufferObj.patientAnswerMsg.msgUid };
      upDateDispatch({ type: 'reviseAnswer', data: { cullNumber, actionIndex: insertIndex, reviseAnswerInsert } });// 订正某问题答案
      nextQAMsgBufferObj.isAnswerPush = false;
    }


    if (nextIndex >= aiDoctorIssuseList.length) { // 最后一个问题，提交
      nextQAMsgBufferObj.isIssuePush = false;
      nextQAMsgBufferObj.nextIssueMsg = {
        componentType: 'submit',
        index: nextIndex,
      };
      scrollBottom(100, true);
    }

    const { nextIssueMsg, patientAnswerMsg, isIssuePush, isAnswerPush, isUpdateCurrentImItem } = nextQAMsgBufferObj;
    // eslint-disable-next-line no-prototype-builtins
    if (!nextIssueMsg.hasOwnProperty('index')) {
      nextIssueMsg.index = nextIndex;
    }
    isAnswerPush && pushList.push(patientAnswerMsg);
    isIssuePush && pushList.push(nextIssueMsg);
    // ['description','annex','patient']
    isUpdateCurrentImItem && setCurrentImItem(nextIssueMsg);
    pushList.length && upDateDispatch({ type: 'push', data: { list: pushList } });

    scrollBottom();
  }, [currentImItem, patientGender, patientAge, attachmentList]);

  const issuseEditRevise = useCallback((msgItem) => {
    const { index: msgIndex = 0 } = msgItem;
    const { index = 0, prevIndex = '', msgUid = '' } = currentImItem;
    if (msgItem.msgUid !== msgUid) {
      // StaticToast.error('请先完成当前问题');
      const item = aiDoctorIssuseList[msgIndex] || {};
      const nextImItem = {
        ...item,
        ...msgItem,
        index: msgIndex,
        title: item.title || item.customExts?.content,
      };
      if (item.componentType == 'patient') { // 变更用户提示是否清空问题
        Modal.confirm({
          // mountContainer: modalRef.current,
          className: 'modal_ref_wrap',
          content: (
            <p className='danger_inquiry_alert_content center'>
              更改用户需重新回答，是否更换？
            </p>
          ),
          onOk: () => {
            upDateDispatch({ type: 'changeUser', data: { reviseAnswerInsert: nextImItem } });// 订正某问题答案
            editInquiry('illnessDescription', '', {
              attachmentList: [],
            });
            setCurrentImItem(nextImItem);
          },
        });
        return;
      }
      nextImItem.prevIndex = prevIndex || index;
      setCurrentImItem(nextImItem);
      scrollBottom();
    }
  }, [currentImItem]);

  const clearCard = () => {
    clearInquiry();
    upDateDispatch({ type: 'clear'});
    init();
  };

  const onDetailChange = useCallback((r = {}) => {
    const { patientCertNo, patientCertType, patientBirthday, patientGender } = r.patient || {};
    const { inquiryType, inquiryTimingType, isYSB } = r;
    setInquiryType(inquiryType);
    setCurrentImItem({
      componentType: 'submit',
      index: 2,
    });
    setInquiryDetail(r);
    setIsHyMallDrugReplenish(isDrugReplenishType(inquiryTimingType) && !isYSB);
    const year = patientCertNo.substring(6,10);
    const month = patientCertNo.substring(10,12);
    const day = patientCertNo.substring(12,14);
    const birthDay = patientCertType === 'I' ? `${year}-${month}-${day}` : patientBirthday;
    const age = format.GetAgeByBirthday(birthDay) || '';  // 这里不从patientBirthDay取了，前端自己从证件号取
    const { list = [] } = state;
    if(list.length < 3) {
      upDateDispatch({ type: 'push', data: { list: [
        {
          contentsType: 'TEXT',
          customExts: {
            content: r.illnessDescription,
          },
          index: 1,
          msgUid: 'H7AL8TSU',
          componentType: 'description',
        },
        {
          from: 'doctor',
          avatar: 'https://cdn-qcloud.zhongan.com/a00000/za-asclepius/applets/common/doctor_default_avater.png',
          contentsType: 'TEXT',
          customExts: {
            content: '请问您想为哪位用户咨询？',
          },
          componentType: 'patient',
          uid: 'MMZQAXHM',
        },
        {
          contentsType: 'TEXT',
          customExts: {
            content: `${r.patient?.patientName} ${{
              M: '男',
              F: '女',
            }[patientGender] || '未知'} ${age}岁`,
          },
          index: 0,
          msgUid: 'AEOV1PD8',
          componentType: 'patient',
        },
      ]},
      });
    }
    scrollBottom(100, true);
  }, [state.list]);

  const goToPrivacy = (url, search = '') => {
    if(url) {
      props.history.push({
        pathname: url,
        search,
      });
    }
  };

  const outExtraVisible = useCallback((item) => (!cardHasPatient || (cardHasPatient && item.componentType !== 'patient')), [cardHasPatient]);
  const textInquiryMode = validate.isFromMiniApplet() || validate.isBestpay() || dmEnv.isApp() || !isVideo;
  const qrcodeImg = `${CDN_PREFIX}static/inquiry/qrcode.png`;
  const popupContainerRef = useRef(null);

  /** 是否即将跳转聊天页面 (对于不需停留的问诊状态，不再展示页面信息) */
  const isDirectToChat = (inquiryDetail && [InquiryStatus.INQUIRY_ING, InquiryStatus.INQUIRY_END, InquiryStatus.INQUIRY_EVALUATED].includes(inquiryDetail.inquiryStatus));

  const ImageConsultationHeadCard = useCallback(() => {
    if ((searchInquiryNo && !inquiryDetail) || isHyMallDrugReplenish) {
      return null;
    }

    return <ImageConsultationHead isVideo={(inquiryType === 'V') || isVideo} />;
  }, [inquiryType, isVideo, isHyMallDrugReplenish, inquiryDetail]);

  const PatientInfoCard = useCallback(() => {
    if (
      (searchInquiryNo && !inquiryDetail) || isDirectToChat
    ) {
      return (
        <div style={{ margin: '1rem' }} >
          <Skeleton.Title animated />
          <Skeleton.Paragraph animated />
        </div>
      );
    }

    if (isHyMallDrugReplenish && inquiryDetail) {
      return (
        <DrugReplenishCard  {...inquiryDetail} />
      );
    }

    return (
      <>
        <MessageBody
          imChatConfig={{ ...state, groupChat: state }}
          outExtraVisible={outExtraVisible}
          outExtra={isAwait ? null : <p className={`${prefixCls}__edifor-msg`}>点击修改</p>}
          extraClick={issuseEditRevise}
          {...props}
        />
      </>
    );
  }, [props, state, searchInquiryNo, inquiryDetail, isDirectToChat, isHyMallDrugReplenish, isAwait, outExtraVisible, issuseEditRevise]);

  const UserAgreementCard = useCallback(() => {
    if ((searchInquiryNo && !inquiryDetail) || isDirectToChat) {
      return (
        <Skeleton.Paragraph animated />
      );
    }

    if (isHyMallDrugReplenish && inquiryDetail) {
      return null;
    }

    return (
      <div className='aiguide-page-content__tip'>
        继续咨询表明您已阅读并同意众安互联网医院的
        <span onClick={() => {
          goToPrivacy('/hospital/agreement');
        }}>《用户协议》</span>、<span onClick={() => {
          goToPrivacy('/hospital/private');
        }}>《隐私政策》</span>和<span onClick={() => {
          goToPrivacy('/hospital/pdf', `url=${encodeURIComponent('https://static.za-doctor.com/pdf/riskinfo.pdf')}&t=${Date.now()}`);
        }}>《风险告知及患者知情同意书》</span>，如您并非线上复诊，平台上的医师仅为您提供健康咨询服务，咨询意见供您参考。
      </div>
    );
  }, [searchInquiryNo, inquiryDetail, isDirectToChat, isHyMallDrugReplenish]);

  return (
    showChangeInquiry ? <div>
      <img style={{width: '100%'}} src='https://static.za-doctor.com/staticImages/aiGuid.jpg' alt='' />
      <FixedButton buttonShape='round'
        buttonClick={() => {
          props.history.replace({
            pathname: '/hospital/inquiryform',
            search: Serialize(othersSearch),
          });
        }} text='立即问诊' />
    </div> :
      <div id={'AiPopupComponents'} ref={popupContainerRef}>
        <div className={textInquiryMode ? 'aiguide-page text-mode' : 'aiguide-page'}
          ref={popWrapperEl}
        >
          {
            !searchInquiryNo && (
              <CheckInquiry ref={checkInquiryRef} position='inquiryForm' currentTab={1} create={create} direct/>
            )
          }
          {
            !textInquiryMode ? (
              <div className='video-tips'>
                <div>本平台暂不支持视频问诊功能</div>
                <br />
                <div className='tips-text'>如需使用，请<span className='text-red'>截图保存二维码或微信扫码</span>打开对应小程序</div>
                <img className='tips-img' src={qrcodeImg} alt='' />
              </div>
            ) :
              <>
                {
                  isBusy?
                    <PhoneMessage doctorInfo={{}} msgInfo={{isNurseOnDuty: 'Y', isAllDoctorRest: 'Y', isVideo: true}} /> :
                    <div className='aiguide-page-content'>
                      <ImageConsultationHeadCard />
                      <PatientInfoCard />
                      {
                        (currentImItem.componentType === 'submit' || isAwait) &&
                        <div className='aiguide-page-content__submit'>
                          <UserAgreementCard />
                          {
                            isAwait ?
                              <AwaitComponent
                                inquiryId={inquiryId}
                                inquiryNo={searchInquiryNo}
                                onDetailChange={onDetailChange}
                              /> :
                              <ImageConsultationCard showTip={supervisionModel === 'supervision'} isVideo={isVideo} onClear={clearCard}/>
                          }
                        </div>
                      }
                    </div>
                }
              </>
          }
        </div>
        {
          currentImItem.componentType !== 'submit' &&
          <p style={{ height: `${placeholderHieght}px` }}></p>
        }
        {
          inquiryNo && textInquiryMode && !isBusy && currentImItem.componentType !== 'submit' &&
            <AiPopupComponents
              popupRef={popupEl}
              popWrapperEl={popWrapperEl}
              onChangePopupHeight={onChangePopupHeight}
              currentItem={currentImItem}
              onComponentConfirm={onComponentConfirm}
              mountContainer={popupContainerRef}
              {...props}
            />
        }
      </div>
  );
};


export default AiGuideInquiry;
