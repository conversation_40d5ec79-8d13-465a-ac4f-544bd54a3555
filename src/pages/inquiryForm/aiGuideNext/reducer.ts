import { ALLERGY_DRUG, ALLFAMILY_ILLNESS } from 'src/pages/patients/editPatient/patientHealth';
import format from 'src/utils/format';
import { CDN_PREFIX } from 'src/utils/staticData';
import {session} from 'src/utils/storage';

const doctorAvatar = `${CDN_PREFIX}applets/common/doctor_default_avater.png`;

interface attachmentItem {
  [key: string]: string | number;
}
interface customExtsType {
  content: string;
  attachmentList?: attachmentItem[];
  // [key: string]: string ;
}

export interface itemType {
  status?: string;
  time?: number;
  from?: string;
  avatar?: string;
  contentsType?: string;
  customExts?: customExtsType;
  componentType?: string;
  index?: number;
  issuseKey?: string;
  extraKey?: string;
  uid?: string;
  msgUid?: string;
  extraTags?: string[];
  imageGather?: boolean;
  prevIndex?: number;
  title?: string;
}


export const doctorMsgBase = {
  status: 'success',
  time: Date.now(),
  from: 'doctor',
  avatar: doctorAvatar,
  contentsType: 'TEXT',
};
export const patientMsgBase = {
  status: 'success',
  time: Date.now(),
  from: '',
  contentsType: 'TEXT',
  customExts: { content: '' },
};
export const aiDoctorIssuseList: itemType[] = [
  {
    ...doctorMsgBase,
    customExts: { content: '请详细描述您的症状、疾病，包括药物过敏史和既往病史，以便我为您安排合适的医生。（如感冒三天了，无过敏史既往史。）' },
    componentType: 'description',
    uid: format.guid(),
  },
  {
    ...doctorMsgBase,
    customExts: { content: '请问您想为哪位用户咨询？' },
    componentType: 'patient',
    uid: format.guid(),
  },
];


export const initState = {
  list: [],
  groupPhotoList: [],
  isMore: false,
  nextReqMessageID: '',
  lastMessageTime: Date.now(),
};

const reducer = (state, action) => {
  const { type, data: { list = [], pendingAnswer = {}, actionIndex = 0, cullNumber = 0, reviseAnswerInsert = {}, ...other } = {} } = action;


  switch (type) {
  case 'push': {
    const data = {
      ...state,
      list: [...state.list, ...list],
      ...other,
    };
    return data;
  }
  case 'insert':
  case 'reviseAnswer':
    // eslint-disable-next-line no-case-declarations
    const nextList = state.list;
    nextList.splice(actionIndex, cullNumber, reviseAnswerInsert);
    return {
      ...state,
      list: nextList,
      ...other,
    };
  case 'annex':
    return {
      ...state,
      ...other,
      groupPhotoList: list,
    };
  case 'changeUser': { // 变更用户
    const { msgUid = '' } = reviseAnswerInsert;
    const nextList = state.list;
    const maxNum = nextList.findIndex((k) => (k.msgUid && msgUid == k.msgUid));
    return {
      ...state,
      list: nextList.slice(0, maxNum),
      groupPhotoList: [],
      ...other,
    };
  }
  case 'clear': {
    return initState;
  }
  default:
    return state;
  }
};

export default reducer;
