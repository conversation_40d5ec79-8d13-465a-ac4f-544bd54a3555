import React, { useCallback, useState } from 'react';
import { shallowEqual, useSelector, useDispatch } from 'react-redux';
import { ChatInput, StaticToast } from 'src/components/common';
import { ApplicationState } from 'src/store';
import { edit_inquiry } from 'src/store/inquiry/action';
// import { ApplicationState } from 'src/store';
import { pushEvent, validate } from 'src/utils';

const AiConditionDescComponents = (props) => {
  const {
    prefixCls = '',
    onConfirm = null,
  } = props;
  const dispatch = useDispatch();
  const { illnessDescription = '' } = useSelector((state: ApplicationState) => {
    const { create = {} } = state.inquiry;
    return create;
  }, shallowEqual);
  const [sendVal, setSendVal] = useState(illnessDescription);
  const editInquiry = (key: string, value: any, payload?: any) => dispatch(edit_inquiry(key, value, payload));
  const handleSumbit = useCallback(() => {
    pushEvent({eventTag: 'prescription_sub'});
    const value = (sendVal || '').trim();
    if (!value || value.length < 6) {
      StaticToast.error('请描述您的问题,至少6个字！');
      return;
    }
    if(validate.hasEmoji(value)) {
      StaticToast.error('不支持输入表情');
      return;
    }
    editInquiry('illnessDescription', value);
    onConfirm && onConfirm(value);
  }, [sendVal]);

  return (
    <div className={`${prefixCls}__conditiondesc`}>
      <ChatInput popWrapperEl={null} sendVal={sendVal} businessNo={''} handleChange={(e) => setSendVal(e.target.value)} handleFocus={null} handleBlur={null} handleSumbit={handleSumbit} chatSendMsg={null} platformCode={''} placeholder='描述越细，解答越准（至少6个字）' />
    </div>
  );
};


export default AiConditionDescComponents;
