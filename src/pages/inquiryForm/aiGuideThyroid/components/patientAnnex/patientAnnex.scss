@import "src/style/index";

.#{$prefixCls} {
  &__tips {
    font-size: r(13);
    color: #666;
    padding: r(15) 0 r(14);
  }

  &__upload {
    padding: 0 r(15);
  }

  &__photo {
    @include display-flex;
    @include align-items(center);
    @include justify-content(flex-start);
    @include flex-wrap(wrap);

    margin-top: r(-16);

    .upload-item {
      padding: r(16) r(30) 0 0;
      position: relative;

      .delete-icon {
        position: absolute;
        top: r(6);
        right: r(22);
        font-size: r(18);
        border-radius: 50%;
        background: #fff;
        border: 1px solid #fff;
      }

      .upload-photo {
        outline: none;
        width: r(74);
        height: r(74);
        border-radius: r(4);
        background: #f2f2f2;
        position: relative;
        display: inline-block;
        // border: r(1) solid rgba(55, 198, 151, 1);
      }
    }

    .upload-button {
      position: relative;
      width: r(74);
      height: r(74);

      .upload-cover {
        width: r(74);
        height: r(74);
        position: absolute;
        left: 0;
        top: 0;
      }

      .upload-text {
        width: r(74);
        text-align: center;
        position: absolute;
        top: 60%;
        left: 0;
        color: #b2b2b2;
        font-size: r(12);
      }

      input[type="file"] {
        position: absolute;
        width: r(74);
        height: r(74);
        display: none;
        left: 0;
        top: 0;
        z-index: 4;
      }
    }
  }
}

.condition_description_modal {
  .za-popup {
    overflow: visible;
    background-color: transparent;
  }

  .za-carousel__item {
    vertical-align: middle;
  }

  .carousel__item__img {
    background: #fff;
    display: inline-block;
    width: 100%;
    max-height: 80vh;
    transform: rotate(0) !important;
  }

  .condition_description_img_num {
    position: fixed;
    bottom: r(30);
    right: r(30);
    color: #fff;
    font-size: r(15);
    z-index: 9999;
  }
}
