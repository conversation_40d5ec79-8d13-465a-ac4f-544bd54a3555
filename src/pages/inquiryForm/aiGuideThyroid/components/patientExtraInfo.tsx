import _ from 'lodash';
import React, { useCallback, useState } from 'react';
import { shallowEqual, useSelector, useDispatch } from 'react-redux';
import { StaticToast, SelectButton } from 'src/components/common';
import { ApplicationState } from 'src/store';
import { edit_inquiry } from 'src/store/inquiry/action';
import { pushEvent, validate } from 'src/utils';
import format from 'src/utils/format';
import { Input, Radio, Button, Checkbox } from 'zarm';

const feverSelectData = ['无发热', '低热', '高热'];

const AiPatientExtraInfoComponents = (props) => {
  const {
    prefixCls = '',
    currentItem = {},
    onConfirm = null,
    onChangePopupHeight = null,
  } = props;
  const prefix = 'inquiry-other';
  const dispatch = useDispatch();

  const editInquiry = (key: string, value: any, payload?: any) => dispatch(edit_inquiry(key, value, payload));


  const { issuseKey = '', extraKey = '', extraTags = [], customExts = {}, title, template, symptomList = [] } = currentItem;

  const { YESORNO, create, YESORNO_OBJ } = useSelector((state: ApplicationState) => {
    const { YESORNO = [], YESORNO_OBJ = {} } = state.dictionary;
    const { create = {} } = state.inquiry;
    return {
      create,
      YESORNO_OBJ: template !== 'revisit' ? { N: '无', Y: '有' } : YESORNO_OBJ,
      YESORNO: template !== 'revisit' ? YESORNO.map((k) => ({ ...k, resName: k.resCode == 'Y' ? '有' : '无' })) : YESORNO,
    };
  }, shallowEqual);

  const { inquiryExtraInfo = {}, inquiryExtraInfo: { drugAllergic = {}, patientFamilyHistory = {} } = {}, patientGender } = create;
  const extraKeyObj = extraKey && inquiryExtraInfo[extraKey] || {};
  const isHasMore = (issuseKey && extraKey) || (extraKey && extraKeyObj.value == '有');
  const radioValueKey = issuseKey ? issuseKey : extraKey;
  const radioValue = issuseKey ? (issuseKey === 'isRevisit' ? create[issuseKey] : inquiryExtraInfo[issuseKey]) : extraKeyObj.value;
  const [checkboxValue, setCheckboxValue] = useState<string[]>([]);
  const selectedTags = extraKeyObj.tags || [];

  const setEditorContent = useCallback((value) => {
    const { inquiryExtraInfo = {} } = create;
    editInquiry('inquiryExtraInfo', format.merge(inquiryExtraInfo, value));
  }, [create]);

  const handleSumbit = useCallback(() => {
    // console.log('currentItem',currentItem);
    // if(issuseKey === 'fever') {
    //   pushEvent({eventTag: 'symptom'});
    // }

    if(issuseKey === 'symptom') {
      pushEvent({eventTag: 'othersymptom'});
    }

    if (issuseKey === 'isRevisit') { // 不存储在 inquiryExtraInfo，与它同级
      pushEvent({eventTag: 'sub_visit'});
    }

    // if(issuseKey === 'liverSick') {
    //   pushEvent({eventTag: 'liver_kidney'});
    // }

    // if(radioValueKey === 'patientFamilyHistory') {
    //   pushEvent({eventTag: 'family_history'});
    // }

    // if(radioValueKey === 'drugAllergic') {
    //   pushEvent({eventTag: 'drug_allergy'});
    // }
    const value: string[] = [];
    if (!radioValue) {
      StaticToast.error('请选择');
      return;
    } if ((issuseKey === 'symptom' || template === 'symptom') && (!checkboxValue || checkboxValue.length === 0)) {
      StaticToast.error('请选择');
      return;
    } else if (issuseKey === 'isRevisit' && radioValue == 'N') {
      StaticToast.error('请完成线下初诊后再进行线上复诊或开药');
      return;
    } else if ((extraKey && extraKeyObj.value == '有' && !selectedTags.length && !extraKeyObj.additional)) {
      StaticToast.error('您还未填写其他信息');
      return;
    }

    if (isHasMore) {
      if (radioValueKey === 'drugAllergic' || radioValueKey === 'patientFamilyHistory') {
        if (validate.hasEmoji(extraKeyObj.additional)) {
          return StaticToast.error('不支持输入表情');
        }
      }
      selectedTags.length && value.push(...selectedTags);
      extraKeyObj.additional && value.push(extraKeyObj.additional);
    }

    issuseKey === 'fever' ? value.unshift(radioValue) : (issuseKey === 'symptom' || template === 'symptom') ? value.push(...checkboxValue) :
      value.unshift(YESORNO_OBJ[['无', 'N'].includes(radioValue) ? 'N' : 'Y'] || radioValue);
    onConfirm && onConfirm(value.join('，'));
  }, [radioValue, extraKeyObj, isHasMore, selectedTags, checkboxValue]);

  const radioOnChange = (key, value, template?, title?) => {
    if (key === 'symptom' || template === 'symptom') {
      const diff = _.difference(value, checkboxValue);
      if (diff.includes('以上均无')) {
        setCheckboxValue(['以上均无']);
        value = '以上均无';
      } else {
        const dict = (value || []).filter((item) => item !== '以上均无');
        setCheckboxValue(dict);
        value = dict.join(',');
      }
    }

    let extra = {
      [`${key}`]: value,
    };

    onChangePopupHeight && onChangePopupHeight();

    if (key === 'isRevisit') { // 不存储在 inquiryExtraInfo，与它同级
      editInquiry(key, value);
      return;
    }
    if (key == 'drugAllergic' || key == 'patientFamilyHistory') {
      let item = {};
      if (value === '无') {
        item = {
          tags: [],
          additional: '',
        };
      }
      extra = {
        [key]: {
          value,
          ...item,
        },
      };
    }
    if (key === 'symptom' || template === 'symptom') {
      extra = {
        [key]: {
          healthClass: title || '您是否有以下症状',
          tags: value === '以上均无' ? [] : value.split(','),
          additional: '',
          placeholder: title || '您是否有以下症状',
          key: title || '您是否有以下症状',
          value: value === '以上均无' ? '无' : '有',
        },
      };
    }

    setEditorContent(extra);
  };

  const selectTag = (key, value) => {
    let tags = [];
    if (key === 'drugAllergic') {
      if ((drugAllergic.tags || []).includes(value)) {
        tags = drugAllergic.tags.filter((k) => k != value);
      } else {
        tags = drugAllergic.tags.concat(value);
      }
    } else if (key === 'patientFamilyHistory') {
      if ((patientFamilyHistory.tags || []).includes(value)) {
        tags = patientFamilyHistory.tags.filter((k) => k != value);
      } else {
        tags = patientFamilyHistory.tags.concat(value);
      }
    }
    setEditorContent({ [key]: { tags } });
  };

  const additionalSupplement = (key, additional) => {
    setEditorContent({ [key]: { additional } });
  };

  return (
    <div className={`${prefixCls}__extraInfo`}>
      <p className={`${prefixCls}__extraInfo-title`}>{title || customExts.content}</p>
      {
        issuseKey === 'fever' ?
          <Radio.Group size='md' shape='round' block type='button' ghost value={radioValue} onChange={(value) => radioOnChange(radioValueKey, value)}>
            {feverSelectData.map((k) => (<Radio key={`${k}-cell`} value={k}>{k}</Radio>))}
          </Radio.Group> : (issuseKey === 'symptom' || template === 'symptom') ?
            <Checkbox.Group className={`${prefixCls}__extraInfo-checkbox ${template === 'symptom' ? 'symptom' : ''}`} size='md' shape='round' type='button' ghost value={checkboxValue} onChange={(value) => radioOnChange(radioValueKey, value, template, title || customExts.content)}>
              {(symptomList.length ? symptomList : (patientGender === 'F' ? ['高血压', '糖尿病', '孕妇', '哺乳期', '以上均无'] : ['高血压', '糖尿病', '以上均无'])).map((k) => (<Checkbox className={`${k.length > 6 ? 'large' : k.length <= 4 ? 'small' : ''}`} key={`${k}-cell`} value={k}>{k}</Checkbox>))}
            </Checkbox.Group> :
            <Radio.Group size='md' shape='round' block type='button' ghost value={radioValue} onChange={(value) => radioOnChange(radioValueKey, value)}>
              {YESORNO.map((k) => (<Radio key={`${k.resCode}-cell`} value={k.resCode}>{k.resName}</Radio>))}
            </Radio.Group>
      }
      {
        isHasMore && (<div className={`${prefix}__more no-border`}>
          {!!extraTags.length && <div className={`${prefix}__tags`}>
            {extraTags.map((k, i) => <SelectButton onClick={() => selectTag(radioValueKey, k)} selected={(selectedTags || []).includes(k)} key={`patientFamilyHistory${i}`}>{k}</SelectButton>)}
          </div>}
          <div className={`${prefix}__issues`}>
            <Input
              type='text'
              rows={2}
              autoHeight
              onChange={(value) => additionalSupplement(radioValueKey, value)}
              maxLength={200}
              placeholder={extraKeyObj.placeholder}
              value={extraKeyObj.additional}
            />
          </div>
        </div>)
      }
      <p className='confirm-btn'><Button theme='primary' shape='round' size='md' block onClick={handleSumbit}>确定</Button></p>
    </div>
  );
};


export default AiPatientExtraInfoComponents;
