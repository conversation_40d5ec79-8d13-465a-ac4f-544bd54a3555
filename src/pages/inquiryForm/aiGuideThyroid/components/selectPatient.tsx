import React, { useCallback, useEffect, useLayoutEffect, useState } from 'react';
import { shallowEqual, useSelector, useDispatch } from 'react-redux';
import { StaticToast, SvgIcon } from 'src/components/common';
import PatientListTags from 'src/pages/chatMedicalManage/components/patientTags';
import { ApplicationState } from 'src/store';
import { edit_inquiry } from 'src/store/inquiry/action';
import { fetch_patients_list } from 'src/store/patients/action';
import { fetchJson, pushEvent } from 'src/utils';
import { jumpBeforeFetchThirdInquiryAbility } from 'src/utils/auth';
import { isFromCAINIAO } from 'src/utils/staticData';
import { rasSecretKeyEncrypt } from 'src/utils/tool';
import validate from 'src/utils/validate';
import { Button, Modal } from 'zarm';
import { patientInquiryExtraInfo } from '../../utils';

const AiSelectPatientComponents = (props) => {
  const {
    prefixCls = '',
    isCheckHasInquiry = true,
    onConfirm = null,
    currentItem = {},
    onChangePopupHeight = null,
  } = props;
  const dispatch = useDispatch();
  const [needLoading, setNeedLoading] = useState<boolean>(true);
  const editInquiry = (key: string, value: any, payload?: any) => dispatch(edit_inquiry(key, value, payload));
  const { patientsList = [], create, GENDER_OBJ } = useSelector((state: ApplicationState) => {
    const { patientsList = [] } = state.patients;
    const { create = {} } = state.inquiry;
    const { GENDER_OBJ = {} } = state.dictionary;

    return {
      patientsList,
      create,
      GENDER_OBJ,
    };
  }, shallowEqual);
  const { patientName = '', patientId, cardHasPatient = false, inquiryType = 'I', inquiryFlowOption = {}, policyNo = '', inquiryExtraInfo = {} } = create;
  // const { patientName, cardHasPatient=false, patientId, policyNo = '', inquiryType, inquiryFlowOption, receptionDepartmentCode = '', isRevisit, inquiryExtraInfo = {} } = create;
  const fetchPatientsList = (onSuccess?) => dispatch(fetch_patients_list({
    inquiryType,
    sensitiveKeyEncry: rasSecretKeyEncrypt(),
    option: {
      needHealthInfo: true,
      needSensitiveEncrypt: !!validate.isBestpay(),
    },
  }, onSuccess));

  useEffect(() => {
    if (patientsList.length && !currentItem.prevIndex) {
      let patient: any = null;
      if (patientId) {
        patient = patientsList.find((i) => i.id == patientId) || {};
      } else {
        patient = patientsList[0];
      }
      selectPatientHandler(patient);
      onChangePopupHeight && onChangePopupHeight();
    }
  }, [patientId, patientsList]);
  useLayoutEffect(() => {
    fetchPatientsList((res) => {
      setNeedLoading(false);
    });
  }, []);
  const selectPatientHandler = useCallback((patient) => {
    const { patientName = '', id = '', age = '', patientGender = '', patientHealth = {} } = patient || {};
    isCheckHasInquiry && checkHasInquiry(id);
    let newInquiryExtraInfo = patientInquiryExtraInfo(patientHealth);
    // 当redux中的inquiryExtraInfo并且患者没有改变时候，健康档案的数据和redux的数据合并
    // console.log('patient', patientId, patient, newInquiryExtraInfo);
    if (patientId == id && inquiryExtraInfo.liverSick) {
      // console.log('newInquiryExtraInfo', newInquiryExtraInfo, inquiryExtraInfo);
      newInquiryExtraInfo = {
        ...newInquiryExtraInfo,
        ...inquiryExtraInfo,
      };
    }
    editInquiry('patientId', parseInt(id, 10), {
      patientName,
      patientAge: age,
      patientGender,
      inquiryExtraInfo: JSON.parse(JSON.stringify(newInquiryExtraInfo)),
    });
  }, [inquiryExtraInfo, patientId]);

  // 检查患者是否存在问诊单
  const checkHasInquiry = useCallback((patientId) => {
    fetchJson({
      type: 'POST',
      url: '/api/api/v1/patient/inquiry/list',
      data: {
        ...inquiryFlowOption,
        inquiryTimingType: '',
        excludeInquiryTimingTypeList: ['drug_replenish', 'mentalConsult'],
        patientId,
        policyNo,
      },
    }).then((res) => {
      const { code = '', result = [] } = res;
      if (code === '0') {
        editInquiry('hasInquiry', result.length ? true : false, {
          onGoingInquiry: result,
        });
      }
    });
  }, [policyNo]);
  const handleSumbit = useCallback(() => {
    pushEvent({eventTag: 'patient_choose'});
    const { patientName = '', patientGender = '', patientAge = '', hasInquiry } = create;
    if (patientName === '') {
      StaticToast.warning('您还未选择需要问诊的患者');
      return;
    } else if (patientId === '') {
      StaticToast.warning('您选择的问诊患者信息有误，请重新选择');
      return;
    } else if (hasInquiry) {
      Modal.confirm({
        // mountContainer: modalRef.current,
        className: 'modal_ref_wrap',
        content: (
          <p className='danger_inquiry_alert_content center'>
            该问诊人有未完成的问诊单，请结束后再发起新的问诊
          </p>
        ),
        okText: '查看问诊单',
        cancelText: '取消',
        onOk: () => {
          const { onGoingInquiry = [], onGoingInquiry: [firstInquiry = {}] = [] } = create;
          const { inquiryStatus } = firstInquiry;
          if (inquiryStatus === 1 || onGoingInquiry.length > 1) {
            window.reactHistory.push({
              pathname: '/hospital/myinquiry',
            });
          } else {
            jumpBeforeFetchThirdInquiryAbility({
              data: firstInquiry,
              position: 'checkInquiry',
              needInquiryDeatil: false,
              notAbilityCallback: () => {
                Modal.alert({
                  title: (
                    <p className='image'>
                      <SvgIcon className='bar-icon' src={require('src/svgs/sprite-icon_pay_fail.svg')} />
                    </p>
                  ),
                  content: (
                    <div className='pop_con'>
                      <p className='pop-h1'>接通失败</p>当前无空闲医生，请稍后再试
                    </div>
                  ),
                  cancelText: '我知道了',
                });
              },
            });
          }
        },
      });
      return;
    }
    const patientGenderStr = GENDER_OBJ[patientGender];
    const content = `${patientName} ${patientGenderStr} ${patientAge}岁`;
    onConfirm && onConfirm(content, create);
  }, [patientId, create]);
  if (patientsList.length === 0 && isFromCAINIAO()) {
    return (
      <div className={`${prefixCls}__patient`}>
        <div className='no_patient'>
          <div className={'line-first'}>
            <span>未查到可用权益，无法继续使用问诊</span>
          </div>
          <div className={'line-second'}>(权益失效用户也无法继续使用)</div>
        </div>
      </div>
    );
  }
  return (
    <div className={`${prefixCls}__patient`}>
      <div className={`${prefixCls}__title`}>
        <h3>选择患者</h3>
      </div>
      <PatientListTags needLoading={needLoading} patientList={patientsList} disabledSelected={cardHasPatient} selectedPatient={{ patientId, patientName }} selectPatientHandler={selectPatientHandler}{...props} />
      <p className='confirm-btn'><Button onClick={handleSumbit} theme='primary' shape='round' size='md' block>确定</Button></p>
    </div>

  );
};


export default AiSelectPatientComponents;
