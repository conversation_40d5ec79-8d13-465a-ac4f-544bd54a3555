/*
 * @Author: <EMAIL> <EMAIL>
 * @Date: 2022-09-28 18:31:52
 * @LastEditTime: 2023-03-21 10:42:20
 * @LastEditors: <EMAIL> <EMAIL>
 * @FilePath: /za-asclepius-patient-h5/src/pages/inquiryForm/aiGuide/components/submit.tsx
 * @Description:
 */
import React, { useCallback } from 'react';
import { shallowEqual, useSelector } from 'react-redux';
import { ApplicationState } from 'src/store';
import { pushEvent } from 'src/utils';
import { Button } from 'zarm';

const AiPatientSubmitComponents = (props) => {
  const { isSelectDepartmentOpen } = useSelector((state: ApplicationState) => {
    const { create: { isSelectDepartmentOpen } = {} } = state.inquiry;
    return {
      isSelectDepartmentOpen,
    };
  }, shallowEqual);

  const {
    prefixCls = '',
    onConfirm = null,
    location: { pathname = '' },
  } = props;

  const handleSumbit = useCallback(() => {
    pushEvent({eventTag: 'submit'});
    const isVideo = /\/hospital\/videointro/ig.test(pathname);
    onConfirm && onConfirm();
    if(isSelectDepartmentOpen) {
      return props.history.push({
        pathname: '/hospital/inquiry/office',
      });
    }

    props.history.push({
      pathname: '/hospital/inquiry/submit',
    });
  }, []);
  return (
    <div className={`${prefixCls}__extraInfo`}>
      <Button theme='primary' shape='round' size='md' block onClick={handleSumbit}>提交</Button>
    </div>
  );
};


export default AiPatientSubmitComponents;
