import { ALLERGY_DRUG, ALLFAMILY_ILLNESS } from 'src/pages/patients/editPatient/patientHealth';
import format from 'src/utils/format';
import { CDN_PREFIX } from 'src/utils/staticData';

const doctorAvatar = `${CDN_PREFIX}applets/common/doctor_default_avater.png`;

interface attachmentItem {
  [key: string]: string | number;
}
interface customExtsType {
  content: string;
  attachmentList?: attachmentItem[];
  // [key: string]: string ;
}

export interface itemType {
  status?: string;
  time?: number;
  from?: string;
  avatar?: string;
  contentsType?: string;
  customExts?: customExtsType;
  componentType?: string;
  index?: number;
  issuseKey?: string;
  extraKey?: string;
  uid?: string;
  msgUid?: string;
  extraTags?: string[];
  imageGather?: boolean;
  prevIndex?: number;
  title?: string;
  template?: string
  symptomList?: string[]
}


const doctorMsgBase = {
  status: 'success',
  time: Date.now(),
  from: 'doctor',
  avatar: doctorAvatar,
  contentsType: 'TEXT',
};
export const patientMsgBase = {
  status: 'success',
  time: Date.now(),
  from: '',
  contentsType: 'TEXT',
  customExts: { content: '' },
};
export const aiDoctorIssuseList: itemType[] = [{
  ...doctorMsgBase,
  customExts: { content: '请问您想为哪位用户咨询？' },
  componentType: 'patient',
  uid: format.guid(),
},
{
  ...doctorMsgBase,
  customExts: { content: '请详细描述您的问题，如症状、疾病、患病时长、服用药品等。（如感冒发烧3天了等请特殊说明，至少6个字）' },
  componentType: 'description',
  uid: format.guid(),
},
{
  ...doctorMsgBase,
  customExts: { content: '您是否有以下症状？' },
  title: '您是否有以下症状？(可多选)',
  componentType: 'extraInfo',
  uid: format.guid(),
  issuseKey: 'symptom',
},
{
  ...doctorMsgBase,
  customExts: { content: '您是否为复诊？' },
  componentType: 'extraInfo',
  uid: format.guid(),
  issuseKey: 'isRevisit',
  template: 'revisit',
},
{
  ...doctorMsgBase,
  customExts: { content: '有无甲状腺相关疾病史？' },
  title: '有无甲状腺相关疾病史？(可多选)',
  componentType: 'extraInfo',
  uid: format.guid(),
  issuseKey: 'thyroidHistory',
  template: 'symptom',
  symptomList: ['慢性甲状腺炎（桥本氏炎）', '甲状腺结节','甲状腺肿大', '甲状腺癌', '甲亢', '其他', '以上均无'],
},
{
  ...doctorMsgBase,
  customExts: { content: '有无甲状腺疾病家族史？' },
  title: '有无甲状腺疾病家族史？(可多选)',
  componentType: 'extraInfo',
  uid: format.guid(),
  issuseKey: 'thyroidFamilyHistory',
  template: 'symptom',
  symptomList: ['慢性甲状腺炎（桥本氏炎）', '甲状腺结节','甲状腺肿大', '甲状腺癌', '甲亢', '其他', '以上均无'],
},
{
  ...doctorMsgBase,
  customExts: { content: '近期有无甲亢相关的症状？' },
  componentType: 'extraInfo',
  uid: format.guid(),
  issuseKey: 'recentHyperthyroidism',
  template: 'symptom',
  symptomList: ['食欲增加', '体重下降', '怕热', '多汗', '突眼', '心动过速', '情绪激动易烦躁', '以上均无'],
},
{
  ...doctorMsgBase,
  customExts: { content: '近期有无甲减相关的症状？' },
  componentType: 'extraInfo',
  uid: format.guid(),
  issuseKey: 'recentHypothyroid',
  template: 'symptom',
  symptomList: ['心率减慢', '反应迟钝', '厌食', '乏力', '怕冷', '疲劳', '皮肤干燥', '以上均无'],
},
{
  ...doctorMsgBase,
  customExts: { content: '近期有无颈部肿大、局部肿块、疼痛的症状？' },
  componentType: 'extraInfo',
  uid: format.guid(),
  issuseKey: 'recentSwelling',
},
{
  ...doctorMsgBase,
  customExts: { content: '近期有无上呼吸道感染病史？' },
  componentType: 'extraInfo',
  uid: format.guid(),
  issuseKey: 'respiratoryInfection',
},
{
  ...doctorMsgBase,
  customExts: { content: '如果您有近三个月的甲状腺相关检查报告，如B超、甲功、活检、核素扫描等，可以一起上传，以便于医生综合判断并给您出具诊疗方案。' },
  componentType: 'annex',
  uid: format.guid(),
},
];


export const initState = {
  list: [{
    ...doctorMsgBase,
    customExts: { content: '尊敬的用户您好，感谢您使用众安甲无忧健康管理计划，请根据提示回答下列问题，以便医生提前了解您的病情，方便诊断。'},
    time: Date.now(),
  },{
    ...doctorMsgBase,
    customExts: { content: '专家临床问诊中，线上接诊需等待。您可提交信息后，扫码添加专属健康管家，为您协调专家时间，获取就医指导等福利权益。'},
    time: Date.now(),
  }],
  groupPhotoList: [],
  isMore: false,
  nextReqMessageID: '',
  lastMessageTime: Date.now(),
};

const reducer = (state, action) => {
  const { type, data: { list = [], pendingAnswer = {}, actionIndex = 0, cullNumber = 0, reviseAnswerInsert = {}, ...other } = {} } = action;

  switch (type) {
  case 'push':
    return {
      ...state,
      list: [...state.list, ...list],
      ...other,
    };
  case 'insert':
  case 'reviseAnswer':
    // eslint-disable-next-line no-case-declarations
    const nextList = state.list;
    nextList.splice(actionIndex, cullNumber, reviseAnswerInsert);
    return {
      ...state,
      list: nextList,
      ...other,
    };
  case 'annex':
    return {
      ...state,
      ...other,
      groupPhotoList: list,
    };
  case 'changeUser': { // 变更用户
    const { msgUid = '' } = reviseAnswerInsert;
    const nextList = state.list;
    const maxNum = nextList.findIndex((k) => (k.msgUid && msgUid == k.msgUid));
    return {
      ...state,
      list: nextList.slice(0, maxNum),
      groupPhotoList: [],
      ...other,
    };
  }
  default:
    return state;
  }
};

export default reducer;
