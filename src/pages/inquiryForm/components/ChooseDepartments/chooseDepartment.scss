@import "src/style/index";
$prefixCls: 'chooseDepartment_page';

.#{$prefixCls} {
  min-height: 100%;
  border-top: solid 1px #e6e6e6;

  .za-tabs__tab {
    width: r(130);
    color: #333;
    font-size: r(14);
    border-bottom: solid 1px #e6e6e6;
    padding: 0;
    line-height: r(40);
    font-family: 'PingFangSC-Regular, PingFang SC';
  }

  .za-tabs__tab.active {
    color: #00bc70;
    font-weight: bold;
    font-size: r(15);
    background-color: #fff;
  }

  .za-tabs__line {
    width: 0;
  }

  .za-tabs__body {
    height: 100vh;
    background-color: #fff;
    overflow: auto;
  }

  .content {
    padding-left: r(15);
    font-size: r(14);
    font-family: 'PingFangSC-Regular, PingFang SC';
    font-weight: 400;
    color: #333;
    line-height: r(40);
  }
  //医生列表css
  &__excellent {
    .doctor-hd {
      margin: 0 auto;
      border-bottom: 1px solid #e6e6e6;
      padding: r(10) 0;
      font-size: r(14);
      color: #333;
      line-height: r(20);
      text-align: center;
      background: #fff;
      font-weight: 400;

      span {
        display: inline-block;
        padding: 0 r(20);

        img {
          width: r(14);
          height: r(14);
        }
      }
    }

    .doctor-list {
      margin: 0 r(15);

      .doctor-icon {
        width: r(52);
        height: r(52);
        border-radius: 3px;
      }

      .doctor-item {
        @include display-flex;

        padding: r(20) 0 r(18);

        &-info {
          @include flex;

          position: relative;
          margin-left: r(10);
          // overflow: hidden;

          .name {
            font-weight: 600;
            color: #333;
            line-height: r(22.5);
            font-size: r(16);
          }

          .attribute-tag {
            padding: 0 r(4);
            border-radius: 3px;
            border: 1px solid #ec9131;
            margin-left: 9px;
            color: #ec9131;
            font-size: 12px;
          }

          .single-line {
            line-height: r(20);
            font-size: 13px;
            color: #333;
            margin-top: r(5);
            word-break: break-word;

            &.years {
              color: #666;
            }

            &.good-at {
              color: #999;
              font-size: 12px;
            }
          }

          .original-work {
            color: #999;
            padding-left: r(8);
          }

          .single-price {
            position: absolute;
            right: 0;
            top: 0;
            color: #333;

            .special {
              font-weight: 600;
              color: $specialist-pages-primary;
              font-size: r(16);
            }
          }

          .light {
            color: var(--text-base-color);
          }
        }

        &:not(:last-child) {
          .doctor-item-info {
            &::after {
              position: absolute;
              content: '';
              right: r(-15);
              bottom: r(-15);
              left: 0;
              border-bottom: 1px solid #e6e6e6;
            }
          }
        }
      }
    }

    .empty-component {
      padding: r(50) 0;

      .text {
        font-size: r(16);
        font-weight: 600;
        color: #1e1e1e;
      }
    }
  }
  // .not-more {
  //   text-align: center;
  //   bottom: r(-30);
  //   color: #b2b2b2;
  // }
}
