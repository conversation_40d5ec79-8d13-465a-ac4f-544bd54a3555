/*
 * @description： 医生选择页 组件
 */
import React from 'react';
// import { fetchJson } from 'src/utils/fetch';
import { useSelector } from 'react-redux';
import { Empty } from 'src/components/common';
import { ApplicationState } from 'src/store';
import format from 'src/utils/format';
// import { Deserialize } from 'src/utils/serialization';

import './chooseDepartment.scss';
// type DoctorItemOptions = {
//   id: string | number;
//   firstWorkOrgName: string;
//   headPortrait: string;
//   staffIntroduction: string;
//   staffName: string;
//   staffProfessionalTitle: string;
//   workDepartment: string;
//   medicalStaffServiceList: Array<any>;
//   [key: string]: string | number | object | boolean;
// };
const prefixCls = 'chooseDepartment_page';
export const unitOfPeople = (num) => {
  const result = Number(num);
  if (result <= 10000) {
    return result + '人';
  }
  return Number((result / 10000).toFixed(2)) + '万人';
};
const ChooseDoctorList = (props) => {
  const { className = '', emptyMsg = '该诊室当前暂无医生接诊', doctorList } = props;

  // const workDepartments = Deserialize(search);
  // const departName = Deserialize(state).itemName;
  // let departmentCode: string | number = workDepartments.departmentCode;
  // let WorkDepartmentsArr: Array<any> = [];
  // WorkDepartmentsArr = workDepartments.departmentCode.split(',');
  // if (departmentCode === '04') { //选择外科的时候加上泌尿外科的code
  //   WorkDepartmentsArr.push('04.04');
  // }

  const { DEPARTMENT_OBJ, PROFESSIONALTITLE_OBJ }: any = useSelector((state: ApplicationState) => {
    const { PROFESSIONALTITLE_OBJ = {} } = state.dictionary || {};
    return {
      DEPARTMENT_OBJ: state.department.obj,
      PROFESSIONALTITLE_OBJ,
    };
  });


  const goGoTextInquiry = (item) => {
    const { staffNo = '' } = item;
    window.reactHistory.push({
      pathname: '/hospital/doctordetail',
      search: `staffNo=${staffNo}`,
    });
  };

  return (
    <div className={`${prefixCls} ${className}`}>
      <div className={`${prefixCls}__excellent`}>

        {doctorList.length ? (
          <ul className='doctor-list'>
            {doctorList.map((k) => {
              const { medicalStaffServiceList = [] } = k;
              const item = medicalStaffServiceList.find((item) => item.productId == 1) || {};
              return (
                <li className='doctor-item' key={+k.id} onClick={() => goGoTextInquiry(k)}>
                  <p>
                    <img className='doctor-icon' src={`${k.headPortrait}`} alt='' />
                  </p>
                  <div className='doctor-item-info'>
                    <p>
                      <strong className='name'>{k.staffName}</strong>
                    </p>
                    <p className='single-line'>
                      {PROFESSIONALTITLE_OBJ[k.staffProfessionalTitle]} {DEPARTMENT_OBJ[k.workDepartment]}
                      {k.originalWorkOrgName && <span className='original-work'>原{k.originalWorkOrgName}</span>}
                    </p>
                    <p className='single-line years'>
                      从业<span className='light'>{k.workSeniority}余年</span>｜服务患者<span className='light'>{unitOfPeople(k.servedPatientNum)}</span>
                    </p>
                    <p className='single-line good-at'>擅长：{k.staffSkills}</p>
                    <p className='single-price'>
                      <span className='special'>{format.digits(item.specialPrice)}</span>/次
                    </p>
                  </div>
                </li>
              );
            })}
          </ul>
        ) : (
          <Empty.doctorEmpty propsConfig={{ text: emptyMsg }} />
        )}
      </div>
      {/* {!!doctorList.length && <p className="not-more">没有更多</p>}åß */}
    </div>
  );
};

export default ChooseDoctorList;
