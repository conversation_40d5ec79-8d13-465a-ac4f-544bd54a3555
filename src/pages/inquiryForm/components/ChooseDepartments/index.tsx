import classnames from 'classnames';
import React, { Fragment,useState,useEffect } from 'react';
import { fetchJson } from 'src/utils/fetch';
import { Tabs } from 'zarm';
import ChooseDoctorList from './doctorList';
import './chooseDepartment.scss';

interface DoctorItemOptions {
  id: string | number;
  firstWorkOrgName: string;
  headPortrait: string;
  staffIntroduction: string;
  staffName: string;
  staffProfessionalTitle: string;
  workDepartment: string;
  medicalStaffServiceList: any[];
  [key: string]: string | number | object | boolean;
}
const prefixCls = 'chooseDepartment_page';
const { Panel } = Tabs;

const ChooseDepartment = (props) => {
  const [doctorList, setDoctorList] = useState<DoctorItemOptions[]>([]);
  const [value, setValue]: any = useState(0);
  const [departmentList,setDepartmentList]: any = useState([]);
  useEffect(() => {
    getDepartmentList();
  }, []);
  useEffect(() => {
    getDoctorList([]);
  }, []);
  const getDepartmentList = () => {
    fetchJson({
      url: '/api/api/v1/patient/hostpital/departmentItemList',
      type: 'POST',
      needLoading: true,
      data: {
        hospitalCode: 'WEBH003',
        excludeDepartmentCodeList:['04.04', '05.01','03.07'],  // 去掉04.04泌尿外科
        option: {
          isQueryHospitalDepartment: true,
          isQueryHospitalDepartmentItem: true,
        },
      },
    }).then((res) => {
      const { code = '', result = [] } = res;
      if (code === '0') {
        // 每个科室加个不限的选项
        result.map((item) => {
          const obj = {
            hospitalId: '',
            departmentId: '',
            itemCode: '',
            itemName: '不限',
          };
          const departmentItemList = item.departmentItemList || [];
          const newDepartmentItemList = departmentItemList.splice(0, 0, obj);
          return newDepartmentItemList;
        });
        // 一级科室加不限
        const deparObj = {
          departmentCode: '',
          departmentItemList:[],
          departmentName: '不限',
          hospitalId: 6000000,
          id: 20000003,
        };
        const newReult = result.splice(0, 0, deparObj);
        console.log('newReult',newReult);
        setDepartmentList(result);
      }
    });
  };
  const getDoctorList = (workDepartments) => {
    const doctorOption = { staffTypes: ['doctor'], staffAttributeList: [1, 2], isInquiry: 'Y', productId: 1 }; // staffAttributeList医生属性：1-内部医生 2-内部合作医生 3-外部医生
    fetchJson({
      url: '/api/api/v1/patient/doctor/list',
      type: 'POST',
      needLoading: true,
      data: {
        ...doctorOption,
        workDepartments,
        specificFlag: 'N',
        option: {
          isQueryMedicalStaffService: true,
          isQueryHeadPortrait: true,
        },
      },
    }).then((res) => {
      const { code = '', result = [] } = res;
      if (code === '0') {
        setDoctorList(result);
      }
    });
  };

  const tabChange = (value) => {
    setValue(value);
    const workDepartments = departmentList[value];
    const departmentCode: string | number = workDepartments.departmentCode;
    let WorkDepartmentsArr: any[] = [];
    WorkDepartmentsArr =  workDepartments.departmentCode==''? []:workDepartments.departmentCode.split(',');
    if (departmentCode === '04') { // 选择外科的时候加上泌尿外科的code
      WorkDepartmentsArr.push('04.04');
    }
    getDoctorList(WorkDepartmentsArr);
  };

  return (
    <Fragment>
      <div className={prefixCls}>
        {
          !!departmentList.length && (
            <Tabs
              scrollable
              value={value}
              onChange={(value) => {
                tabChange(value);
              }}
            >
              {
                departmentList.map((item, index) => <Panel title={item.departmentName} className={classnames('', { active: value == index })} key={item.id}>
                  <ChooseDoctorList departmentList={departmentList} doctorList={doctorList} />
                </Panel>)
              }
            </Tabs>
          )
        }
      </div>
    </Fragment>
  );
};

export default ChooseDepartment;
