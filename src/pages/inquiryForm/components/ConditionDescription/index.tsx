import React, { useCallback, useState } from 'react';
import { shallowEqual, useDispatch, useSelector } from 'react-redux';
import { CompatibleFileInput, StaticToast, SvgIcon } from 'src/components/common';
import { ApplicationState } from 'src/store';
import { edit_inquiry, edit_inquiry_addFile } from 'src/store/inquiry/action';
import { Input, Icon, Modal, Carousel } from 'zarm';
import './conditionDescription.scss';

const maxAttachmentTotal = 9;
const prefixCls = 'comp_condition_description';

const ConditionDescription = (props) => {

  const [showImageCarousel, setShowImageCarousel] = useState(false);
  const [imageCarouselActive, setImageCarouselActive] = useState(0);

  const { create = {} } = useSelector((state: ApplicationState) => state.inquiry, shallowEqual);
  const { illnessDescription, attachmentList = [], inquiryNo } = create;
  const dispatch = useDispatch();
  const editInquiry = (key: string, value: any, payload?: any) => dispatch(edit_inquiry(key, value, payload));

  // 上传附件前的验证
  const onUploadBeforeValidate = useCallback((e) => {
    if (!inquiryNo) {
      StaticToast.warning('没有生成问诊单编号，图片无法上传，请重新进入诊单填写页');
      return false;
    }
    return true;
  }, [inquiryNo]);

  // 上传附件
  const uploadFileClick = useCallback((data) => {
    // editInquiry('attachmentList', [...attachmentList, data]);
    dispatch(edit_inquiry_addFile(data));
  }, [inquiryNo, attachmentList]);

  // 删除附件
  const deleteFileClick = useCallback((id: number) => {
    editInquiry('attachmentList', [
      ...attachmentList.filter((i) => i.id !== id),
    ]);
  }, [attachmentList]);

  // 点击图片放大展示
  const photoClick = useCallback((item) => {
    const index = attachmentList.findIndex((k) => k.id == item.id && k.attachmentDownloadUrl === item.attachmentDownloadUrl);
    if (index >= 0) {
      setShowImageCarousel(true);
      setImageCarouselActive(index);
    }
  }, [attachmentList]);

  return (
    <div className={`${prefixCls}`}>
      <div className={`${prefixCls}__title`}>
        <p>咨询问题</p>
        <p className='tip'>必填</p>
      </div>
      <div className='inquiry_mention'>
        <img className='wrong_round' src={require('src/images/icon_close_red.png')} />
        <p>问诊暂不支持进行心理咨询/倾诉</p>
      </div>
      <Input
        className={`${prefixCls}__input`}
        type='text'
        rows={3}
        autoHeight
        showLength
        maxLength={100}
        placeholder='为了更好地获得医生帮助，请尽可能详细描述身体现状，如症状、疾病、患病时长、复用药品等'
        onChange={(value) => editInquiry('illnessDescription', (value || '').trim())}
        value={illnessDescription || ''}
        onBlur={() => blur()}
      />
      <div className={`${prefixCls}__title`}>
        <p>上传图片</p>
        <p className='tip'>选填</p>
      </div>
      <p className={`${prefixCls}__tips`}>检查单/病历/患处图片/处方单图片（最多9张）</p>
      <div className={`${prefixCls}__photo`}>
        {attachmentList.length
          ? attachmentList.map((item: any, index: number) => (
            <div className='upload-item' key={`upload-photo-${index}`}>
              <img className='upload-photo' src={`${item.attachmentDownloadUrl}`} alt='' onClick={() => photoClick(item)} />
              <Icon className='delete-icon' theme='danger' type='minus-round-fill' onClick={() => deleteFileClick(item.id)} />
            </div>
          ))
          : null}
        {attachmentList.length < maxAttachmentTotal && (
          <div className='upload-item'>
            <div className='upload-button'>
              <CompatibleFileInput
                id='upload_file'
                className='upload-input'
                maxAttachmentTotal={maxAttachmentTotal}
                hasAttachmentTotal={attachmentList.length}
                businessNo={inquiryNo}
                attachmentType='inquiryVoucher'
                onUploadBeforeValidate={onUploadBeforeValidate}
                onUploadSuccess={uploadFileClick}
              />
              <label htmlFor='upload_file'>
                <SvgIcon className='upload-cover' type='img' src={require('../../images/add_photo.png')} />
                <p className='upload-text'>{attachmentList.length ? `${attachmentList.length}/${maxAttachmentTotal}` : '添加图片'}</p>
              </label>
            </div>
          </div>
        )}
      </div>

      <Modal
        className='previewimage-component__imagemodal condition_description_modal'
        visible={showImageCarousel}
        maskClosable
        onCancel={() => {
          setShowImageCarousel(false);
        }}
      >
        <div
          onClick={() => {
            setShowImageCarousel(false);
          }}
        >
          <Carousel className='image_modal_carousel' activeIndex={imageCarouselActive} showPagination={false} onChange={(activeIndex) => {
            setImageCarouselActive(activeIndex!);
          }}>
            {attachmentList.map((photo, index) => (
              <div className='carousel__item__pic' key={+index}>
                <img className='carousel__item__img' src={`${photo.attachmentDownloadUrl}`} alt='' draggable={false} />
              </div>
            ))}
          </Carousel>
          <p className='condition_description_img_num'>{imageCarouselActive + 1} / {attachmentList.length}</p>
        </div>
      </Modal>
    </div>
  );
};

export default ConditionDescription;
