@import 'src/style/index';
$prefixCls: 'comp_condition_patients';

.#{$prefixCls} {
  margin: r(15);
  background: #fff;
  padding: r(15);
  border-radius: r(8);
  font-size: r(15);
  color: #333;

  &__title {
    font-weight: bold;
    font-size: r(16);
    @include display-flex;
    @include align-items(center);
    @include justify-content(space-between);

    .tip {
      font-weight: normal;
      font-size: r(13);
      color: #b2b2b2;
    }
  }

  &__patients {
    @include display-flex;

    margin-top: r(14);
    flex-wrap: wrap;

    .patient {
      padding: r(5) r(15);
      border-radius: r(15);
      background: rgba(0, 0, 0, 0.05);
      border: 1px solid rgba(0, 0, 0, 0.1);
      margin: 0 r(5) r(10);
      transition: color, background-color, border-color 0.1s linear;
      font-size: r(14);

      .icon_add {
        margin-right: r(-1);
      }

      &.selected,
      &:active {
        background: rgba(236, 145, 49, 0.06);
        border-color: rgba(236, 145, 49, 0.32);
        color: #ec9131;
      }
    }
  }

  &__cell {
    padding: r(10) 0 r(3);
    @include display-flex;
    @include justify-content(space-between);

    .optional {
      font-size: r(14);
      color: #b2b2b2;
    }

    .za-select {
      max-width: r(120);
      font-size: r(14);

      .za-select__input {
        position: relative;
        padding: 0 r(16) 0 0;
        height: r(21);
        line-height: r(21);
        text-align: right;

        &::after {
          content: '';
          position: absolute;
          right: r(2);
          top: 50%;
          margin-top: r(-5.2);
          width: r(9);
          height: r(9);
          transform: rotate(-45deg);
          border-right: 2px solid #d6d6d6;
          border-bottom: 2px solid #d6d6d6;
        }
      }
    }

    .za-radio {
      color: #666;
    }

    .za-radio--checked {
      color: var(--text-base-color);
      font-weight: 600;
    }
  }
}
