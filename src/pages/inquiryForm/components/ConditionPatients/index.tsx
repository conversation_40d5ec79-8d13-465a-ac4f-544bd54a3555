import classnames from 'classnames';
import React, { useCallback, useEffect, useLayoutEffect } from 'react';
import { shallowEqual, useDispatch, useSelector } from 'react-redux';
import { Link } from 'react-router-dom';
import { InquiryOtherInfo } from 'src/components/common';
import { ApplicationState } from 'src/store';
import { edit_inquiry } from 'src/store/inquiry/action';
import { fetch_patients_list } from 'src/store/patients/action';
import { fetchJson } from 'src/utils';
import { TransformEnumArray } from 'src/utils/serialization';
import { rasSecretKeyEncrypt } from 'src/utils/tool';
import validate from 'src/utils/validate';

// import format from 'src/utils/format';
import { Icon, Radio, Select } from 'zarm';
import { patientInquiryExtraInfo } from '../../utils';

// import HasInquiry from '../HasInquiry';
import './conditionPatients.scss'

const prefixCls = 'comp_condition_patients'

const ConditionPatients = (props) => {

  const { YESORNO = [], patientsList = [], create, departmentList } = useSelector((state: ApplicationState) => {
    const { patientsList = [] } = state.patients;
    const { create = {} } = state.inquiry;
    const { YESORNO = [] } = state.dictionary;
    const departmentList: any = state.department.list || [];  //部门列表

    return {
      create,
      patientsList,
      YESORNO: YESORNO || [],
      departmentList: TransformEnumArray(departmentList, 'departmentCode', 'departmentName'),    //部门列表
    };
  }, shallowEqual);
  const { patientName, cardHasPatient, patientId, policyNo = '', inquiryType, inquiryFlowOption, receptionDepartmentCode = '', isRevisit, inquiryExtraInfo = {} } = create;
  const dispatch = useDispatch();
  const editInquiry = (key: string, value: any, payload?: any) => dispatch(edit_inquiry(key, value, payload));
  const fetchPatientsList = (onSuccess) => dispatch(fetch_patients_list({
    inquiryType: inquiryType,
    sensitiveKeyEncry: rasSecretKeyEncrypt(),
    option: {
      needHealthInfo: true,
      needSensitiveEncrypt: !!validate.isBestpay(),
    },
  }, onSuccess));
  useLayoutEffect(() => {
    //用于处理 提交问诊后，再返回的问题，直接返回至第一个填写页
    if (!create.inquiryNo) {
      // storage.set('isWindowHistoryGo',1,0.5);
      window.history.go(-2);
    };
  }, []);
  useEffect(() => {
    // if (patientsList.length) {
    //   return;
    // }
    fetchPatientsList((patientsList) => {
      if (patientsList.length) {
        let patient: any = null;
        if (patientId) {
          patient = patientsList.filter((i) => i.id == patientId)[0];
        } else {
          patient = patientsList[0];
        }
        selectPatient(patient);
      }
    });
  }, []);

  // useEffect(() => {
  //   if (patientsList.length) {
  //     let patient: any = null;
  //     if (patientId) {
  //       patient = patientsList.filter((i) => i.id == patientId)[0];
  //     } else {
  //       patient = patientsList[0];
  //     }
  //     selectPatient(patient);
  //   }
  // }, [patientsList])

  //选择患者
  const selectPatient = useCallback((patient) => {
    const { patientName = '', id = '', age = '', patientGender = '', patientHealth = {} } = patient || {};
    checkHasInquiry(id);
    let newInquiryExtraInfo = patientInquiryExtraInfo(patientHealth);
    //当redux中的inquiryExtraInfo并且患者没有改变时候，健康档案的数据和redux的数据合并
    console.log('patient', patientId, patient, newInquiryExtraInfo);
    if (patientId == id && inquiryExtraInfo.liverSick) {
      console.log('newInquiryExtraInfo', newInquiryExtraInfo, inquiryExtraInfo);
      newInquiryExtraInfo = {
        ...newInquiryExtraInfo,
        ...inquiryExtraInfo
      }
    }
    editInquiry('patientId', parseInt(id, 10), {
      patientName,
      patientAge: age,
      patientGender,
      // orderGoods: [],
      // platformCode: '',
      inquiryExtraInfo: JSON.parse(JSON.stringify(newInquiryExtraInfo)),
    });
  }, [inquiryExtraInfo, patientId]);

  //检查患者是否存在问诊单
  const checkHasInquiry = useCallback((patientId) => {
    fetchJson({
      type: 'POST',
      url: '/api/api/v1/patient/inquiry/list',
      data: {
        ...inquiryFlowOption,
        inquiryTimingType: '',
        excludeInquiryTimingTypeList: ['drug_replenish', 'mentalConsult'],
        patientId,
        policyNo,

      },
      success: (res) => {
        const { code = '', result = [] } = res;
        if (code === '0') {
          editInquiry('hasInquiry', result.length ? true : false, {
            onGoingInquiry: result
          });
        }
      },
    });
  }, [policyNo]);

  //是否去医院就诊过
  const radioChange = useCallback((key, value) => {
    editInquiry(key, value);
    if (key === 'isRevisit' && value === 'N') {
      // StaticToast.warning('请完成线下初诊后再进行线上复诊或开药');
    }
  }, []);

  return (
    <div>
      {/* <HasInquiry hasInquiry={hasInquiry} /> */}
      <div className={`${prefixCls}`}>
        <div className={`${prefixCls}__title`}>
          <p>请选择问诊对象</p>
          <p className='tip'>必填</p>
        </div>
        <div className={`${prefixCls}__patients`}>
          {cardHasPatient ? (
            <div className="patient selected">{patientName}</div>
          ) : (
            <>
              {patientsList.map((patient, index) => (
                <div
                  className={classnames('patient', { selected: patient.id === patientId })}
                  key={+index}
                  onClick={() => { selectPatient(patient) }}
                >
                  {patient.patientName || ''}
                </div>
              ))}
              <Link
                to={{ pathname: '/hospital/editpatient/baseinfo' }}
              >
                <div className="patient">
                  <Icon className="icon_add" type="add" size="sm" />
                  增加
                </div>
              </Link>
            </>
          )}
        </div>
        <div className={classnames(`${prefixCls}__cell`)}>
          <p>是否复诊</p>
          <Radio.Group value={isRevisit} onChange={(value) => radioChange('isRevisit', value)}>
            {YESORNO.map((k) => (<Radio key={`${k.resCode}-isHospital`} value={k.resCode}>{k.resName}</Radio>))}
          </Radio.Group>
        </div>
        {
          isRevisit === 'Y' && <React.Fragment>
            <div className={classnames(`${prefixCls}__cell`)}>
              <p>请选择科室<span className='optional'>（选填）</span></p>
              <Select
                className='department_select'
                hasArrow={false}
                value={receptionDepartmentCode}
                dataSource={departmentList}
                onOk={(selected) => {
                  console.log(selected);
                  editInquiry('receptionDepartmentCode', selected[0]?.value);
                }}
              />
            </div>
          </React.Fragment>
        }
      </div>
      <InquiryOtherInfo inquiryType={inquiryType} inquiryExtraInfo={create.inquiryExtraInfo} patientGender={create.patientGender} editorHandler={editInquiry} />
    </div>
  )
}

export default ConditionPatients;
