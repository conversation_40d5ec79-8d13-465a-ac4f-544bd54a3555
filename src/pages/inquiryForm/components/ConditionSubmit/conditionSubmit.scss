@import "src/style/index";
$prefixCls: 'comp_condition_submit';

.#{$prefixCls} {
  &__wrapper {
    margin-top: r(10);

    &:first-child {
      margin-top: r(15);
    }
  }

  &__fixbottom {
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
    z-index: 1000;
    background: #fff;
    padding: r(10) r(15) r(20);
    border-top: 1px solid #f5f5f5;
    @include display-flex;
    @include align-items(center);
    @include justify-content(space-between);

    &-amount {
      @include display-flex;
      @include align-items(center);

      margin-right: r(10);
      font-size: r(15);
      line-height: 1.2;
      color: #666;

      .real_amount,
      .preferential {
        margin-left: r(6);
        font-size: r(21);
        font-weight: bold;
        color: #ff5050;
      }

      .preferential {
        font-size: r(13);
      }
    }

    .btn_submit {
      max-width: r(150);
      min-width: r(88);
      height: r(44);
      font-size: r(17);
      font-weight: bold;
      color: #fff;
      @include flex(1);
    }
  }
}
