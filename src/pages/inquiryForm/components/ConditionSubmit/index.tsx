import React, { useCallback, useEffect, useMemo, useState, useLayoutEffect } from 'react';
import { shallowEqual, useDispatch, useSelector } from 'react-redux';
import { DeliveryAddress, DeliveryMethod, RightsAndDiscount, StaticToast } from 'src/components/common';
import BuyService from 'src/components/common/buyService';
import { ApplicationState } from 'src/store';
import { fetch_address_list } from 'src/store/address/action';
import { CouponType } from 'src/store/coupon/type';
import { clear_inquiry, edit_inquiry, submit_inquiry } from 'src/store/inquiry/action';
import { fetchJson, validate, getIsBlackUser, pushEvent } from 'src/utils';
// import { isFromCAINIAO, isFromAliNOCAINIAO } from 'src/utils/staticData';
import format from 'src/utils/format';
import { isFromAliNOCAINIAO } from 'src/utils/staticData';
import storage from 'src/utils/storage';
import { Button, Modal } from 'zarm';
import ConfirmPop from '../../conforpop';
import { unifiedOrderProcessPay } from '../../utils';
import './conditionSubmit.scss';

const prefixCls = 'comp_condition_submit';

const ConditionSubmit = () => {
  // 如果问诊单流向UMP的话，就需要地址信息。但是目前只支持不支持云闪付和支付宝里面送药，所以这两个情况不需要展示地址组件
  // const isNeedAddress = !validate.isFromUnionPay() && !validate.isAlipayApplet(); //validate.isFromOwnMiniApplet();

  // 费用试算中的时候 不能提交问诊问诊单
  const [isCalculatePrice, setIsCalculatePrice] = useState(false);
  // 优惠券列表
  const [couponList, setCouponList] = useState<CouponType[]>([]);
  // 权益列表
  const [vasList, setVasList] = useState([]);
  // 用户选择的权益和优惠券
  const [checkedCoupon, setCheckedCoupon] = useState<any>({});
  const [checkedVas, setCheckedVas]: any = useState({});
  // 选择的地址
  const [address, setAddress]: [any, any] = useState({});
  // 配送方式
  const [deliveryWayList, setDeliveryWayList]: any = useState([]);
  // 风险告知书
  const [popVisible, setPopVisible] = useState(false);
  const { create = {}, addressList = [] } = useSelector((state: ApplicationState) => ({
    create: state.inquiry.create,
    addressList: state.address.list,
  }), shallowEqual);
  const {
    patientId = '',
    patientName = '',
    deliveryAddressId,
    deliveryWay,
    inquiryNo,
    inquiryType,
    isVideoBooking,
    orderRealAmount = 0,
    orderAmount = 0,
    hasInquiry,
    // policyNo = ''
  } = create;
  const dispatch = useDispatch();
  const fetchAddressList = () => dispatch(fetch_address_list());
  const editInquiry = (key: string, value: any, payload?: any) => dispatch(edit_inquiry(key, value, payload));
  const submitInquiry = (onSuccess: any) => dispatch(submit_inquiry(onSuccess, { isClear: true }));
  const clearInquiry = () => dispatch(clear_inquiry());

  useEffect(() => {
    if (!addressList.length) {
      fetchAddressList();
    }
  }, []);

  useEffect(() => {
    if (addressList.length) {
      const address = addressList.filter((i) => i.id == deliveryAddressId || (!deliveryAddressId && i.isDefault === 'Y'))[0];
      if (address) {
        editInquiry('deliveryAddressId', address.id);
        setAddress(address);
      }
    }
  }, [addressList]);


  const calculatePrice = useCallback((create, select?) => {
    setIsCalculatePrice(true);
    const {
      orderGoods = [], platformCode = '',
      useRightsAndDiscount, policyNo,
      userRightsId,
      fromSelfChannel,
      inquiryTimingType,
      cardNo,
      staffId,
      rightsId,
      isChangeInquiry,
    } = create;

    const data: any = {
      orderGoods: orderGoods || [],
      platformCode: platformCode || '',
      patientId,
      orderType: 'inquiry',
      inquiryType,
      orderPreferenceList: [],
      returnValidPreference: true,
      relatedBizNo: inquiryNo,
      inquiryTimingType,
      policyNo,
      cardNo,
      staffId,
      fromSelfChannel,
      rightsId,
      userRightsId,
      isChangeInquiry,
    };
    const { userPreferenceObjectId = '' } = useRightsAndDiscount || {};
    if (userPreferenceObjectId) {
      data.orderPreferenceList.push(useRightsAndDiscount);
    }
    if(userRightsId && !userPreferenceObjectId && !select) {
      data.orderPreferenceList.push({
        preferenceObjectType: '3',
        userPreferenceObjectId: userRightsId,
        isPolicyRights: false,
      });
    }
    fetchJson({
      url: '/api/api/v1/patient/preference/calculate',
      type: 'POST',
      data: {
        ...data,
      },
      needLoading: true,
      success: (res) => {
        const { code = '0', result: { couponList = [], rightsList = [], order: { orderGoods = [], platformCode = '', orderAmount = '', orderRealAmount = '', orderPreferenceList = [] } = {} } = {} } = res || {};
        if (code === '0') {
          // 问诊可以用的优惠券
          const coupon: CouponType[] = couponList.map((element) => ({
            ...element.coupon,
            couponEffectiveTime: (element.couponEffectiveTime || '').replace(/-/g, '/'),
            couponExpiryTime: (element.couponExpiryTime || '').replace(/-/g, '/'),
            id: element.id,
            userCouponStatus: element.userCouponStatus,
          }));
          setCouponList(coupon);
          setVasList(rightsList);
          const { userPreferenceObjectId, preferenceObjectType = '' } = orderPreferenceList[0] || {};
          if (preferenceObjectType == '1') {
            const checkedCoupon = coupon.find(({ id }) => id == userPreferenceObjectId) || {};
            setCheckedCoupon(checkedCoupon);
            setCheckedVas({});
          } else if (preferenceObjectType == '3-1') {
            const checkedVas = rightsList.find(({ id }) => id == userPreferenceObjectId) || {};
            setCheckedCoupon({});
            setCheckedVas(checkedVas);
          } else {
            setCheckedCoupon({});
            setCheckedVas({});
          }
          if (orderPreferenceList[0]) {
            const findItem = rightsList.find((item) => item.id === orderPreferenceList[0].userPreferenceObjectId);
            if (findItem) {
              orderPreferenceList[0].drawVasCode = findItem.drawVasCode;
            }
          }
          editInquiry('useRightsAndDiscount', orderPreferenceList[0] || {}, {
            orderGoods,
            orderPreferenceList,
            platformCode,
            orderRealAmount,
            orderAmount,
          });
          setIsCalculatePrice(false);
        }
      },
    });
  }, []);
  useLayoutEffect(() => {
    // 用于处理 提交问诊后，再返回的问题，直接返回至第一个填写页
    if (!create.inquiryNo) {
      // storage.set('isWindowHistoryGo',1,0.5);
      window.history.go(-2);
    }
  }, []);

  /** **************** 处理价格试算逻辑，视频预约单不走试算逻辑 ******************/
  useEffect(() => {
    if (!patientId || isVideoBooking) {
      return;
    }
    calculatePrice(create);
  }, []);

  /** ************************ 根据选择的地址和权益处理配送方式*************************************/
  useEffect(() => {
    if (!deliveryAddressId) {
      return;
    }
    const { id = '', isPolicyRights } = checkedVas || {};
    fetchJson({
      type: 'POST',
      url: '/api/api/v1/patient/user/deliveryAddress/deliveryWay',
      isloading: true,
      data: {
        inquiryType,
        deliveryAddressId,
        userRightsId: id || '',
        isPolicyRights,
      },
      success: (res) => {
        const { code = '0', result = [] } = res;
        if (code === '0') {
          setDeliveryWayList(result || []);
        }
      },
    });
  }, [deliveryAddressId, checkedVas.id]);

  /** ************************ 设置默认配送方式 ***************************/
  useEffect(() => {
    if (deliveryWayList.length) {
      if (!deliveryWayList.includes(deliveryWay)) {
        editInquiry('deliveryWay', deliveryWayList[0] || '');
      }
    }
  }, [deliveryWayList, deliveryWay]);

  // preferenceObjectType :优惠劵 =1，权益 = 3
  // 点击优惠券
  const couponClick = useCallback((item: CouponType) => {
    if (!item.id || item.id !== checkedCoupon.id) {
      const param = item.id ? {
        userPreferenceObjectId: item.id,
        preferenceObjectType: '1',
      } : {};
      editInquiry('useRightsAndDiscount', param);
      calculatePrice({ ...create, useRightsAndDiscount: param }, true);
    }
  }, [checkedCoupon, create]);

  const vasClick = useCallback((item) => {
    if (!item.id || item.id !== checkedVas.id) {
      const param = item.id ? {
        userPreferenceObjectId: item.id,
        preferenceObjectType: '3',
        isPolicyRights: item.isPolicyRights,
      } : {};
      editInquiry('useRightsAndDiscount', param);
      calculatePrice({ ...create, useRightsAndDiscount: param }, true);
    }
  }, [checkedVas, create]);

  const deliveryClick = useCallback((value) => {
    editInquiry('deliveryWay', value);
  }, []);

  // 去我的地址 修改或添加地址
  const toMyAddress = useCallback(() => {
    pushEvent({eventTag: 'address_choose'});
    window.reactHistory.push({
      pathname: '/hospital/myaddress',
      state: { textInquiryGoBack: true, backUrl: location.pathname, search: location.search, eventName: 'inquiry_address_success' },
    });
  }, []);

  // 校验当前页面的信息录入
  const validata = (data: any, callBack: Function) => {
    const {
      inquiryNo = '',
      patientName = '',
      patientId = '',
      illnessDescription = '',
      deliveryAddressId = '',
      inquiryExtraInfo = {},
      deliveryWay = '',
      isRevisit = '',
    } = data;
    const { liverSick = '', drugAllergic = {}, patientFamilyHistory = {}, thyroidHistory = {} } = inquiryExtraInfo;
    const { useRightsAndDiscount, policyNo } = create;
    // thyroidHistory.value 只有甲状腺有这个值
    // const checkExtroInfo = thyroidHistory.value ? false : !liverSick || !drugAllergic.value || (drugAllergic.value == '有' && !drugAllergic.tags.length && !drugAllergic.additional) || !patientFamilyHistory.value || (patientFamilyHistory.value == '有' && !patientFamilyHistory.tags.length && !patientFamilyHistory.additional);
    const checkExtroInfo = false;
    if (inquiryNo === '') {
      StaticToast.warning('当前问诊单已经提交支付');
      return false;
    } else if (patientName === '') {
      StaticToast.warning('您还未选择需要问诊的患者');
      return false;
    } else if (patientId === '') {
      StaticToast.warning('您选择的问诊患者信息有误，请重新选择');
      return false;
    } else if (illnessDescription.trim() === '') {
      StaticToast.warning('您还未填写病情描述');
      return false;
    } else if (!deliveryAddressId && !isFromAliNOCAINIAO()) {
      StaticToast.warning('请选择您的地址');
      return false;
    } else if (!deliveryWay && !isFromAliNOCAINIAO()) {
      StaticToast.warning('请选择配送方式');
      return false;
      // } else if (!isRevisit) {
      //   StaticToast.warning('您还未填写是否复诊');
      //   return;
    } else if (!isRevisit || isRevisit === 'N') {
      // StaticToast.warning('请完成线下初诊后再进行线上复诊或开药');
      // return;
    } else if (checkExtroInfo) {
      StaticToast.warning('您还未填写其他信息');
      return false;
    } else if (policyNo && !useRightsAndDiscount.userPreferenceObjectId) {
      StaticToast.warning('必须选择一项权益优惠');
      return false;
    }
    typeof callBack === 'function' && callBack(data);
    return true;
  };

  // 提交问诊单
  const submitInquiryClick = useCallback(() => {
    pushEvent({eventTag: 'inquiry_sub'});

    if(create.isChangeInquiry) {
      pushEvent({eventTag: 'inquiry_changeInquiry_sub'});
    }

    if (isCalculatePrice) {
      StaticToast.warning('问诊单价格正在试算中，请稍后提交问诊单');
      return;
    }
    if (hasInquiry) {
      Modal.confirm({
        content: (
          <p className='danger_inquiry_alert_content center'>
            该问诊人有未完成的问诊单
            <br />
            请结束后再发起新的问诊哦~
          </p>
        ),
        okText: '我知道了',
        cancelText: '取消',
      });
      return;
    }

    // pushEvent({
    //   eventTag: 'ZAHLWYY_TWWZ_XXTXY',
    //   text: '图文问诊_信息填写页',
    //   attrs: { ZAHLWYY_CLICK_CONTENT: '图文问诊_信息填写页' },
    // });
    validata(create, submitToDoctor);
  }, [create, isCalculatePrice]);

  // 确认风险告知书之后创建问诊单的数据，处理为后端需要的格式
  const agreeConfirm = useCallback(() => {
    pushEvent({eventTag: 'inquiry_sub_end'});
    if(create.isChangeInquiry) {
      pushEvent({eventTag: 'inquiry_changeInquiry_sub_end'});
    }
    setPopVisible(false);
    submitInquiry((result) => {
      clearInquiry();
      // 删除ai智能导诊页的缓存数据
      storage.remove('isAiGuideInquiryBack');
      storage.remove('aiGuideInquiryData');
      storage.remove('aiGuideInquiryThyroidData');
      unifiedOrderProcessPay(result);
    });
  }, []);
  const closePop = () => {
    if (validate.isAlipayApplet()) {
      my && my.navigateBack && my.navigateBack();
    } else {
      setPopVisible(false);
    }
  };
  // 提交问诊单
  const submitToDoctor = useCallback(() => {
    // 是否有选择科室
    if (create.isSelectDepartmentOpen) {
      fetchJson({
        url: '/api/api/v1/patient/inquiryDepartment/validateInquiryDepartment',
        type: 'POST',
        data: {
          departmentCode: create.departmentCode === '-1' ? '' : create.departmentCode,
          inquiryType: create.inquiryFlowOption.inquiryType,
        },
        needLoading: true,
      }).then((res) => {
        if (res.code === '0' && res.result) {
          setPopVisible(true);
          return;
        }
        Modal.confirm({
          content: '您所选科室医生资源已被抢占，如需继续问诊，您可能需等待5-10分钟，超时会自动取消或退款，是否继续问诊？',
          onOk: () => {
            setPopVisible(true);
            return true;
          },
        });
      });
      return;
    }
    setPopVisible(true);
  }, []);

  /** **************** 处理优惠券数据 ******************/
  const ableCouple = useMemo(() => couponList.filter(({ userCouponStatus }) => userCouponStatus === 1), [couponList]);
  // 共优惠金额
  const diffAmount = Number(orderAmount - orderRealAmount);

  return (
    <div className={`${prefixCls}`}>
      <RightsAndDiscount
        className={`${prefixCls}__wrapper`}
        checkedVas={checkedVas}
        vasList={vasList}
        vasClick={vasClick}
        patientName={patientName}
        ableCouple={ableCouple}
        checkedCoupon={checkedCoupon}
        couponClick={couponClick} />
      {
        // 阿里非 菜鸟过来的不开药，所以不用填地址发货相关信息
        !isFromAliNOCAINIAO() && (
          <React.Fragment>
            <DeliveryAddress className={`${prefixCls}__wrapper`} address={address} deliveryAddressId={deliveryAddressId} toMyAddress={toMyAddress} />
            <DeliveryMethod className={`${prefixCls}__wrapper`} deliveryWay={deliveryWayList} checkedDeliveryWay={deliveryWay} deliveryClick={deliveryClick} />
          </React.Fragment>
        )
      }
      <BuyService className={`${prefixCls}__wrapper`}></BuyService>

      <div className={`${prefixCls}__fixbottom`}>
        <p className={`${prefixCls}__fixbottom-amount`}>
          <span>总计</span>
          <span className='real_amount'>¥{format.digits(orderRealAmount)}</span>
          {diffAmount > 0 && <span className='preferential'>已优惠¥{format.digits(diffAmount)}</span>}
          {orderAmount == 0 && <span className='preferential'>限时免费</span>}
        </p>
        <Button className='btn_submit' theme='primary' shape='round' onClick={submitInquiryClick} disabled={getIsBlackUser()}>
          {((orderRealAmount == 0) && '去问诊') || '去支付'}
        </Button>
      </div>
      <ConfirmPop popVisible={popVisible} agreeConfirm={agreeConfirm} closePop={closePop} />
    </div>
  );
};

export default ConditionSubmit;
