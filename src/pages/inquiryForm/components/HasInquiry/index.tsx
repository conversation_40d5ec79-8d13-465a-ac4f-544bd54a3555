import React from 'react';
import { Icon } from 'zarm';
import { Link } from 'react-router-dom';
import './HasInquiry.scss'

export default function HasInquiry({ hasInquiry }: { hasInquiry: boolean }) {
  if (!hasInquiry) return null;

  return (
    <Link
      to='/hospital/myinquiry?defaultType=1'
      className='has-inquiry-container'
    >
      <Icon type='info-round' theme='primary' size='sm' />
      <span>您有未完成的订单</span>
      <Icon type='arrow-right' theme='primary' size='sm' />
    </Link>
  );
}
