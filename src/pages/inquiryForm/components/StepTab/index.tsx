import React from "react";
import { SvgIcon } from "src/components/common";
import './stepTab.scss';

const tabList = [{
  id: 0,
  title: '1.病情描述'
}, {
  id: 1,
  title: '2.确认咨询人'
}, {
  id: 2,
  title: '3.提交问诊'
}];

const tabLength = tabList.length;

const StepTab = (props) => {
  const { currentTab = 0 } = props;
  return (
    <div className='comp_step_tab'>
      {
        tabList.map(({ id, title }, index) => {
          return <React.Fragment key={`steptab${id}`}>
            <p className={`step_title ${index <= currentTab ? 'active' : ''}`}>{title}</p>
            {
              index < (tabLength - 1) && <p className={`step_narrow_group ${index <= currentTab ? 'active' : ''}`}>
                <SvgIcon className='step_icon_narrow' src={require('src/svgs/sprite-icon_arrow.svg')} />
                <SvgIcon className='step_icon_narrow' src={require('src/svgs/sprite-icon_arrow.svg')} />
              </p>
            }
          </React.Fragment>
        })
      }
    </div>
  )
};

export default StepTab;