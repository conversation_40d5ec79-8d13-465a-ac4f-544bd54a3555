@import "src/style/index";

.prescriptiondetial_page_pop {
  .pop {
    border-radius: r(8) r(8) 0 0;
    width: 100%;
    background: #fff;
    color: #666;
    padding-bottom: r(12);

    .conforpop-doc {
      overflow: scroll;
      height: 60vh;
    }

    .html-doc {
      margin: 0 r(15) r(22);
    }

    .button-group {
      border-top: 1px solid #e6e6e6;
      @media (-webkit-min-device-pixel-ratio: 2) {
        border-top: 0.5px solid #e6e6e6;
      }

      padding: 0 r(15);

      .tips {
        text-align: center;
        margin-top: r(10);
        color: #8b8b8b;
        font-size: 13px;
      }

      .tips-highlight {
        color: #00bc70;
        margin-top: r(5);
        text-align: center;
        margin-bottom: r(12);
      }

      .btn-active {
        box-shadow: 0 0 18px 0 rgba(0, 188, 112, 0.84);
      }

      .btn-disable {
        background-color: #ababab;
        color: #fff;
      }
    }

    .graph {
      margin-top: r(15);
      line-height: r(21);

      &.highlight {
        color: #333;
        font-weight: bold;
      }

      .dot {
        width: r(6);
        display: inline-block;
        height: r(6);
        margin-right: r(4);
        background-color: #666;
        border-radius: 50%;
        margin-bottom: 1px;
      }
    }

    .text-red {
      color: #01bc71;
      font-weight: bold;
    }

    .title {
      text-align: center;
      font-size: 16px;
      color: #666;
      font-weight: bold;
      padding: r(11) 0;
      border-bottom: 1px solid #e6e6e6;
      @media (-webkit-min-device-pixel-ratio: 2) {
        border-bottom: 0.5px solid #e6e6e6;
      }
    }

    .text {
      padding: r(8) 0;
      font-size: r(15);
      color: #666;
      line-height: r(21);
      text-align: center;
    }
  }
}
