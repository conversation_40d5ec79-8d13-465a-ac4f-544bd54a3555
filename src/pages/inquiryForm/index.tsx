import React, { useEffect } from 'react';
import { ConditionSubmit } from './components';
import bridge from 'src/utils/bridge';
import { shallowEqual, useSelector } from 'react-redux';
import { ApplicationState } from 'src/store';
// import { ApplicationState } from 'src/store';


import './inquiry.scss';
import { pushEvent } from 'src/utils';

const prefixCls = 'inquiry_page';

const Inquiry = (props) => {

  const create = useSelector((state: ApplicationState) => {
    const { create = {} } = state.inquiry;
    return create;
  }, shallowEqual);
  useEffect(() => {
    const { inquiryType = '', isChangeInquiry = false } = create;
    if(isChangeInquiry) {
      pushEvent({eventTag: 'inquiryForm_changeInquiry'});
    }
    inquiryType == 'V' && bridge.setTitle('视频问诊');
  }, [create]);
  return (
    <div className={prefixCls}>
      <ConditionSubmit />
    </div>
  );
};

export default Inquiry;
