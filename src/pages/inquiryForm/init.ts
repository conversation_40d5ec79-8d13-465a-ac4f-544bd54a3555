import { clear_inquiry, edit_inquiry, fetch_inquiry_no } from 'src/store/inquiry/action';
import { Deserialize, fetchJson, getChannelResourceCode } from 'src/utils';

// 患者问诊信息初始化
const patientInquiryInfoInit = (props, dispatch) => {
  const {
    location: { pathname = '', search = '' },
  } = props;

  let {
    patientId = '', policyNo = '', fromSelfChannel = '', inquiryTimingType = '',
    appointmentScheduleId = '', inquiryType = 'I', staffId = '',
    code: thirdDoctorCode = '',
    cardNo = '', channelContractNo = '', channelInquiryNo = '',
    couponId = '', userRightsId = '', exterChannelPoint = '', rightsId = '', isChangeInquiry = 'N', businessType = '' } = Deserialize(search);

  const isVideoBooking = pathname.includes('/hospital/video/booking') && appointmentScheduleId; // 专科视频问诊预约进入的
  const isVideo = /\/hospital\/videointro/ig.test(pathname);

  /**
   * 某些链路下（比如权益没正常发放），url上会携带字符串类型值为'undefined'的userRightsId
   * 导致判断为空的逻辑无法正常工作，把userRightsId一路带到下单接口，接口直接报错
   * 需要进行强转！！
   */
  if (userRightsId === 'undefined') {
    userRightsId = '';
  }

  const fetchInquiryNo = (options = {}) => dispatch(fetch_inquiry_no(options));
  const editInquiry = (key: string, value: any, payload?: any) => dispatch(edit_inquiry(key, value, payload));
  const clearInquiry = () => dispatch(clear_inquiry());

  const isSelectDepartmentOpen = async () => {
    if (staffId) {
      return editInquiry('isSelectDepartmentOpen', false);
    }
    const res = await fetchJson({
      type: 'GET',
      url: '/api/api/v1/patient/channel/getUserChannel',
      data: {
        channelResourceCode: getChannelResourceCode(),
      },
      isloading: true,
    });
    if (res.code === '0') {
      editInquiry('isSelectDepartmentOpen', isVideo ? res.result.isVideoSelectDepartment === 'Y' : res.result.isSelectDepartment === 'Y');
    }
  };

  clearInquiry();
  fetchInquiryNo();
  isSelectDepartmentOpen();
  inquiryType = isVideoBooking || isVideo ? 'V' : inquiryType;
  // productId: 1、图文  2、视频  14、专科
  const inquiryFlowOption = {
    productId: inquiryType == 'I' ? 1 : 2,
    inquiryStatusList: inquiryType == 'I' ? [1, 2, 3, 7, 8, 10, 11] : [1, 2, 3, 7, 8],
    inquiryType,
    payInquiryType: inquiryType == 'I' ? 'text' : 'video',
    businessAppointmentDomain: {},
    inquiryTimingType: inquiryTimingType || '',
  };
  if (isVideoBooking) {
    // appointment =预约单，standard=标准单
    // 有 appointmentScheduleId字段，则为视频预约单
    inquiryFlowOption.productId = 14;
    inquiryFlowOption.businessAppointmentDomain = { appointmentScheduleId };
    inquiryFlowOption.inquiryTimingType = 'appointment';
    editInquiry('platformCode', 'ZA', {
      isVideoBooking: true,
      businessAppointmentDomain: { appointmentScheduleId },
      inquiryTimingType: 'appointment',
    });
  }
  editInquiry('isVideo', isVideo);
  editInquiry('inquiryFlowOption', inquiryFlowOption, {
    inquiryType,
    policyNo,
    fromSelfChannel,
    productId: inquiryFlowOption.productId,
    inquiryTimingType: inquiryFlowOption.inquiryTimingType,
  });
  // 直赔进入的，带有保单号、患者ID 时
  policyNo && patientId && editInquiry('patientId', patientId, {
    cardHasPatient: true,
  });

  businessType === 'CTAI' && patientId && editInquiry('patientId', patientId, {
    cardHasPatient: true,
  });

  // 支付宝渠道需要添加的字段
  if (channelContractNo && channelInquiryNo) {
    editInquiry('channelInquiryNo', channelInquiryNo);
    editInquiry('channelContractNo', channelContractNo);
  }


  if(userRightsId) {
    editInquiry('userRightsId', userRightsId);
  }

  if(isChangeInquiry === 'Y') {
    editInquiry('isChangeInquiry', true);
  }

  if(rightsId) {
    editInquiry('rightsId', rightsId);
  }

  if(exterChannelPoint) {
    editInquiry('exterChannelPoint', exterChannelPoint);
  }

  const fetchDoctoInfo = async () => {
    const res = await fetchJson({
      type: 'POST',
      url: '/api/api/v1/patient/doctor/detail',
      isloading: true,
      data: {
        id: staffId,
        option: {
          isQueryMedicalStaffService: true,
        },
      },
    });
    const { code = '', result = {} } = res;
    if (code === '0') {
      const { medicalStaffServiceList = [] } = result;
      const [price = {}] = medicalStaffServiceList.filter((element) => element.productId === inquiryFlowOption.productId) || [];
      editInquiry('staffId', staffId, {
        orderRealAmount: price.specialPrice,
        orderAmount: price.specialPrice,
        platformCode: 'ZA',
      });
    }
  };

  if (staffId) {
    editInquiry('staffId', staffId,
      {
        platformCode: 'ZA',
      });
    isVideoBooking && fetchDoctoInfo();
  }

  if(thirdDoctorCode) {
    editInquiry('thirdDoctorCode',thirdDoctorCode);
  }

  if (couponId) {
    editInquiry('useRightsAndDiscount', {
      userPreferenceObjectId: couponId,
      preferenceObjectType: '1',
    });
  }

  /** 有cardNo时，查询cardNo信息 */
  const fetchServiceCard = async () => {
    const res = await fetchJson({
      type: 'POST',
      url: '/api/api/v1/patient/medical/service/package/preUse',
      isloading: true,
      data: { cardNo },
    });
    const { code = '', result = {} } = res;
    if (code === '0') {
      const data = JSON.parse(result);
      const { illnessDescription = '', patientId = '', depaerment = '' } = data;
      editInquiry('cardNo', cardNo);
      editInquiry('illnessDescription', illnessDescription);
      patientId && editInquiry('patientId', patientId, {
        cardHasPatient: true,
        workDepartment: depaerment,
      });
    }
  };
  if (cardNo) {
    fetchServiceCard();
  }
};

export default patientInquiryInfoInit;
