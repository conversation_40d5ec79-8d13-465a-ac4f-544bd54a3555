@import "src/style/index";

.InquiryOffice {
  padding: r(10);
  background: #f5f5f5;
  min-height: 100vh;

  &-card {
    background: #fff;
    border-radius: r(8);
    padding: r(15);

    &__title {
      font-size: r(16);
      font-family: PingFangSC-Semibold, PingFang SC;
      font-weight: 600;
      color: #1e1e1e;
    }

    &__content {
      margin-top: r(16);
    }

    &__item {
      background: #f5f5f5;
      border-radius: 1000px;
      border: 1px solid rgba(0, 0, 0, 0.1);
      padding: r(6) r(15);
      display: inline-block;
      margin-right: r(10);
      margin-bottom: r(10);
      font-size: r(14);
      font-family: PingFangSC-Regular, PingFang SC;
      color: #333;

      &.active {
        color: #ec9131;
        background: rgba(236, 145, 49, 0.06);
        border: 1px solid rgba(236, 145, 49, 0.32);
      }

      &.disabled {
        background: #fff;
        opacity: 0.5;
        cursor: not-allowed;
      }
    }

    &__unselected {
      padding: r(10) 0;
      color: #00a864;
      font-size: r(12);
      display: flex;
      align-items: center;
      justify-content: center;
    }

    &__icon {
      width: r(16);
      height: r(16);
      margin-right: r(5);
    }

    &__desc {
      background: #f8f8f8;
      border-radius: r(8);
      padding: r(15);
      margin-top: r(18);
      font-size: r(13);
      color: #333;
    }
  }

  &-btn {
    width: 100%;
    padding: r(10) r(20) r(15) r(15);
    background: #fff;
    position: fixed;
    bottom: 0;
    bottom: env(safe-area-inset-bottom);
    left: 0;
  }

  &-modal {
    .za-modal__body {
      padding: 0;
    }

    .za-popup__wrapper {
      top: r(-110);
    }

    &__content {
      padding: r(20);
    }

    &__tip {
      font-size: r(17);
      color: #e64848;
      text-align: center;
    }

    &__info {
      color: #333;
      font-size: r(14);
      margin-top: 10px;
      text-align: center;
    }

    &__title {
      font-size: r(17);
      font-weight: 600;
      color: #1e1e1e;
      margin: r(15) 0;
      text-align: center;
    }

    &__footer {
      background: rgba(255, 255, 255, 0);
      border-top: 1px solid #e6e6e6;
      display: flex;
      align-items: center;
      justify-content: space-around;
      font-size: r(16);
      width: 100%;
      text-align: center;
    }

    &__cancel {
      padding: r(11) 0;
      width: 50%;
      color: #666;
      border-right: 1px solid #e6e6e6;
    }

    &__ok {
      padding: r(11) 0;
      width: 50%;
      color: #00a864;
    }
  }
}
