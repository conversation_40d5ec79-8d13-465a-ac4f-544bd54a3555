import classnames from 'classnames';
import React, { useCallback, useEffect, useReducer, useState } from 'react';
import { shallowEqual, useDispatch, useSelector } from 'react-redux';
import { StaticToast, SvgIcon } from 'src/components/common';
import { ApplicationState } from 'src/store';
import { edit_inquiry } from 'src/store/inquiry/action';
import { Deserialize, fetchJson, getChannelResourceCode, pushEvent } from 'src/utils';
import format from 'src/utils/format';
import { Button, Modal } from 'zarm';
import './office.scss';
import { unifiedOrderProcessPay } from './utils';

let inquiryData: any = {
  inquiryNo: '', // 问诊单编号
  attachmentList: [], // 附件列表
  productId: 1,
};

// 问诊科室状态：1：可选择可接诊，2：可选择不能接诊，3：默认选中 ,4：不可选
const Office = (props) => {
  const [list, setList] = useState<any[]>([]);
  const [current, setCurrent] = useState<any>({});
  const [unDisabledList, setUnDisabledList] = useState<any[]>([]);
  const [visible, setVisible] = useState(false);
  const [shouldSelectOffice, setShouldSelectOffice] = useState(false);
  const dispatch = useDispatch();

  const {
    location: { search = '' },
  } = props;


  const { type, serviceContractId  } = Deserialize(search);

  const { create } = useSelector((state: ApplicationState) => {
    const { create = {} } = state.inquiry;
    return {
      create,
    };
  }, shallowEqual);

  const isSelectDepartmentOpen = async () => {
    const res = await fetchJson({
      type: 'GET',
      url: '/api/api/v1/patient/channel/getUserChannel',
      data: {
        channelResourceCode: getChannelResourceCode(),
      },
      isloading: true,
    });
    if (res.code === '0') {
      setShouldSelectOffice(create.isVideo ? res.result.isVideoSelectDepartment === 'Y' : res.result.isSelectDepartment === 'Y');
      return res.result;
    }
  };

  const init = async () => {
    pushEvent({eventTag: 'department'});
    const res = await fetchJson({
      type: 'GET',
      url: '/api/api/v1/patient/inquiryDepartment/queryAllInquiryDepartment',
      isloading: true,
      data: {
        inquiryType: create.inquiryFlowOption.inquiryType,
        patientId: create.patientId,
      },
    });
    if (res.code === '0') {
      setList(res.result);
      setUnDisabledList(
        res.result.filter((item) => item.inquiryDepartmentStatus === '1' || item.inquiryDepartmentStatus === '3')
      );
      // 暂时只有值班医生为默认选中
      const defaultSelected = res.result.filter((item) => item.inquiryDepartmentStatus === '3');
      if(defaultSelected.length) {
        setCurrent(defaultSelected[0]);
      }
    }
    const channelInfo = await isSelectDepartmentOpen();
    await fetchInquiryNo();
    await serviceContractQuery();
    // 如果是药省保不选科室就直接跳转
    if(channelInfo.isSelectDepartment !== 'Y' && type === 'direct') {
      return goAwait();
    }
  };

  const select = (item) => {
    pushEvent({eventTag: 'department_sub'});
    // disabled或者默认选中
    if(
      item.inquiryDepartmentStatus === '4' ||
      (item.inquiryDepartmentStatus === '3' && current.departmentCode === item.departmentCode)
    ) {
      return;
    }
    setCurrent({});
    if(unDisabledList.map((item) => item.departmentCode).indexOf(item.departmentCode) < 0) {
      return setVisible(true);
    }
    setCurrent(item);
  };

  useEffect(() => {
    init();
  }, []);

  const fetchInquiryNo = async () => {
    const res = await fetchJson({
      url: '/api/api/v1/patient/bizno/getByType',
      type: 'POST',
      data: { bizNoType: 'INQUIRY_NO' },
    });
    const { code = '', result = '' } = res;
    if (code === '0' && result) {
      inquiryData.inquiryNo = result;
      return;
    }
  };

  const serviceContractQuery = async () => {
    const res = await fetchJson({
      url: '/api/api/v1/ysb/serviceContract/query',
      type: 'POST',
      data: { id: serviceContractId, isNeedQueryRights: true },
      isloading: true,
    });
    const { code = '', result = [] } = res;
    if (code === '0' && result) {
      inquiryData = {
        ...inquiryData,
        ...(result[0] || {}),
      };
      return;
    }
  };

  const calculate = async () => {
    const res = await fetchJson({
      url: '/api/api/v1/patient/preference/calculate',
      type: 'POST',
      data: {
        orderGoods: [],
        platformCode: '',
        patientId: inquiryData.patientId,
        orderType: 'inquiry',
        inquiryType: 'I',
        orderPreferenceList: [{
          preferenceObjectType: '3',
          userPreferenceObjectId: inquiryData.userRightsId,
          isPolicyRights: false,
        }],
        returnValidPreference: true,
        relatedBizNo: inquiryData.inquiryNo,
        inquiryTimingType: '',
        policyNo: inquiryData.policyNo,
        fromSelfChannel: '',
      },
      isloading: true,
    });
    const { code = '0', result: { order: { orderGoods = [], platformCode = '', orderAmount = '', orderRealAmount = '', orderPreferenceList = [] } = {} } = {} } = res || {};

    if (code === '0') {
      inquiryData = {
        ...inquiryData,
        ...{
          useRightsAndDiscount: orderPreferenceList[0],
          orderGoods,
          orderPreferenceList,
          platformCode,
          orderRealAmount,
          orderAmount,
        },
      };
      return;
    }
  };

  const unifiedOrder = async () => {
    const res = await fetchJson({
      url: '/api/api/v1/patient/order/unifiedOrder',
      type: 'POST',
      isloading: true,
      data: {
        businessEntry: {
          uid: format.guid(),
          isSelectDepartmentOpen: true,
          inquiryFlowOption: {
            productId: 1,
            inquiryStatusList: [
              1,
              2,
              3,
              7,
              8,
              10,
              11,
            ],
            inquiryType: 'I',
            payInquiryType: 'text',
            businessAppointmentDomain: {},
            inquiryTimingType: 'assign_doctor',
          },
          inquiryType: 'I',
          policyNo: inquiryData.policyNo,
          fromSelfChannel: '',
          productId: 1,
          platformCode: 'ZA',
          inquiryNo: inquiryData.inquiryNo,
          patientId: inquiryData.patientId,
          patientName: inquiryData.patientName,
          patientAge: inquiryData.patientAge,
          patientGender: create.patientGender,
          inquiryExtraInfo: {
            liverSick: '无',
            drugAllergic: {
              class: '您是否有对药物过敏',
              tags: [],
              additional: '无',
              placeholder: '请补充您的药物过敏',
              key: '您是否有对药物过敏',
              value: '无',
            },
            patientFamilyHistory: {
              healthClass: '您是否有遗传病史',
              class: '您是否有家族病史',
              tags: [],
              additional: '',
              placeholder: '您是否有家族病史',
              key: '您是否有家族病史',
              value: '无',
            },
            fever: '无发热',
            symptom: {
              healthClass: '您是否有以下症状',
              tags: [
                inquiryData.symptomName,
              ],
              additional: '',
              placeholder: '您是否有以下症状',
              key: '您是否有以下症状',
              value: '有',
            },
          },
          onGoingInquiry: [],
          illnessDescription: inquiryData.symptomName,
          isRevisit: 'Y',
          departmentCode: current.departmentCode === '-1' ? '' : current.departmentCode,
          workDepartment: current.departmentCode === '-1' ? '' : current.departmentCode,
          orderPreferenceList: inquiryData.orderPreferenceList,
          deliveryAddressId: (inquiryData.serviceContractDeliveryAddress || {}).id,
          deliveryWay: '1',
          serviceContractDeliveryAddress: inquiryData.serviceContractDeliveryAddress,
          inquiryThirdRelationList: [
            {
              thirdPlatformCode: 'ZA',
            },
          ],
          ...inquiryData,
          id: undefined,
        },
        workDepartment: current.departmentCode === '-1' ? '' : current.departmentCode,
        orderPreferenceList: inquiryData.orderPreferenceList,
        orderGoods: inquiryData.orderGoods,
        orderType: 'inquiry',
        relatedBizNo: inquiryData.inquiryNo,
        deliveryAddressId: (inquiryData.serviceContractDeliveryAddress || {}).id,
        deliveryWay: '1',
        serviceContractDeliveryAddress: inquiryData.serviceContractDeliveryAddress,
        bizType: 'I',
        cardNo: '',
        platformCode: 'ZA',
        policyNo: '',
        vasCode: ((inquiryData.userRightsDetail || {}).userRightsDomain || {}).drawVasCode,
        ...inquiryData,
        id: undefined,
      },
    });
    if(res.code === '0') {
      return res.result;
    }
    StaticToast.error(res.message);
    return Promise.reject(res);
  };

  const goAwait = async () => {
    await calculate();
    const result = await unifiedOrder();
    unifiedOrderProcessPay(result, true);
  };

  const ok = () => {
    if(!current.departmentCode) {
      return StaticToast.warning('请先选择科室！');
    }
    setVisible(false);
    dispatch(edit_inquiry('departmentCode', current.departmentCode));

    if(type === 'direct') {
      return goAwait();
    }

    props.history.push({
      pathname: '/hospital/inquiry/submit',
    });
  };

  return shouldSelectOffice ? (
    <div className={'InquiryOffice'}>
      <Modal
        width='80%'
        visible={visible}
        className='InquiryOffice-modal'
        onCancel={() => setVisible(false)}
      >
        <div className='InquiryOffice-modal__content'>
          <h3 className='InquiryOffice-modal__tip'>
            {
              unDisabledList.length ? '当前该科室暂无值班医生在线，是否选择其他科室继续进行？' : '当前该科室暂无值班医生在线'
            }
          </h3>
          {
            unDisabledList.length ?
              <>
                <p className='InquiryOffice-modal__title'>当前可选科室：</p>
                {unDisabledList.map((item) => (
                  <div
                    className={classnames('InquiryOffice-card__item', {
                      active: current.departmentCode === item.departmentCode,
                    })}
                    key={item.departmentCode}
                    onClick={() => {
                      select(item);
                    }}
                  >
                    {item.departmentName}
                  </div>
                ))}
              </> :
              <p className='InquiryOffice-modal__info'>提示：当前所有科室都无空闲值班医生，如需使用请稍后再试</p>
          }
        </div>
        <div className='InquiryOffice-modal__footer'>
          {
            !!unDisabledList.length &&
              <div className='InquiryOffice-modal__cancel' onClick={() => setVisible(false)}>取消</div>
          }
          <div
            className='InquiryOffice-modal__ok'
            // eslint-disable-next-line @typescript-eslint/no-misused-promises
            onClick={() => unDisabledList.length ? ok() : setVisible(false)}
          >
                确定
          </div>
        </div>
      </Modal>
      <div className='InquiryOffice-card'>
        <h3 className='InquiryOffice-card__title'>请选择科室：</h3>
        <div className='InquiryOffice-card__content'>
          {list.map((item) => (
            <div
              className={classnames('InquiryOffice-card__item', {
                active: current.departmentCode === item.departmentCode,
                disabled: item.inquiryDepartmentStatus === '4',
              })}
              key={item.departmentCode}
              onClick={() => {
                select(item);
              }}
            >
              {item.departmentName}
            </div>
          ))}
          {!current.departmentCode && (
            <p className='InquiryOffice-card__unselected'>
              <SvgIcon className='InquiryOffice-card__icon' src={require('src/svgs/sprite-icon_tips.svg')} /> 请先基于您的需要选择对应的科室
            </p>
          )}
        </div>
        {current.departmentCode && (
          <div className='InquiryOffice-card__desc'>
            <p>{current.description}</p>
          </div>
        )}
      </div>
      <div className='InquiryOffice-btn'>
        <Button theme='primary' shape='round' size='md' block onClick={() => ok()}>
          {type === 'direct' ? '发起问诊' : '下一步'}
        </Button>
      </div>
    </div>
  ) : <div></div>;
};

export default Office;
