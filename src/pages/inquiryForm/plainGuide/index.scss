@import "src/style/index";
$prefixCls: 'aipopup-components';
@import "../aiGuide/components/patientAnnex/patientAnnex.scss";

.PlainGuide {
  min-height: 100vh;
  background: #f5f5f5;
  padding-bottom: r(100);

  &-container {
    padding: r(10) r(15);
  }

  &-patient {
    background-color: #fff;
    border-radius: r(7);
    margin-top: r(10);
    padding: r(15);
    padding-bottom: r(20);

    &__title {
      font-size: r(16);
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      color: #000;
      margin-bottom: r(15);
    }

    &__content {
      display: flex;
      align-items: center;
      justify-content: left;
      flex-wrap: wrap;
    }

    &__tag {
      background: rgba(0, 0, 0, 0.04);
      border-radius: 32px;
      margin-right: r(10);
      padding: r(10) r(15);
      font-size: r(14);
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      margin-top: r(10);
      color: rgba(0, 0, 0, 0.8);
      border: 1px solid rgba(0, 0, 0, 0.04);

      &.add {
        border: 1px solid rgba(0, 0, 0, 0.1);
        background-color: #fff;
        padding: r(10) r(13);
      }

      &.active {
        background: rgba(0, 188, 112, 0.08);
        border: 1px solid rgba(0, 188, 112, 0.8);
        color: #00bc70;
      }
    }
  }

  &-content {
    background-color: #fff;
    border-radius: r(7);
    margin-top: r(10);
    padding: r(15);
  }

  &-desc {
    &__title {
      font-size: r(14);
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      color: #333;
    }

    &__text {
      textarea {
        margin-top: r(10);
        background: #f8f8f8;
        border-radius: r(8);
        color: #333;
        padding: r(10);
        width: 100%;
        border: 0;
        resize: none;
        line-height: r(19);
      }
    }
  }

  &-item {
    border-bottom: 1px solid #e6e6e6;
    padding: r(17) 0;
    font-size: r(15);
    font-family: PingFangSC, PingFang SC;
    font-weight: 400;

    .za-radio__text {
      color: #666;
    }

    span {
      font-size: r(15);
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      color: #333;
    }

    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  &-upload {
    margin-top: r(31);

    .#{$prefixCls}__title {
      font-size: r(16);
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      color: #000;
    }

    .#{$prefixCls}__upload {
      padding: 0;
    }
  }

  &-extra {
    margin: r(15) 0;

    &__title {
      margin-top: r(15);
      font-size: r(15);
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      color: #333;
    }

    textarea {
      margin-top: r(10);
      background: #f8f8f8;
      border-radius: r(8);
      color: #333;
      padding: r(10);
      width: 100%;
      border: 0;
      resize: none;
      line-height: r(19);
    }
  }

  &-submit {
    position: fixed;
    bottom: 0;
    background-color: #fff;
    width: 100%;
    padding: r(10) r(30) r(30);
  }
}
