import React, { useEffect, useCallback } from 'react';
import { shallowEqual, useSelector, useDispatch } from 'react-redux';
import { Link } from 'react-router-dom';
import { StaticToast } from 'src/components/common';
import { ALLERGY_DRUG, ALLFAMILY_ILLNESS } from 'src/pages/patients/editPatient/patientHealth';
import { ApplicationState } from 'src/store';
import { edit_inquiry } from 'src/store/inquiry/action';
import { fetch_patients_list } from 'src/store/patients/action';
import { Deserialize, validate } from 'src/utils';
import format from 'src/utils/format';
import './index.scss';
import { rasSecretKeyEncrypt } from 'src/utils/tool';
import { Button, Radio } from 'zarm';
import PatientAnnex from '../aiGuide/components/patientAnnex';
import patientInquiryInfoInit from '../init';
import classNames from 'classnames';
import { patientInquiryExtraInfo } from '../utils';


const YOUORWU = [
  {
    resCode: '有',
    resName: '是',
  },
  {
    resCode: '无',
    resName: '否',
  },
];

const defaultHealth = {
  patientFamilyHistory: '[{"healthClass":"您是否有遗传病史","additional":"","placeholder":"您是否有家族病史","class":"您是否有家族病史","value":"无","key":"您是否有家族病史","tags":[]}]',
  patientMedicalHistory: '[{"placeholder":"请补充您患有或患过明确诊断的疾病","class":"您是否有患有或患过明确诊断的疾病","value":"无","key":"您是否有患有或患过明确诊断的疾病"}]',
  patientAllergicHistory: '[{"additional":"无","placeholder":"请补充您的药物过敏","class":"您是否有对药物过敏","value":"无","key":"您是否有对药物过敏","tags":[]}]',
  patientHabit: '[]',
  liverHealth: '无',
};

const PlainGuide = (props) => {
  const dispatch = useDispatch();

  const editInquiry = (key: string, value: any, payload?: any) => dispatch(edit_inquiry(key, value, payload));
  const { desc = '' } = Deserialize(window.location.search);

  const { create = {}, patientsList, YESORNO } = useSelector((state: ApplicationState) => {
    const { create = {} } = state.inquiry;
    const { GENDER_OBJ = {}, YESORNO = [] } = state.dictionary;
    const { patientsList = [] } = state.patients;
    return {
      create,
      GENDER_OBJ,
      patientsList,
      YESORNO,
    };
  }, shallowEqual);

  const {
    patientId,
    isSelectDepartmentOpen,
  } = create;

  const {
    inquiryExtraInfo = {},
    inquiryExtraInfo: {
      drugAllergic = {},
      patientFamilyHistory = {} } = {},
    isRevisit,
  } = create;

  const selectPatientHandler = useCallback((patient) => {
    const { patientName = '', id = '', age = '', patientGender = '', patientHealth = {} } = patient || {};
    let newInquiryExtraInfo = patientInquiryExtraInfo({...defaultHealth, ...patientHealth});
    // 当redux中的inquiryExtraInfo并且患者没有改变时候，健康档案的数据和redux的数据合并
    if (patientId == id && inquiryExtraInfo.liverSick) {
      newInquiryExtraInfo = {
        ...newInquiryExtraInfo,
        ...inquiryExtraInfo,
      };
    }
    editInquiry('patientId', parseInt(id, 10), {
      patientName,
      patientAge: age,
      patientGender,
      inquiryExtraInfo: JSON.parse(JSON.stringify(newInquiryExtraInfo)),
    });
  }, [inquiryExtraInfo, patientId]);

  const fetchPatientsList = (onSuccess?) => dispatch(fetch_patients_list({
    inquiryType: 'I',
    sensitiveKeyEncry: rasSecretKeyEncrypt(),
    option: {
      needHealthInfo: true,
      needSensitiveEncrypt: !!validate.isBestpay(),
    },
  }, onSuccess));

  useEffect(() => {
    if (patientsList.length) {
      let patient = null;
      if (patientId) {
        patient = patientsList.find((i) => i.id == patientId) || {};
      } else {
        patient = patientsList[0];
      }
      selectPatientHandler(patient);
    }
  }, [patientsList, patientId]);

  useEffect(() => {
    fetchPatientsList();
    patientInquiryInfoInit(props, dispatch);
    editInquiry('illnessDescription', decodeURIComponent(desc));
    editInquiry('isRevisit', 'Y');
  }, []);

  const { illnessDescription = '' } = create;

  const setEditorContent = useCallback((value) => {
    const { inquiryExtraInfo = {} } = create;
    editInquiry('inquiryExtraInfo', format.merge(inquiryExtraInfo, value));
  }, [create]);

  const radioOnChange = (key, value) => {
    let extra = {
      [`${key}`]: value,
    };

    if (key === 'isRevisit') { // 不存储在 inquiryExtraInfo，与它同级
      editInquiry(key, value);
      return;
    }
    if (key == 'drugAllergic' || key == 'patientFamilyHistory') {
      let item = {};
      if (value === '无') {
        item = {
          tags: [],
          additional: '',
        };
      }
      extra = {
        [key]: {
          value,
          ...item,
        },
      };
    }

    setEditorContent(extra);
  };

  const selectTag = (key, value) => {
    let tags = [];
    if (key === 'drugAllergic') {
      if ((drugAllergic.tags || []).includes(value)) {
        tags = drugAllergic.tags.filter((k) => k != value);
      } else {
        tags = drugAllergic.tags.concat(value);
      }
    } else if (key === 'patientFamilyHistory') {
      if ((patientFamilyHistory.tags || []).includes(value)) {
        tags = patientFamilyHistory.tags.filter((k) => k != value);
      } else {
        tags = patientFamilyHistory.tags.concat(value);
      }
    }
    setEditorContent({ [key]: { tags } });
  };

  const additionalSupplement = (key, additional) => {
    setEditorContent({ [key]: { additional } });
  };

  return (
    <div className={'PlainGuide'}>
      <div className='PlainGuide-container'>
        <div className='PlainGuide-patient'>
          <div className='PlainGuide-patient__title'>
            请选择就诊人
          </div>
          <div className='PlainGuide-patient__content'>
            {
              patientsList.map((item) => (
                <span onClick={() => selectPatientHandler(item)} key={item.id} className={classNames('PlainGuide-patient__tag', { active: item.id === patientId })}>{item.patientName}</span>
              ))
            }
            <Link to={{ pathname: '/hospital/editpatient/baseinfo' }} className='PlainGuide-patient__tag add'>+&nbsp;新增</Link>
          </div>
        </div>
        <div className='PlainGuide-content'>
          <div className='PlainGuide-desc'>
            <div className='PlainGuide-desc__title'>
                请详细描述您的问题，如症状、疾病、患病时长、服用药品等。（至少6个字）
            </div>
            <div className='PlainGuide-desc__text'>
              <textarea
                rows={3}
                value={create.illnessDescription}
                placeholder='请输入您的问题描述'
                onChange={(e) => {
                  editInquiry('illnessDescription', e.target.value);
                }}
              />
            </div>
          </div>
          <div className='PlainGuide-item'>
            <span>是否复诊？</span>
            <Radio.Group
              value={create.isRevisit}
              onChange={(value) => {
                editInquiry('isRevisit', value);
              }}
            >
              {YESORNO.map((k) => (<Radio key={`${k.resCode}-cell`} value={k.resCode}>{k.resName}</Radio>))}
            </Radio.Group>
          </div>
          <div className='PlainGuide-item'>
            <span>是否存在肝肾功能异常？</span>
            <Radio.Group
              value={inquiryExtraInfo.liverSick}
              onChange={(value) => {
                radioOnChange('liverSick', value);
              }}
            >
              {YOUORWU.map((k) => (<Radio key={`${k.resCode}-cell`} value={k.resCode}>{k.resName}</Radio>))}
            </Radio.Group>
          </div>
          <div className='PlainGuide-item'>
            <span>是否药物过敏？</span>
            <Radio.Group
              value={drugAllergic.value}
              onChange={(value) => {
                radioOnChange('drugAllergic', value);
              }}
            >
              {YOUORWU.map((k) => (<Radio key={`${k.resCode}-cell`} value={k.resCode}>{k.resName}</Radio>))}
            </Radio.Group>
          </div>
          {
            drugAllergic.value === '有' &&
            <div className='PlainGuide-extra'>
              <div className='PlainGuide-patient__content'>
                {
                  ALLERGY_DRUG.map((item) => (
                    <span onClick={() => selectTag('drugAllergic', item)} className={classNames('PlainGuide-patient__tag', {
                      active: drugAllergic.tags.includes(item),
                    })} key={item}>{item}</span>
                  ))
                }
              </div>
              <div className='PlainGuide-extra__title'>
                其他
              </div>
              <textarea
                rows={2}
                placeholder='请补充'
                onChange={(e) => {
                  additionalSupplement('drugAllergic', e.target.value);
                }}
              />
            </div>
          }
          <div className='PlainGuide-item'>
            <span>是否有家族遗传病史？</span>
            <Radio.Group
              value={patientFamilyHistory.value}
              onChange={(value) => {
                radioOnChange('patientFamilyHistory', value);
              }}
            >
              {YOUORWU.map((k) => (<Radio key={`${k.resCode}-cell`} value={k.resCode}>{k.resName}</Radio>))}
            </Radio.Group>
          </div>
          {
            patientFamilyHistory.value === '有' &&
            <div className='PlainGuide-extra'>
              <div className='PlainGuide-patient__content'>
                {
                  ALLFAMILY_ILLNESS.map((item) => (
                    <span onClick={() => selectTag('patientFamilyHistory', item)} className={classNames('PlainGuide-patient__tag', {
                      active: patientFamilyHistory.tags.includes(item),
                    })} key={item}>{item}</span>
                  ))
                }
              </div>
              <div className='PlainGuide-extra__title'>
                其他
              </div>
              <textarea
                rows={2}
                placeholder='请补充'
                onChange={(e) => {
                  additionalSupplement('patientFamilyHistory', e.target.value);
                }}
              />
            </div>
          }
          <div className='PlainGuide-upload'>
            <PatientAnnex
              showJump={false}
              prefixCls={'aipopup-components'}>
            </PatientAnnex>
          </div>
        </div>
      </div>
      <div className='PlainGuide-submit'>
        <Button
          shape='round'
          onClick={() => {
            if(illnessDescription.length < 6) {
              return StaticToast.error('请描述您的问题,至少6个字！');
            }
            if(isRevisit !== 'Y') {
              StaticToast.error('请完成线下初诊后再进行线上复诊或开药');
              return;
            }
            if (validate.hasEmoji(drugAllergic.additional) || validate.hasEmoji(patientFamilyHistory.additional)) {
              return StaticToast.error('不支持输入表情');
            }
            if(isSelectDepartmentOpen) {
              return props.history.push({
                pathname: '/hospital/inquiry/office',
              });
            }

            props.history.push({
              pathname: '/hospital/inquiry/submit',
            });
          }}
          className='PlainGuide-submit__btn'
          block theme='primary'>下一步</Button>
      </div>
    </div>
  );
};


export default PlainGuide;
