import React from 'react';
import { StaticToast } from 'src/components/common';
import { fetchJson, reverseArray } from 'src/utils';
import { jumpBeforeFetchThirdInquiryAbility } from 'src/utils/auth';
import format from 'src/utils/format';
import { commonPay } from 'src/utils/pay';
import { Serialize } from 'src/utils/serialization';
import { INQURIY_OTHER_EXTRAINFO } from 'src/utils/staticData';

interface vasRight {
  businessType: string;
  patientPackageId: string;
  patientPackageType: string;
  yearMaxCount: number;
  discount: number;
  monthMaxCount: number;
  currentMonthUsedCount: number;
}

function monthDrugsTips(count, maxCount) {
  return (
    <React.Fragment key={`${count}${maxCount}`}>
      本月还剩<span className='orange'>{count}次</span>优惠，本年可用
      <span className='orange'>{maxCount}次</span>
    </React.Fragment>
  );
}

export function getName(right) {
  let name = '';
  const drugsTips: any[] = [];
  const vasRightsList = (right.vasRightsList || []) as vasRight[];
  for (const r of reverseArray(vasRightsList)) {
    const {
      businessType,
      yearMaxCount,
      discount,
      monthMaxCount,
      currentMonthUsedCount,
    } = r;

    if (businessType === 'videoInquiry') {
      if (name) {
        name += ' | ';
      }
      if (yearMaxCount == -1) {
        name += '不限次0元问诊';
      } else {
        name += `${yearMaxCount}次0元问诊`;
      }
    }
    if (businessType === 'videoDrugs') {
      if (name) {
        name += ' | ';
      }
      if (yearMaxCount > 0) {
        name += `${yearMaxCount}次`;
      }
      if (discount > 0) {
        name += `${parseInt(String(discount / 10), 10)}折购药`;
      }

      if (monthMaxCount - currentMonthUsedCount >= 0) {
        drugsTips.push(monthDrugsTips(monthMaxCount - currentMonthUsedCount, yearMaxCount));
      }
    }
  }

  return {
    name,
    drugsTips,
  };
}

export const patientInquiryExtraInfo = (patientHealth) => {
  const { liverHealth = '', patientAllergicHistory = '[]', patientFamilyHistory = '[]' } = patientHealth;
  let allergicHistoryText: Array<{ class?: string, [key: string]: undefined | string | number }> = [];
  let familyHistoryText: Array<{ class?: string, [key: string]: undefined | string | number }> = [];
  try {
    allergicHistoryText = JSON.parse(patientAllergicHistory);
    familyHistoryText = JSON.parse(patientFamilyHistory);
  } catch (error) {
  }
  let drugAllergic = {};
  if (Array.isArray(allergicHistoryText)) {
    drugAllergic = allergicHistoryText.find((k) => (k.class && k.class == INQURIY_OTHER_EXTRAINFO.drugAllergic.class)) || {};
  }
  let familyHistory = {};
  if (Array.isArray(familyHistoryText)) {
    familyHistory = familyHistoryText.find((k) => (k.class && k.class == INQURIY_OTHER_EXTRAINFO.patientFamilyHistory.class)) || {};
  }
  return format.merge(INQURIY_OTHER_EXTRAINFO, {
    liverSick: liverHealth || '无',
    drugAllergic,
    patientFamilyHistory: familyHistory,
  });
};


// 问诊下单 / 心理咨询下单 共用此逻辑
const appointmentOrder = (inquiryTimingType) => {
  if (inquiryTimingType == 'appointment') {
    window.reactHistory.push({
      pathname: '/hospital/specialist/booking/success',
    });
    return true;
  }
  return false;
};
export const unifiedOrderProcessPay = (result, needReplace = false) => {
  const {
    relatedBizNo: inquiryNo,
    businessEntry: { id: inquiryId = '', inquiryTimingType = '', inquiryType = '' } = {},
  } = result;
  const query = { inquiryNo, inquiryId };

  const search = Serialize(query);
  const thirdInquiryAbilityData = query;

  if (result.orderRealAmount === 0) {
    if (appointmentOrder(inquiryTimingType)) {
      return;
    }
    jumpBeforeFetchThirdInquiryAbility({
      needTextRedirectTo: true,
      data: thirdInquiryAbilityData,
      needReplace,
    });
    return;
  }
  commonPay({
    orderRealAmount: result.orderRealAmount,
    orderNo: result.orderNo,
    orderTime: result.orderTime,
    inquiryNo: result.relatedBizNo,
    orderType: 'inquiry',
    payInquiryType: inquiryType == 'I' ? 'text' : 'video',
    inquiryTimingType,
    h5ZACashierIsReplace: true,
    returnUrl: inquiryTimingType == 'appointment' ? `${window.location.origin}/hospital/video/booking?${search}` : '',
  });
};

let inquiryData: any = {
  inquiryNo: '', // 问诊单编号
  attachmentList: [], // 附件列表
};

export const autoUnifiedOrder = async (create, current = {
  departmentCode: '-1',
}) => {
  const fetchInquiryNo = async () => {
    const res = await fetchJson({
      url: '/api/api/v1/patient/bizno/getByType',
      type: 'POST',
      data: { bizNoType: 'INQUIRY_NO' },
    });
    const { code = '', result = '' } = res;
    if (code === '0' && result) {
      inquiryData.inquiryNo = result;
      return;
    }
  };
  const calculate = async () => {
    const res = await fetchJson({
      url: '/api/api/v1/patient/preference/calculate',
      type: 'POST',
      data: {
        orderGoods: [],
        platformCode: 'ZA',
        patientId: create.patientId,
        orderType: 'inquiry',
        inquiryType: create.inquiryType,
        orderPreferenceList: [{
          preferenceObjectType: '3',
          userPreferenceObjectId: create.userRightsId,
          isPolicyRights: false,
        }],
        returnValidPreference: true,
        relatedBizNo: inquiryData.inquiryNo,
        inquiryTimingType: create.inquiryFlowOption?.inquiryTimingType,
        policyNo: create.policyNo,
        fromSelfChannel: '',
      },
      isloading: true,
    });
    const { code = '0', result: { order: { orderGoods = [], platformCode = '', orderAmount = '', orderRealAmount = '', orderPreferenceList = [] } = {} } = {} } = res || {};

    if (code === '0') {
      inquiryData = {
        ...inquiryData,
        ...{
          useRightsAndDiscount: orderPreferenceList[0],
          orderGoods,
          orderPreferenceList,
          platformCode,
          orderRealAmount,
          orderAmount,
        },
      };
      return;
    }
  };

  const unifiedOrder = async () => {
    const res = await fetchJson({
      url: '/api/api/v1/patient/order/unifiedOrder',
      type: 'POST',
      isloading: true,
      data: {
        businessEntry: {
          uid: format.guid(),
          inquiryFlowOption: create.inquiryFlowOption,
          inquiryType: create.inquiryFlowOption?.inquiryType,
          policyNo: create.policyNo,
          fromSelfChannel: '',
          productId: create.inquiryFlowOption?.productId,
          platformCode: 'ZA',
          inquiryNo: inquiryData.inquiryNo,
          patientId: create.patientId,
          patientName: create.patientName,
          patientAge: create.patientAge,
          patientGender: create.patientGender,
          inquiryExtraInfo: {
            liverSick: '无',
            drugAllergic: {
              class: '您是否有对药物过敏',
              tags: [],
              additional: '无',
              placeholder: '请补充您的药物过敏',
              key: '您是否有对药物过敏',
              value: '无',
            },
            patientFamilyHistory: {
              healthClass: '您是否有遗传病史',
              class: '您是否有家族病史',
              tags: [],
              additional: '',
              placeholder: '您是否有家族病史',
              key: '您是否有家族病史',
              value: '无',
            },
          },
          onGoingInquiry: [],
          illnessDescription: create.illnessDescription,
          isRevisit: 'Y',
          departmentCode: current.departmentCode === '-1' ? '' : current.departmentCode,
          workDepartment: current.departmentCode === '-1' ? '' : current.departmentCode,
          orderPreferenceList: inquiryData.orderPreferenceList,
          deliveryWay: '1',
          inquiryThirdRelationList: [
            {
              thirdPlatformCode: 'ZA',
            },
          ],
          ...inquiryData,
          id: undefined,
        },
        workDepartment: current.departmentCode === '-1' ? '' : current.departmentCode,
        orderPreferenceList: inquiryData.orderPreferenceList,
        orderGoods: inquiryData.orderGoods,
        orderType: 'inquiry',
        relatedBizNo: inquiryData.inquiryNo,
        deliveryWay: '1',
        bizType: create.inquiryFlowOption?.inquiryType,
        cardNo: '',
        platformCode: 'ZA',
        policyNo: '',
        vasCode: ((inquiryData.userRightsDetail || {}).userRightsDomain || {}).drawVasCode,
        ...inquiryData,
        id: undefined,
        inquiryType: create.inquiryFlowOption?.inquiryType,
      },
    });
    if(res.code === '0') {
      return res.result;
    }
    StaticToast.error(res.message);
    return Promise.reject(res);
  };

  try {
    await fetchInquiryNo();
    await calculate();
    return await unifiedOrder();
  } catch (e) {
    return Promise.reject(e);
  }
};
