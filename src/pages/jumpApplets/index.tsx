import React, { useEffect } from 'react';
// import { Deserialize } from 'src/utils/serialization';
import validate from 'src/utils/validate';
import { JsSdkConfig } from 'src/utils/wechat';
import { Button } from 'zarm';

import './jumpApplets.scss';

const getSchemeConfig = () => {
  let homePage = '/pages/index';
  let webviewPage = '/pages/webview';
  let articleUrl = 'https%3A%2F%2Fmp.weixin.qq.com%2Fs%2FBqxz7xyVsMRNunTm9RBDKQ';
  let consultUrl = 'https%3A%2F%2Fonline.za-doctor.com%2Fhospital%2Fchatmedicalmanage';

  return {
    //去首页
    home: {
      path: `${homePage}?channelSource=CSN32000284`,
      schemeUrl: "weixin://dl/business/?t=gAi9XYBo3Wc"
    },
    //去咨询页
    consult: {
      path: `${webviewPage}?channelSource=CSN32000284&src=${consultUrl}`,
      schemeUrl: "weixin://dl/business/?t=My8awkCn5Yo"
    },
    //去文章页
    article: {
      path: `${webviewPage}?channelSource=CSN32000317&src=${articleUrl}`,
      schemeUrl: "weixin://dl/business/?t=kP0armFduxv"
    },
    //2020.02.03新增

    //咨询页
    CSN35000188: {
      path: `${webviewPage}?channelSource=CSN35000188&src=${consultUrl}`,
      schemeUrl: "weixin://dl/business/?t=8tLIXFg5agp"
    },
    //咨询页
    CSN36000167: {
      path: `${webviewPage}?channelSource=CSN36000167&src=${consultUrl}`,
      schemeUrl: "weixin://dl/business/?t=vOgxw2s6Noa"
    },
    //首页
    CSN35000221: {
      path: `${homePage}?channelSource=CSN35000221`,
      schemeUrl: "weixin://dl/business/?t=B2fAznI6SUh"
    },
    //去首页 02.19日新增
    CSN45500113: {
      path: `${homePage}?channelSource=CSN45500113`,
      schemeUrl: "weixin://dl/business/?t=IFkgJAfsvfk"
    },
    //去文章页
    CSN37500184: {
      path: `${webviewPage}?channelSource=CSN37500184&src=${articleUrl}`,
      schemeUrl: "weixin://dl/business/?t=3BLaVZDSUyu"
    },
    //去文章页
    CSN36000200: {
      path: `${webviewPage}?channelSource=CSN36000200&src=${articleUrl}`,
      schemeUrl: "weixin://dl/business/?t=GJBeDKQJIKc"
    },
    //去文章页
    CSN37500217: {
      path: `${webviewPage}?channelSource=CSN37500217&src=${articleUrl}`,
      schemeUrl: "weixin://dl/business/?t=R68BdLKByPa"
    },
    CSN47500201: {
      path: `/pages/guidance/index?channelSource=CSN47500201&word=6666`,
      schemeUrl: "weixin://dl/business/?t=f1piPvfVtuq"
    },

    //关怀短信需求 用户召回
    wechat_applet: {
      path: `${webviewPage}?channelSource=wechat_applet&src=${consultUrl}`,
      schemeUrl: "weixin://dl/business/?t=K9DZShgfqSj"
    },
    //互医公众号-拉新活动-活动导流
    CSN47500917: {
      path: `${homePage}?channelSource=CSN47500917`,
      schemeUrl: "weixin://dl/business/?t=Gs02VttlQem"
    },

    //生成短信唤起小程序链接
    CSN48500147: {
      path: `${webviewPage}?channelSource=CSN48500147&src=https%3A%2F%2Fmp.weixin.qq.com%2Fs%2F5f3EPioVbKXh9HaQsBTAmg`,
      schemeUrl: "weixin://dl/business/?t=Aczd3x0Unmc"
    },

  }
}
const JumpApplets = (props) => {
  const prefixCls = 'jumpapplets-pages';
  const { match: { params: { source = 'home', } = {} } = {} } = props;
  let isWeixinOrAppletsScenes = validate.isFromWeixin();
  let schemeConfig = getSchemeConfig();
  const currentItem = schemeConfig[source || 'home'] || schemeConfig.home;

  useEffect(() => {
    if (isWeixinOrAppletsScenes) {
      let launchBtn = document.getElementById('launch-btn');
      if (launchBtn) {
        launchBtn.addEventListener('ready', function (e) {
          console.log('开放标签 ready', e);
        })
        launchBtn.addEventListener('launch', function (e) {
          console.log('开放标签 success', e);
        })
        launchBtn.addEventListener('error', function (e) {
          console.log('开放标签 fail', e);
        })
      }
      JsSdkConfig();
    } else {
      window.location.href = currentItem.schemeUrl;
    }
  }, []);

  return (
    <div className={prefixCls}>
      <div>
        <p className={`${prefixCls}__tips`}>正在打开“众安互联网医院”...</p>
        <div style={{ display: isWeixinOrAppletsScenes ? 'block' : '' }}>
          <wx-open-launch-weapp id="launch-btn" username="gh_b915b88f3aa9" path={currentItem.path}>
            <script type="text/wxtag-template">
              <button style={{ width: "220px", height: "45px", textAlign: "center", fontSize: "17px", display: "block", margin: "20px auto 0", padding: " 8px 24px", border: "none", borderRadius: "4px", backgroundColor: "#07c160", color: "#fff" }} className={`za-button za-button--primary za-button--md za-button--radius ${prefixCls}__open`}>打开小程序</button>
            </script>
          </wx-open-launch-weapp>
        </div>
        <a href={currentItem.schemeUrl} style={{ display: isWeixinOrAppletsScenes ? 'none' : '' }}>
          <Button theme='primary' className={`${prefixCls}__open`}>打开小程序</Button>
        </a>
      </div>
    </div>
  )
}

export default JumpApplets;
