/*
 * @date    :2021-02-23
 * @description：唤起小程序
 */
import React, { useCallback, useEffect, useState } from 'react';
import { StaticToast } from 'src/components/common';
import { fetchJson } from 'src/utils/fetch';
import { Deserialize, Serialize } from 'src/utils/serialization';
import validate from 'src/utils/validate';
import { JsSdkConfig } from 'src/utils/wechat';
import { Button } from 'zarm';

import './jumpApplets.scss';
import bridge from 'src/utils/bridge';

interface ICurrentConfig {
  appletEvokeId: string;
  appletPath: string;
  openLink: string;
  openLinkParam: string;
}
// 为处理 微信生成短链的限制后台配置时，把{}改成（），url带入参数时，反解后加入进去，再转回来
const bigParantheses = (stringJson = '') => {
  // prettier-ignore
  // eslint-disable-next-line no-useless-escape
  const res = stringJson.replace(/\(/g, '{').replace(/\)/g, '}').replace(/\'/g, '"');
  let result = {};
  try {
    result = JSON.parse(res) || {};
  } catch (error) {}
  return result;
};
// //转回小括号
const smallParantheses = (stringJson = '') =>
  // prettier-ignore
  // eslint-disable-next-line no-useless-escape
  stringJson.replace(/\{/g, '(').replace(/\}/g, ')').replace(/\"/g, "'")
;
const reverseSolutionSJon = (param, query = {}) => {
  const jsonObj = bigParantheses(param);
  const mergeObj = { ...jsonObj, ...query };
  return smallParantheses(JSON.stringify(mergeObj));
};
const reverseAppletPage = (pagePath = '', appletPathParams = '') => {
  if (appletPathParams) {
    const [path = ''] = pagePath.split('?');
    return `${path}?${appletPathParams}`;
  }
  return pagePath;
};
const OpenApplets = (props) => {
  const prefixCls = 'jumpapplets-pages';
  const {
    match: { params: { id = '' } = {} } = {},
    location: { search = '' },
  } = props;
  const query = (search && Deserialize(search)) || {};
  const [errorMsg, setErrorMsg] = useState('');
  const [currentConfig, setCurrentConfig] = useState<ICurrentConfig>({
    appletEvokeId: '',
    appletPath: '',
    openLink: '',
    openLinkParam: '',
  });
  const [isImg, setIsImg] = useState(false);
  const [imgSrc, setImgSrc] = useState('https://cdn-qcloud.zhongan.com/a00000/za_hospital/imgs/medicineUpgrade/grade200.png');
  const UA = navigator.userAgent.toLowerCase();
  const isWXWork = UA.match(/wxwork/i);
  const isWeixinOrAppletsScenes = !isWXWork && validate.isFromWeixin(); // 微信环境，且不是在企业微信里

  useEffect(() => {
    getSchemeConfig(id);
    if (isWeixinOrAppletsScenes) {
      const launchBtn = document.getElementById('launch-btn');
      if (launchBtn) {
        launchBtn.addEventListener('ready', function(e) {
          console.log('开放标签 ready', e);
        });
        launchBtn.addEventListener('launch', function(e) {
          console.log('开放标签 success', e);
        });
        launchBtn.addEventListener('error', function(e) {
          console.log('开放标签 fail', e);
        });
      }
      JsSdkConfig();
    }
  }, []);

  const generateLinkSave = useCallback(
    (options) => {
      /* url参数带有 zaSign =za，即不走 验签接口，直接动态生成 */
      fetchJson({
        url: `/api/api/v1/patient/link/${query.zaSign === 'za' || query.token ? 'saveShortLink' : 'saveData'}`,
        type: 'POST',
        data: { ...options, linkType: 'personal' },
        needLogin: false,
        isloading: false,
        success: (res) => {
          if (res && res.code === '0') {
            const { openLink, appletPathParams = '' } = res.result || {};
            // console.log(appletPathParams, 'appletPathParams');
            setCurrentConfig((prev) => ({
              ...prev,
              appletPath: reverseAppletPage(prev.appletPath, appletPathParams),
              openLink,
            }));
            autoOpenLink(openLink);
          } else {
            StaticToast.error(res.message);
          }
        },
        error: (e) => {
          StaticToast.error(e.message);
        },
      });
    },
    [currentConfig],
  );
  // 查询是否有生成短链接
  const getUserJumpUrl = useCallback(
    (options) => {
      fetchJson({
        url: '/api/api/v1/patient/user/getUserJumpUrl',
        type: 'POST',
        data: options,
        needLogin: false,
        isloading: false,
        success: (res) => {
          if (res && res.code === '0') {
            if (res.result) {
              setCurrentConfig((prev) => ({
                ...prev,
                openLink: res.result,
              }));
              autoOpenLink(res.result);
              return;
            }
            generateLinkSave(options);
          } else {
            StaticToast.error(res.message);
          }
        },
        error: (e) => {
          StaticToast.error(e.message);
        },
      });
    },
    [currentConfig],
  );
  const autoOpenLink = (openLink) => {
    if (!isWeixinOrAppletsScenes && openLink) {
      // 不是微信环境，尝试打开链接
      window.location.href = openLink;
    }
  };
  const getSchemeConfig = (id = '') => {
    if (!id) {
      setErrorMsg('配置信息出错');
      return;
    }
    fetchJson({
      url: '/api/api/v1/patient/link/queryDetail',
      type: 'POST',
      data: {
        id,
        notWechat:!isWeixinOrAppletsScenes,
      },
      needLogin: false,
      isloading: false,
      success: (res) => {
        if (res && res.code === '0') {
          const {
            appletPathParams = '',
            appletEvokeId = '',
            openLink = '',
            appletPath = '',
            channelSource = '',
            channelResourceCode = '',
          } = res.result || {};
          const appletParams = (appletPathParams && Deserialize(appletPathParams)) || {};
          const { backUrl = '', token = '', loginPhone = '', ...other } = query;

          if ((appletParams.src || appletParams.param) && Object.keys(query).length > 0) {
            appletParams.param = reverseSolutionSJon(appletParams.param || '()', { ...other });
          }

          // 自定义图片配置
          if(appletParams.img === '1') {
            setIsImg(true);
            bridge.setTitle('免费领取200元购药金');
            setImgSrc(appletParams.imgSrc || imgSrc);
            delete appletParams.img;
            delete appletParams.imgSrc;
          }


          const data: any = { channelResourceCode, token, loginPhone,  ...other, ...appletParams };

          const nextSearch = Serialize(data);

          delete data.src;
          delete data.target;

          const openLinkNextSearch = Serialize(data);
          const isParams = appletPath.includes('?');

          const nextAppletPath = `${appletPath}${isParams ? '&' : '?'}${nextSearch}`;
          const openLinkParam = `${openLink}&cq=${encodeURIComponent(openLinkNextSearch)}`;

          console.log(openLinkParam, 'openLinkParam');
          console.log(nextAppletPath, 'nextAppletPath');

          setCurrentConfig({
            appletEvokeId,
            appletPath: nextAppletPath,
            openLink,
            openLinkParam,
          });

          // 如果有动态参数
          // 专门给车险用，没有thirdUserId就会报错，这里限制一下可以让URL带其他参数时不调这个接口
          if (Object.keys(query).length && (query.thirdUserId || query.userId)) {
            const nextParam = Serialize({ channelResourceCode, channelSource, ...appletParams, ...other });
            const options = {
              ...(res.result || {}),
              appletPathParams: nextParam,
              thirdUserId: query.userId,
              token,
              ...other,
              backUrl,
            };
            delete options.userId;
            if (query.userId) {
              // 仅针对 车险、寿险渠道的userId进行判断
              getUserJumpUrl(options);
            } else {
              generateLinkSave(options);
            }
            return;
          } else if (openLinkParam) {
            // 不是微信环境，尝试打开链接
            autoOpenLink(openLinkParam);
          }
        } else {
          setErrorMsg(res.message);
          StaticToast.error(res.message);
        }
      },
      error: (e) => {
        setErrorMsg(e.message);
        StaticToast.error(e.message);
      },
    }).catch((err) => {
      console.log(err, 'err,测试');
    });
  };

  return (
    isImg ? (
      <div>
        {
          !errorMsg && (
            <>
              <div className='fst-ignore' style={{ display: isWeixinOrAppletsScenes ? 'block' : '' }}>
                <wx-open-launch-weapp
                  id='launch-btn'
                  username={currentConfig.appletEvokeId}
                  path={currentConfig.appletPath}>
                  <script type='text/wxtag-template'>
                    <img style={{ width: '100%' }} src={imgSrc} />
                  </script>
                </wx-open-launch-weapp>
              </div>
              <a href={currentConfig.openLinkParam} style={{ display: isWeixinOrAppletsScenes ? 'none' : '' }}>
                <img style={{ width: '100%' }} src={imgSrc} />
              </a>
            </>
          )
        }
      </div>
    ) : (
      <div className={prefixCls}>
        <div className='fst-ignore'>
          <p className={`${prefixCls}__tips`}>{errorMsg ? errorMsg : '正在打开“众安互联网医院”...'}</p>
          {!errorMsg && (
            <>
              <div style={{ display: isWeixinOrAppletsScenes ? 'block' : '' }}>
                <wx-open-launch-weapp
                  id='launch-btn'
                  username={currentConfig.appletEvokeId}
                  path={currentConfig.appletPath}>
                  <script type='text/wxtag-template'>
                    <button
                      style={{
                        width: '220px',
                        height: '45px',
                        textAlign: 'center',
                        fontSize: '17px',
                        display: 'block',
                        margin: '20px auto 0',
                        padding: ' 8px 24px',
                        border: 'none',
                        borderRadius: '4px',
                        backgroundColor: '#07c160', color: '#fff',
                      }}
                      className={`za - button za - button--primary za - button--md za - button--radius ${prefixCls}__open`}
                    >
                        打开小程序
                    </button>
                  </script>
                </wx-open-launch-weapp>
              </div>
              <a href={currentConfig.openLinkParam} style={{ display: isWeixinOrAppletsScenes ? 'none' : '' }}>
                <Button theme='primary' className={`${prefixCls}__open`}>
                    打开小程序
                </Button>
              </a>
            </>
          )}
        </div>
      </div>
    )
  );
};

export default OpenApplets;
