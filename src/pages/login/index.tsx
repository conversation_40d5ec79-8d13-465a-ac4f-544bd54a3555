import React, { useCallback, useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { StaticToast, InputNumber } from 'src/components/common';
import { getChannelResourceCode } from 'src/utils';
import { jumpBeforeAuth } from 'src/utils/auth';
import cookies from 'src/utils/cookie';
import { fetchJson } from 'src/utils/fetch';
import { Deserialize, processLoginData } from 'src/utils/serialization';
// import { bind_doctor } from 'src/store/chatmedicalmanage/action';
import { isFromCAINIAO, isFromAliNOCAINIAO } from 'src/utils/staticData';
import storage from 'src/utils/storage';
import useInterval from 'src/utils/useInterval';
import validate from 'src/utils/validate';
// import { useDispatch } from 'react-redux';
import { Input, Button, Checkbox, Popper } from 'zarm';
import './login.scss';

const Login = (props) => {
  const {
    location: { search = '' },
  } = props;
  const { pathname = '', jumpSearch = '', auth = '', openId = '', toggleAccountChannelResource, loginFrom = '' } = Deserialize(search);
  const [count, setCount] = useState(0);
  const [isRunning, setIsRunning] = useState(false);
  const [mobilePhone, setMobilePhone]: [any, any] = useState('');
  const [verifyCode, setVerifyCode]: [any, any] = useState('');
  const [checked, setChecked]: any = useState(false);
  const [visible, setVisible]: any = useState(false);
  const [employeeNo, setEmployeeNo]: any = useState('');
  const channelResourceCode = toggleAccountChannelResource || getChannelResourceCode();

  /** 是否来自于微保流程 */
  const isLoginFromWeiBao_PolicyNo = loginFrom && loginFrom === 'WEIBAO_POLICYNO';
  const [policyNo, setPolicyNo]: any = useState('');

  useInterval(
    () => {
      setCount(count - 1);
    },
    isRunning ? 1000 : null,
  );

  useEffect(() => {
    count === 0 && setIsRunning(false);
  }, [count]);

  const blur = useCallback(() => {
    window.scroll({
      top: 0,
      behavior: 'smooth',
    });
  }, []);

  const handleCheckBox = (e) => {
    setChecked(e.target.checked);
    setVisible(false);
  };

  const getSendVerifyCode = useCallback(() => {
    if (!mobilePhone) {
      StaticToast.error('手机号码不能为空');
      return;
    }
    if (!validate.isMobile(mobilePhone)) {
      StaticToast.success('您输入的手机号码格式有误');
      return;
    }

    const channelSource = cookies.get('channelSource');
    if (channelSource === 'za_app') {
      StaticToast.warning('众安APP渠道用户暂不支持直接登录，请从APP内跳转进入');
      return;
    }

    if (channelSource === 'CSN02000105' || channelSource === 'CSN07000487') {
      StaticToast.warning('最福利用户暂不支持直接登录，请从APP内跳转进入');
      return;
    }
    const sendVerifyCodeParams: any = {
      loginPhone: mobilePhone,
      channelResourceCode,
      channelSource: cookies.get('channelSource'),
    };
    // 判断是否属于阿里商保菜鸟登录
    if (isFromCAINIAO() || isFromAliNOCAINIAO()) {
      sendVerifyCodeParams.channelApplicationCode = 'ALI_CAI_NIAO';
    }

    fetchJson({
      url: '/api/api/v1/patient/user/sendVerifyCode',
      type: 'POST',
      data: sendVerifyCodeParams,
      isloading: true,
      success: (res) => {
        if (res && res.code === '0') {
          StaticToast.success('短信发送成功');
          setCount(60);
          setIsRunning(true);
        } else {
          StaticToast.error(res.message || '短信发送失败');
        }
      },
      error: (e) => {
        StaticToast.error(e.message);
      },
    });
  }, [mobilePhone]);

  const login = useCallback(() => {
    if (mobilePhone === '') {
      StaticToast.warning('请输入手机号码');
      return;
    }
    if (!validate.isMobile(mobilePhone)) {
      StaticToast.success('您输入的手机号码格式有误');
      return;
    }
    if (verifyCode === '') {
      StaticToast.warning('请输入短信验证码');
      return;
    }
    if ((isFromCAINIAO() || isFromAliNOCAINIAO()) && employeeNo === '') {
      StaticToast.warning('请输入员工号');
      return;
    }
    if (checked === false) {
      setVisible(true);
      return;
    }
    const bindThirdUserParams: any = {
      loginPhone: mobilePhone,
      verificationCode: verifyCode,
      channelResourceCode,
      channelSource: cookies.get('channelSource'),
    };
    if (isFromCAINIAO() || isFromAliNOCAINIAO()) {
      bindThirdUserParams.employeeNo = employeeNo;
      bindThirdUserParams.channelApplicationCode = 'ALI_CAI_NIAO';
    } else {
      bindThirdUserParams.isUnitedLogin = false;
      bindThirdUserParams.openId = openId;
    }

    // 判断是否属于微保来的，需要保单号联合登录
    if (isLoginFromWeiBao_PolicyNo) {
      if (!policyNo) {
        StaticToast.warning('请输入保单号');
        return;
      }
      // 校验保单号对应的权益是否存在
      bindThirdUserParams.isCheckPolicyVas = true;
      bindThirdUserParams.vasCode = 'CancerScreening';
      bindThirdUserParams.policyNo = policyNo;
    }

    // isUnitedLogin 后端会默认为true， 所以如果不是联合登陆的情况下isUnitedLogin要带一个false过去
    fetchJson({
      url: '/api/api/v1/patient/user/bindThirdUser',
      type: 'POST',
      data: bindThirdUserParams,
      isloading: true,
      needLogin: false,
      success: (res) => {
        const { result = {} } = res;
        processLoginData(result);
        console.log(res);
        const { bizMap: { isFirstDrawTrialServpack = 'N', policyNo = '' } = {} } = result;
        storage.set('isFirstDrawTrialServpack', isFirstDrawTrialServpack);
        cookies.set('channelResourceCode', channelResourceCode);
        if (auth) {
          jumpBeforeAuth(JSON.parse(decodeURIComponent(auth)));
          return;
        }
        if (!pathname) {
          props.history.replace({
            pathname: '/hospital/home',
            search: `channelResourceCode=${channelResourceCode}`,
          });
        } else {
          let paredJumpSearch = decodeURIComponent(jumpSearch);
          console.log({
            pathname: decodeURIComponent(pathname),
            search: paredJumpSearch,
          });
          if (isLoginFromWeiBao_PolicyNo) {
            paredJumpSearch += `${paredJumpSearch}&policyNo=${policyNo}`;
          }
          props.history.replace({
            pathname: decodeURIComponent(pathname),
            search: paredJumpSearch,
          });
        }
      },
      error: (error) => {
        StaticToast.error(error.message);
      },
    });
  }, [mobilePhone, verifyCode, employeeNo, checked, visible, policyNo]);

  const getLoginBtnText = () => {
    if (isLoginFromWeiBao_PolicyNo) {
      return '登录并领取';
    }
    return '登录';
  };

  const getSubTitle = () => {
    if (isLoginFromWeiBao_PolicyNo) {
      return '增值服务快速领取使用';
    }
    return '';
  };

  return (
    <div className='login_page'>
      <p className='title'>众安互联网医院</p>
      <p className='sub_title'>{getSubTitle()}</p>
      {
        isLoginFromWeiBao_PolicyNo && (
          <div className='telephone_wrapper'><Input className='opt_input' placeholder='请输入保单号' value={policyNo} onChange={(value) => setPolicyNo(value)} onBlur={() => blur()} /></div>
        )
      }
      <div className='telephone_wrapper'>
        <InputNumber type='tel' className='opt_input' placeholder='请输入11位手机号码' maxLength={11} value={mobilePhone} onChange={(value) => setMobilePhone(value)} onBlur={() => blur()} />
        {count === 0 ? (
          <div className='code' onClick={() => getSendVerifyCode()}>
            发送验证码
          </div>
        ) : (
          <div className='code'>{count}s</div>
        )}
      </div>
      <div className='telephone_wrapper'>
        <InputNumber type='number' className='opt_input' placeholder='请输入手机验证码' value={verifyCode} onChange={(value) => setVerifyCode(value?.toString().slice(0, 6))} onBlur={() => blur()} />
      </div>

      {
        (isFromCAINIAO() || isFromAliNOCAINIAO()) && (
          <InputNumber type='number' className='opt_input' placeholder='请输入阿里员工号' value={employeeNo} onChange={(value) => setEmployeeNo(value?.toString())} onBlur={() => blur()} />
        )
      }

      <Button theme='primary' className='login' onClick={() => login()}>
        {getLoginBtnText()}
      </Button>

      <div className='tips'>
        <p style={{ marginRight: 5 }}>
          <Popper visible={visible} trigger='manual' className='custom-arrow-content' direction='topLeft' content='请先勾选同意'>
            <Checkbox
              id='agreement'
              checked={checked}
              onChange={(e) => {
                handleCheckBox(e);
              }}
            />
          </Popper>
        </p>
        <p>
          我已阅读并同意
          <span>
            <Link to={{ pathname: '/hospital/agreement' }}>《众安用户注册协议》</Link>
          </span>
          和
          <span>
            <Link to={{ pathname: '/hospital/private' }}>《个人隐私政策协议》</Link>
          </span>
          ，未注册绑定的手机号验证成功后将自动注册。
        </p>
      </div>
      <Input style={{ display: 'none' }} />
    </div>
  );
};

export default Login;
