@import "src/style/index";

.login_page {
  padding: 0 r(20);
  min-height: 100vh;
  background: #fff;

  .title {
    padding: r(49) 0 r(27);
    color: #464646;
    font-size: r(27);
    font-weight: 600;
    text-align: center;
  }

  .sub_title {
    margin: r(-20) 0 r(20);
    color: #00bc70;
    font-size: r(15);
    text-align: center;
  }

  .telephone_wrapper {
    position: relative;
    @include display-flex;
    @include align-items(center);

    margin-bottom: r(11);
    background: #fbfbfb;

    .code {
      width: r(80);
      height: r(30);
      border-radius: r(4);
      border: r(1) solid #ccc;
      text-align: center;
      line-height: r(28);
      color: #4a4a4a;
      font-size: r(13);
      margin-right: r(10);

      &:active {
        background: #ccc;
        color: #fff;
      }
    }
  }

  .opt_input {
    background: #fbfbfb;
    padding: r(11) r(13);
    font-size: r(15);
    flex: 1;

    input {
      background: inherit;
      height: 100%;
    }
  }

  .login {
    margin: r(20) 0 r(11);
    height: r(44);
    width: 100%;
    border-radius: r(4);
    text-align: center;
    color: #fff;
    font-size: r(16);
    font-weight: 600;
    line-height: r(44);
    background: var(--theme-primary);

    &:active {
      opacity: 0.8;
    }
  }

  .tips {
    color: #666;
    font-size: r(12);
    line-height: r(19);
    margin-top: r(25);
    @include display-flex;
    @include align-items(top);

    span {
      color: var(--theme-primary);
      border-radius: 50%;
    }

    a {
      transition: opacity 0.3 ease-in;

      &:active {
        opacity: 0.3;
      }
    }
  }
}

.popper-page .basic-demo .za-cell__content {
  padding: 20px 0;
  justify-content: center;
}

.custom-content,
.custom-arrow-content {
  padding: 2px 4px;
  background: rgba(0, 0, 0, 0.6);
  font-size: 10px;
  font-family: PingFangSC-Medium, PingFang SC;
  font-weight: 500;
  color: #fff;
  margin-top: 8px;
}

.custom-arrow-content .za-popper__arrow {
  position: absolute;
  width: 0;
  height: 0;
  border-color: transparent;
  border-style: solid;
}

.custom-arrow-content[x-placement^=top] {
  margin-bottom: 10px;
  border-radius: 18px 18px 18px 0;
}

.custom-arrow-content[x-placement^=top] .za-popper__arrow {
  bottom: -4px;
  border-width: 4px 4px 0;
  border-top-color: #ddd;
}

.custom-arrow-content[x-placement^=right] {
  margin-left: 10px;
}

.custom-arrow-content[x-placement^=right] .za-popper__arrow {
  left: -4px;
  border-width: 4px 4px 4px 0;
  border-right-color: #ddd;
}

.custom-arrow-content[x-placement^=bottom] {
  margin-top: 10px;
}

.custom-arrow-content[x-placement^=bottom] .za-popper__arrow {
  top: -4px;
  border-width: 0 4px 4px;
  border-bottom-color: #ddd;
}

.custom-arrow-content[x-placement^=left] {
  margin-right: 10px;
}

.custom-arrow-content[x-placement^=left] .za-popper__arrow {
  right: -4px;
  border-width: 4px 0 4px 4px;
  border-left-color: #ddd;
}

.popper-page .direction-demo .za-button {
  width: 60px;
  margin-right: 10px;
  margin-bottom: 10px;
}

.popper-page .direction-demo .za-cell__content {
  padding: 15px 0;
  justify-content: center;
}
