import React from 'react';
import { Carousel } from 'zarm';
import { ShopInfo } from 'src/store/massageTherapy/type';

import './index.scss';

function ShopIntro({ shopInfo }: { shopInfo: ShopInfo }) {
  const { shopImageList = [], shopName = '', districtName = '', districtAddress = '', shopDesc = '', shopStatus, businessDate, businessHours } = shopInfo;
  return (
    <div className="massage_therapy_service_intro_comp">
      {
        shopImageList.length 
          ? shopImageList.length > 1 
            ? (
              <Carousel autoPlay loop showPagination={false}>
                {shopImageList.map((src, i) => (
                  <div key={i} className="main_picture">{<img src={src} alt="" className="main_picture__img" />}</div>
                ))}
              </Carousel>
            )
            : <div className="main_picture">{<img src={shopImageList[0]} alt="" className="main_picture__img" />}</div>
          : <></>
      }
      <div className="service_info">
        <h2 className="service_info__title">{shopName}</h2>
        <div className="service_info__addr">
          <p className="service_info__addr--strong">{`${districtName}${districtAddress}`}</p>
          <p className="service_info__addr--weak">{shopDesc}</p>
        </div>
        <p className="business_info">
          <span className="business_info__status">{shopStatus}</span>
          <span className="business_info__time">
            {businessDate} {businessHours}
          </span>
        </p>
      </div>
    </div>
  );
}

export default ShopIntro;
