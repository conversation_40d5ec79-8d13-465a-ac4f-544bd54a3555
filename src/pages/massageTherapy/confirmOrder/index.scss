@import 'src/style/index';

.massagetherapy_order_confirm_page {
  .header_address {
    position: relative;
    min-height: r(74);
    padding: r(15) r(15) r(20);
    background: #fff;
    @include display-flex;
    @include align-items(center);

    color: #333;

    .icon_address {
      width: r(18);
      height: r(18);
      margin-right: r(4);
    }

    .bottom_line {
      position: absolute;
      bottom: 0;
      left: 0;
      right: 0;
    }
  }

  .address_info {
    position: relative;
    padding-right: r(50);
    font-size: r(16);
    font-weight: bold;
    @include flex(1);

    .address_text {
      margin-top: r(5);
      font-size: r(15);
      color: #666;
      font-weight: normal;
    }

    .icon_narrow {
      position: absolute;
      right: r(0);
      top: 50%;
      transform: translateY(-50%);
      font-size: r(15);
      color: #d8d8d8;
    }
  }

  .product_wrap {
    margin-top: r(10);
    background: #fff;

    .product_title {
      padding: r(12) 0 r(10) r(15);
      font-size: r(13);
      font-weight: bold;
      color: #333;
    }

    .product_item {
      padding: r(17) r(15);
      background: #fbfbfb;
      @include display-flex;

      .product_img {
        width: r(44);
        height: r(44);
        margin-right: r(10);

        img {
          width: 100%;
          height: 100%;
          border: r(1) solid #e6e6e6;
          border-radius: r(8);
        }
      }

      .product_info {
        flex: 1;
        font-size: r(15);
        color: #1e1e1e;
        @include display-flex;
        @include justify-content(space-between);

        .product_time {
          margin-top: r(5);
          font-size: r(13);
          line-height: r(19);
          color: #999;
        }
      }

      .product_amount {
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        align-items: flex-end;

        .product_price {
          font-size: r(12);
          font-weight: bold;
          color: #1e1e1e;
        }

        .product_num {
          font-size: r(12);
          color: #999;
        }
      }
    }

    .subtotal {
      padding: r(15) r(15) r(10);
      font-size: r(14);
      color: #666;
      text-align: right;

      .subtotal_price {
        color: #1e1e1e;
        font-size: r(14);

        .sign,
        .integer,
        .point {
          font-weight: 600;
          font-size: r(12);
          color: #ff5050;
        }

        .integer {
          font-size: r(16);
        }
      }
    }
  }

  .fixed_footer {
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100vw;
    padding: r(12) r(15) r(27);
    font-size: r(14);
    color: #333;
    background: #fff;
    @include display-flex;
    @include align-items(center);
    @include justify-content(space-between);

    .footer_text {
      vertical-align: 2px;
    }

    .footer_price {
      font-weight: bold;

      .sign,
      .integer,
      .point {
        font-size: r(16);
        color: #ff5050;
      }

      .integer {
        font-size: r(21);
      }
    }

    .btn_buy {
      height: r(40);
      width: r(114);
      box-sizing: border-box;
      font-size: r(17);
      font-weight: bold;

      &.btn_cancel {
        margin-right: r(12);
        color: var(--text-base-color);
        border: 1px solid var(--theme-primary);
      }
    }

    .btn_pay,
    .btn_cancel {
      height: r(32);
      width: r(94);
      font-size: r(14);
    }
  }
}
