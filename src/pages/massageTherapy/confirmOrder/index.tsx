import React, { useCallback, useEffect } from 'react';
import { shallowEqual, useSelector } from 'react-redux';
import { Button } from 'zarm';
import dayjs from 'dayjs';
import { ApplicationState } from 'src/store';
import { Deserialize, fetchJson, throttle } from 'src/utils';

import './index.scss';
import DashColorLine from 'src/pages/shoppingCart/components/DashColorLine';
import { FormatPrice, StaticToast } from 'src/components/common';
import { commonPay } from 'src/utils/pay';

let bizNo = '';

const OrderConfirm = (props) => {
  const {
    location: { search = '' },
  } = props;
  const { appointmentId, appointmentDate, appointmentTime, patientName, patientId } = Deserialize(decodeURIComponent(search));

  const { shopInfo, productInfo } = useSelector((state: ApplicationState) => {
    const { shopInfo, productInfo } = state.massageTherapy;
    return { shopInfo, productInfo };
  }, shallowEqual);

  // 获取下单所需的业务号
  const getBizNo = () => {
    return fetchJson({
      type: 'POST',
      url: '/api/api/v1/patient/bizno/getByType',
      data: { bizNoType: 'APPOINTMENT_NO' },
      isloading: true,
    }).then((res) => {
      if (+res.code === 0) {
        bizNo = res.result;
      }
    });
  };

  useEffect(() => {
    if (shopInfo.id <= 0 || productInfo.id <= 0) {
      StaticToast.error('订单信息有误');
    }
    getBizNo();
  }, []);

  const unifiedOrder = throttle(
    useCallback(() => {
      fetchJson({
        type: 'POST',
        url: '/api/api/v1/patient/order/unifiedOrder',
        data: {
          needSaveOrderPayInfo: true,
          orderAmount: productInfo.productPrice,
          orderRealAmount: productInfo.productRealPrice,
          orderType: 'physiotherapy',
          platformCode: 'ZA',
          businessEntry: {
            bizNo: productInfo.productCode,
            appointmentPatientId: patientId,
            appointmentPatientName: patientName,
            appointmentScheduleId: appointmentId,
          },
          orderGoods: [
            {
              goodsId: productInfo.id,
              goodsCode: productInfo.productCode,
              goodsName: productInfo.productName,
              goodsNum: 1,
              goodsPrice: productInfo.productPrice,
              goodsRealPrice: productInfo.productRealPrice,
            },
          ],
          relatedBizNo: bizNo,
        },
        isloading: true,
      }).then((res) => {
        const { result = {}, code } = res || {};
        if (code === '0') {
          commonPay({
            orderType: result.orderType,
            orderRealAmount: result.orderRealAmount,
            orderNo: result.orderNo,
            orderTime: result.orderTime,
            returnUrl: `${location.origin}/hospital/massageTherapy/success`
          })
        }
      });
    }, []),
    500,
  );
  let { attachmentLists = [] }=productInfo;
  let [ attachmentItem ] = attachmentLists;

  return (
    <div className="massagetherapy_order_confirm_page">
      <div className="header_address">
        <img className="icon_address" src={require('src/pages/inquiryForm/images/icon_address.svg')} />
        <div className="address_info">
          <React.Fragment>
            <p>{shopInfo.shopName}</p>
            <p className="address_text">{`${shopInfo.districtName}${shopInfo.districtAddress}`}</p>
          </React.Fragment>
        </div>
        <div className="bottom_line">
          <DashColorLine />
        </div>
      </div>

      <div className="product_wrap">
        <p className="product_title">商品详情</p>
        <div className="product_item">
          <div className="product_img">
            <img src={productInfo.productImage || attachmentItem?.attachmentDownloadUrl} alt="" />
          </div>
          <div className="product_info">
            <div>
              <p>{productInfo.productName}</p>
              <p className="product_time">{dayjs(appointmentDate).format('MM月DD日')} {appointmentTime} {productInfo.duration}分钟</p>
            </div>

            <div className="product_amount">
              <p className="product_price">
                <FormatPrice price={productInfo.productRealPrice || 0} />
              </p>
              <p className="product_num">x1</p>
            </div>
          </div>
        </div>
        <div className="subtotal">
          <p className="subtotal_price">
            共1件商品 小计：
            <FormatPrice price={productInfo.productRealPrice || 0} />
          </p>
        </div>
      </div>

      <div className="fixed_footer">
        <p>
          <span className="footer_text">合计：</span>
          <span className="footer_price">
            <FormatPrice price={productInfo.productRealPrice || 0} />
          </span>
        </p>
        <Button className="btn_buy" theme="primary" shape="round" onClick={unifiedOrder}>
          立即支付
        </Button>
      </div>
    </div>
  );
};

export default OrderConfirm;
