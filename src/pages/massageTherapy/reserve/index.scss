@import 'src/style/index';

.reserve_page {
  background-color: #f5f5f5;
  padding-bottom: r(150);

  .service {
    padding: r(15);
    background-color: #fff;
    color: #333;
    margin-bottom: r(10);

    .service_item {
      display: flex;
      justify-content: space-between;
      background-color: #fff;

      &__picture {
        width: r(75);
        height: r(75);
        margin-right: r(10);

        img {
          width: 100%;
          height: 100%;
          border-radius: r(5);
        }
      }

      &__info {
        flex: 1;

        &__title {
          font-size: r(14);
          line-height: r(20);
          margin-bottom: r(8);
          @include line(2);
        }

        &__intro {
          font-size: r(13);
          line-height: r(19);
          color: #999;
        }
      }
    }

    .label {
      margin-top: r(15);
      font-size: r(15);
      line-height: r(21);
      font-weight: 500;

      &.emphasis {
        &::before {
          content: '*';
          color: #ff3b30;
          margin-right: r(5);
        }
      }
    }

    .service_time {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .value {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .not_select {
          color: var(--theme-primary);
        }

        .za-icon {
          color: #d6d6d6;
        }
      }
    }

    .service_users {
      .label {
        width: 100%;

        .za-icon {
          margin-left: r(5);
          color: #e0e0e0;
        }
      }

      &__wrapper {
        width: 100%;
        display: flex;
        flex-wrap: wrap;

        &__item {
          padding: 0 r(10);
          width: calc((100% - r(24)) / 3);
          height: r(32);
          line-height: r(30);
          margin: r(12) r(12) 0 0;
          text-align: center;
          border: r(1) solid rgba($color: #000, $alpha: 0.1);
          border-radius: r(4);
          background-color: #f5f5f5;
          @include line(1);

          &.selected {
            color: var(--theme-primary);
            border-color: rgba(0, 183, 109, 0.5);
            background-color: rgba(0, 183, 109, 0.06);
          }

          &:nth-child(3n),
          &:last-child {
            margin-right: 0;
          }

          .za-icon {
            vertical-align: sub;
          }
        }
      }
    }
  }

  .detail {
    padding: r(15);
    background-color: #fff;
    color: #333;

    .group_title {
      display: flex;
      align-items: center;
      height: r(21);

      &__icon {
        width: r(18);
        height: r(18);
        margin-right: r(6);
      }

      &__text {
        font-size: r(15);
        font-weight: 500;
        line-height: r(21);
      }
    }

    .group_content {
      font-size: r(13);
      padding: r(10) 0;

      .service_flow {
        width: 100%;
        height: r(65);
        margin: r(5) 0;
      }

      .intro_item {
        padding: r(10) 0;
        line-height: r(19);

        &:not(:last-child) {
          box-shadow: 0 r(1) 0 0 rgba(0, 0, 0, 0.1);
        }

        &__title {
          font-size: r(13);
          margin-bottom: r(5);
          color: #999;
        }

        &__content {
          white-space: break-spaces;
        }
      }
    }
  }

  .submit {
    position: fixed;
    bottom: 0;
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: r(10) r(15);
    padding-bottom: calc(env(safe-area-inset-bottom) + r(10));
    background-color: #fff;
    border-top: r(1) solid #e6e6e6;

    .price {
      display: flex;
      justify-content: space-between;
      align-items: center;

      &__sale {
        font-size: r(21);
        font-weight: 500;
        color: #ff5050;
        margin-right: r(7);
      }

      &__origin {
        font-size: r(13);
        color: #999;
        text-decoration: line-through;
      }
    }

    .button {
      width: r(150);
      height: r(44);
      font-size: r(17);
      font-weight: 600;
    }
  }

  .za-popup {
    width: r(300) !important;

    .za-modal__header {
      padding-top: r(20);

      &__title {
        font-size: r(17);
        line-height: r(24);
        font-weight: 600;
        color: #1e1e1e;
      }
    }

    .za-modal__body {
      padding: r(10) r(20) r(20);
      font-size: r(14);
      line-height: r(20);
      font-weight: normal;
      text-align: center;
    }

    .za-modal__footer {
      width: 100%;
      padding: r(11) 0;
      border-top: r(1) solid #e6e6e6;
      line-height: r(23);
      font-size: r(16);
      text-align: center;
      color: var(--text-base-color);

      .footer_btn {
        width: 100%;
      }
    }
  }

  .reserve_time_popup {
    .za-popup {
      width: 100% !important;

      .popup_box {
        width: 100%;
        border-radius: r(8) r(8) 0 0;
        background-color: #fff;

        &__header {
          display: flex;
          align-items: center;
          line-height: r(50);
          border-bottom: r(1) solid #e6e6e6;

          &__title {
            width: 100%;
            margin-right: r(-38);
            font-size: r(16);
            font-weight: 600;
            text-align: center;
            color: #1e1e1e;
          }

          &__close {
            color: #d8d8d8;
            font-size: r(23);
            margin-right: r(15);
          }
        }

        &__body {
          max-height: r(420);
          display: flex;
          overflow-y: scroll;

          &__date {
            flex-shrink: 0;
            width: r(135);
            line-height: r(40);
            font-size: r(13);
            text-align: center;
            color: #666;
            background-color: #f8f8f8;
            overflow-y: scroll;

            &__item.active {
              font-weight: 600;
              color: var(--text-base-color);
              background-color: #fff;
            }
          }

          &__time {
            flex-grow: 1;
            padding: r(10) r(15);
            background-color: #fff;
            overflow-y: scroll;

            &__item {
              display: inline-block;
              width: calc(50% - r(5));
              height: r(30);
              line-height: r(30);
              margin-bottom: r(10);
              text-align: center;
              border-radius: r(4);
              border: r(1) solid rgba(0, 0, 0, 0.2);

              &:nth-child(2n + 1) {
                margin-right: r(10);
              }

              &.active {
                font-weight: 600;
                color: var(--text-base-color);
                background-color: rgba(0, 183, 109, 0.06);
                border: r(1) solid rgba(0, 183, 109, 0.5);
              }

              &.disabled {
                border: none;
                color: #999;
                background-color: #f5f5f5;
              }
            }
          }
        }

        &__footer {
          width: 100%;
          padding: r(10) r(15);
          padding-bottom: calc(env(safe-area-inset-bottom) + r(10));
          background-color: #fff;
          border-top: r(1) solid #e6e6e6;
        }
      }
    }
  }
}
