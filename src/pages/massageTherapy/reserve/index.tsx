import React, { useState, useRef, useEffect, useCallback } from 'react';
import { useSelector, shallowEqual, useDispatch } from 'react-redux';
import { Link } from 'react-router-dom';
import { Icon, Button, Modal, Popup } from 'zarm';
import classnames from 'classnames';
import { ApplicationState } from 'src/store';
import { ProductInfo } from 'src/store/massageTherapy/type';
import { fetch_patients_list } from 'src/store/patients/action';
import { fetchJson } from 'src/utils';
import format from 'src/utils/format';
import { Serialize } from 'src/utils/serialization';

import { StaticToast } from 'src/components/common';
// import { ShopIntro } from '../components';

import './index.scss';

function ServiceItem({ serviceInfo }: { serviceInfo: ProductInfo }) {
  const { productImage, productName, duration, remark, attachmentLists = []  } = serviceInfo;
  let [ attachmentItem ] = attachmentLists;
  return (
    <div className="service_item">
      <div className="service_item__picture">
        <img src={productImage || attachmentItem?.attachmentDownloadUrl} alt="" className="service_item__picture__img" />
      </div>
      <div className="service_item__info">
        <p className="service_item__info__title">{productName}</p>
        <p className="service_item__info__intro">
          <span>
            {duration}分钟{remark ? ` | ${remark}` : ''}
          </span>
        </p>
      </div>
    </div>
  );
}

export interface BookingScheduleItemOptions {
  id?: string | number;
  enableAppointCount?: number;
  enableAppointDate?: string;
  enableAppointEndTime?: string;
  enableAppointStartTime?: string;
  remainAppointCount?: number;
  serviceDuration?: number;
  staffId?: number;
  staffName?: string;
  staffType?: string;
  children?: BookingScheduleItemOptions[];
  [key: string]: string | number | object | boolean | undefined;
}
export interface Patient {
  id?: string | number;
  accountId?: string | number;
  patientName?: string;
}

const extraInfoCodeMapping = {
  serviceContent: '服务内容',
  appointPart: '针对部位',
  tabooNotice: '禁忌提示',
  refundRule: '退款规则',
  useRule: '使用规则',
};

// let bizNo = '';

function ReservePage(props) {
  const mountPoint: any = useRef(undefined);
  const [tipsVisible, setTipsVisible] = useState(false);
  const [popupVisible, setPopupVisible] = useState(false);
  const [bookingScheduleList, setBookingScheduleList] = useState<BookingScheduleItemOptions[]>([]);
  const [selectedBooking, setSelectedBooking] = useState<BookingScheduleItemOptions>({});
  const [selectedChildrenBooking, setSelectedChildrenBooking] = useState<BookingScheduleItemOptions>({});
  const [selectedPatient, setSelectedPatient] = useState<Patient>({});

  const {
    patientsList = [],
    // shopInfo,
    productInfo,
  } = useSelector((state: ApplicationState) => {
    const { patientsList = [] } = state.patients;
    const { shopInfo, productInfo } = state.massageTherapy;
    return {
      patientsList,
      shopInfo,
      productInfo,
    };
  }, shallowEqual);
  const dispatch = useDispatch();
  const fetchPatientsList = (onSuccess) =>
    dispatch(
      fetch_patients_list(
        {
          option: {
            needHealthInfo: true,
          },
        },
        onSuccess,
      ),
    );

  useEffect(() => {
    fetchPatientsList((patientsList) => {
      if (patientsList.length) {
        // let patient: any = null;
        console.log({ patientsList });
      }
    });
    getScheduleList();
    pageScrollTop();
  }, [productInfo]);

  // 获取排班时间
  const getScheduleList = () => {
    return fetchJson({
      type: 'POST',
      url: '/api/api/v1/patient/appointment/schedule/list',
      data: { staffType: 'physiotherapy', days: 7 },
      isloading: true,
    }).then((res) => {
      const { code = '', result = [] } = res;
      if (code === '0') {
        const bookingSchedule = result.reduce((prev, cur) => {
          let key = cur.enableAppointDate;
          cur.startTime = format.date(cur.enableAppointStartTime, 'hh:mm');
          cur.endTime = format.date(cur.enableAppointEndTime, 'hh:mm');
          if (prev.hasOwnProperty(key)) {
            prev[key].children.push(cur);
          } else {
            prev[key] = cur;
            prev[key].week = `周${'日一二三四五六'.charAt(new Date(cur.enableAppointDate.replace(/-/g, '/')).getDay())}`;
            prev[key].children = [cur];
          }
          return prev;
        }, {});
        setBookingScheduleList(Object.keys(bookingSchedule).map((k) => bookingSchedule[k]));
      }
    });
  };

  // 预约时间
  const openPopup = useCallback(() => {
    if (!bookingScheduleList.length) {
      StaticToast.error('抱歉，暂无可预约时间');
      return;
    }
    setSelectedBooking(bookingScheduleList[0] || {});
    setPopupVisible(true);
  }, [bookingScheduleList]);

  // 选取时间
  const confimSelected = useCallback(() => {
    if (!selectedChildrenBooking.id) {
      StaticToast.error('请选择预约的时间');
      return;
    }
    fetchJson({
      type: 'GET',
      url: '/api/api/v1/patient/appointment/schedule/isAvailable',
      data: { scheduleId: selectedChildrenBooking.id, productCode: productInfo.productCode, type: 'physiotherapy' },
      isloading: true,
    }).then((res) => {
      const { code = '', result = false } = res;
      if (code === '0') {
        if (!result) {
          setSelectedChildrenBooking({});
          StaticToast.error('该时间段已被其他用户预约，请重新选择');
          return;
        }
        setPopupVisible(false);
        return;
      } else {
        setSelectedChildrenBooking({});
      }
      StaticToast.error(res.message);
    });
  }, [selectedChildrenBooking]);

  // 选择使用者
  const selectPatient = useCallback(
    (patient) => {
      setSelectedPatient(patient);
    },
    [patientsList],
  );

  // 立即预约事件处理
  const submitHandle = async () => {
    if (!selectedChildrenBooking.id) {
      StaticToast.error('请选择预约的时间');
      return;
    }
    if (!selectedPatient.id) {
      StaticToast.error('请选择使用者');
      return;
    }
    props.history.push({
      pathname: '/hospital/massageTherapy/orderConfirm',
      search: encodeURIComponent(
        Serialize({
          appointmentId: selectedChildrenBooking.id,
          appointmentDate: selectedChildrenBooking.enableAppointDate,
          appointmentTime: selectedChildrenBooking.startTime,
          patientName: selectedPatient.patientName,
          patientId: selectedPatient.id,
        }),
      ),
    });
  };

  const pageScrollTop = useCallback(() => {
    window.scroll({ top: 0, behavior: 'smooth' });
  }, []);

  let { children = [] } = selectedBooking;
  return (
    <div className="reserve_page">
      {/* <ShopIntro shopInfo={shopInfo} /> */}
      <div className="service">
        <ServiceItem serviceInfo={productInfo} />
        <div className="service_time">
          <div className="label emphasis">预约时间</div>
          <div onClick={() => openPopup()} className="value">
            {selectedChildrenBooking.id ? <p className="">{`${selectedChildrenBooking.enableAppointDate} ${selectedChildrenBooking.startTime}`}</p> : <p className="not_select">请选择时间</p>}
            <Icon type="arrow-right" size="sm"></Icon>
          </div>
        </div>
        <div className="service_users">
          <div className="label emphasis">
            请选择使用者<Icon onClick={() => setTipsVisible(true)} type="question-round-fill" size="sm"></Icon>
          </div>
          <div className="service_users__wrapper">
            {patientsList.map((p) => (
              <div
                key={p.id}
                onClick={() => {
                  selectPatient(p);
                }}
                className={classnames('service_users__wrapper__item', { selected: p.id === selectedPatient.id })}
              >
                {p.patientName}
              </div>
            ))}
            <Link className="service_users__wrapper__item" to={{ pathname: '/hospital/editpatient/baseinfo' }}>
              <div>
                <Icon type="add" size="sm" />
                新增
              </div>
            </Link>
          </div>
        </div>
      </div>
      <div className="detail">
        <div className="group_title">
          <img className="group_title__icon" src={require('./images/sylc_icon.png')} alt="icon" />
          <p className="group_title__text">使用流程</p>
        </div>
        <div className="group_content">
          <img className="service_flow" src={require('./images/sylc.png')} alt="使用流程" />
        </div>
        <div className="group_title">
          <img className="group_title__icon" src={require('./images/fwxq_icon.png')} alt="icon" />
          <p className="group_title__text">服务详情</p>
        </div>
        <div className="group_content">
          {productInfo.extInfoList.map((ext) => (
            <div key={ext.extraInfoCode} className="intro_item">
              <p className="intro_item__title">{extraInfoCodeMapping[ext.extraInfoCode] || ''}</p>
              <p className="intro_item__content">{ext.extraInfoValue || ''}</p>
            </div>
          ))}
        </div>
      </div>
      <div className="submit">
        <div className="price">
          <div className="price__sale">¥{Number(productInfo.productRealPrice).toFixed(2)}</div>
          <div className="price__origin">¥{Number(productInfo.productPrice).toFixed(2)}</div>
        </div>
        <div className="button">
          <Button onClick={() => submitHandle()} block shape="round" theme="primary">
            立即预约
          </Button>
        </div>
      </div>
      <div ref={mountPoint} id="mount_point" style={{ position: 'relative', zIndex: 1 }} />
      <Modal
        title="提示"
        visible={tipsVisible}
        mountContainer={() => mountPoint.current}
        footer={
          <p onClick={() => setTipsVisible(false)} className="footer_btn">
            我知道了
          </p>
        }
      >
        <p>本店提供专业的中医理疗服务，可能涉及专业的中医诊断治疗，对于不同个体都有差异，因此需要提前明确使用者，避免影响医生理疗。</p>
      </Modal>
      <Popup className="reserve_time_popup" visible={popupVisible} direction="bottom" afterOpen={() => console.log('打开')} afterClose={() => console.log('关闭')} destroy={false} mountContainer={() => mountPoint.current}>
        <div className="popup_box">
          <div className="popup_box__header">
            <p className="popup_box__header__title">预约时间</p>
            <Icon onClick={() => setPopupVisible(false)} className="popup_box__header__close" type="wrong"></Icon>
          </div>
          <div className="popup_box__body">
            <div className="popup_box__body__date">
              {bookingScheduleList.map((item, i) => (
                <div
                  key={`${item.id}${i}`}
                  onClick={() => {
                    setSelectedBooking(item);
                    setSelectedChildrenBooking({});
                  }}
                  className={classnames('popup_box__body__date__item', { active: item.id == selectedBooking.id })}
                >
                  {item.enableAppointDate} {item.week}
                </div>
              ))}
            </div>
            <div className="popup_box__body__time">
              {children.map((k, index) => {
                const isDisabled = !k.remainAppointCount || k.remainAppointCount <= 0;
                return (
                  <div key={`children${k.id}${index}`} onClick={() => !isDisabled && setSelectedChildrenBooking(k)} className={classnames('popup_box__body__time__item', { active: !isDisabled && k.id == selectedChildrenBooking.id, disabled: isDisabled })}>
                    {k.startTime}
                  </div>
                );
              })}
            </div>
          </div>
          <div className="popup_box__footer">
            <Button onClick={confimSelected} block shape="round" theme="primary">
              确认
            </Button>
          </div>
        </div>
      </Popup>
    </div>
  );
}

export default ReservePage;
