import { routersTypes } from 'src/pages/routes';
const route: routersTypes[] = [
  {
    path: '/hospital/massageTherapy/serviceList',
    name: 'ServiceList',
    component: () => import(/* webpackPrefetchPlaceHolder */ './serviceList'),
    auth: true,
    exact: true,
    realAuth: false,
    title: '中医理疗',
  },
  {
    path: '/hospital/massageTherapy/reserve',
    name: 'Reserve',
    component: () => import(/* webpackPrefetchPlaceHolder */ './reserve'),
    auth: true,
    exact: true,
    realAuth: true,
    title: '中医理疗',
  },
  {
    path: '/hospital/massageTherapy/orderConfirm',
    name: 'ConfirmOrder',
    component: () => import(/* webpackPrefetchPlaceHolder */ './confirmOrder'),
    auth: true,
    exact: true,
    realAuth: true,
    title: '确认订单',
  },
  {
    path: '/hospital/massageTherapy/success',
    name: 'Success',
    component: () => import(/* webpackPrefetchPlaceHolder */ './success'),
    auth: true,
    exact: true,
    realAuth: true,
    title: '中医理疗',
  },
];
export default route;

