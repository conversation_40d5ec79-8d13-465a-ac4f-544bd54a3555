@import 'src/style/index';

.massagetherapy_service_list_page {
  .detail {
    padding: r(15) r(15) 0;
    background-color: #fff;
    color: #333;

    .group_title {
      display: flex;
      align-items: center;
      height: r(21);

      &__icon {
        width: r(18);
        height: r(18);
        margin-right: r(6);
      }

      &__text {
        font-size: r(15);
        font-weight: 500;
        line-height: r(21);
      }
    }

    .group_content {
      font-size: r(13);
      padding-top: r(10);

      .service_item {
        padding: r(10) 0;

        &__title {
          line-height: r(20);
          font-size: r(14);
          margin-bottom: r(3);
        }

        &__intro {
          line-height: r(19);
          color: #999;
          margin-bottom: r(5);
        }

        &__other {
          display: flex;
          justify-content: space-between;
          align-items: center;

          &__price {
            font-size: r(14);
            font-weight: 500;
            color: #999;
            text-decoration: line-through;

            &--highlight {
              line-height: r(21);
              font-size: r(16);
              color: #ff5050;
              margin-right: r(5);
            }
          }

          &__btn {
            width: r(65);

            .za-button {
              height: r(27);
            }
          }
        }

        &:not(:last-child) {
          box-shadow: 0 r(1) 0 0 rgba(0, 0, 0, 0.1);
        }
      }
    }
  }
}
