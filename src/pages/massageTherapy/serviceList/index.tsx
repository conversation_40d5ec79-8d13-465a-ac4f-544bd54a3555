import React, { useState, useEffect, useCallback } from 'react';
import { useDispatch } from 'react-redux';
import { Button } from 'zarm';
import { ShopIntro } from '../components';
import { fetchJson } from 'src/utils/fetch';
import { ShopInfo } from 'src/store/massageTherapy/type';
import { select_shop, select_product } from 'src/store/massageTherapy/action';

import './index.scss';

function ServiceList(props) {
  const dispatch = useDispatch();
  const [shopInfo, setShopInfo] = useState<ShopInfo>({} as ShopInfo);

  const toServiceReserve = useCallback(() => {
    props.history.push({
      pathname: '/hospital/massageTherapy/reserve',
    });
  }, []);

  const selectService = (index) => {
    const { productList } = shopInfo;
    if (productList[index]) {
      dispatch(select_product(productList[index], toServiceReserve));
    }
  };

  useEffect(() => {
    fetchJson({
      url: '/api/api/v1/patient/physiotherapy/shop/list',
      type: 'POST',
      data: {
        shopType: 'physiotherapy',
      },
      isloading: true,
    }).then((res) => {
      console.log({ res });
      if (+res.code === 0 && res.result?.length) {
        const shopInfo = res.result[0];
        setShopInfo(shopInfo);
        dispatch(select_shop(shopInfo));
      }
    });
  }, []);

  return (
    <div className="massagetherapy_service_list_page">
      <ShopIntro shopInfo={shopInfo} />
      <div className="detail">
        <div className="group_title">
          <img className="group_title__icon" src={require('./images/yd_icon.png')} alt="icon" />
          <p className="group_title__text">预订</p>
        </div>
        <div className="group_content">
          {shopInfo.productList &&
            shopInfo.productList.map((item, i) => (
              <div key={i} className="service_item">
                <p className="service_item__title">{item.productName}</p>
                <p className="service_item__intro">
                  {item.duration}分钟{item.remark ? ` | ${item.remark}` : ''}
                </p>
                <div className="service_item__other">
                  <div>
                    <span className="service_item__other__price--highlight">￥{Number(item.productRealPrice).toFixed(2)}</span>
                    <span className="service_item__other__price">¥{Number(item.productPrice).toFixed(2)}</span>
                  </div>
                  <div className="service_item__other__btn">
                    <Button onClick={() => selectService(i)} block shape="round" theme="primary" size="sm">
                      预订
                    </Button>
                  </div>
                </div>
              </div>
            ))}
        </div>
      </div>
    </div>
  );
}

export default ServiceList;
