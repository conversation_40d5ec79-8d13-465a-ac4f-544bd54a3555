@import "src/style/index";
$prefixCls: 'mentalform-page';

.#{$prefixCls} {
  min-height: 90vh;
  padding-bottom: r(95);

  &__title {
    line-height: r(22.5);
    @include display-flex;
    @include justify-content(space-between);

    margin-bottom: r(15);

    .alias {
      font-size: r(16);
      font-weight: 600;
      color: #1e1e1e;
    }

    .extra {
      color: $base-green;
      font-size: r(12);

      .za-icon {
        font-size: r(14);
        margin-right: 4px;
        vertical-align: -3px;
      }
    }
  }

  &__card {
    background-color: #fff;
    border-radius: r(8);
    padding: r(15);
    margin: r(10) r(15) r(10);
    //共用AI导诊页的问诊选择组件
    .components {
      &__patient {
        margin-top: -0.732143rem;

        .confirm-btn {
          display: none;
        }
      }

      &__title {
        display: none;
      }
    }
  }

  &__input {
    height: r(110);
    background: #f8f8f8;
    border-radius: r(8);
    padding: r(15);
    font-size: r(13);

    textarea::placeholder {
      font-size: r(13);
    }
  }
  //问诊选择
  &__inquiry {
    .inquiry-row {
      @include borderRadius($color:$border-color,$radius:16px);
      @include display-flex;
      @include align-items(center);

      padding: r(20) r(15);
      transition: all .3s;

      &.active {
        background-color: #f9fdfc;

        &::after {
          border-color: $base-green;
        }
      }

      &:not(:first-child) {
        margin-top: r(10);
      }
    }

    .inquiry-icon {
      width: r(44);
      height: r(44);
    }

    .col-intro {
      @include flex;

      margin-left: 10px;
      overflow: hidden;

      .product-price {
        color: #ff5050;
        font-size: r(14);
        margin-left: r(3);
        font-weight: 600;
      }

      .title {
        .name {
          font-size: r(16);
          font-weight: 600;
          color: $base-text-color;
        }

        color: #666;
        font-size: r(12);
        line-height: r(20);
        margin-bottom: r(3);
      }

      .desc {
        font-size: r(13);
        color: #999;
        @include line;
      }
    }
  }

  &__fixbottom {
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
    z-index: 1000;
    background: #fff;
    padding: r(10) r(15) r(20);
    border-top: 1px solid #f5f5f5;
    @include display-flex;
    @include align-items(center);
    @include justify-content(space-between);

    &-amount {
      @include display-flex;
      @include align-items(center);

      margin-right: r(10);
      font-size: r(15);
      line-height: 1.2;
      color: #666;

      .real_amount,
      .preferential {
        margin-left: r(6);
        font-size: r(21);
        font-weight: bold;
        color: #ff5050;
      }

      .preferential {
        font-size: r(13);
      }
    }

    .btn_submit {
      max-width: r(150);
      min-width: r(88);
      height: r(44);
      font-size: r(17);
      font-weight: bold;
      color: #fff;
      @include flex(1);
    }
  }
}
