import React, { useEffect, useState, useCallback, useMemo } from 'react';
import { RouteComponentProps } from 'react-router-dom';
import { Icon, Input, Button } from 'zarm';
import classnames from 'classnames';
import { RightsAndDiscount, StaticToast } from 'src/components/common';
import { Deserialize, fetchJson, } from 'src/utils';
import format from 'src/utils/format';
import AiGuideSelectPatient from 'src/pages/inquiryForm/aiGuide/components/selectPatient';
import patientInquiryInfoInit from 'src/pages/inquiryForm/init';
import { shallowEqual, useSelector, useDispatch } from 'react-redux';
import { ApplicationState } from 'src/store';
import { CouponType } from 'src/store/coupon/type';
import { clear_inquiry, edit_inquiry, fetchProductList, submit_inquiry } from 'src/store/inquiry/action';
import throttle from 'src/utils/throttle';
import { unifiedOrderProcessPay } from 'src/pages/inquiryForm/utils';
import './form.scss';

const prefixCls = 'mentalform-page';
type ServiceProductType = {
  id?: number;
  productCode?: string;
  productName?: string;
  productPrice?: string;
  productRealPrice?: number;
  productShowName?: string;
  productStatus?: string;
  productSupplier?: string;
  productStock?: string;
  icon: string;
  productType: number;
  inquiryName: string;
  inquiryType: string;
  desc: string;
}
const INQUIRYTYPELIST_STATIC: ServiceProductType[] = [{
  icon: require('../images/icon-i.png'),
  inquiryType: 'I',
  productType: 12,
  inquiryName: '图文',
  desc: '图文聊天形式咨询，响应及时',

}, {
  icon: require('../images/icon-v.png'),
  inquiryType: 'V',
  productType: 13,
  inquiryName: '视频',
  desc: '咨询师视频咨询，沟通更方便真实',
}]


const MentalConsultIntro = (props: RouteComponentProps) => {
  const {
    location: { search = '' },
  } = props;

  let { inquiryTimingSubType = '', inquiryTimingType = 'mentalConsult', } = Deserialize(search);
  const [mentalProductList, setMentalProductList] = useState<ServiceProductType[]>([]);
  //费用试算中的时候 不能提交问诊问诊单
  const [isCalculatePrice, setIsCalculatePrice] = useState(false);
  //优惠券列表
  const [couponList, setCouponList] = useState<CouponType[]>([]);
  //权益列表
  const [vasList, setVasList] = useState([]);
  //用户选择的权益和优惠券
  const [checkedCoupon, setCheckedCoupon] = useState<any>({});
  const [checkedVas, setCheckedVas]: any = useState({});

  const dispatch = useDispatch();
  const editInquiry = (key: string, value: any, payload?: any) => dispatch(edit_inquiry(key, value, payload));
  const submitInquiry = (onSuccess: any) => dispatch(submit_inquiry(onSuccess, { isClear: true }));
  const clearInquiry = () => dispatch(clear_inquiry());

  const { patientsList = [], create, create: { orderRealAmount = 0,
    orderAmount = 0, patientName = '', inquiryNo = '', illnessDescription = '', productType = '', productId = '', inquiryType = '', patientId = '' } = {} } = useSelector((state: ApplicationState) => {
      const { create = {}, } = state.inquiry;
      const { patientsList = [] } = state.patients;
      return { create, patientsList };
    }, shallowEqual);
  useEffect(() => {
    patientInquiryInfoInit(props, dispatch);
    editInquiry('inquiryTimingSubType', inquiryTimingSubType, {
      inquiryTimingType,
      inquiryType: '',
      productId: '',
    });
    dispatch(fetchProductList({ productTypeList: [12, 13], productStatus: 1 }, (res: ServiceProductType[]) => {
      const result = res.map(k => {
        let item = INQUIRYTYPELIST_STATIC.find((u: ServiceProductType) => u.productType == k.productType) || {};
        return { ...k, ...item };
      })
      setMentalProductList(result);
    }));
    return () => { clearInquiry() };
  }, []);
  useEffect(() => {
    if (patientsList.length && productType && inquiryNo && inquiryType && patientId) {
      calculatePrice(create);
    }
  }, [inquiryNo, inquiryType, productType, patientId, patientsList]);
  const onChangeIllnessDesc = (value) => editInquiry('illnessDescription', (value || '').trim());
  const selectProduct = (item: ServiceProductType) => {
    if (!patientId) {
      StaticToast.warning('您还未选择咨询人');
    }
    editInquiry('productId', item.id, {
      inquiryType: item.inquiryType,
      productType: item.productType,
    });
  };
  const calculatePrice = useCallback((create) => {
    setIsCalculatePrice(true);
    let {  platformCode = '', productId = '', useRightsAndDiscount, policyNo, inquiryTimingType, inquiryTimingSubType } = create;
    const data: any = {
      orderGoods:  [{ goodsId: productId, goodsNum: 1 }],
      platformCode: platformCode || '',
      patientId: patientId,
      orderType: 'inquiry',
      inquiryType: inquiryType,
      orderPreferenceList: [],
      returnValidPreference: false,
      relatedBizNo: inquiryNo,
      inquiryTimingType,
      policyNo,
      inquiryTimingSubType
    };
    const { userPreferenceObjectId = '' } = useRightsAndDiscount || {};
    if (userPreferenceObjectId) {
      data.orderPreferenceList.push(useRightsAndDiscount);
    }
    fetchJson({
      url: '/api/api/v1/patient/preference/calculate',
      type: 'POST',
      data: {
        ...data,
      },
      needLoading: true,
      success: (res) => {
        const { code = '0', result: { couponList = [], rightsList = [], order: { orderGoods = [], platformCode = '', orderAmount = '', orderRealAmount = '', orderPreferenceList = [] } = {} } = {} } = res || {};
        if (code === '0') {
          //问诊可以用的优惠券
          const coupon: CouponType[] = couponList.map((element) => ({
            ...element.coupon,
            couponEffectiveTime: (element.couponEffectiveTime || '').replace(/-/g, '/'),
            couponExpiryTime: (element.couponExpiryTime || '').replace(/-/g, '/'),
            id: element.id,
            userCouponStatus: element.userCouponStatus,
          }));
          const { userPreferenceObjectId, preferenceObjectType = '' } = orderPreferenceList[0] || {};
          if (preferenceObjectType == '1') {
            const checkedCoupon = coupon.find(({ id }) => id == userPreferenceObjectId) || {};
            setCheckedCoupon(checkedCoupon);
            setCheckedVas({});
          } else if (preferenceObjectType == '3-1') {
            const checkedVas = rightsList.find(({ id }) => id == userPreferenceObjectId) || {};
            setCheckedCoupon({});
            setCheckedVas(checkedVas);
          } else {
            setCheckedCoupon({});
            setCheckedVas({});
          }
          setCouponList(coupon);
          setVasList(rightsList);
          editInquiry('useRightsAndDiscount', orderPreferenceList[0] || {}, {
            orderGoods,
            orderPreferenceList,
            platformCode,
            orderRealAmount,
            orderAmount
          });
          setIsCalculatePrice(false);
        }
      },
    });
  }, [create]);
  //点击优惠券
  const couponClick = useCallback((item: CouponType) => {
    if (!item.id || item.id !== checkedCoupon.id) {
      const param = item.id ? {
        userPreferenceObjectId: item.id,
        preferenceObjectType: '1',
      } : {}
      editInquiry('useRightsAndDiscount', param);
      calculatePrice({ ...create, useRightsAndDiscount: param });
    }
  }, [checkedCoupon, create]);
  const ableCouple = useMemo(() => couponList.filter(({ userCouponStatus }) => userCouponStatus === 1), [couponList]);
  const vasClick = useCallback((item) => {
    if (!item.id || item.id !== checkedVas.id) {
      const param = item.id ? {
        userPreferenceObjectId: item.id,
        preferenceObjectType: '3',
        isPolicyRights: item.isPolicyRights
      } : {};
      editInquiry('useRightsAndDiscount', param);
      calculatePrice({ ...create, useRightsAndDiscount: param });
    }
  }, [checkedVas, create]);
  //提交咨询单
  const submitConsult = throttle(useCallback(() => {
    if (isCalculatePrice) {
      StaticToast.warning('咨询单价格正在试算中，请稍后提交咨询单');
      return;
    }
    const {
      inquiryNo = '',
      patientName = '',
      patientId = '',
      illnessDescription = '',
      productId = '',
    } = create;

    if (inquiryNo === '') {
      StaticToast.warning('当前咨询单已经提交支付');
      return false;
    } else if (patientName === '') {
      StaticToast.warning('您还未选择咨询人');
      return false;
    } else if (patientId === '') {
      StaticToast.warning('您选择的咨询人信息有误，请重新选择');
      return false;
    } else if (illnessDescription.trim() === '' || illnessDescription.length < 10) {
      StaticToast.error('请填写咨询问题,至少10个字！');
      return false;
    } else if (!productId) {
      StaticToast.warning('请选择咨询方式');
      return false;
    };
    submitInquiry((result) => {
      clearInquiry();
      unifiedOrderProcessPay(result);
    });
    return;

  }, [create, isCalculatePrice]), 800);
  const diffAmount = Number(orderAmount - orderRealAmount);

  return (
    <div className={prefixCls} >
      <section className={`${prefixCls}__card`}>
        <div className={`${prefixCls}__title`}>
          <h3 className={'alias'}>咨询人</h3>
        </div>

        <AiGuideSelectPatient prefixCls='components' isCheckHasInquiry={false} currentItem={{ prevIndex: patientId }} {...props} />
      </section>
      <section className={`${prefixCls}__card`}>
        <div className={`${prefixCls}__title`}>
          <h3 className={'alias'}>咨询问题</h3>
          <p className="extra"><Icon type='info-round' theme='primary' />便于医生快速了解情况</p>
        </div>
        <Input
          className={`${prefixCls}__input`}
          type="text"
          rows={3}
          autoHeight
          maxLength={200}
          onChange={(value) => onChangeIllnessDesc(value)}
          value={illnessDescription}
          placeholder="请输入至少10个字的问题描述"
        />
      </section>
      <section className={`${prefixCls}__card`}>
        <div className={`${prefixCls}__title`}>
          <h3 className={'alias'}>咨询方式</h3>
        </div>
        <ul className={`${prefixCls}__inquiry`}>
          {mentalProductList.map(k => {
            return (
              <li className={classnames('inquiry-row', { active: (k.id && k.id === productId && k.inquiryType === inquiryType) })} key={`row-${k.inquiryType}`} onClick={() => selectProduct(k)}>
                <p className='inquiry-col'><img className='inquiry-icon' src={k.icon} alt={k.inquiryName} /></p>
                <div className='inquiry-col col-intro'>
                  <p className="title"><span className='name'>{k.inquiryName}咨询</span> <span className='product-price'>¥{format.digits(k.productRealPrice || 0)}</span>/次</p>
                  <p className="desc">{k.desc}</p>
                </div>
              </li>
            )
          })}
        </ul>
      </section>
      <RightsAndDiscount
        className={`${prefixCls}__card`}
        checkedVas={checkedVas}
        vasList={vasList}
        vasClick={vasClick}
        patientName={patientName}
        ableCouple={ableCouple}
        checkedCoupon={checkedCoupon}
        couponClick={couponClick} />
      <div className={`${prefixCls}__fixbottom`}>
        <p className={`${prefixCls}__fixbottom-amount`}>
          <span>总计</span>
          <span className='real_amount'>¥{format.digits(orderRealAmount)}</span>
          {diffAmount > 0 && <span className="preferential">已优惠¥{format.digits(diffAmount)}</span>}
        </p>
        <Button className='btn_submit' theme="primary" shape="round" onClick={submitConsult} >
          立即{((orderRealAmount == 0) && '咨询') || '购买'}
        </Button>
      </div>
    </div>
  )
};

export default MentalConsultIntro;
