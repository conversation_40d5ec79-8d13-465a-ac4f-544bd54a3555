import React, { useCallback, useEffect, useState } from 'react';
import { Button } from 'zarm';
import { StaticToast } from 'src/components/common';
import { fetchJson } from 'src/utils';
import './intro.scss';
import format from 'src/utils/format';

const prefixCls = 'mentalIntro-page';
type ServiceType = {
  code: string;
  itemDesc: string;
  name: string;
}
type Ruleype = {
  serviceTime?: string;
  serviceDays?: string;
  isDisabled: boolean;
}
const getWeekWhich = (workDay: number) => {
  const week = '日一二三四五六日'.charAt(workDay);
  return (week && `周${week}`) || '';
}
const getContinueWeek = (list) => {
  const start = list.shift();
  const end = list.pop();
  const startWeek = getWeekWhich(start);
  const endWeek = end && getWeekWhich(end) || '';
  return `${startWeek}${endWeek && '至'}${endWeek}`

}
//咨询服务 日
const getConsultWorkDays = (list) => {
  let result: string[] = ['周一至周日'];
  const sortList = list.sort();
  if (!list.length) {
    return '';
  }
  if (sortList.length < 7) {
    result = [];
    let itemArr: number[] = [];
    const firstItem = sortList[0];
    sortList.reduce((prev, current, index) => {
      if (current - prev == 1) {
        itemArr.push(current);
      } else {
        itemArr.length && result.push(getContinueWeek(itemArr));
        itemArr = [current];
      }
      (index == sortList.length - 1) && result.push(getContinueWeek(itemArr));
      return current;
    }, firstItem);
  };
  return result.join("、");
  // if (list.length < 7) {

  //   result = list.sort().reduce((prev: string[], current) => {
  //     const week: any = getWeekWhich(current);
  //     week && prev.push(`周${week}`);
  //     return prev;
  //   }, []);
  // };
  // return result.join("、");
}

//咨询服务时间
const getConsultServiceTime = (workTimes: string[] = []) => {
  let result: string[] = [];
  return (workTimes).reduce((prev, current) => {
    const time: RegExpMatchArray | null = current.match(/(?:[01]\d|2[0-3])(?::[0-5]\d)/);
    if (time) {
      prev.push(time[0]);
    };
    return prev;
  }, result);
}

//是否在服务时间内
const isServiceTimeRange = (workTimes: string[] = []) => {
  const [startTime, endTime] = workTimes;
  const nowDate = format.date(new Date(), 'yyyy/MM/dd');
  const nowTime = Date.now();
  const startTimestamp = new Date(`${nowDate} ${startTime}`).getTime();
  const endTimestamp = new Date(`${nowDate} ${endTime}`).getTime();
  const isStartService = nowTime >= startTimestamp;
  const isEndService = nowTime <= endTimestamp;
  return isEndService && isStartService;
}
const MentalConsultIntro = (props) => {
  const [mentalServiceList, setMentalServiceList] = useState<ServiceType[]>([]);
  const [mentalServiceRule, setMentalServiceRule] = useState<Ruleype>({ isDisabled: true });
  useEffect(() => {
    getPsychologicalServiceRule();
  }, []);
  const getPsychologicalServiceRule = () => {
    fetchJson({
      type: 'GET',
      url: '/api/api/v1/patient/psychological/inquiryRule/queryDetail',

    }).then((res) => {
      if (res && res.code === '0') {
        const { workTimes = [], workDays = [], serviceItemList = [] } = res.result || {};
        const workTimeRange = getConsultServiceTime(workTimes);
        let currentWeek = new Date().getDay();
        //填运营后台的坑，周日 写成 7 了
        currentWeek = currentWeek == 0 ? 7 : currentWeek;
        const isServiceDayWithin = !workDays.some(k => (currentWeek == k));
        const isServiceTimeWithin = isServiceTimeRange(workTimes);
        const isDisabled = isServiceDayWithin || !isServiceTimeWithin;
        setMentalServiceRule({
          serviceTime: workTimeRange.join("-"),
          isDisabled,
          serviceDays: getConsultWorkDays(workDays),
        });
        setMentalServiceList(serviceItemList);
        return;
      }
      StaticToast.error(res.message);
    });
  }

  const jumpTo = useCallback((code) => {
    if (mentalServiceRule.isDisabled) {
      StaticToast.error('非工作时间内无法使用');
      return
    }
    props.history.push(`/hospital/mentalConsult/form?inquiryTimingSubType=${code}`)
  }, [mentalServiceRule.isDisabled]);
  return (
    <div className={prefixCls} >
      <div className={`${prefixCls}__bg`}>
        <p className='highlights'>生活中遇到情绪问题，不知如何解决？</p>
        <p className='highlights'>我们为您提供各类心理咨询服务</p>
        <p className="service-time">工作时间：{mentalServiceRule.serviceTime}</p>
      </div>
      <section className={`${prefixCls}__serviceday`}>
        <p className={`${prefixCls}__header`}>{mentalServiceRule.serviceDays}</p>

      </section>
      <section className={`${prefixCls}__core`}>
        <header className={`${prefixCls}__header`}>
          根据自身情况选择对应的咨询类型
        </header>
        <ul className={`${prefixCls}__list`}>
          {mentalServiceList.map(k => {
            return (k && (
              <li className='mental-row' key={`row-${k.code}`} onClick={() => jumpTo(k.code)}>
                <div className="mental-col mental-details">
                  <h3 className='title'>{k.name}</h3>
                  <p className='desc'>{k.itemDesc}</p>
                </div>
                <p className="mental-col"><Button size="xs" theme="primary" disabled={mentalServiceRule.isDisabled} shape="round">去咨询</Button></p>
              </li>)
            )
          })}
        </ul>
      </section>
    </div>
  )
};

export default MentalConsultIntro;
