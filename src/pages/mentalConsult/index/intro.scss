@import "src/style/index";
$prefixCls: 'mentalIntro-page';

.bg-radius {
  background-color: #fff;
  border-radius: r(8);
}

.#{$prefixCls} {
  background-color: #f5f5f5;
  background-image: url(../images/intro-bg.png);
  background-size: 100% auto;
  background-repeat: no-repeat;
  padding: 0 r(15) r(15);
  min-height: 100vh;

  &__bg {
    height: r(162);
    padding: r(62) 0 0;
    font-size: r(12);
    font-weight: 600;

    .highlights {
      color: #f38370;
      line-height: r(20);
      font-size: r(13);
    }

    .service-time {
      margin-top: r(13);
      background-image: url(../images/time-bg.png);
      background-size: r(147.5) r(41);
      background-repeat: no-repeat;
      height: r(37.5);
      color: #fff1ef;
      padding: r(4) 0 0 r(7.5);
    }
  }

  &__serviceday,
  &__core {
    background-color: #fff;
    border-radius: r(8);
    padding: r(15);
  }

  &__serviceday {
    margin-bottom: r(10);

    .#{$prefixCls}__header {
      background-image: url(../images/icon-week.png);
      margin-bottom: 0;
      font-weight: 600;
      font-size: r(13);
      color: #999;
    }
  }

  &__header {
    padding-left: r(23);
    background-image: url(../images/icon-info.png);
    background-repeat: no-repeat;
    background-size: r(18) r(18);
    background-position: 0 50%;
    font-size: r(16);
    font-weight: 600;
    color: $base-text-color;
    line-height: r(20);
    margin-bottom: r(15);
  }

  &__list {
    .mental-row {
      padding: r(20) r(15);
      // border:r(1) solid $border-color;
      @include borderRadius($color:$border-color,$radius:16px);
      @include display-flex;
      @include align-items(center);

      .mental-details {
        @include flex;

        margin-right: 10px;
        overflow: hidden;

        .title {
          font-size: r(16);
          font-weight: 600;
          color: $base-text-color;
          line-height: r(22.5);
          margin-bottom: r(3);
        }

        .desc {
          font-size: r(13);
          color: #999;
          @include line;
        }
      }

      .za-button {
        font-size: r(13);
        width: r(62.5);
        font-weight: 600;
      }

      &:not(:first-child) {
        margin-top: r(10);
      }
    }
  }
}
