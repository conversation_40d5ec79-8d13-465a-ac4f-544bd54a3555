// author: renjie
// 2022.12.28
// 针对广告位发放服务包做一个中间页处理
import React, {useEffect} from 'react';
import { Deserialize } from 'src/utils/serialization';
import {redirectUrl} from 'src/utils/ad';
import { fetchJson } from 'src/utils';

const AutoServpackPage =  props => {
    useEffect(() => {
        const { location: { search = '' } } = props;
        const { adRequestUrl, adType, adPosition, adNo, servpackCodeList, appId, extraData } = Deserialize(search);
        //发放服务包
        fetchJson({
            type: "POST",
            url: "/api/api/v1/patient/user/servpack/adverDraw",
            data:{
                adPosition: adPosition,
                adNo: adNo,
                servpackCodeList: servpackCodeList.split(',')
            },
            isloading: true,
        }).then(res => {
            //跳转应该要跳的页面
            redirectUrl({adRequestUrl: decodeURIComponent(adRequestUrl), adType, appId, extraData})

        }).catch(e => {
            console.log('领取接口报错', e);
            redirectUrl({adRequestUrl: decodeURIComponent(adRequestUrl), adType, appId, extraData})
        })
    },[])

    return (
        <div className='auto-servpack-page'>

        </div>
    )
}
export default AutoServpackPage