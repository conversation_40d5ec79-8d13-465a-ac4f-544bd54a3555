import React, { useEffect } from 'react';
import {jumpBeforeAuth} from 'src/utils/auth'
import { THIRD_PLATFORM_RESOURCECODE } from 'src/utils/staticData';
import { getEnv } from 'src/utils/env';

const DidiMiddlePage = () => {
    const config = {
        dev: {
            url: '/goods/equity?groupId=oQBzKQ',
            channelPoint: 'NyvtqCov'
        },
        test: {
            url: '/goods/equity?groupId=oQBzKQ',
            channelPoint: 'NyvtqCov'
        },
        pre: {
            url: '/goods/equity?groupId=W08JJQ',
            channelPoint: 'zFknaP2A'
        },
        prd: {
            url: '/goods/equity?groupId=VMLOA0',
            channelPoint: 'PTmDMFU3'
        }
    }
    useEffect(() => {
        const env = getEnv();
        jumpBeforeAuth({
            partnerCode: THIRD_PLATFORM_RESOURCECODE.ZA_UMBRELLA,
            businessType: 'index',
            target: config[env]['url'],
            channelPoint: config[env]['channelPoint'],
        })
    },[])
    return (
        <div />
    )
}
export default DidiMiddlePage