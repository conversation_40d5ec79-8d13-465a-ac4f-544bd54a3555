import React, { useEffect } from 'react';
import cookies from 'src/utils/cookie';
import { Deserialize } from 'src/utils/serialization';
import validate from 'src/utils/validate';

const JUMP_ENV = {
  applets: 'applets',
  web: 'web',
  all: 'all'
}

const BridgeBack = (props) => {
  const { location: { search = '' } } = props;
  const { target = '', param = '', env = '' } = Deserialize(search);

  useEffect(() => {
    //针对橄榄枝的跳转
    if (env === JUMP_ENV.applets && validate.isFromMiniApplet()) {
      wx.miniProgram.redirectTo({
        url: `${target}?payParams=${param}`,
      });
    } else if (validate.isFromMiniApplet()) {
      wx.miniProgram.switchTab({
        url: '/pages/index',
      });
    } else {
      const channelResourceCode = cookies.get('channelResourceCode');
      const channelSource = cookies.get('channelSource');
      props.history.replace({
        pathname: '/hospital/home',
        search: channelResourceCode ? `channelResourceCode=${channelResourceCode}` : `channelSource=${channelSource}`
      });
    }
  }, []);

  return (
    <div></div>
  )
}

export default BridgeBack;
