/**
 * author: renjie
 * date: 2023.2.13
 * 必须是实名认证过的页面，接受链接里的vasCode，读取拥有的权益，并读取权益中心配置的链接进行授权跳转，目前用于短线链接领取权益并跳转方伞
 */
import React, { useEffect, useState } from 'react';
import { Deserialize } from 'src/utils/serialization';
import { fetchJson } from 'src/utils';
import { StaticToast } from 'src/components/common';
import {Modal} from 'zarm';
import {jumpBeforeAuth} from 'src/utils/auth'
import { THIRD_PLATFORM_RESOURCECODE } from 'src/utils/staticData';
import { DEPLOY__ENV } from 'src/utils/env';
import './index.scss';
import { xflowPushEvent } from 'src/utils/pageTrack';

const channelPoint = DEPLOY__ENV({
  'tst': 'fAPHzObM',
  'uat': 'upCnVrf9',
  'prd': 'ZzTFgYMX',
});

const VasDirect = props => {
  const { location: { search = '' } } = props;
  const { vasCode } = Deserialize(search);
  const [count, setCount ] = useState(5);

  useEffect(() => {
    getData();
  })  

  const getData = () => {
    if(!vasCode){
      StaticToast.error('缺少参数vasCode');
      return ;
    }
    fetchJson({
      type: 'POST',
      url: '/api/api/v1/patient/policy/queryPolicyVasInfo',
      data: {
        vasCode,
      },
      isloading: true,
    }).then(res => {
      if(res && res.code === '0'){
        const {result = []} = res;
        if(result.length > 0){
          const [rightsInfo = {}] = result;
          if(!rightsInfo.isSelected){
            Modal.alert({
              content:'无可用权益，即将跳转个人中心',
              cancelText:'确定',
              onCancel:() => {
                props.history.replace('/hospital/personalcenter');
              }
            })
            return ;
          }
          if(rightsInfo.isGracePeriod === 'Y'){
            Modal.alert({
              content:'您的健康权益已失效，请及时缴纳保费后可使用权益',
              cancelText:'我知道了',
              onCancel:() => {
                xflowPushEvent(['click', 'ZAHLWYY_KXQDJFTC_WZDL	', '宽限期点击', { ZAHLWYY_CLICK_CONTENT: `宽限期待缴费弹窗_我知道了按钮` }]);
                props.history.replace('/hospital/personalcenter');
              }
            })
            return ;
          }
          if(rightsInfo.useUrl){
            jumpBeforeAuth({
              partnerCode: THIRD_PLATFORM_RESOURCECODE.ZA_UMBRELLA,
              businessType: 'custom',
              target: rightsInfo.useUrl,
              channelPoint: channelPoint,
            })
          }else{
            if(count === 0){
              Modal.alert({
                content:'暂时没有查询到权益，请稍后再试',
                cancelText:'我知道了',
                onCancel:() => {
                  window.location.reload();
                }
              })
              return ;
            }else{
              setCount(count -1);
              getData();
            }
          }
        }else{
          StaticToast.error('无可用权益')
        }
      }
    })
  }

  return (
    <div className='vasdirect-page'>正在为您查询权益，请稍后...</div>
  )
}
export default VasDirect