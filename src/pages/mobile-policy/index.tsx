import React from 'react';
import { NavLink } from 'react-router-dom';


const MobilePolicy = () => {
  return (
    <div className="mobile-policy">
      <img src={require('./images/8.png')} alt='' />
      <a href='https://mp.weixin.qq.com/s/TEJ2q6_dgEq8QxbJtvdxpg'>
        <img src={require('./images/6.png')} alt='' />
      </a>
      <img src={require("./images/7.png")} alt=""/>
      <NavLink to='/hospital/specialist/pregnancyTest/2?channelResourceCode=YYGHTW-H5'>
        <img src={require("./images/4.png")} alt=""/>
      </NavLink>
      <NavLink to="/hospital/specialist/pregnancyTest/5?channelResourceCode=YYGHTW-H5">
        <img src={require("./images/3.png")} alt='' />
      </NavLink>
      <NavLink to='/hospital/preauth?partnerCode=YDL&businessType=heartAssess&channelResourceCode=YYGHTW-H5'>
        <img src={require("./images/2.png")} alt='' />
      </NavLink>
      <NavLink to='/hospital/specialist/pregnancyTest/4?channelResourceCode=YYGHTW-H5'>
        <img src={require("./images/5.png")} alt='' />
      </NavLink>
      <NavLink to="/hospital/specialist/pregnancyTest/1?channelResourceCode=YYGHTW-H5">
        <img src={require("./images/1.png")} alt='' />
      </NavLink>
      <img src={require("./images/9.png")} alt='' />
    </div>
  );
};

export default MobilePolicy;
