import React, { useCallback } from 'react';
import SvgIcon from 'src/components/common/svg';
import { Button } from 'zarm';
import './noPage.scss';
import { Deserialize } from 'src/utils';

const NotPage = (props) => {
  const { hideHome } = Deserialize(window.location.search);
  const notIcon = require('./images/404.svg');
  const goTo = useCallback(() => {
    props.history.replace('/hospital/home');
  }, []);

  return (
    <div className='no_page'>
      <div>
        <div className='icon-404'>
          <SvgIcon type='img' src={notIcon} width='200' />
        </div>
        {
          !hideHome &&
          <Button ghost size='sm' theme='primary' onClick={() => goTo()}>去首页</Button>
        }
      </div>
    </div>);
};


export default NotPage;
