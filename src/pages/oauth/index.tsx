import React, { useEffect } from 'react';
import { Deserialize } from 'src/utils/serialization';
import { fetchJson } from 'src/utils/fetch';
import cookies from 'src/utils/cookie';
import { StaticToast } from 'src/components/common';
import './index.scss';

const Oauth = (props) => {
    const { location: { search = '' } } = props;
    const { code = '', url } = Deserialize(search);

    useEffect(() => {
        if (!code) {
            const redirect_uri = encodeURIComponent(window.location.href);
            const scope = 'snsapi_userinfo';
            const appid = 'wxe6934ee723a347eb'; //微信公众号appid
            window.location.href = `https://open.weixin.qq.com/connect/oauth2/authorize?appid=${appid}&redirect_uri=${redirect_uri}&response_type=code&scope=${scope}#wechat_redirect`
        } else {
            fetchJson({
                url: '/api/api/v1/patient/user/getThirdOpenId',
                method: 'POST',
                type: 'POST',
                data: {
                    channelSource: cookies.get('channelSource') || '',
                    channelResourceCode: cookies.get('channelResourceCode') || '',
                    requestData: JSON.stringify({
                        channelType: 'public',
                        code: code
                    }),
                }
            }).then(res => {
                console.log('res',res);
                if (res.code === '0') {
                    StaticToast.success('授权成功!');
                    //这里判断如果是H5跳过来，则跳回H5，并带上授权成功标志。如果是小程序进webview，则小程序返回
                    if (url) {
                        let jumpUrl = decodeURIComponent(url);
                        jumpUrl.includes('?') ? jumpUrl += '&' : '?';
                        jumpUrl += 'oauth=1';
                        window.location.replace(jumpUrl)
                    } else {
                        wx && wx.miniProgram.postMessage({ data: { bindPublic: true } })
                        setTimeout(() => {
                            wx && wx.miniProgram.navigateBack({
                                delta: 1
                            });
                        }, 500)
                    }
                } else {
                    StaticToast.error('授权失败'+ res.message ? `,${res.message}` : '');
                }
            })
        }
    })
    return (
        <div className='oauth-page'>
            正在进行微信公众号授权...
        </div>
    )
}
export default Oauth;