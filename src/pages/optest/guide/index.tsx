import React from 'react';
// import SvgIcon from 'src/components/common/svg';
import './opguide.scss';

const prefixCls = 'optest_guide_page';

const Guide = (props) => {

  return (
    <div className={prefixCls}>
      <div className={`${prefixCls}__header`}>
      </div>
      <div className={`${prefixCls}__main`}>
        <div className='guide_card'>
          <p className='title'>1.网上预约</p>
          <p className='single_line'>网上预约<span className='green'>http://new.sdfyy.cn</span></p>
        </div>
        <div className='guide_card'>
          <p className='title'>2.电话预约</p>
          <p className='single_line'>电话智慧医疗预约<span className='green strong'>12320</span></p>
        </div>
        <div className='guide_card'>
          <p className='title'>3. 现场挂号</p>
          <div className='multi_line'>
            <p>1）门诊大楼一楼挂号窗口挂号；</p>
            <p>2）持身份证、市民卡至门诊大厅候诊大厅自助挂号。</p>
          </div>
        </div>
        <div className='guide_card special'>
          <p className='title long'>4. 关注「苏州大学附属第一医院」公众号</p>
          <div className='sub_title'><p className='num_wrap'><span className='num'>1</span></p><p>扫码关注苏州大学附属医院公众号</p></div>
          <img className='img_qrcode' src={require('./images/oa_qrcode.png')} />
          <div className='sub_title'><p className='num_wrap'><span className='num'>2</span></p><p>选择微服务-预约挂号</p></div>
          <img className='img_step_two' src={require('./images/guide_step_two.png')} />
          <div className='sub_title'><p className='num_wrap'><span className='num'>3</span></p><p>点击导航外科-选择骨科门诊</p></div>
          <img className='img_step_three' src={require('./images/guide_step_three.png')} />
        </div>
        <div className='guide_card'>
          <p className='title'>地图导航</p>
          <img className='img_address' src={require('./images/guide_address.png')} />
        </div>
      </div>
    </div>
  )
}

export default Guide;
