@import "src/style/index";
$prefixCls: 'optest_guide_page';

.#{$prefixCls} {
  height: 100vh;
  padding-bottom: r(39);
  overflow-y: scroll;
  background: #eafbf4;

  &__header {
    height: r(280);
    background: url('./images/guide_bg.png') no-repeat 100%/100%;
  }

  &__main {
    margin: 0 r(15);
    margin-top: r(-130);

    .guide_card {
      position: relative;
      min-height: r(105);
      margin-top: r(25);
      padding-top: r(35);
      background: #fff;
      box-shadow: 0 r(-3) r(12) 0 rgba(0, 188, 112, 0.2);
      border-radius: r(10);
      font-family: PingFangSC-Semibold, PingFang SC;
      font-weight: 600;

      &:first-child {
        margin-top: 0;
      }

      &:last-child {
        margin-top: r(34);
      }

      &.special {
        margin-top: r(45);
      }

      .title {
        position: absolute;
        top: r(-18);
        width: r(178);
        height: r(42);
        line-height: r(48);
        left: 50%;
        text-align: center;
        transform: translateX(-50%);
        background: url('./images/title_bg_short.png') no-repeat 100%/100%;
        font-size: r(17);
        color: #fff;

        &.long {
          top: r(-30);
          width: r(363);
          background: url('./images/title_bg_long.png') no-repeat 100%/100%;
        }
      }

      .single_line {
        padding-top: r(14);
        text-align: center;
        font-size: r(15);

        .green {
          margin-left: r(10);
          color: #00bc70;

          &.strong {
            font-size: r(19);
            vertical-align: middle;
          }
        }
      }

      .multi_line {
        padding: 0 r(10) r(17) r(20);
        line-height: r(26);
      }

      .sub_title {
        position: relative;
        margin-left: r(23);
        padding-left: r(23);

        .num_wrap {
          position: absolute;
          width: r(19);
          height: r(19);
          left: r(-8);
          top: r(-6);
          background: rgba(228, 240, 132, 0.37);
          border-radius: 50%;
          z-index: 1;
        }

        .num {
          position: absolute;
          display: inline-block;
          width: r(19);
          height: r(19);
          left: r(8);
          top: r(6);
          line-height: r(19);
          font-size: r(14);
          text-align: center;
          background: linear-gradient(270deg, #7cb5ff 0%, #80eefe 100%);
          z-index: 2;
          border-radius: 50%;
          color: #fff;
        }
      }

      .img_qrcode {
        width: r(241);
        margin: r(9) 0 r(25) r(60);
      }

      .img_step_two {
        width: r(288);
        margin: r(9) 0 r(20) r(43);
      }

      .img_step_three {
        width: r(146);
        margin: r(24) 0 r(39) r(101);
      }

      .img_address {
        width: r(265);
        margin: r(9) 0 r(30) r(40);
      }
    }
  }
}
