import React, { useCallback, useState } from 'react';
import { Card, StaticToast } from 'src/components/common';
import { Button, Checkbox } from 'zarm';
import './opquestion.scss';
import { questionList } from './config';
import { Deserialize, fetchJson } from 'src/utils';
import format from 'src/utils/format';
import cookies from 'src/utils/cookie';

const prefixCls = 'optest_question_page';

const dataSource = [
  {
    value: '0',
    label: '没有',
    score: 0,
  }, {
    value: '1',
    label: '平地摔倒',
    score: 1,
  }, {
    value: '2',
    label: '从高于1米的地方跌落',
    score: 1,
  }, {
    value: '3',
    label: '骑乘自行车/电动车时摔倒',
    score: 1,
  }, {
    value: '4',
    label: '被机动车撞倒',
    score: 1,
  }, {
    value: '5',
    label: '乘坐交通工具时遭遇剧烈颠簸',
    score: 1,
  }, {
    value: '6',
    label: '扭伤',
    score: 1,
  }
]

const Question = (props) => {
  // const [pickerVisible, setPickerVisible] = useState(false);
  // const [hasHurt, setHasHurt]: any = useState({});
  const [answers, setAnswers] = useState({});

  const { location: { search = '' } } = props;
  const { birthday = '', sex: userSex = 'M', weight = '', name } = Deserialize(search);

  const submit = useCallback(() => {
    const list = Object.values(answers);
    if ((userSex === 'M' && list.length < 11) || (userSex === 'F' && list.length < 12)) {
      StaticToast.error('请回答完所有问题');
      return;
    }
    let result = {
      risk: '',
      IOF: '',
      OSTA: '',
    };
    //OSTA=(体重-年龄）X0.2
    const age = format.GetAgeByBirthday(birthday);
    const OSTA = (Number(weight) - Number(age)) * 0.2;
    if (OSTA > -1) {
      result.risk = '低风险';
    } else if (OSTA <= -1 && OSTA >= -4) {
      result.risk = '中风险';
    } else {
      result.risk = '高风险';
    }
    result.OSTA = '' + OSTA;
    const ans = userSex === 'M' ? list.slice(0, 8) : list.slice(0, 9);;
    result.IOF = ans.indexOf('是') > -1 ? '阳性' : '阴性';

    fetchJson({
      url: '/api/api/v1/patient/osteolysistest/save',
      type: 'POST',
      data: {
        openId: cookies.get('openId') || '',
        channelSource: cookies.get('channelSource'),
        channelResourceCode: cookies.get('channelResourceCode'),
        name: decodeURIComponent(name),
        gender: userSex,
        birthday,
        weight,
        answer: JSON.stringify(answers),
        testResult: JSON.stringify(result),
      },
      needLogin: false,
      isloading: true,
      success: res => {
        if (res && res.code === '0') {
          props.history.push({
            pathname: '/hospital/optest/result',
            search: `risk=${result.risk}&IOF=${result.IOF}&hasBackache=${answers[11]}`
          })
        }
      }
    });
  }, [answers]);

  const answerQuestion = useCallback((index, value) => {
    setAnswers({
      ...answers,
      [index]: value,
    })
  }, [answers]);

  const list = questionList.filter((item) => {
    return !item.sex || item.sex === userSex;
  })

  const selectChange = useCallback((value) => {
    if (value.indexOf('没有') > -1 && value.length >= 2) {
      return
    }
    answerQuestion(12, value);
  }, [answers]);

  return (
    <div className={prefixCls}>
      <Card prefixCls={`${prefixCls}__card`}>
        {
          (list || []).map(({ question, no }, index) => {
            // if (sex && sex !== userSex) {
            //   return null;
            // }
            return (
              <div className='question_wrap' key={`question${no}`}>
                <p className='question_q'>{index + 1}、{question}</p>
                <p className='question_a'>
                  <span className={`question_a_option ${answers[no] === '是' ? 'active' : ''}`} onClick={() => { answerQuestion(no, '是') }}>是</span>
                  <span className={`question_a_option ${answers[no] === '否' ? 'active' : ''}`} onClick={() => { answerQuestion(no, '否') }}>否</span>
                </p>
              </div>
            )
          })
        }
        <div className='question_wrap'>
          <p className='question_q'>{list.length + 1}、您最近一个月是否有以下受伤的情况？（多选）</p>
          <Checkbox.Group value={answers[12]} onChange={(val) => selectChange(val)}>
            {
              dataSource.map(({ label }, index) => {
                const selectValue = answers[12] || []
                let disabled = false;
                if (label === '没有') {
                  disabled = selectValue.indexOf('没有') == -1 && selectValue.length > 0
                } else {
                  disabled = selectValue.indexOf('没有') > -1
                }
                return <Checkbox key={index} disabled={disabled} value={label}>{label}</Checkbox>
              })
            }
          </Checkbox.Group>
        </div>
      </Card>
      <div className='btn_test_wrap'>
        <Button theme="primary" block shape="round" onClick={() => submit()}>查看结果并获得建议</Button>
      </div>
    </div>)
}


export default Question;