@import "src/style/index";
$prefixCls: 'optest_question_page';

.#{$prefixCls} {
  padding-bottom: r(70);

  &__card {
    margin: r(15) r(15) r(25);
    border-radius: r(8);

    .question_wrap {
      margin: 0 r(15);
      padding: r(15) 0 r(16);
      font-size: r(14);
      color: #333;
      border-bottom: r(1) solid #e9e9e9;

      &:last-child {
        border-bottom: none;
      }
    }

    .question_a {
      position: relative;
      margin-top: r(12);
      overflow: hidden;
    }

    .question_a_option {
      display: inline-block;
      width: r(45);
      height: r(32);
      border: r(1) solid #d6d6d6;
      line-height: 2.2;
      text-align: center;
      border-radius: r(4) 0 0 r(4);

      &.active {
        border: r(1) solid #00bc70;
        background: #00bc70;
        color: #fff;
      }

      &:last-child {
        border-radius: 0 r(4) r(4) 0;
        margin-left: r(-1);
        border-left: r(1) solid #d6d6d6;
        // &::after {
        //   position: absolute;
        //   top: 0;
        //   right: r(-1);
        //   content: '';
        //   width: r(1);
        //   height: 100%;
        //   background: #D6D6D6;
        // }
      }
    }

    .za-checkbox-group {
      display: block;
      margin-top: r(18);

      .za-checkbox-group__inner {
        width: 100%;
        margin: 0;
      }

      .za-checkbox {
        display: block;
        position: relative;
        margin-right: 0;
        margin-bottom: r(10);
        // width: 100%;

        &:last-child {
          margin-bottom: r(5);
        }

        .za-checkbox__text {
          margin: 0;
          font-size: r(14);
          color: #333;
        }

        .za-checkbox__widget {
          position: absolute;
          right: 0;
        }

        .za-checkbox__inner {
          width: r(17);
          height: r(17);
          border-color: #d6d6d6;
          transition: none;
        }
      }

      .za-checkbox--checked {
        .za-checkbox__inner {
          background: #fff;

          &::before {
            width: r(8);
            height: r(14);
            border: r(2) solid #00bd6d;
            border-left: 0;
            border-top: 0;
            transform: rotate(45deg) scale(0.75);
            margin-top: r(-3);
            margin-left: 0;
            transition: width 0.2s;
          }
        }
      }
    }
  }

  .btn_test_wrap {
    margin: 0 r(30);
  }
}
