import React, { useCallback } from 'react';
import { Deserialize } from 'src/utils';
// import SvgIcon from 'src/components/common/svg';
import { Button } from 'zarm';
import './opresult.scss';

const prefixCls = 'optest_result_page';


const getDesctibe = (risk, IOF, hasBackache) => {

  if (hasBackache === '否') {
    if (risk === '低风险' && IOF === '阴性') {
      return (
        <React.Fragment>
          <p>说明：根据您提供的信息，您<span className='green'>没有</span>骨质疏松性椎体骨折的相关临床表现，您诊断为骨质疏松性椎体骨折的<span className='green'>风险较低</span>。</p>
          <p className='result_suggest'>建议您正常生活，加强运动、避免跌倒。注意定期评测。</p>
        </React.Fragment>
      )
    } else {
      return (
        <React.Fragment>
          <p>说明：根据您提供的信息，您<span className='green'>没有</span>骨质疏松性椎体骨折的相关临床表现，您诊断为骨质疏松性椎体骨折的<span className='green'>风险较低</span>。<span className='orange'>但您仍具有骨质疏松症的患病风险</span>。</p>
          <p className='result_suggest'>建议您正常生活，加强运动、避免跌倒。注意定期评测。</p>
        </React.Fragment>
      );
    }
  } else {
    switch (risk) {
      case '低风险':
        return (
          <React.Fragment>
            <p>说明：根据您提供的信息，您虽然<span className='orange'>有骨质疏松性椎体骨折相关临床表现</span>，但您诊断为骨质疏松性椎体骨折的<span className='green'>风险较低</span>。</p>
            <p className='result_suggest orange'>建议您待合适的时间前往医院就诊。</p>
          </React.Fragment>
        );
      case '中风险':
        return (
          <React.Fragment>
            <p>说明：根据您提供的信息，您诊断为骨质疏松性椎体骨折的<span className='red'>风险较高</span>。</p>
            <p className='result_suggest orange'>建议您待合适的时间前往医院就诊。</p>
          </React.Fragment>
        );
      case '高风险':
        return (
          <React.Fragment>
            <p>说明：根据您提供的信息，您诊断为骨质疏松性椎体骨折的<span className='red'>风险很高</span>。</p>
            <p className='result_suggest red'>建议您尽快前往医院就诊。</p>
          </React.Fragment>
        );
    }
  }
  return null;
}

const Result = (props) => {

  const { location: { search = '' } } = props;
  // risk=低风险&IOF=阴性&hasBackache=否
  const { risk = '', IOF = '', hasBackache = '' } = Deserialize(search);

  const goTo = useCallback((page) => {
    props.history.push({
      pathname: `/hospital/optest/${page}`,
    })
  }, []);

  return (
    <div className={prefixCls}>
      <div className={`${prefixCls}__main`}>
        <div className='result_img_wrap'>
          {decodeURIComponent(IOF) === '阳性' ? <img className='result_img' src={require('./images/opresult_danger_img.png')} /> : <img className='result_img' src={require('./images/opresult_img.png')} />}
        </div>
        <p className='result_title'>测试结果</p>
        {getDesctibe(decodeURIComponent(risk), decodeURIComponent(IOF), decodeURIComponent(hasBackache))}
        <div className='btn_test_wrap'>
          <Button theme="primary" block shape="round" onClick={() => goTo('about')}>关于我们</Button>
          <Button className='btn_secondary' block ghost theme="primary" shape="round" onClick={() => goTo('guide')}>如何预约挂号</Button>
        </div>
        <div className='of_guide'>
          <p>建议截图保存</p>
          <p>就诊时可作为医学参考</p>
        </div>
      </div>
      <div className={`${prefixCls}__footer`}>
        <p>长按保存图片后识别二维码</p>
        <p>获取更多健康咨询</p>
        <img src={require('./images/qrcode.jpg')} className='footer_img' />
      </div>
    </div>
  )
}


export default Result;
