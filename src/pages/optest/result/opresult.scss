@import "src/style/index";
$prefixCls: 'optest_result_page';

.#{$prefixCls} {
  height: 100vh;
  overflow-y: scroll;

  &__main {
    background: #fff;
    padding: r(15) r(17) r(35);
    font-size: r(14);
    color: #6d6f76;

    .result_img_wrap {
      text-align: center;
      font-size: 0;
    }

    .result_img {
      width: r(155);
      height: r(145);
    }

    .result_title {
      margin: r(15) 0 r(17);
      font-size: r(17);
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: bold;
      color: #484848;
      text-align: center;
    }

    .result_suggest {
      margin-top: r(10);
    }

    .btn_test_wrap {
      margin: r(25) r(13) r(35);

      .za-button {
        font-weight: bold;
        font-size: r(17);
      }

      .btn_secondary {
        margin-top: r(10);
      }
    }

    .of_guide {
      text-align: center;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: bold;
    }
  }

  &__footer {
    position: relative;
    height: r(97);
    padding: r(31) 0 0 r(34);
    font-size: r(14);
    color: #6d6f76;

    .footer_img {
      position: absolute;
      width: r(76);
      height: r(76);
      top: r(11);
      right: r(30);
    }
  }

  .red {
    color: #e64848;
    font-family: PingFangSC-Semibold, PingFang SC;
  }

  .orange {
    color: #ff7240;
  }

  .green {
    color: #00bc70;
    font-family: PingFangSC-Semibold, PingFang SC;
  }
}
