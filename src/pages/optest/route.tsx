/*
 * @authors :<PERSON>
 * @description：  患者管理模块路由
 */
const route = [
  {
    path: '/hospital/optest/userinfo',
    name: 'OptestUser',
    component: () => import(/* webpackPrefetchPlaceHolder */ './user'),
    auth: false,
    exact: true,
    title: '完善个人信息',
  }, {
    path: '/hospital/optest/question',
    name: 'OptestQuestion',
    component: () => import(/* webpackPrefetchPlaceHolder */ './question'),
    auth: false,
    exact: true,
    title: '骨质疏松风险自测问卷',
  }, {
    path: '/hospital/optest/result',
    name: 'OptestResult',
    component: () => import(/* webpackPrefetchPlaceHolder */ './result'),
    auth: false,
    exact: true,
    title: '结果',
  }, {
    path: '/hospital/optest/about',
    name: 'OptestAbout',
    component: () => import(/* webpackPrefetchPlaceHolder */ './about'),
    auth: false,
    exact: true,
    title: '骨科简介',
  }, {
    path: '/hospital/optest/guide',
    name: 'OptestGuide',
    component: () => import(/* webpackPrefetchPlaceHolder */ './guide'),
    auth: false,
    exact: true,
    title: '首诊必读',
  },
];
export default route;

