import React, { useCallback, useEffect, useState } from 'react';
import { Card, InputNumber, StaticToast } from 'src/components/common';
import { fetchJson } from 'src/utils';
import cookies from 'src/utils/cookie';
import format from 'src/utils/format';
import { Button, Cell, DatePicker, Input } from 'zarm';
import './opuser.scss';

const prefixCls = 'optest_userinfo_page';

const UserInfo = (props) => {
  const [userInfo, setUserInfo]: any = useState({
    sex: 'M',
    name: '',
    birthday: '',
    weight: '',
  });
  const [datePickerVisible, setDatePickerVisible] = useState(false);

  useEffect(() => {
    fetchJson({
      url: '/api/api/v1/patient/osteolysistest/query/detail',
      type: 'POST',
      data: {
        openId: cookies.get('openId') || '',
        channelSource: cookies.get('channelSource'),
        channelResourceCode: cookies.get('channelResourceCode'),
      },
      needLogin: false,
      isloading: true,
      success: res => {
        if (res && res.code === '0') {
          const { name = '', weight = '', birthday = '', gender = '' } = res.result
          setUserInfo({
            name,
            weight,
            birthday: birthday.slice(0, 10),
            sex: gender
          })
        }
      }
    });
  }, []);
  const infoChange = useCallback((key, value) => {
    setUserInfo((preInfo) => {
      return {
        ...preInfo,
        [key]: value,
      }
    })
  }, []);

  const goTo = useCallback(() => {
    const list = Object.values(userInfo);
    console.log(list);
    if (list.length < 4 || list.indexOf('') > -1) {
      StaticToast.error('请填写完所有信息');
      return;
    }
    const { name, weight, birthday, sex } = userInfo;
    props.history.push({
      pathname: '/hospital/optest/question',
      search: `weight=${weight}&birthday=${birthday}&sex=${sex}&name=${name}`
    })
  }, [userInfo]);

  return (
    <div className={prefixCls}>
      <Card prefixCls={`${prefixCls}__card`}>
        <Cell className={`${prefixCls}__cell`} title="就诊人姓名">
          <Input
            type="text"
            placeholder="请输入您的姓名"
            onChange={(value) => { infoChange('name', value) }}
            value={userInfo.name}
            maxLength={15}
          />
        </Cell>
        <Cell className={`${prefixCls}__cell`} title="性别">
          <p className='cell_sex'>
            <span className={`${userInfo.sex === 'M' ? 'active' : ''} cell_sex_option`} onClick={() => { infoChange('sex', 'M') }}>男</span>
            <span className={`${userInfo.sex === 'F' ? 'active' : ''} cell_sex_option`} onClick={() => { infoChange('sex', 'F') }}>女</span>
          </p>
        </Cell>
        <Cell className={`${prefixCls}__cell`} title="出生日期" hasArrow onClick={() => { setDatePickerVisible(true); }}>
          <p className='cell_date'>{userInfo.birthday || <span className='placeholder'>请选择您的出生日期</span>}</p>
        </Cell>
        <Cell className={`${prefixCls}__cell`} title="体重(kg）">
          <InputNumber
            type="number"
            placeholder="请输入您的体重"
            onChange={(value) => infoChange('weight', value)}
            value={userInfo.weight}
            maxLength={6}
            onBlur={() => blur()}
          />
        </Cell>
      </Card>
      <div className='btn_test_wrap'>
        <Button theme="primary" block shape="round" onClick={() => goTo()}>去测试</Button>
      </div>
      <DatePicker
        visible={datePickerVisible}
        mode="date"
        value={userInfo.birthday}
        max={new Date()}
        min={'1900-01-01'}
        onOk={(value) => {
          infoChange('birthday', format.date(value, 'yyyy-MM-dd'));
          setDatePickerVisible(false);
        }}
        onCancel={() => setDatePickerVisible(false)}
      />
    </div>)
}

export default UserInfo;