@import "src/style/index";
$prefixCls: 'optest_userinfo_page';

.#{$prefixCls} {
  &__card {
    margin: r(15) r(15) r(25);
    border-radius: r(8);
  }

  &__cell {
    &:first-child {
      &::after {
        display: none;
      }
    }

    &::after {
      left: r(8);
      right: r(8);
      width: auto;
      transform: scale(1, 0.5);
    }

    .za-cell__content {
      font-size: r(14);
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: bold;
      color: #333;
      justify-content: flex-end;
    }

    .za-cell__title {
      font-size: r(15);
      color: #8d8e93;
    }

    input {
      text-align: right;
    }

    input::placeholder,
    .placeholder {
      font-family: PingFangSC-Regular, PingFang SC;
      color: #979797;
      font-weight: 400;
    }

    .cell_date {
      text-align: right;

      @include flex;
    }

    .cell_sex {
      width: r(92);
      height: r(28);
      border: r(1) solid #00bc70;
      border-radius: r(6);
      overflow: hidden;
    }

    .cell_sex_option {
      display: inline-block;
      width: 50%;
      height: 100%;
      line-height: 1.9;
      color: #00bc70;
      text-align: center;

      &.active {
        background: #00bc70;
        color: #fff;
      }
    }

    .za-cell__arrow {
      &::after {
        width: r(14);
        height: r(14);
        margin-top: r(5);
        border-right: r(2) solid #d6d6d6;
        border-top: r(2) solid #d6d6d6;
        transform: scale(0.66) rotate(45deg);
      }
    }

    .za-input__clear {
      display: none;
    }

    .za-input__content {
      text-align: right;
    }

    .za-input__virtual-input {
      @include justify-content(flex-end);
    }
  }

  .btn_test_wrap {
    margin: 0 r(30);
  }
}
