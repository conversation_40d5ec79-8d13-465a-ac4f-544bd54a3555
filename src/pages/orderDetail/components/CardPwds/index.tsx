import React, { CSSProperties, useImperativeHandle, useState } from 'react';
import { CopyToClipboard } from 'react-copy-to-clipboard';
import CloseIcon from 'src/images/icon_lucency_close.png';
import { Popup } from 'zarm';
import { Button } from 'zarm-v3';
import './index.scss';

const container: CSSProperties = {
  background: '#fff',
  textAlign: 'center',
  position: 'relative',
  borderRadius: '8px',
  padding: '14px',
};

const desc: CSSProperties = {
  fontSize: '16px',
  fontWeight: 600,
  color: '#1E1E1E',
  lineHeight: '23px',
};

const closeIcon: CSSProperties = {
  position: 'absolute',
  bottom: '-3rem',
  left: '50%',
  transform: 'translateX(-50%)',
  width: '36px',
};

function CardPwds({ qrRef, pwds, onCopyResult }: { qrRef: any, pwds: string[], onCopyResult: (result: boolean) => void }) {
  const [visible, setVisible] = useState(false);

  const toggle = () => setVisible(!visible);

  useImperativeHandle(qrRef, () => ({
    toggle,
  }));

  return (
    <div className={'card-pwds'}>
      <Popup
        visible={visible}
        direction='center'
        width='80%'
        onMaskClick={toggle}
      >
        <div style={container}>
          <div className='card-pwds__title'>全部兑换码</div>
          <div className='card-pwds__section'>
            <div className='card-pwds__section-title'>
              权益兑换码：
            </div>
            <div className='card-pwds__section-list'>
              {
                pwds.map((item, index) => (
                  <div className='card-pwds__section-item' key={item}>
                    {item}
                    <CopyToClipboard
                      text={item}
                      onCopy={(text, result) => {
                        onCopyResult(result);
                      } }
                    >
                      <Button size='sm' theme='default'>复制</Button>
                    </CopyToClipboard>
                  </div>
                ))
              }
            </div>
          </div>
          <img style={closeIcon} onClick={toggle} src={CloseIcon} alt='' />
        </div>
      </Popup>
    </div>
  );
}

export default CardPwds;
