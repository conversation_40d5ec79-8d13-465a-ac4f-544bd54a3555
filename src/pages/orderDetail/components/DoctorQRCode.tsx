/**
 * Created by ylkj on 2021/11/2 at 2:48 下午.
 */

import React, { CSSProperties, useImperativeHandle, useState } from 'react';
import { Popup } from 'zarm';

const container: CSSProperties = {
  background: '#fff',
  textAlign: 'center',
  position: 'relative',
  borderRadius: '8px',
  padding: '14px',
};

const desc: CSSProperties = {
  fontSize: '16px',
  fontWeight: 600,
  color: '#1E1E1E',
  lineHeight: '23px',
};

const weightFont: CSSProperties = {
  color: '#00A864',
};

const qrImg: CSSProperties = {
  marginTop: '12px',
  width: '80%',
};

const closeIcon: CSSProperties = {
  position: 'absolute',
  bottom: '-3rem',
  left: '50%',
  transform: 'translateX(-50%)',
  width: '36px',
};

function DoctorQRCode({ qrRef, qrcode, qrText = '医生' }: { qrRef: any, qrcode?: string, qrText?: string }) {
  const [visible, setVisible] = useState(false);

  const toggle = () => setVisible(!visible);

  useImperativeHandle(qrRef, () => ({
    toggle,
  }));

  return (
    <div className={'DoctorQRCode'}>
      <Popup
        visible={visible}
        direction='center'
        width='80%'
        onMaskClick={toggle}
      >
        <div style={container}>
          <p style={desc}>
            请<span style={weightFont}>长按识别</span>二维码<br/>或者<span style={weightFont}>点击保存</span>图片后扫码添加{qrText}微信
          </p>
          <img style={qrImg} src={qrcode || require('../../massageTherapy/success/imgs/qrcode.png')} alt='' />
          <img style={closeIcon} onClick={toggle} src={require('../../../images/icon_lucency_close.png')} alt='' />
        </div>
      </Popup>
    </div>
  );
}

export default DoctorQRCode;
