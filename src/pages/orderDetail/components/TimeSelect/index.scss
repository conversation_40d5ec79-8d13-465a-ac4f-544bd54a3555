@import 'src/style/index';

.TimeSelect {
  .reserve_time_popup {
    .za-popup {
      width: 100% !important;

      .popup_box {
        width: 100%;
        border-radius: r(8) r(8) 0 0;
        background-color: #fff;

        &__header {
          display: flex;
          align-items: center;
          line-height: r(50);
          border-bottom: r(1) solid #e6e6e6;

          &__title {
            width: 100%;
            margin-right: r(-38);
            font-size: r(16);
            font-weight: 600;
            text-align: center;
            color: #1e1e1e;
          }

          &__close {
            color: #d8d8d8;
            font-size: r(23);
            margin-right: r(15);
          }
        }

        &__body {
          max-height: r(420);
          display: flex;
          overflow-y: scroll;

          &__date {
            flex-shrink: 0;
            width: r(135);
            line-height: r(40);
            font-size: r(13);
            text-align: center;
            color: #666;
            background-color: #f8f8f8;
            overflow-y: scroll;

            &__item.active {
              font-weight: 600;
              color: var(--text-base-color);
              background-color: #fff;
            }
          }

          &__time {
            flex-grow: 1;
            padding: r(10) r(15);
            background-color: #fff;
            overflow-y: scroll;

            &__item {
              display: inline-block;
              width: calc(50% - r(5));
              height: r(30);
              line-height: r(30);
              margin-bottom: r(10);
              text-align: center;
              border-radius: r(4);
              border: r(1) solid rgba(0, 0, 0, 0.2);

              &:nth-child(2n + 1) {
                margin-right: r(10);
              }

              &.active {
                font-weight: 600;
                color: var(--text-base-color);
                background-color: rgba(0, 183, 109, 0.06);
                border: r(1) solid rgba(0, 183, 109, 0.5);
              }

              &.disabled {
                border: none;
                color: #999;
                background-color: #f5f5f5;
              }
            }
          }
        }

        &__footer {
          width: 100%;
          padding: r(10) r(15);
          padding-bottom: calc(env(safe-area-inset-bottom) + r(10));
          background-color: #fff;
          border-top: r(1) solid #e6e6e6;
        }
      }
    }
  }
}
