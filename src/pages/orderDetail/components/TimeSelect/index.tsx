/**
 * Created by ylkj on 2021/11/8 at 10:58 上午.
 */

import classnames from 'classnames';
import React, { useCallback, useEffect, useImperativeHandle, useRef, useState } from 'react';
import { shallowEqual, useSelector } from 'react-redux';
import { StaticToast } from 'src/components/common';
import { Button, Icon, Popup } from 'zarm';
import { ApplicationState } from '../../../../store';
import { fetchJson } from '../../../../utils';
import format from '../../../../utils/format';
import { BookingScheduleItemOptions } from '../../../massageTherapy/reserve';
import { changeAppointment } from './api';
import './index.scss';

let orderId = '';
function TimeSelect({ timeRef, success }) {
  const mountPoint: any = useRef(undefined);
  const [popupVisible, setPopupVisible] = useState(false);
  const [bookingScheduleList, setBookingScheduleList] = useState<BookingScheduleItemOptions[]>([]);
  const [selectedBooking, setSelectedBooking] = useState<BookingScheduleItemOptions>({});
  const [selectedChildrenBooking, setSelectedChildrenBooking] = useState<BookingScheduleItemOptions>({});
  const { children = [] } = selectedBooking;

  const { productInfo } = useSelector((state: ApplicationState) => {
    const { shopInfo, productInfo } = state.massageTherapy;
    return {
      shopInfo,
      productInfo,
    };
  }, shallowEqual);

  useImperativeHandle(timeRef, () => ({
    select: openPopup,
  }));

  useEffect(() => {
    getScheduleList();
    pageScrollTop();
  }, [productInfo]);

  const pageScrollTop = useCallback(() => {
    window.scroll({ top: 0, behavior: 'smooth' });
  }, []);

  // 获取排班时间
  const getScheduleList = () => fetchJson({
    type: 'POST',
    url: '/api/api/v1/patient/appointment/schedule/list',
    data: { staffType: 'physiotherapy', days: 7 },
    isloading: true,
  }).then((res) => {
    const { code = '', result = [] } = res;
    if (code === '0') {
      const bookingSchedule = result.reduce((prev, cur) => {
        const key = cur.enableAppointDate;
        cur.startTime = format.date(cur.enableAppointStartTime, 'hh:mm');
        cur.endTime = format.date(cur.enableAppointEndTime, 'hh:mm');
        // eslint-disable-next-line no-prototype-builtins
        if (prev.hasOwnProperty(key)) {
          prev[key].children.push(cur);
        } else {
          prev[key] = cur;
          prev[key].week = `周${'日一二三四五六'.charAt(new Date(cur.enableAppointDate.replace(/-/g, '/')).getDay())}`;
          prev[key].children = [cur];
        }
        return prev;
      }, {});
      setBookingScheduleList(Object.keys(bookingSchedule).map((k) => bookingSchedule[k]));
    }
  });

  // 预约时间
  const openPopup = useCallback(
    (data) => {
      if (!bookingScheduleList.length) {
        StaticToast.error('抱歉，暂无可预约时间');
        return;
      }
      setSelectedBooking(bookingScheduleList[0] || {});
      setPopupVisible(true);
      orderId = data.orderId;
    },
    [bookingScheduleList],
  );

  // 选取时间
  const confirmSelected = useCallback(() => {
    if (!selectedChildrenBooking.id) {
      StaticToast.error('请选择预约的时间');
      return;
    }

    changeAppointment(orderId, selectedChildrenBooking.id).then((res) => {
      const { code = '' } = res;
      if (code === '0') {
        setPopupVisible(false);
        getScheduleList();
        success && success();
        return;
      }
      StaticToast.error(res.message);
    });
  }, [selectedChildrenBooking]);

  return (
    <div className='TimeSelect'>
      <div
        ref={mountPoint}
        id='mount_point'
        style={{
          position: 'relative',
          zIndex: 1,
        }}
      />
      <Popup className='reserve_time_popup' visible={popupVisible} direction='bottom' mountContainer={() => mountPoint.current} afterOpen={() => console.log('打开')} afterClose={() => console.log('关闭')} destroy={false}>
        <div className='popup_box'>
          <div className='popup_box__header'>
            <p className='popup_box__header__title'>预约时间</p>
            <Icon onClick={() => setPopupVisible(false)} className='popup_box__header__close' type='wrong' />
          </div>
          <div className='popup_box__body'>
            <div className='popup_box__body__date'>
              {bookingScheduleList.map((item, i) => (
                <div
                  key={`${item.id}${i}`}
                  onClick={() => {
                    setSelectedBooking(item);
                    setSelectedChildrenBooking({});
                  }}
                  className={classnames('popup_box__body__date__item', { active: item.id == selectedBooking.id })}
                >
                  {item.enableAppointDate} {item.week}
                </div>
              ))}
            </div>
            <div className='popup_box__body__time'>
              {children.map((k, index) => {
                const isDisabled = !k.remainAppointCount || k.remainAppointCount <= 0;
                return (
                  <div key={`children${k.id}${index}`} onClick={() => !isDisabled && setSelectedChildrenBooking(k)} className={classnames('popup_box__body__time__item', { active: !isDisabled && k.id == selectedChildrenBooking.id, disabled: isDisabled })}>
                    {k.startTime}
                  </div>
                );
              })}
            </div>
          </div>
          <div className='popup_box__footer'>
            <Button onClick={confirmSelected} block shape='round' theme='primary'>
              确认
            </Button>
          </div>
        </div>
      </Popup>
    </div>
  );
}

export default TimeSelect;
