import classnames from 'classnames';
import dayjs from 'dayjs';
import React, { useCallback, useEffect, useRef, useState } from 'react';
import { CopyToClipboard } from 'react-copy-to-clipboard';
import { shallowEqual, useDispatch, useSelector } from 'react-redux';
import { RouteComponentProps } from 'react-router-dom';
import { Card, DrugItem, FormatPrice, InvoicePopup, O2OExpress, StaticToast, SvgIcon } from 'src/components/common';
import ClaimTips from 'src/pages/shoppingCart/components/ClaimTips';
import { ApplicationState } from 'src/store';
import { cancel_order, fetch_order_detail } from 'src/store/order/action';
import sprite_spot_icon from 'src/svgs/sprite-address-spot-new.svg';
import { fetchJson, validate } from 'src/utils';
import { commonPay } from 'src/utils/pay';
import { Deserialize } from 'src/utils/serialization';
import { ORDER_STATUS_OBJ, REFUND_STATUS_OBJ, THIRD_PLATFORM_RESOURCECODE } from 'src/utils/staticData';
import { Button, Modal } from 'zarm';
import CardPwds from './components/CardPwds';
import DoctorQRCode from './components/DoctorQRCode';
import TimeSelect from './components/TimeSelect';
import './orderdetail.scss';
import VenderHeader from '../solidMedicine/components/VenderHeader';
import { isYSB } from 'src/utils/tool';

const HEADER = {
  // 待支付
  1: {
    class: 'wait_pay',
    icon: require('src/svgs/sprite-RMB.svg'),
    background: require('./images/status-waitpay.png'),
  },
  // 支付失败
  2: {
    class: 'pay_failure',
    icon: require('src/svgs/sprite-close.svg'),
    background: require('./images/status-close.png'),
  },
  // 审核中
  3: {
    class: 'in_review',
    icon: require('src/svgs/sprite-ongoing.svg'),
    background: require('./images/status-wait.png'),
  },
  // 审核失败
  4: {
    class: 'review_failure',
    icon: require('src/svgs/sprite-ongoing.svg'),
    background: require('./images/status-close.png'),
  },
  // 待发货
  5: {
    class: 'wait_deliever',
    icon: require('src/svgs/sprite-wait-deliever.svg'),
    background: require('./images/status-wait.png'),
  },
  // 已发货
  6: {
    class: 'has_deliever',
    icon: require('src/svgs/sprite-wait-deliever.svg'),
    background: require('./images/status-wait.png'),
  },
  // 已完成
  7: {
    class: 'compelete',
    icon: require('src/svgs/sprite-right.svg'),
    background: require('./images/status-complete.png'),
  },
  // 已退款
  8: {
    class: 'compelete',
    icon: require('src/svgs/sprite-right.svg'),
    background: require('./images/status-complete.png'),
  },
  // 支付中
  9: {
    class: 'in_review',
    icon: require('src/svgs/sprite-ongoing.svg'),
    background: require('./images/status-wait.png'),
  },
  // 已开票
  10: {
    class: 'compelete',
    icon: require('src/svgs/sprite-right.svg'),
    background: require('./images/status-complete.png'),
  },
  // 已预约
  11: {
    class: 'compelete',
    icon: require('src/svgs/sprite-right.svg'),
    background: require('./images/status-complete.png'),
  },
  // 已取消
  99: {
    class: 'cancel',
    icon: require('src/svgs/sprite-close.svg'),
    background: require('./images/status-cancel.png'),
  },
};

/**
 * 展示为微信支付的支付方式
 * 8: 益丰支付
 * 61: 九州通h5支付
 */
const showWechatPayWay = [8, 61];

const OrderDetail = (props: RouteComponentProps) => {
  const [invoicePopupVisible, setInvoicePopupVisible] = useState(false);
  const [hasInvoice, setHasInvoice] = useState(false);
  const [invoiceDetail, setInvoiceDetail]: any = useState({});
  const [o2oInfo, setO2oInfo]: any = useState({});
  const [showMore, setShowMore] = useState(false);
  const dispatch = useDispatch();
  const cancelOrder = (options, onSuccess?: any) => dispatch(cancel_order(options, onSuccess));
  const {
    location: { search = '' },
  } = props;
  const { orderId = '', prescriptionNo, directPay } = Deserialize(search);
  const { orderNo: searchOrderNo = '' } = Deserialize(search);
  const { orderDetail, address, couponDiscount } = useSelector((state: ApplicationState) => {
    // let servpackDiscount: any = {};
    const { orderAmount = '', expressFee = '', orderRealAmount = '' } = state.order.detail;
    const couponDiscount = Number(orderAmount) + Number(expressFee) - Number(orderRealAmount);
    return {
      orderDetail: state.order.detail,
      address: state.order.detail.userDeliveryAddress || {},
      couponDiscount,
    };
  }, shallowEqual);

  const {
    orderStatus = '',
    isExistsExprss = '',
    orderGoods = [],
    orderAmount = '',
    expressFee = '',
    orderRealAmount = '',
    orderNo = '',
    orderTime = '',
    channelSource = '',
    businessEntry = [],
    orderRefund,
    id,
    orderType,
    decoctionAmount,
    vasCode,
    orderPay = {},
    cardInstancePwdList = [],
    subOrderType,
  } = orderDetail;

  const { deliveryWay, orderExtraInfo = {} } = orderDetail;

  const qrRef = useRef<{ toggle: () => {} }>();
  const cardQrRef = useRef<{ toggle: () => {} }>();
  const cardPwdsRef = useRef<{ toggle: () => {} }>();
  const timeRef = useRef<{ select: Function }>();

  // 根据订单状态渲染相应的头部
  const Header = ({ status, orderTime, orderRefund, orderNo, platformCode }) => {
    if (!status) {
      return null;
    }
    const renderData = HEADER[status] || {};
    let statusInfo = ORDER_STATUS_OBJ[status];
    if (status === 6 && platformCode === THIRD_PLATFORM_RESOURCECODE.UNION_DRUG) {
      statusInfo = '待取货';
    }
    return (
      <div className={classnames('orderdetail_header', renderData.class)}>
        <img className={'background-image'} alt='' src={renderData.background}></img>
        <div className='status'>
          <span>{statusInfo}</span>
          {orderRefund && <span className='onRefund'>·{REFUND_STATUS_OBJ[orderRefund.refundStatus]}</span>}
          {status === 1 && countdown(orderTime, orderNo)}
        </div>
      </div>
    );
  };
  useEffect(() => {
    getInvoiceData();
  }, [orderId, orderDetail.id]);

  const getInvoiceData = () => {
    if (orderId || orderDetail.id) {
      fetchJson({
        url: '/api/api/v1/patient/order/invoice/detail',
        type: 'POST',
        data: {
          orderId: orderId || id,
          options: {
            isQueryAttachment: true,
          },
        },
      }).then((res) => {
        if (res.code === '0') {
          setHasInvoice(res.result ? true : false);
          setInvoiceDetail(res.result);
        }
      });
    }
  };

  const countdown = (orderTime, orderNo) => {
    console.log('orderTime', orderTime);
    // eslint-disable-next-line react-hooks/rules-of-hooks
    const [minute, setMinute] = useState(0);
    // eslint-disable-next-line react-hooks/rules-of-hooks
    const [second, setSecond] = useState(0);
    const expiredTime = dayjs(orderTime).add(30, 'minute');
    // eslint-disable-next-line react-hooks/rules-of-hooks
    useEffect(() => {
      const si = setInterval(() => {
        const diffMinutes = dayjs(expiredTime).diff(dayjs(), 'minute');
        const diffSeconds = dayjs(expiredTime).diff(dayjs(), 'second') - diffMinutes * 60;
        console.log('diffMinutes', diffMinutes, diffSeconds);
        if (diffSeconds >= 0 && diffMinutes >= 0) {
          setMinute((v) => diffMinutes);
          setSecond((v) => diffSeconds);
        }

        if (diffMinutes === 0 && diffSeconds === 0) {
          cancelOrder({ orderId: orderId || id }, () => {
            window.location.reload();
          });
        }
      }, 1000);
      return () => clearInterval(si);
    }, []);
    const zeroPad = (value) => {
      if (value < 10) {
        // eslint-disable-next-line @typescript-eslint/restrict-plus-operands
        return '0' + value;
      }
      return value;
    };
    const zeroPadMinute = zeroPad(minute);
    const zeroPadSecond = zeroPad(second);
    return (
      <span className='countdown'>
        请在{zeroPadMinute}分{zeroPadSecond}秒 内支付
      </span>
    );
  };

  const fetchOrderDetail = (options: any, onSuccess?: any) => dispatch(fetch_order_detail(options, onSuccess));

  useEffect(() => {
    initData();
  }, []);

  const toPay = (e, order) => {
    const { thirdOrderId = '', thirdOrderNo = '', orderTime = '', orderRealAmount = '', id = '', platformCode = '', servpackBizType, relatedBizNo = '', orderType } = order;
    e && e.stopPropagation();
    commonPay({
      orderRealAmount,
      platformCode,
      thirdOrderId: thirdOrderId || thirdOrderNo,
      orderTime,
      orderNo: order.orderNo,
      id,
      orderType,
      servpackBizType,
      relatedBizNo,
    });
  };

  const initData = () => {
    if (orderId) {
      fetchOrderDetail({
        orderId,
        option: {
          isQueryExpressLogistics: true,
          isQueryDeliveryAddress: true,
          isQueryOrderPreference: true,
          isQueryGoods: true,
          isQueryRefund: true,
          isQueryPay: true,
        },
      });
    } else if (searchOrderNo) {
      fetchOrderDetail({
        orderNo: searchOrderNo,
        option: {
          isQueryExpressLogistics: true,
          isQueryDeliveryAddress: true,
          isQueryOrderPreference: true,
          isQueryGoods: true,
          isQueryRefund: true,
          isQueryPay: true,
        },
      });
    } else if (prescriptionNo) {
      fetchOrderDetail({
        relatedBizNo: prescriptionNo,
        option: {
          isQueryExpressLogistics: true,
          isQueryDeliveryAddress: true,
          isQueryOrderPreference: true,
          isQueryGoods: true,
          isQueryRefund: true,
          isQueryPay: true,
        },
      }, (res) => {
        if(res.orderStatus === 1 && directPay === '1') {
          toPay(null, res);
        }
      });
    }
  };

  useEffect(() => {
    if (deliveryWay === '2') {
      fetchJson({
        url: '/api/api/v1/patient/order/o2o/query',
        type: 'POST',
        data: {
          orderNo: orderDetail.orderNo,
        },
      }).then((res) => {
        if (res.code === '0') {
          setO2oInfo(res.result || {});
        }
      });
    }
  }, [deliveryWay]);

  const toExpressList = useCallback(() => {
    props.history.push({
      pathname: '/hospital/expressList',
      search: `orderId=${orderId || id}`,
    });
  }, [orderId, id]);

  const toCheckInvoice = useCallback(() => {
    setInvoicePopupVisible(true);
  }, [orderDetail]);

  const isPhysiotherapy = orderType === 'physiotherapy';

  if (!orderStatus) {
    return null;
  }
  // 取消订单
  const toCancelOrder = (orderId) => {
    Modal.confirm({
      title: '确认信息',
      content: '确定取消该订单',
      onCancel: () => { },
      onOk: () => {
        cancelOrder({ orderId }, () => {
          fetchOrderDetail({
            orderId,
            option: {
              isQueryDeliveryAddress: true,
              isQueryOrderPreference: true,
              isQueryGoods: true,
            },
          });
        });
      },
    });
  };

  const toRefoundOrder = (orderId) => {
    Modal.confirm({
      title: '确认信息',
      content: '确定取消退款？',
      onCancel: () => { },
      onOk: () => {
        cancelOrder({ orderId }, () => {
          fetchOrderDetail({
            orderId,
            option: {
              isQueryDeliveryAddress: true,
              isQueryOrderPreference: true,
              isQueryGoods: true,
              isQueryRefund: true,
            },
          });
        });
      },
    });
  };
  const gotoPrecription = () => {
    props.history.push({
      pathname: '/hospital/report',
      state: {
        inquiryId: businessEntry[0].inquiryId,
      },
    });
  };

  function changeAppointment() {
    timeRef.current!.select({ orderId: orderId || id });
  }

  const renderOprateButton = ({ orderStatus, isExistsExprss, deliveryWay, subOrderType, supportSjxyxCancelOrder }) => {
    const showToPay = orderStatus === 1;
    /**
     * 判断是否显示退款按钮
     *
     * 显示条件：
     * 1. supportSjxyxCancelOrder不为false
     * 2. 且满足以下任一条件：
     *    - 服务订单且订单状态为5（已完成）
     *    - 订单状态为1（待支付）
     *    - 订单状态为11且允许更改预约
     *    - 平台为SYY_SPHCHINA且订单状态为3（待发货）
     */
    const showRefund = supportSjxyxCancelOrder === false ? false : orderType === 'serviceOrder' && orderStatus === 5 ? true : orderStatus === 1 || (orderStatus === 11 && orderDetail.allowChangeAppointment) ||
    (platformCode === THIRD_PLATFORM_RESOURCECODE.SYY_SPHCHINA && orderStatus === 3);
    const showExpress = (orderStatus === 6 || orderStatus === 7 || orderStatus === 10) && isExistsExprss === 'Y';
    const showAppoint = orderStatus === 11 && orderDetail.allowChangeAppointment;
    const hasCancelOrder = (orderStatus === 5 || (platformCode === THIRD_PLATFORM_RESOURCECODE.KYUSHU_POP && orderStatus === 3)) && (orderRefund || {}).refundStatus !== 1 && (orderRefund || {}).refundStatus !== 2;
    const showService = true;
    /** 服务商品绑卡券 */
    const isHpCardService = ['hpCard'].includes(subOrderType);
    const showCardEnter = [3, 5, 6, 7, 10].includes(orderStatus) && isHpCardService;
    const showRights = ([3, 5, 6, 7, 10].includes(orderStatus) && orderType === 'productServpack') && !showCardEnter;

    return (
      (showToPay || showRefund || showRefund || showExpress || showService || hasCancelOrder) && (
        <div className='pay-container'>
          <div className='button_wrapper'>
            {showRefund && (
              <Button className='cancel' shape='round' theme='primary' size='xs' ghost onClick={() => toCancelOrder(orderId || orderDetail.id)}>
                取消订单
              </Button>
            )}
            {hasCancelOrder && isYSB(vasCode) && (
              <Button className='cancel' shape='round' theme='primary' size='xs' ghost onClick={() => toRefoundOrder(orderId || orderDetail.id)}>
                申请退款
              </Button>
            )}
            {showAppoint && (
              <Button className='cancel' shape='round' theme='primary' size='xs' ghost onClick={() => changeAppointment()}>
                改约
              </Button>
            )}
            {showToPay && (
              <Button className='pay' shape='round' theme='primary' size='xs' onClick={(e) => toPay(e, orderDetail)}>
                立即支付
              </Button>
            )}
            {showExpress && deliveryWay != '2' && (
              <Button className='pay' shape='round' theme='primary' size='xs' ghost onClick={() => toExpressList()}>
                查看物流
              </Button>
            )}
            {
              showService && !isYSB(vasCode) && <Button
                className='pay'
                shape='round'
                theme='primary'
                size='xs'
                ghost
                onClick={() =>
                  window.location.href = validate.isAlipayApplet() ?
                    'https://webchat-sh.clink.cn/chat.html?accessId=1936f9be-d24f-46cf-b0d1-09d34b8bef9d&language=zh_CN'
                    : 'https://webchat-sh.clink.cn/chat.html?accessId=1936f9be-d24f-46cf-b0d1-09d34b8bef9d&language=zh_CN'
                }>
                  联系客服</Button>
            }
            {showRights && (
              <Button shape='round' theme='primary' style={{marginLeft: 10}} size='xs' onClick={() => props.history.push({
                pathname: '/hospital/group/rights',
                search: `?orderId=${orderId || id}`,
              })}>
                去使用
              </Button>
            )}
          </div>
        </div>
      )
    );
  };
  const noOprateButton = [2, 4, 8, 99].includes(orderStatus); // 有底部操作按钮时，最下方订单信息要距离底部有一定空间
  console.log('orderStatus', orderStatus);
  const { platformCode, userDeliveryAddress: { districtName = '', contactUserAddress = '', contactUserName = '', contactUserPhone = '' } = {} } = orderDetail;
  const [province, city, area] = districtName.split('-');

  const RenderInvoice = () => {
    const cannotApplyInvoicePlatform = [THIRD_PLATFORM_RESOURCECODE.ZAHY]; // ZA - 表示开药方为国药
    const showTicket = orderType !== 'serviceOrder' && [3, 5, 6, 7, 10].includes(orderStatus) &&
    [THIRD_PLATFORM_RESOURCECODE.ZAHY, THIRD_PLATFORM_RESOURCECODE.SYY_SPHCHINA, THIRD_PLATFORM_RESOURCECODE.ZA_HAOYAOSHI].includes(platformCode);
    // 购药是理赔的话，不能由用户开发票
    const isDrugInvoicing = orderType !== 'physiotherapy' && (orderExtraInfo.claimSettlementNo || cannotApplyInvoicePlatform.includes(platformCode));
    if (showTicket) {
      if (hasInvoice) {
        return (
          <div className='ticket-done'>
            <div className='ticket-title'>发票信息</div>
            <div className='cell'>
              <div className='label'>发票类型</div>
              <div className='cell-value'>电子普通发票</div>
            </div>
            <div className='cell'>
              <div className='label'>发票抬头</div>
              <div className='cell-value'>{invoiceDetail.invoiceTitleType === '1' ? '个人' : '单位'}</div>
            </div>
            <div className='tips'>*发票将在订单完成后24h内以短信形式发送到用户手机上</div>
          </div>
        );
      } else {
        return (
          isDrugInvoicing && (
            <div className='cell cell-ticket'>
              <div className='label'>发票</div>
              {orderExtraInfo.claimSettlementNo ? (
                <p className='goto-ticket ticket-tips'>购药可享受理赔，不支持个人申请发票</p>
              ) : (
                <div className='goto-ticket' onClick={toCheckInvoice}>
                  未开具发票
                  <SvgIcon className='icon-arrow' src={require('src/svgs/sprite-icon_arrow.svg')} />
                </div>
              )}
            </div>
          )
        );
      }
    } else {
      return null;
    }
  };
  const onCloseInvoicePopup = () => {
    setInvoicePopupVisible(false);
  };
  const onSuccessInvoicePopup = (value) => {
    fetchJson({
      url: '/api/api/v1/patient/order/invoice/save',
      type: 'POST',
      data: {
        ...value,
        invoiceType: '1',
      },
      success: (res) => {
        if (res && res.code === '0') {
          StaticToast.success('申请开票成功');
          setInvoicePopupVisible(false);
          getInvoiceData();
        }
      },
      error: (e) => {
        StaticToast.error(e.message);
      },
    });
  };

  const showDoctorQRCode = () => {
    qrRef.current!.toggle();
  };

  const onCopyResult = (result) => result ? StaticToast.success('复制成功') : StaticToast.error('复制失败，请手动复制');
  const list = showMore ? orderGoods : orderGoods.slice(0, 3);
  const isChinese = (businessEntry[0] || {}).presClassCode === 3;
  const handleMethod = ((businessEntry[0] || {}).prescriptionExt || {}).handleMethod;
  /** 展示卡券相关信息模块 */
  const isShowCardSection = ['hpCard'].includes(subOrderType) && cardInstancePwdList?.length > 0;
  /** 卡券企业微信二维码（不一定有） */
  const cardQrUrl = isShowCardSection ? orderGoods[0].cardDomain?.attachmentDomain?.attachmentDownloadUrl : '';

  return (
    <div className='orderdetail_page'>
      <VenderHeader history={props.history} platformCode={platformCode}></VenderHeader>
      <Header status={orderStatus} orderTime={orderTime} orderRefund={orderRefund} orderNo={orderNo} platformCode={platformCode} />
      {orderStatus === 1 && address.contactUserName && (
        <Card prefixCls='address'>
          <SvgIcon className='icon' src={sprite_spot_icon} />
          <div className='info'>
            <div className='username-phone'>
              {address.contactUserName || ''} {address.contactUserPhone || ''}
            </div>
            <div className='contactuser-address'>
              {(address.districtName || '').split('-').join('')}
              {address.contactUserAddress || ''}
            </div>
          </div>
        </Card>
      )}

      <Card prefixCls={orderStatus === 1 ? 'order' : 'order order-border-radius'}>
        {
          isShowCardSection && (
            <div className='card-section'>
              <div className='card-section__pwd'>
                <div className='card-section__pwd-title'>
                  权益兑换码：
                </div>
                <div className='card-section__pwd-list'>
                  {
                    cardInstancePwdList.slice(0, 3).map((item) => (
                      <div className='card-section__pwd-item' key={item}>
                        {item}
                        <CopyToClipboard
                          text={item}
                          onCopy={(text, result) => {
                            onCopyResult(result);
                          } }
                        >
                          <Button size='sm' theme='default'>复制</Button>
                        </CopyToClipboard>
                      </div>
                    ))
                  }
                  {
                    cardInstancePwdList.length > 3 && (
                      <div className='card-section__pwd-more' onClick={() => {
                        cardPwdsRef.current!.toggle();
                      }}>
                        全部兑换码 <i></i>
                      </div>
                    )
                  }
                </div>
              </div>
              {
                cardQrUrl && (
                  <div className='card-section__qr'>
                    <img className='card-section__qr-img' src={cardQrUrl} onClick={() => {
                      cardQrRef.current!.toggle();
                    }} />
                    <div className='card-section__qr-tip'>
                      <p>卡券服务使用过程中，如有问题可添加微信咨询服务人员</p>
                      <p>长按识别添加微信或截图保存在微信中扫码</p>
                    </div>
                  </div>
                )
              }
            </div>
          )
        }
        <div className='title'>商品详情</div>
        <div className='drug_wrapper'>
          {
            list.length ? list.map((good, i) => <DrugItem isChinese={isChinese} key={good.id || i} good={good} orderDetail={orderDetail} />) : null
          }
          {
            orderGoods.length > 3 ? <div
              onClick={() => {
                setShowMore(!showMore);
              }}
              style={{
                fontSize: '12px',
                color: '#00BC70',
                textAlign: 'center',
                paddingBottom: '12px',
              }}>{showMore ? '收起' : '展开全部'}
              <img style={{width: '12px'}} src={require(showMore? 'src/images/shouqi.png' : 'src/images/chakangengduo.png')} alt='' />
            </div> : null
          }
        </div>
        <div className='cell-box'>
          {
            isChinese &&
            <>
              <div className='cell'>
                <div className='label'>煎药方式</div>
                <div className='cell-value'>
                  {handleMethod}
                </div>
              </div>
              <div className='cell'>
                <div className='label'>煎药费用</div>
                <div className='num'>
                  <FormatPrice price={decoctionAmount} />
                </div>
              </div>
            </>
          }
          <div className='cell'>
            <div className='label'>商品总价</div>
            <div className='num'>
              <FormatPrice price={orderAmount} />
            </div>
          </div>

          {!isPhysiotherapy && (
            <div className='cell'>
              <div className='label'>运费</div>
              <div className='num'>
                <FormatPrice price={expressFee} />
              </div>
            </div>
          )}
          {couponDiscount ? (
            <div className='cell'>
              <div className='label'>{isYSB(vasCode) ? '直赔报销' : '优惠'}</div>
              <div className='num coupon'>
                - <FormatPrice price={couponDiscount} />
              </div>
            </div>
          ) : null}
        </div>
        {orderExtraInfo.calcResultExplain && <ClaimTips className={orderExtraInfo.claimSettlementNo ? '' : 'reject'} content={orderExtraInfo.calcResultExplain} />}
        <div className='cell real_pay'>
          <div className='unit'>
            <span className='real-pay-label'>实付金额：</span>
            <FormatPrice price={orderRealAmount} />
          </div>
        </div>
      </Card>
      {deliveryWay == '2' && <O2OExpress o2oInfo={o2oInfo} />}
      {businessEntry.length > 0 && (
        <div className='cell cell-patient'>
          <div className='label'>用药人:{businessEntry[0].patientName}</div>
          <div className='goto-precription' onClick={gotoPrecription}>
            查看问诊单
            <SvgIcon className='icon-arrow' src={require('src/svgs/sprite-icon_arrow.svg')} />
          </div>
        </div>
      )}
      {isPhysiotherapy && (
        <div className='cell cell-patient'>
          <div className='label'>为您提供免费的中医健康咨询服务</div>
          <div className='goto-precription' onClick={showDoctorQRCode}>
            添加医生微信
            <SvgIcon className='icon-arrow' src={require('src/svgs/sprite-icon_arrow.svg')} />
          </div>
        </div>
      )}
      <RenderInvoice />

      <Card prefixCls={noOprateButton ? 'order_info' : 'order_info  has-pay'}>
        <div className='order-info-title'>订单信息</div>
        <div className='cell'>
          <div className='label'>订单编号</div>
          <div className='cell-value'>
            <span>{orderNo}</span>
            <CopyToClipboard
              text={orderNo}
              onCopy={(text, result) => {
                onCopyResult(result);
              } }
            >
              <a rel='noopener noreferrer' className='link-divider-vertical'>&nbsp;|&nbsp;复制</a>
            </CopyToClipboard>
          </div>
        </div>
        {orderStatus !== 1 && contactUserPhone && (
          <div className='cell'>
            <div className='label'>{isPhysiotherapy ? '店铺地址' : '收货信息'}</div>
            <div className='cell-value address-value'>
              <div>
                {province} {city === '市辖区' ? '' : city}
                {area}
                {contactUserAddress}
              </div>
              <div>
                {isPhysiotherapy ? '使用者' : '收货人'} {contactUserName} {contactUserPhone}
              </div>
            </div>
          </div>
        )}

        <div className='cell'>
          <div className='label'>下单时间</div>
          <div className='cell-value'>{orderTime}</div>
        </div>
        {(orderStatus !== 1 && orderStatus !== 99) && (
          <div className='cell'>
            <div className='label'>付款方式</div>
            <div className='cell-value'>
              {
                channelSource === 'wechat_applet'
                  || (isYSB(vasCode) && showWechatPayWay.includes(orderPay.payWay))
                  ? '微信支付'
                  : !orderPay.payWay
                    ? ''
                    : '其他支付'}
            </div>
          </div>
        )}
      </Card>
      {renderOprateButton(orderDetail)}
      <InvoicePopup orderId={id} value={{}} visible={invoicePopupVisible} onClose={onCloseInvoicePopup} onSuccess={onSuccessInvoicePopup} />
      <DoctorQRCode qrRef={qrRef} />
      <DoctorQRCode qrRef={cardQrRef} qrcode={cardQrUrl} qrText='客服' />
      <CardPwds qrRef={cardPwdsRef} pwds={cardInstancePwdList} onCopyResult={onCopyResult} />
      {orderStatus === 11 && orderDetail.allowChangeAppointment && <TimeSelect timeRef={timeRef} success={initData} />}
    </div>
  );
};

export default OrderDetail;
