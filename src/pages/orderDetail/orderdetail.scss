@import 'src/style/index';

.orderdetail_page {
  padding-bottom: r(80);

  .background-image {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: r(100);
  }

  .orderdetail_header {
    position: relative;

    .status_icon {
      position: absolute;
      right: r(30);
      top: r(15);
    }

    .status {
      @include display-flex;
      @include align-items(center);

      position: relative;
      color: #fff;
      font-size: r(21);
      padding: r(20) r(10) r(20) r(15);

      .countdown {
        background-color: #fff;
        padding: r(2) r(5);
        color: #00a864;
        font-weight: bold;
        font-size: r(12);
        margin-left: r(10);
      }

      .text_icon {
        height: r(22);
        width: r(22);
        margin-right: r(5);
        color: #fff;
      }

      .on-refund {
        width: r(46);
        height: r(20);
        background: rgba(255, 255, 255, 0.16);
        border-radius: 4px;
        border: 1px solid rgba(255, 255, 255, 0.2);
        font-size: r(12);
        display: flex;
        align-items: center;
        justify-content: center;
        margin-left: r(10);
      }
    }

    &.wait_pay,
    &.in_review {
      .status_icon {
        width: r(90);
        height: r(62);
      }
    }

    &.pay_failure,
    &.review_failure,
    &.cancel {
      .status_icon {
        width: r(81);
        height: r(68);
      }
    }

    &.wait_deliever,
    &.has_deliever {
      .status_icon {
        width: r(95);
        height: r(54);
      }
    }

    &.compelete {
      .status_icon {
        width: r(86);
        height: r(68);
      }
    }
  }

  .address {
    padding: r(15) r(15) r(18) r(15);
    @include display-flex;
    @include align-items(center);
    @include justify-content(space-between);

    margin: 0;
    border-bottom: 1px solid #e6e6e6;
    @media (-webkit-min-device-pixel-ratio: 2) {
      border-bottom: 0.5px solid #e6e6e6;
    }

    .icon {
      width: r(18);
      height: r(18);
      margin-right: r(10);
      color: var(--theme-success);
    }

    .info {
      @include flex;

      font-size: r(14);

      .username-phone {
        color: #464646;
        margin-bottom: r(5);
        font-weight: bold;
      }

      .contactuser-address {
        color: #9b9b9b;
      }
    }

    .check_express {
      width: r(80);
      height: r(30);
      border-radius: r(4);
      border: r(1) solid var(--theme-success);
      color: var(--theme-success);
      line-height: r(28);
      text-align: center;
      font-size: r(14);
      font-weight: bold;

      &:active {
        color: #fff;
        background: var(--theme-success);
      }
    }
  }

  .order {
    margin: 0;

    &.order-border-radius {
      border-top-left-radius: r(8);
      border-top-right-radius: r(8);
    }

    .title {
      color: #464646;
      font-size: r(14);
      margin-top: r(10);
      margin-bottom: r(10);
      margin-left: r(15);
      font-weight: 500;
    }

    .drug_wrapper {
      background: #fbfbfb;
    }
  }

  .order_info {
    margin: r(10) 0 r(15) 0;

    &.has-pay {
      margin-bottom: r(90);
    }

    .cell {
      .label {
        width: 60px;
      }

      .cell-value {
        color: #1e1e1e;
      }
    }
  }

  .cell-box {
    margin-top: r(5);

    .cell:last-child {
      position: relative;

      &::after {
        content: '';
        position: absolute;
        background-color: #e6e6e6;
        height: r(1);
        left: r(15);
        right: r(15);
        bottom: 0;
      }
    }
  }

  .ticket-done {
    margin: r(10) 0;
    background-color: #fff;

    .ticket-title {
      padding: r(15);
      color: #333;
      font-size: r(15);
    }

    .tips {
      padding: r(12) r(15);
      color: #b2b2b2;
      font-size: r(12);
    }

    .cell-value {
      color: #1e1e1e;
    }
  }

  .cell {
    @include display-flex;
    @include align-items(center);
    @include justify-content(space-between);

    padding: r(10) r(15);
    color: #9b9b9b;
    font-size: r(14);

    .cell-value {
      color: #333;
    }

    .num.coupon {
      color: #ff5050;

      .integer,
      .point,
      .sign {
        color: #ff5050;
        font-weight: bold;
      }
    }

    &.cell-patient,
    &.cell-ticket {
      background-color: #fff;
      margin-top: r(10);

      .label {
        color: #333;
      }

      .goto-precription {
        color: #00a864;
      }

      .goto-ticket {
        color: #b2b2b2;
      }

      .icon-arrow {
        width: r(6);
        height: r(12);
        margin-left: r(3);
      }
    }

    .button {
      width: r(80);
      height: r(30);
      border-radius: r(4);
      margin-left: r(10);
      line-height: r(30);
      text-align: center;
      border: r(1) solid var(--theme-success);
      font-size: r(14);
      color: var(--theme-success);

      &:active {
        color: #fff;
        background: var(--theme-success);
      }
    }

    .label {
      color: #666;
      font-size: r(14);
      min-width: 5em;
    }

    .address-value {
      color: #666;
      font-size: r(14);
      text-align: right;
    }
  }

  .order-info-title {
    padding: r(15);
    background-color: #fff;
    color: #333;
    font-size: r(15);
  }

  .real_pay {
    margin-left: r(15);
    margin-right: r(15);
    padding: r(17) 0;
    color: #464646;
    display: flex;
    justify-content: flex-end;

    .integer,
    .point,
    .sign {
      color: #ff5050;
      font-weight: bold;
    }

    .integer {
      font-size: r(16);
    }

    .real-pay-label {
      font-size: r(14);
      color: #1e1e1e;
    }
  }

  .pay-container {
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
    height: r(80);
    background-color: #fff;
    border-top: 1px solid #e6e6e6;
    @media (-webkit-min-device-pixel-ratio: 2) {
      border-bottom: 0.5px solid #e6e6e6;
    }

    .button_wrapper {
      display: flex;
      justify-content: flex-end;
      margin-right: r(15);
      margin-top: r(12);

      .cancel,
      .pay {
        margin-right: r(12);
      }
    }
  }

  .drug:nth-last-child(2) {
    .info-price {
      border-bottom: 0 !important;
    }
  }

  .card-section {
    padding: r(15) r(15) 0;

    &__pwd {
      display: flex;
      justify-content: space-between;
      margin-bottom: r(10);

      &-title {
        font-size: r(14);
        font-weight: 500;
        font-family: PingFangSC-Semibold, PingFang SC;
      }

      &-list {
        display: flex;
        flex-direction: column;
        align-items: flex-end;

        button {
          margin-left: r(5);
          height: auto;
          font-size: r(12);
          padding: r(2) r(5);
          border-color: #00a864;
          color: #00a864;
        }
      }

      &-item {
        margin-bottom: r(3);
      }

      &-more {
        margin-top: r(3);
        font-size: r(12);
        color: #00a864;

        i {
          display: inline-block;
          position: relative;
          width: r(10);
          height: r(10);

          &::after,
          &::before {
            content: "";
            position: absolute;
            width: r(6);
            height: r(6);
            top: 50%;
            border-width: 1px 1px 0 0;
            border-style: solid;
            border-color: #bbb #bbb transparent transparent;
            transform: rotate(45deg) translateY(-50%);
          }

          &::after {
            left: r(-4);
            opacity: .7;
          }
        }
      }
    }

    &__qr {
      display: flex;
      align-items: center;
      background: #fbfbfb;
      margin: r(0) r(-15);
      padding: r(15);

      &-img {
        width: r(120);
      }

      &-tip {
        margin-left: r(15);

        p {
          margin-bottom: r(10);
          font-size: r(13);
        }
      }
    }
  }
}
