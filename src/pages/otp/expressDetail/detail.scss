@import "src/style/index";

.orderdetail_page {
  .no_data_bg {
    display: block;
    margin: r(130) auto 0;
    width: r(116);
    height: r(109);
  }

  .no_data_text {
    color: #9b9b9b;
    font-size: r(15);
    text-align: center;
    margin-top: r(10);
  }

  .express_detail_header {
    position: relative;

    .status_icon {
      position: absolute;
      right: r(30);
      top: r(15);
    }

    .status {
      @include display-flex;
      @include align-items(center);

      color: #fff;
      font-size: r(19);
      padding: r(30) r(20) r(25);

      .text_icon {
        height: r(22);
        width: r(22);
        margin-right: r(5);
        color: #fff;
      }
    }

    &.wait_pay,
    &.in_review {
      .status_icon {
        width: r(90);
        height: r(62);
      }
    }

    &.pay_failure,
    &.review_failure,
    &.cancel {
      .status_icon {
        width: r(81);
        height: r(68);
      }
    }

    &.wait_deliever,
    &.has_deliever {
      .status_icon {
        width: r(95);
        height: r(54);
      }
    }

    &.compelete {
      .status_icon {
        width: r(86);
        height: r(68);
      }
    }
  }

  .address {
    padding: r(10) r(15);
    @include display-flex;
    @include align-items(center);
    @include justify-content(space-between);

    .icon {
      width: r(14);
      height: r(18);
      margin-right: r(10);
    }

    .info {
      @include flex;

      font-size: r(14);

      & > p:first-of-type {
        color: #464646;
        margin-bottom: r(4);
      }

      & > p:last-of-type {
        color: #9b9b9b;
      }
    }

    .check_express {
      width: r(80);
      height: r(30);
      border-radius: r(4);
      border: r(1) solid var(--theme-success);
      color: var(--theme-success);
      line-height: r(28);
      text-align: center;
      font-size: r(14);
      font-weight: bold;

      &:active {
        color: #fff;
        background: var(--theme-success);
      }
    }
  }

  .order {
    padding: r(15);

    .title {
      color: #464646;
      font-size: r(14);
    }

    .drug_wrapper {
      margin-top: r(15);
      padding: r(10);
      background: #f0f0f0;
      border-radius: r(5);

      .drug {
        @include display-flex;

        font-size: r(14);
        margin-top: r(10);

        &:first-of-type {
          margin-top: 0;
        }

        .photo {
          width: r(80);
          height: r(80);
          background: #fff;
          border-radius: r(3);
        }

        .info {
          @include flex;

          margin: 0 r(15);

          .name {
            @include line(2);

            color: #464646;
            margin-bottom: r(5);
          }

          .sub_name {
            @include line(2);

            color: #9b9b9b;
          }
        }

        .price {
          text-align: right;

          .n {
            color: #9b9b9b;
          }
        }
      }
    }
  }

  .order_info {
    padding: r(10) r(20);
  }

  .cell {
    @include display-flex;
    @include align-items(center);
    @include justify-content(space-between);

    padding: r(4) 0;
    color: #9b9b9b;
    font-size: r(14);
  }

  .real_pay {
    padding-top: r(8);
    border-top: r(1) solid #e6e6e6;
    color: #464646;

    & > p:last-of-type {
      font-size: r(21);
      color: #ff5050;

      .unit {
        font-size: r(14);
      }
    }
  }
}
