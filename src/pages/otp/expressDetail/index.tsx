import classnames from 'classnames';
import React, { useState, useEffect, useCallback } from 'react';
import { useDispatch } from 'react-redux';
import { Card, StaticToast, Background, SvgIcon } from 'src/components/common';
import { ORDER_STATUS_OBJ } from 'src/utils/staticData';
import { Deserialize } from 'src/utils/serialization';
import { push_detail } from 'src/store/order/action';
import { fetchJson } from 'src/utils/fetch';
import './detail.scss';

import no_data_svg from 'src/svgs/no-data.svg';

const HEADER = {
  // 待支付
  1: {
    class: 'wait_pay',
    icon: require('src/svgs/sprite-RMB.svg'),
    background: require('src/svgs/status-wait.svg'),
  },
  // 支付失败
  2: {
    class: 'pay_failure',
    icon: require('src/svgs/sprite-close.svg'),
    background: require('src/svgs/status-close.svg'),
  },
  // 审核中
  3: {
    class: 'in_review',
    icon: require('src/svgs/sprite-ongoing.svg'),
    background: require('src/svgs/status-wait.svg'),
  },
  // 审核失败
  4: {
    class: 'review_failure',
    icon: require('src/svgs/sprite-ongoing.svg'),
    background: require('src/svgs/status-close.svg'),
  },
  // 待发货
  5: {
    class: 'wait_deliever',
    icon: require('src/svgs/sprite-wait-deliever.svg'),
    background: require('src/svgs/status-deliever.svg'),
  },
  // 已发货
  6: {
    class: 'has_deliever',
    icon: require('src/svgs/sprite-wait-deliever.svg'),
    background: require('src/svgs/status-deliever.svg'),
  },
  // 已完成
  7: {
    class: 'compelete',
    icon: require('src/svgs/sprite-right.svg'),
    background: require('src/svgs/status-complete.svg'),
  },
  // 已取消
  99: {
    class: 'cancel',
    icon: require('src/svgs/sprite-close.svg'),
    background: require('src/svgs/status-close.svg'),
  },
};


// 根据订单状态渲染相应的头部
const Header = ({ status }) => {
  if (!status) {
    return null;
  }
  const renderData = HEADER[status];
  return (
    <div className={classnames('express_detail_header', renderData.class)}>
      <Background singleCube={true} />
      <SvgIcon type='img' className='status_icon' src={renderData.background} />
      <div className='status'><SvgIcon className='text_icon' src={renderData.icon} />{ORDER_STATUS_OBJ[status]}</div>
    </div>
  );
};


const OptDetail = (props) => {

  const { location: { search = '' } } = props;
  const { orderNo = '', accountId = '' } = Deserialize(search);
  const [renderData, setRenderDate]: [any, any] = useState({});

  const dispatch = useDispatch();
  const pushDetail = (data: any) => dispatch(push_detail(data));

  useEffect(() => {
    if (orderNo !== '' && accountId !== '') {
      fetchJson({
        url: '/api/api/v1/otp/queryOrder',
        type: 'GET',
        data: {
          orderNo,
          accountId,
        },
        needLogin: false,
        success: (res) => {
          if (res && res.code === '0') {
            const { result = {} } = res;
            setRenderDate(result);
            pushDetail(result);
          }
        },
        error: (error) => {
          StaticToast.error(error.message);
        },
      });
    }

  }, [orderNo, accountId]);

  const toExpressList = useCallback(() => {
    props.history.push({
      pathname: '/hospital/expresslist',
      search: 'from=otp',
    });
  }, []);

  if (JSON.stringify(renderData) === '{}') {
    return <div className='orderdetail_page'>
      <SvgIcon type='img' className='no_data_bg' src={no_data_svg} />
      <p className='no_data_text'>查询不到数据</p>
    </div>;
  }

  return <div className='orderdetail_page'>
    <Header status={renderData.orderStatus} />
    {/* <Background singleCube={true}/> */}
    {/* <img className={classnames('bg_status_cover', background_img[renderData.orderStatus])} src={require(`src/images/${background_img[renderData.orderStatus]}.png`)} alt="" />
    <div className='head'>
      <div className={classnames('status', classname_data[renderData.orderStatus])}><img src={require(`src/images/${status_data[renderData.orderStatus]}.png`)} alt="" />{ORDER_STATUS_OBJ[renderData.orderStatus]}</div>
    </div> */}

    <Card prefixCls='address'>
      <img className='icon' src={require('src/images/icon_shopping_address.png')} alt='' />
      <div className='info'>
        <p>{renderData.userDeliveryAddress.contactUserName} {renderData.userDeliveryAddress.contactUserPhone}</p>
        <p>{renderData.userDeliveryAddress.districtName.split('-').join('')}{renderData.userDeliveryAddress.contactUserAddress}</p>
      </div>
      {(renderData.orderStatus === 6 || renderData.orderStatus === 7) ? <div className='check_express' onClick={() => toExpressList()}>
        查看物流
      </div> : null}
    </Card>

    <Card prefixCls='order'>
      <p className='title'>商品详情（共{renderData.orderGoods.length}件商品）</p>
      <div className='drug_wrapper'>
        {renderData.orderGoods && renderData.orderGoods.length ? renderData.orderGoods.map((good) => <div className='drug' key={`goods_${good.id}`}>
          <img className='photo' src={good.goodsEntry.drugsPictureList.length ? good.goodsEntry.drugsPictureList[0].attachment && `${good.goodsEntry.drugsPictureList[0].attachment.attachmentDownloadUrl}` : ''} />
          <div className='info'>
            <p className='name'>{good.goodsEntry.drugName}</p>
            <p className='sub_name'>{good.goodsEntry.drugCommonName} {good.goodsEntry.drugDosageForm}</p>
          </div>
          <div className='price'>
            <p className='p'>¥{good.goodsRealPrice}</p>
            <p className='n'>×{good.goodsNum}</p>
          </div>
        </div>) : null}
      </div>
      <div className='cell'>
        <p>总价</p><p style={{ color: '#464646' }}>¥{Number(renderData.orderAmount).toFixed(2)}</p>
      </div>
      <div className='cell'>
        <p>运费</p><p>¥{Number(renderData.expressFee).toFixed(2)}</p>
      </div>
      <div className='cell real_pay'>
        <p>实付</p><p><span className='unit'>¥</span> {Number(renderData.orderRealAmount).toFixed(2)}</p>
      </div>
    </Card>

    <Card prefixCls='order_info'>
      <div className='cell'>
        <p>订单编号</p><p>{renderData.orderNo}</p>
      </div>
      <div className='cell'>
        <p>下单时间</p><p>{renderData.orderTime}</p>
      </div>
      <div className='cell'>
        <p>付款方式</p><p>{renderData.channelSource === 'wechat_applet' ? '微信支付' : '其他支付'}</p>
      </div>
    </Card>
  </div>;
};

export default OptDetail;
