import React, { useCallback, useState, useEffect } from 'react';
import { StaticToast } from 'src/components/common';
import { fetchJson } from 'src/utils/fetch';
import useInterval from 'src/utils/useInterval';
import { Input } from 'zarm';
import './otp.scss';
import { pushEvent } from 'src/utils/pageTrack';

const OtpLogin = (props) => {
  const [key] = useState(window.location.pathname.split('/').pop() || '');
  const [needOtpLogin, setNeedOtpLogin] = useState(false);
  const [count, setCount] = useState(0);
  const [isRunning, setIsRunning] = useState(false);
  const [mobilePhone, setMobilePhone]: [any, any] = useState('');
  const [verifyCode, setVerifyCode]: [any, any] = useState('');

  useInterval(
    () => {
      setCount(count - 1);
    },
    isRunning ? 1000 : null,
  );

  useEffect(() => {
    count === 0 && setIsRunning(false);
  }, [count]);

  const blur = useCallback(() => {
    window.scroll({
      top: 0,
      behavior: 'smooth',
    });
  }, []);

  const getSendVerifyCode = useCallback(() => {
    fetchJson({
      url: '/api/api/v1/otp/sendVerifyCode',
      type: 'POST',
      data: {
        mobilePhone,
        key,
      },
      needLogin: false,
      success: (res) => {
        if (res && res.code === '0') {
          setCount(60);
          setIsRunning(true);
        }
      },
      error: (e) => {
        StaticToast.error(e.message);
      },
    });
  }, [mobilePhone]);

  const login = useCallback(() => {
    if (mobilePhone === '') {
      StaticToast.warning('请输入手机号码');
      return;
    }
    if (verifyCode === '') {
      StaticToast.warning('请输入短信验证码');
      return;
    }
    fetchJson({
      url: '/api/api/v1/otp/login',
      type: 'POST',
      data: {
        key,
        mobilePhone,
        verificationCode: verifyCode,
      },
      needLogin: false,
      success: (res) => {
        if (res && res.code === '0') {
          location.replace(res.result);
        } else {
          StaticToast.error(res.message);
        }
      },
      error: (error) => {
        StaticToast.error(error.message);
      },
    });
  }, [mobilePhone, verifyCode]);

  useEffect(() => {
    fetchJson({
      url: `/api/api/otp/${key}`,
      type: 'GET',
      needLogin: false,
      needToast: false,
      data: {
        key,
      },
      success: (res) => {
        if (res.code === '-100') {
          setNeedOtpLogin(true);
        } else if (res && res.code === '0') {
          const timeId = setTimeout(() => {
            console.log('进入setTimeout');
            location.replace(res.result);
          }, 200);
          pushEvent({
            eventTag: 'PAGE_SHORT_LINK',
            text: '跳转链接',
            attrs: {
              PAGE_SHORT_LINK_KEY: key,
            },
            callback: () => {
              console.log('进入pushEvent');
              clearTimeout(timeId);
              location.replace(res.result);
            },
          });
        }
      },
      error: (error) => {
        location.replace('/hospital/noPage?hideHome=true');
      },
    });
  }, []);

  if (needOtpLogin) {
    return (
      <div className='opts_login_page fst-ignore'>
        <p className='title'>众安互联网医院</p>
        <div className='telephone_wrapper'>
          <Input className='opt_input' placeholder='请输入11位手机号码' maxLength={11} value={mobilePhone} onChange={(value) => setMobilePhone(value)} onBlur={() => blur()} />
          {count === 0 ? (
            <div className='code' onClick={() => getSendVerifyCode()}>
              发送验证码
            </div>
          ) : (
            <div className='code'>{count}s</div>
          )}
        </div>
        <Input className='opt_input' placeholder='请输入手机验证码' value={verifyCode} onChange={(value) => setVerifyCode(value)} onBlur={() => blur()} />
        <div className='login' onClick={() => login()}>
          登录
        </div>
        <p className='tips'>
          若手机号未注册，将会进入注册流程。注册即视为同意 <span>《众安用户注册协议》、《众安隐私协议》</span>
        </p>
      </div>
    );
  }
  return null;
};

export default OtpLogin;
