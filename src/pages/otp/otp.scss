@import "src/style/index";

.opts_login_page {
  padding: 0 r(20);
  min-height: 100vh;
  background: #fff;

  .title {
    padding: r(49) 0 r(27);
    color: #464646;
    font-size: r(27);
    font-weight: 600;
    text-align: center;
  }

  .telephone_wrapper {
    position: relative;
    @include display-flex;
    @include align-items(center);

    margin-bottom: r(11);
    background: #fbfbfb;

    .code {
      width: r(80);
      height: r(30);
      border-radius: r(4);
      border: r(1) solid #ccc;
      text-align: center;
      line-height: r(28);
      color: #4a4a4a;
      font-size: r(13);
      margin-right: r(10);
    }
  }

  .opt_input {
    background: #fbfbfb;
    padding: r(11) r(13);
    font-size: r(15);
    flex: 1;

    input {
      background: inherit;
    }
  }

  .login {
    margin: r(20) 0 r(11);
    height: r(44);
    width: 100%;
    background: var(--theme-primary);
    border-radius: r(4);
    text-align: center;
    color: #fff;
    font-size: r(16);
    font-weight: 600;
    line-height: r(44);
  }

  .tips {
    color: #666;
    font-size: r(12);
    line-height: r(19);

    span {
      color: var(--theme-primary);
    }
  }
}
