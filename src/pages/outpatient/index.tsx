import classnames from 'classnames';
import React, { useState, useCallback, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { SvgIcon, Avatar, CeilingNav } from 'src/components/common';
import { fetchJson } from 'src/utils/fetch';
import format from 'src/utils/format';
import { Deserialize } from 'src/utils/serialization';
import validate from 'src/utils/validate';
import { Button } from 'zarm';
import { IntroConfig, QuestionConfig, PDF_Config } from './utils';
import './outpatient.scss';
import { xflowPushEvent } from 'src/utils/pageTrack';

const PAYFRA = {
  4: '月缴',
  5: '年缴',
};

const DrugListCheck = () => (
  <Link className='drug_component' to={{ pathname: '/hospital/pdf', search: `url=${encodeURIComponent('https://cdn-qcloud.zhongan.com/a00000/za-asclepius/pdf/drug_list.pdf')}` }}>
    <div>处方药清单</div>
    <div className='right'>
      <div>查看</div>
      <SvgIcon type='img' src={require('./images/icon_arrow.png')} />
    </div>
  </Link>
);

const User = ({ data, status }) => {
  const { name = '', payFrequency = '', policyEffectiveDate = '', policyExpiryDate = '', isWaitPeriod = '' } = data;
  return (
    <div className={classnames('user_component')}>
      <div className='base'>
        <Avatar prefixCls='avatar' />
        <div className='info'>
          <div className='top'><div className='name'>{name}</div><div className='type'><p>{PAYFRA[payFrequency]}</p></div></div>
          <div className={classnames('bottom', { waiting: isWaitPeriod })}>
            <p className='date_type'> {isWaitPeriod ? '等待期中' : '保障中'}</p>
            <p className='date'>{format.date(policyEffectiveDate, 'yyyy.MM.dd')}至{format.date(policyExpiryDate, 'yyyy.MM.dd')}</p>
          </div>
        </div>
      </div>
      <div className='tags'>
        <div className='tag'>全年视频问诊</div>
        <div className='tag'>报销{status === 1 || status === 2 ? '50%' : '70%'}药费</div>
        <div className='tag'>全国送药到家</div>
      </div>
    </div>
  );
};

const Intro = ({ status }) => {
  const renderData = IntroConfig[status];

  return (
    <div className='intro_component anchor-node'>
      <div className='card_title'>服务介绍</div>
      {renderData.map((item) => (
        <div className='intro' key={item.key} style={{ backgroundImage: `url(${item.backgroundImage})` }}>
          <div className='text'>
            {item.text.map((line, index) => (
              <p key={+index}>{line}</p>
            ))}
          </div>
        </div>
      ))}
    </div>
  );
};

const Progress = ({ status }) => {
  const pdfConfig = PDF_Config[status];
  const [progressData] = useState([{
    key: 'progress_one',
    title: '点击立即问诊，填写信息并发起问诊',
    desc: '填写基本信息，医生根据健康特征辅助诊断。',
  }, {
    key: 'progress_two',
    title: '根据病情开具处方药或调整用药',
    desc: '结合您的病情与过往病史，医生开具处方药或调整用药并下定医嘱。',
  }, {
    key: 'progress_three',
    title: '购药结算并等待送达',
    desc: '众安对责任范围内药品费用进行实时结算。中国大陆地区，北京、上海、广州、深圳、成都、天津、济南、杭州、南京、武汉、东莞、佛山等城市核心城区最快28分钟左右送达，其余地区平均1-3个自然日内。',
  }]);
  return (
    <div className='progress_component anchor-node'>
      <div className='card_head'>
        <div className='card_title'>服务流程</div>
        <Link className='link' to={{ pathname: '/hospital/pdf', search: `url=${encodeURIComponent(pdfConfig.intro)}` }}>服务介绍</Link>
      </div>

      <ul className='progress'>
        {
          progressData.map((item) => (
            <li className='progress-item' key={item.key}>
              <span className='item-tail'></span>
              <span className='item-head'>
                <span className='item-head-inner'></span>
              </span>
              <p className='item-title'>{item.title}</p>
              <p className='item-content'>{item.desc}</p>
            </li>
          ))
        }
      </ul>
    </div>
  );
};

const Question = ({ status }) => {
  const questionList = QuestionConfig[status];
  const pdfConfig = PDF_Config[status];

  return (
    <div className='question_component anchor-node'>
      <div className='card_head'>
        <div className='card_title'>常见问题</div>
        <Link className='link' to={{ pathname: '/hospital/pdf', search: `url=${encodeURIComponent(pdfConfig.question)}` }}>查看更多</Link>
      </div>

      <div className='question_list'>
        {questionList.map((item) => (<div className='question' key={item.key}>
          <div className='q'>
            <SvgIcon type='img' className='icon' src={require('./images/icon_question.png')} />
            <p>{item.q}</p>
          </div>
          <div className='a'>{item.a}</div>
        </div>))}
      </div>
    </div>
  );
};

const Attention = (status) => {

  const attentionList = status === 1 || status === 2
    ? [
      '1. 本服务针对已投保《众安尊享e生2020（门急诊版）》的被保险人开放权益；如需开通，请先投保该保险产品。',
      '2. 等待期过后，被保险人即可使用该权益；若被保险人未成年，则可在与被保人监护人的陪同下使用该服务。服务仅在保单有效期内有效。',
      <span> 3. 7x24小时问诊客服热线：<a href='tel:************'>************</a>。</span>,
    ]
    : [
      '1. 本服务针对已获得众安医疗权益的被保险人开放使用，如需开通请联系客服。',
      '2. 视频问诊不消耗购药报销次数，您可以随时更换医生问诊咨询。',
    ];

  return (
    <div className='attention_component anchor-node'>
      <div className='card_title'>注意事项</div>

      <div className='attention_list'>
        {attentionList.map((item, index) => (
          <div className='attention' key={+index}>{item}</div>
        ))}
      </div>

      <p className='supply'>{status === 1 || status === 2 ? '本服务由众安互联网医院与联合医务、壹点灵联合发起' : '本服务由众安互联网医院与联合医务联合发起'}</p>
    </div>
  );
};

const NA_intro = () => {
  const toKnow = useCallback(() => {
    // location.href = 'https://dm.zhongan.com/m/mobile/insurance-mall?code=1&dataType=GOODSV2';
    location.href = 'https://a.zhongan.com/gp/83088075?cid=10002542175&templateType=B&bizOrigin=APP_Alltab';
  }, []);

  return <div className='na_intro_component anchor-node' style={{ backgroundImage: `url(${require('./images/outpatient_banner.png')})` }}>
    <div className='contain'>
      <p className='head'>众安质保｜金牌医生｜视频问诊｜药费报销｜在线直结｜送药到家</p>
      <div className='text'><div className='col'>最高</div><div className='key'>全年省<span className='orange'>5000</span>元药费</div></div>
      <div className='wing_wrapper'>
        <SvgIcon type='img' className='wing' src={require('./images/outpatient_wing.png')} />
        <div className='center'>
          <p className='top'>门诊药费报销50%</p>
          <p className='bottom'>小病不出门，视频问医生</p>
        </div>
        <SvgIcon type='img' className='wing oppo' src={require('./images/outpatient_wing.png')} />
      </div>
      <div className='button' onClick={() => toKnow()}>立即了解</div>
    </div>
  </div>;
};

const NA_service = () => <div className='na_service_component'>
  <SvgIcon type='img' src={require('./images/outpatient_nobuy_intro.png')} />
</div>;

const NA_case = () => {
  const [caseText] = useState([
    {
      key: 'na_case_one',
      name: '投保',
      value: '王先生投保了众安尊享e生2020（门急诊版），获得众安互联网医院门诊权益。',
    }, {
      key: 'na_case_two',
      name: '看病',
      value: '等待期后，王先生半夜期间感到皮肤瘙痒难耐。在线视频问诊后，医生诊断为带状疱疹并开具处方药。',
    }, {
      key: 'na_case_three',
      name: '报销',
      value: <>
        <p className='prase'>
          案例①：王先生购药时，药费原价应付100元，由众安报销50%（即50元），未超过单次报销额度上限，王先生个人仅需支付50元。
        </p>
        <p>
          案例②：王先生购药时，药费原价应付1200元，众安计划报销50%（即600元），但超过单次报销上限500元，即王先生个人支付1200-500=700元
        </p>
      </>,
    }, {
      key: 'na_case_four',
      name: '送药',
      value: '大约28分钟，王先生在家收到放心药。',
    },
  ]);
  return <div className='na_case_component anchor-node'>
    <div className='card_head'>
      <div className='card_title'>服务案例</div>
    </div>

    <div className='contain'>
      <div className='contain_head'>
        <SvgIcon type='img' src={require('./images/outpatient_avatar.png')} />
        <span>王先生 30岁 上海</span>
      </div>
      <div className='case_progress'>
        {caseText.map((item) => (
          <div className='line' key={item.key}>
            <div className='tag'>{item.name}</div>
            <div className='desc'>{item.value}</div>
          </div>
        ))}
      </div>
    </div>
  </div>;
};

const NA_question = ({ status }) => {
  const pdfConfig = PDF_Config[status];

  const [questionList] = useState([
    {
      key: 'question_one',
      q: '请问服务使用人必须是被保人吗？',
      a: '门诊购药服务仅限6-65周岁的被保人使用。首次问诊时，我们将会进行信息认证及人像截图比对，确保后续问诊人是您本人，来保证问诊过程安全可信。心理倾诉不限年龄限制。',
    }, {
      key: 'question_two',
      q: '请问覆盖120种疾病是哪些？',
      a: <span>你好，我们针对生活中常见的120种疾病进行诊治，可以满足您日常需求。请点击<Link className='blue' to={{ pathname: '/hospital/pdf', search: `url=${encodeURIComponent(pdfConfig.disease)}` }}>《120种疾病列表》</Link>查看。</span>,
    }, {
      key: 'question_three',
      q: '如何理解每月2次，全年10次药品报销服务？',
      a: '你好，被保人每个自然月最多享2次药费报销服务，全年累计享10次。服务到期前，剩余报销次数不能累加在一起使用，每月依然最多使用2次。未使用的报销次数不做返还、兑现。',
    }, {
      key: 'question_four',
      q: '心理倾诉的服务时间是什么时候？',
      a: '心理倾诉服务时间：10：00-22：00，服务通话限时25分钟；若咨询师不在线，请换个时间拨打。',
    },
  ]);
  return (
    <div className='question_component anchor-node'>
      <div className='card_head'>
        <div className='card_title'>常见问题</div>
        <Link className='link' to={{ pathname: '/hospital/pdf', search: `url=${encodeURIComponent(pdfConfig.question)}` }}>查看更多</Link>
      </div>

      <div className='question_list'>
        {questionList.map((item) => (<div className='question' key={item.key}>
          <div className='q'>
            <SvgIcon type='img' className='icon' src={require('./images/icon_question.png')} />
            <p>{item.q}</p>
          </div>
          <div className='a'>{item.a}</div>
        </div>))}
      </div>
    </div>
  );
};

const NA_fixbottom = () => {
  const toHome = useCallback(() => {
    if (validate.isFromMiniApplet()) {
      wx.miniProgram.switchTab({
        url: '/pages/index',
      });
    } else {
      window.reactHistory.push({
        pathname: '/hospital/home',
      });
    }
  }, []);

  const toKnow = useCallback(() => {
    xflowPushEvent(['click', 'ZAHLWYY_HLWMZ_JSSY', '互联网门诊_介绍首页', { ZAHLWYY_CLICK_CONTENT: '互联网门诊_立即了解' }]);
    // location.href = 'https://dm.zhongan.com/m/mobile/insurance-mall?code=1&dataType=GOODSV2';
    location.href = 'https://a.zhongan.com/gp/83088075?cid=10002542175&templateType=B&bizOrigin=APP_Alltab';
  }, []);

  return <div className='fixbottom_component'>
    <div className='bottom'>
      <div className='na_za_wrapper' onClick={() => toHome()}>
        <SvgIcon type='img' src={require('./images/clinic_home_icon.png')} />
        <p>首页</p>
      </div>
      <div className='button_wrapper'>
        <Button theme='primary' block onClick={() => toKnow()}>立即了解</Button>
      </div>
    </div>
  </div>;
};

const FixBottom = ({ status }) => {
  const toIlogText = {
    1: '互联网门诊_门急诊月缴介绍页',
    2: '互联网门诊_门急诊年缴介绍页',
    3: '互联网门诊_门急诊加油包月缴介绍页',
    4: '互联网门诊_门急诊加油包年缴介绍页',
  };

  const za_cs = useCallback(() => {
    location.href = validate.isAlipayApplet() ? 'https://webchat-sh.clink.cn/chat.html?accessId=1936f9be-d24f-46cf-b0d1-09d34b8bef9d&language=zh_CN' : 'https://webchat-sh.clink.cn/chat.html?accessId=1936f9be-d24f-46cf-b0d1-09d34b8bef9d&language=zh_CN';
  }, []);

  return (
    <div className='fixbottom_component'>
      <div className='bottom'>
        <div className='za_wrapper' onClick={() => za_cs()}>
          <SvgIcon type='img' src={require('./images/za_cs.png')} />
        </div>
        <div className='button_wrapper'>
          <div className='text' onClick={() => {
            xflowPushEvent(['click', 'ZAHLWYY_HLWMZ_JSSY', '互联网门诊_介绍首页', { ZAHLWYY_CLICK_CONTENT: `${toIlogText[status]}` }]);
          }}>
            <p>视频问诊功能本平台暂不支持</p>
            <p>请至微信 - 众安海南互联网医院小程序 使用</p>
          </div>
        </div>
      </div>
    </div>
  );
};


const judgeStatus = (right: any) => {
  const { insurePolicyType = '', payFrequency = '' } = right;
  if (insurePolicyType == 1 && payFrequency == 4) {
    // 门急诊 月缴
    return 1;
  }

  if (insurePolicyType == 1 && payFrequency == 5) {
    // 门急诊 年缴
    return 2;
  }

  if (insurePolicyType == 2 && payFrequency == 4) {
    // 尊享加油包 月缴
    return 3;
  }

  if (insurePolicyType == 2 && payFrequency == 5) {
    // 尊享加油包 年缴
    return 4;
  }
  return 0;
};

const RIGHT_NAME = {
  1: '众安互联网门诊',
  2: '尊享加油包',
};

const Outpatient = (props) => {
  const { location: { search = '' } } = props;
  const { patientId = '' } = Deserialize(search);
  const [policy, setPolicy]: [any, any] = useState([]);
  const [status, setStatus] = useState(0);
  const [select, setSelect] = useState(0);

  useEffect(() => {
    const fetchStatus = async () => {
      const param = {
        patientId,
        vasCode: 'ohClinic',
      };
      if (!patientId) {
        delete param.patientId;
      }

      const res = await fetchJson({
        type: 'POST',
        url: '/api/api/v1/patient/policy/queryPolicyList',
        data: param,
      });

      const { result = [] } = res;
      const data: any = [];
      if (result.length) {
        result.forEach((item) => {
          const { policyInsurant = {}, policyEffectiveDate = '', insurePolicyType = '', policyExpiryDate = '', insuranceWaitPeriod = '', policyProduct = {}, payFrequency = '' } = item;
          data.push({
            ...policyInsurant,
            ...policyProduct,
            policyEffectiveDate,
            policyExpiryDate,
            insuranceWaitPeriod,
            insurePolicyType,
            payFrequency,
          });
        });
        setPolicy(data);
        setStatus(judgeStatus(data[0]));
      } else {
        setStatus(9999);
      }
    };
    fetchStatus();
  }, []);

  return (
    <div className='outpatient_page'>
      {status === 9999 ? <>
        <NA_intro />
        <CeilingNav list={['服务特色', '服务案例', '服务流程', '常见问题']} activeBgColor='var(--theme-primary)' className='tabbar_component' />
        <NA_service />
        <NA_case />
        <Progress status={status} />
        <NA_question status={status} />
        <NA_fixbottom />
      </> : (status === 1 || status === 2 || status === 3 || status === 4) ? <>
        <div className='background'></div>
        {policy.length > 1 && <div className='user_tab_component'>
          {policy.map((item, index) => (<div className={classnames('user_tab', { active: index === select })} key={+index} onClick={() => {
            setSelect(index);
            setStatus(judgeStatus(item));
          }}>
            {RIGHT_NAME[item.insurePolicyType]}
          </div>))}
        </div>}
        <DrugListCheck />
        <User data={policy[select]} status={status} />
        <CeilingNav list={['服务介绍', '服务流程', '常见问题', '注意事项']} activeBgColor='var(--theme-primary)' className='tabbar_component' />
        <Intro status={status} />
        <Progress status={status} />
        <Question status={status} />
        <Attention status={status} />
        {/* <Agreement /> */}
        <FixBottom status={status} />
      </> : null}
    </div>
  );
};

export default Outpatient;
