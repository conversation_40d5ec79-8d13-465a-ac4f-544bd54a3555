@import "src/style/index";

@mixin card {
  position: relative;
  background-color: #fff;
  z-index: 1;
  padding: r(20) r(15);
  margin-bottom: r(10);
}

.outpatient_page,
.videointro_page {
  // background-color: #eff0fa;
  padding-bottom: r(150);

  .orange {
    color: #ff7023;
  }

  .blue {
    color: #309eeb;
  }

  .bold {
    font-weight: bold;
  }

  .background {
    position: absolute;
    right: 0;
    top: 0;
    width: 100%;
    height: r(115);
    background: linear-gradient(230deg, #3e4565 0%, #323953 100%);
  }

  .card_head {
    @include display-flex;
    @include align-items(center);
    @include justify-content(space-between);

    .link {
      position: relative;
      color: var(--theme-primary);
      font-size: r(11);
      font-weight: 600;
      padding-right: r(11);

      &::after {
        content: '';
        position: absolute;
        transform: rotate(-45deg) translateY(-50%);
        right: r(0);
        top: 50%;
        width: r(6);
        height: r(6);
        border: r(2) solid var(--theme-primary);
        border-radius: r(2);
        border-left: none;
        border-top: none;
      }
    }
  }

  .card_title {
    position: relative;
    color: #333;
    font-size: r(16);
    font-weight: 600;
    padding-left: r(12);
    margin-bottom: r(11);

    &::before {
      content: '';
      position: absolute;
      left: 0;
      top: 50%;
      transform: translateY(-50%);
      width: r(5);
      height: r(15);
      background-color: #10cc89;
    }
  }

  .user_tab_component {
    // position: relative;
    // padding-top: r(37);
    // z-index: 1000;
    position: relative;
    z-index: 1000;
    padding: r(20) 0 r(5);
    @include display-flex;

    .user_tab {
      position: relative;
      flex: 1;
      text-align: center;
      color: #fff;
      font-size: r(15);
      font-weight: bold;

      &.active::after {
        content: '';
        position: absolute;
        width: 50%;
        height: r(3);
        border-radius: r(1);
        background: #fff;
        left: 50%;
        bottom: r(-5);
        transform: translateX(-50%);
      }
    }

    // .user_tab {
    //   position: relative;
    //   width: r(148);
    //   height: r(39);
    //   z-index: 1000;
    //   background-size: 100% 100%;
    //   background-repeat: no-repeat;
    //   text-align: center;
    //   line-height: r(39);
    //   padding-right: r(15);
    //   color: #fff;
    //   font-size: r(13);
    //   font-weight: bold;

    //   &.active {
    //     color: #333;
    //   }

    //   &:last-child {
    //     width: r(168);
    //     padding-right: 0;
    //     position: relative;
    //     margin-left: r(-28);
    //     z-index: 999;
    //   }
    // }
  }

  .drug_component {
    display: block;
    position: relative;
    height: r(40);
    width: r(345);
    margin: r(10) auto;
    background: rgba(255, 255, 255, 0.16);
    border-radius: r(6);
    z-index: 1;
    padding: 0 r(15);
    color: #fff;
    font-size: r(13);
    @include display-flex;
    @include align-items(center);
    @include justify-content(space-between);

    .right {
      @include display-flex;
      @include align-items(center);

      color: rgba(255, 255, 255, 0.6);
      font-size: r(12);
    }

    img {
      display: inline-block;
      width: r(7);
      height: r(11);
      opacity: 0.6;
      margin-left: r(6);
    }

    &:active {
      background: rgba(255, 255, 255, 0.06);
    }
  }

  .user_component {
    position: relative;
    width: r(345);
    padding: r(19) r(16);
    margin: r(10) auto r(20);
    background: #fff;
    box-shadow: 0 r(10) r(13) 0 rgba(221, 227, 244, 0.2);
    z-index: 10;
    border-radius: r(6);
    overflow: hidden;

    .base {
      @include display-flex;

      .avatar {
        width: r(60);
        height: r(60);
        border-radius: r(4);
        margin-right: r(18);
      }

      .top,
      .bottom {
        @include display-flex;
        @include align-items(center);
      }
    }

    .badage {
      position: absolute;
      width: r(105);
      height: r(24);
      top: 0;
      right: 0;
      background: linear-gradient(111deg, #28e1a8 0%, #0dc1cb 100%);
      border-radius: 0 0 0 r(8);
      color: #fff;
      font-size: r(12);
      line-height: r(24);
      text-align: center;
    }

    .name {
      color: #333;
      font-size: r(18);
      font-weight: 600;
      margin-right: r(10);
    }

    .type {
      width: r(40);
      height: r(20);
      background: linear-gradient(137deg, rgba(255, 215, 71, 1) 0%, rgba(237, 176, 40, 1) 100%);
      transform: skew(-10deg);
      border-radius: r(4);

      p {
        transform: skew(10deg);
        font-size: r(11);
        color: #fff;
        line-height: r(20);
        text-align: center;
      }
    }

    .bottom {
      margin-top: r(5);
      border-radius: r(2);
      border: 1px solid #d0efe3;

      &.waiting {
        border-color: #f6ecd7;

        .date_type {
          background-color: #f6ecd7;
          color: #f99415;
        }
      }
    }

    .date_type {
      background-color: rgba(16, 204, 137, 0.16);
      color: var(--theme-primary);
      font-size: r(11);
      padding: r(2.5) r(12);
    }

    .date {
      color: #999;
      font-size: r(10);
      padding: 0 r(13);
    }

    .tags {
      @include display-flex;
      @include align-items;
      @include justify-content(space-around);

      margin-top: r(15);

      .tag {
        @include display-flex;
        @include align-items(center);
        @include justify-content(center);

        background-color: #f6f6fb;
        border-radius: r(3);
        padding: r(3) r(11);
        color: var(--theme-primary);
        font-size: r(12);
      }
    }
  }

  .tabbar_component {
    position: relative;
    box-shadow: 0 r(10) r(13) 0 rgba(221, 227, 244, 0.2);
    z-index: 1000;
  }

  .intro_component {
    @include card;

    .intro {
      position: relative;
      width: r(345);
      height: r(200);
      border-radius: r(10);
      overflow: hidden;
      background-size: 100%;
      background-repeat: no-repeat;
      margin: 0 auto r(15);
      border: 1px solid rgba($color: #f1f1f1, $alpha: 0.6);

      .text {
        position: absolute;
        bottom: 0;
        left: 0;
        width: 100%;
        height: 50%;
        border-radius: r(10) r(10) 0 0;
        background-color: #fff;
        border-top: 1px solid rgba($color: #f1f1f1, $alpha: 0.6);
        padding: r(14) r(9);

        p {
          color: #666;
          font-size: r(14);
          line-height: r(24);
          position: relative;
          margin-left: r(9);
          padding-left: r(13);

          &::before {
            content: '';
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            width: r(5);
            height: r(5);
            border-radius: 50%;
            background-color: var(--theme-primary);
          }
        }
      }
    }
  }

  .progress_component {
    @include card;

    .progress {
      .progress-item {
        position: relative;
        padding-left: r(15);
        padding-bottom: r(11);
        color: #bcbcbc;
        font-size: r(13);

        .item-title,
        .item-content {
          line-height: r(24);
        }

        .item-title {
          color: #464646;
          font-size: r(14);
        }

        .item-tail {
          position: absolute;
          top: r(21);
          left: r(4.5);
          height: calc(100% - 20px);
          opacity: .5;
          border-left: 1px dashed #a2e7ca;
        }

        .item-head {
          position: absolute;
          left: r(-2);
          top: r(5);
          width: r(15);
          height: r(15);
          border: 2px solid #fff;
          border-radius: 100px;
          background-color: #a2e7ca;

          @include display-flex;
          @include align-items(center);
          @include justify-content(center);

          .item-head-inner {
            display: inline-block;
            width: r(6);
            height: r(6);
            border-radius: 50%;
            background-color: #01bc70;
            opacity: 1;
          }
        }

        &:last-child {
          .item-tail {
            display: none;
          }
        }
      }
    }
  }

  .question_component {
    @include card;

    .question_list {
      .question {
        margin-bottom: r(15);

        .q {
          @include display-flex;
          @include align-items(center);

          margin-bottom: r(8);
          color: #333;
          font-size: r(14);
          font-weight: 600;

          .icon {
            width: r(20);
            height: r(17.5);
            margin-right: r(8);
          }
        }

        .a {
          color: #999;
          font-size: r(13);
          line-height: r(20);
          margin-left: r(28);
          padding-right: r(6);
        }
      }
    }
  }

  .attention_component {
    @include card;

    .attention_list {
      .attention {
        color: #999;
        font-size: r(14);
        margin-bottom: r(6);
      }
    }

    .supply {
      text-align: center;
      color: #cecece;
      font-size: r(12);
    }
  }

  .fixbottom_component {
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
    background: #fff;
    z-index: 1000;
    overflow: hidden;
    border-radius: r(20) r(20) 0 0;
    box-shadow: 0 -1px 0 0 #e6e6e6;

    .top {
      .warning {
        @include display-flex;
        @include align-items(center);
        @include justify-content(space-between);

        width: r(345);
        height: r(40);
        margin: r(15) auto;
        padding: 0 r(10);
        background: #fff7cb;
        border-radius: r(6);
        color: #ec9131;
        z-index: 10;
        font-size: r(13);

        .left {
          @include display-flex;
          @include align-items(center);
        }

        .right {
          position: relative;
          padding-right: r(10);

          &::after {
            content: '';
            position: absolute;
            transform: rotate(-45deg) translateY(-50%);
            right: 0;
            top: 40%;
            width: r(8);
            height: r(8);
            border: r(2) solid #ec9131;
            border-radius: r(2);
            border-left: none;
            border-top: none;
          }
        }

        .trumpt {
          width: r(15);
          height: r(15);
          margin: 0 r(6);
        }
      }
    }

    .bottom {
      @include display-flex;
      @include align-items(center);

      // border: 1px solid #f0f0f0;
      height: r(70);

      .za_wrapper {
        width: r(70);
        height: 100%;
        @include display-flex;
        @include align-items(center);
        @include justify-content(center);

        border-right: 1px solid #f0f0f0;

        img,
        .svg {
          display: block;
          width: r(42);
          height: r(46);
        }
      }

      .na_za_wrapper {
        width: r(70);
        height: 100%;
        @include display-flex;
        @include align-items(center);
        @include justify-content(center);

        flex-direction: column;
        border-right: 1px solid #f0f0f0;
        color: var(--theme-primary);
        font-size: r(9);

        img {
          display: block;
          width: r(26);
          height: r(23);
          margin-bottom: r(3);
        }
      }

      .button_wrapper {
        flex: 1;
        margin: 0 r(20);

        .text {
          padding: r(2);
          background: #fff7cb;
          color: #ec9131;
          font-size: r(13);
          text-align: center;
          line-height: r(19);
          border-radius: r(6);
        }
      }
    }
  }

  .na_intro_component {
    background-size: 100%;
    background-repeat: no-repeat;
    padding-top: r(158);
    box-shadow: 0 r(10) r(13) 0 rgba(221, 227, 244, 0.2);
    margin-bottom: r(18);

    .contain {
      width: r(345);
      height: r(250);
      background: #fff;
      border-radius: r(6);
      margin: 0 auto;
      padding: r(10) 0;

      .head {
        font-size: r(10);
        color: #c7c7c7;
        text-align: center;
      }

      .text {
        color: #333;
        @include display-flex;
        @include align-items(center);
        @include justify-content(center);

        margin-top: r(10);

        .col {
          display: inline-block;
          width: r(15);
          font-size: r(15);
          font-weight: 600;
          line-height: r(16);
          margin-right: r(9);
        }

        .key {
          font-weight: bold;
          font-size: r(32);
        }
      }

      .wing_wrapper {
        @include display-flex;
        @include align-items(center);
        @include justify-content(center);

        .wing {
          width: r(67.5);
          height: r(103.5);
        }

        .center {
          .top {
            color: #a27e5a;
            font-size: r(18);
            font-weight: bold;
            margin-bottom: r(5);
          }

          .bottom {
            color: #999;
            font-size: r(13);
          }
        }

        .oppo {
          transform: rotateY(180deg);
        }
      }

      .button {
        position: relative;
        width: r(238);
        height: r(49);
        background: linear-gradient(180deg, #00dfa9 0%, var(--theme-primary) 100%);
        border-radius: r(25);
        margin: r(-6) auto r(10);
        text-align: center;
        line-height: r(49);
        color: #fff;
        font-size: r(18);
        font-weight: 600;

        &:active {
          background: linear-gradient(180deg, #00be8f 0%, #00965a 100%);
        }

        &::before {
          position: absolute;
          top: r(8);
          left: r(5);
          content: '';
          width: r(217);
          height: r(50);
          background: linear-gradient(180deg, rgba(0, 223, 169, 1) 0%, rgba(0, 188, 112, 1) 100%);
          border-radius: 44px;
          opacity: 0.38;
          filter: blur(r(5));
        }
      }
    }
  }

  .na_service_component {
    background: #fff;
    padding-top: r(10);
    margin-bottom: r(10);

    img {
      width: 100%;
      display: block;
    }
  }

  .na_case_component {
    @include card();

    .contain {
      margin: r(11) 0;
      background-color: #f9fafb;
      border-radius: r(6);
      overflow: hidden;

      .contain_head {
        position: relative;
        background: linear-gradient(308deg, rgba(50, 229, 156, 1) 0%, rgba(20, 213, 146, 1) 100%);
        padding: r(11) r(16);
        margin-bottom: r(20);

        &::before {
          content: '';
          position: absolute;
          bottom: r(-6);
          left: r(56);
          width: r(15);
          height: r(15);
          background: inherit;
          transform: rotate(45deg);
        }

        span {
          display: inline-block;
          color: #fff;
          font-size: r(18);
          padding-left: r(20);
          vertical-align: middle;
        }

        img {
          display: inline-block;
          width: r(50);
          height: r(50);
        }
      }

      .case_progress {
        padding: r(12);

        .line {
          margin-bottom: r(15);
          @include display-flex;

          .tag {
            width: r(37);
            height: r(18.5);
            line-height: r(18.5);
            text-align: center;
            border-radius: r(2);
            background: linear-gradient(132deg, rgba(75, 249, 186, 1) 0%, rgba(35, 178, 195, 1) 100%);
            color: #fff;
            font-size: r(12);
            margin-right: r(15);
          }

          .desc {
            flex: 1;
            color: #666;
            font-size: r(14);
            line-height: r(20);
          }
        }
      }
    }
  }

  .agreement_component {
    padding: r(8) r(15);
    color: #9b9b9b;
    font-size: r(14);
    @include display-flex;

    label {
      display: block;
      margin-left: r(6);
      margin-top: r(-1);
    }

    a {
      color: var(--theme-success);
    }
  }
}

.videointro_page {
  .videointro_component {
    background: #44cda7;
    padding: r(20) r(17) r(17);

    img {
      width: 100%;
    }
  }

  // .videoprogress_component {
  //   margin-bottom: r(10);
  // }
}
