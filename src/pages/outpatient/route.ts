import { routersTypes } from 'src/pages/routes';

const route: routersTypes[] = [
  {
    path: '/hospital/outpatient',
    name: 'Outpatient',
    component: () => import(/* webpackPrefetchPlaceHolder */ './index'),
    auth: true,
    exact: true,
    title: '互联网门诊险',
    realAuth: true,
  }, {
    path: '/hospital/videointro',
    name: 'VideoIntro',
    component: () => import(/* webpackPrefetchPlaceHolder */ './videoIntro'),
    auth: true,
    exact: true,
    title: '视频问诊',
    realAuth: true,
  },
];

export default route;

