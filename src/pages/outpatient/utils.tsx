import React from 'react';
import { Link } from 'react-router-dom';


//  门急诊 月缴
//  return 1
//  门急诊 年缴
//  return 2
//  尊享加油包 月缴
//  return 3
//  尊享加油包 年缴
//  return 4

export const IntroConfig = {
  "1": [
    {
      key: 'intro_one',
      backgroundImage: require('./images/outpatient_intro_one.png'),
      text: [
        '全年不限次视频问诊，深度沟通了解病情',
        '百位医生全天候诊，平均15秒解决接诊',
        '足不出门，1对1解决120种疾病困扰'
      ]
    }, {
      key: 'intro_two',
      backgroundImage: require('./images/outpatient_intro_two.png'),
      text: [
        <span>门诊购药0免赔，每次药费报销<span className='orange bold'>50%</span>最高享500元</span>,
        <span>每月享<span className='orange bold'>1次</span>药费报销服务，全年共享10次</span>,
        '购药时自动报销，省心省力省时间'
      ]
    }, {
      key: 'intro_three',
      backgroundImage: require('./images/outpatient_intro_three.png'),
      text: [
        '风雨无阻，购药即送，快速吃到放心药',
        '国家级药师审核药品，100%正品保障',
        '7x24小时客服服务'
      ]
    }, {
      key: 'intro_four',
      backgroundImage: require('./images/outpatient_intro_four.png'),
      text: [
        '国家专业心理咨询师1对1服务',
        '25分钟电话倾诉，一键拨打无需等待',
        '匿名沟通，保护个人隐私'
      ]
    }
  ],
  "2": [
    {
      key: 'intro_one',
      backgroundImage: require('./images/outpatient_intro_one.png'),
      text: [
        '全年不限次视频问诊，深度沟通了解病情',
        '百位医生全天候诊，平均15秒解决接诊',
        '足不出门，1对1解决120种疾病困扰'
      ]
    }, {
      key: 'intro_two',
      backgroundImage: require('./images/outpatient_intro_two.png'),
      text: [
        <span>门诊购药0免赔，每次药费报销<span className='orange bold'>50%</span>最高享500元</span>,
        <span>每月享<span className='orange bold'>2次</span>药费报销服务，全年共享10次</span>,
        '购药时自动报销，省心省力省时间'
      ]
    }, {
      key: 'intro_three',
      backgroundImage: require('./images/outpatient_intro_three.png'),
      text: [
        '风雨无阻，购药即送，快速吃到放心药',
        '国家级药师审核药品，100%正品保障',
        '7x24小时客服服务'
      ]
    }, {
      key: 'intro_four',
      backgroundImage: require('./images/outpatient_intro_four.png'),
      text: [
        '国家专业心理咨询师1对1服务',
        '25分钟电话倾诉，一键拨打无需等待',
        '匿名沟通，保护个人隐私'
      ]
    }
  ],
  "3": [
    {
      key: 'intro_one',
      backgroundImage: require('./images/outpatient_intro_one.png'),
      text: [
        '全年不限次视频问诊，深度沟通了解病情',
        '百位医生全天候诊，平均15秒解决接诊',
        '足不出门，1对1解决120种疾病困扰'
      ]
    }, {
      key: 'intro_two',
      backgroundImage: require('./images/outpatient_intro_two.png'),
      text: [
        <span>门诊购药0免赔，每次药费报销<span className='orange bold'>70%</span>最高享500元</span>,
        <span>每月享<span className='orange bold'>1次</span>药费报销服务，全年共享10次</span>,
        '购药时自动报销，省心省力省时间'
      ]
    }, {
      key: 'intro_three',
      backgroundImage: require('./images/outpatient_intro_three.png'),
      text: [
        '风雨无阻，购药即送，快速吃到放心药',
        '国家级药师审核药品，100%正品保障',
        '7x24小时客服服务'
      ]
    }
  ],
  "4": [
    {
      key: 'intro_one',
      backgroundImage: require('./images/outpatient_intro_one.png'),
      text: [
        '全年不限次视频问诊，深度沟通了解病情',
        '百位医生全天候诊，平均15秒解决接诊',
        '足不出门，1对1解决120种疾病困扰'
      ]
    }, {
      key: 'intro_two',
      backgroundImage: require('./images/outpatient_intro_two.png'),
      text: [
        <span>门诊购药0免赔，每次药费报销<span className='orange bold'>70%</span>最高享500元</span>,
        <span>每月享<span className='orange bold'>2次</span>药费报销服务，全年共享10次</span>,
        '购药时自动报销，省心省力省时间'
      ]
    }, {
      key: 'intro_three',
      backgroundImage: require('./images/outpatient_intro_three.png'),
      text: [
        '风雨无阻，购药即送，快速吃到放心药',
        '国家级药师审核药品，100%正品保障',
        '7x24小时客服服务'
      ]
    }
  ],
}

export const QuestionConfig = {
  "1": [
    {
      key: 'question_one',
      q: '请问服务使用人必须是被保人吗？',
      a: '门诊购药服务仅限6-65周岁的被保人使用。首次问诊时，我们将会进行信息认证及人像截图比对，确保后续问诊人是您本人，来保证问诊过程安全可信。心理倾诉不限年龄限制。'
    }, {
      key: 'question_two',
      q: '请问覆盖120种疾病是哪些？',
      a: <span>你好，我们针对生活中常见的120种疾病进行诊治，可以满足您日常需求。请点击<Link className='blue' to={{ pathname: '/hospital/pdf', search: `url=${encodeURIComponent('https://cdn-qcloud.zhongan.com/a00000/za-asclepius/pdf/120diseases.pdf')}` }}>《120种疾病列表》</Link>查看。</span>
    }, {
      key: 'question_three',
      q: '如何理解每月1次，全年10次药品报销服务？',
      a: '你好，被保人每个自然月最多享1次药费报销服务，全年累计享10次。服务到期前，剩余报销次数不能累加在一起使用，每月依然最多使用1次。未使用的报销次数不做返还、兑现。'
    }, {
      key: 'question_four',
      q: '药品报销费用是怎样计算的？',
      a: <div><p>我们对责任范围内的药品费用进行实时结算。每次药品费用报销比例50%，单次最高报销500元，剩余费用则由您承担。 </p><p>举例：若药费原价应付100元，由众安报销50%（即50元），未超过单次报销额度500上限，个人仅需支付50元；若药费原价应付1200元，众安计划报销50%（即600元），但超过单次报销上限500元，个人支付1200-500=700元。</p></div>
    }, {
      key: 'question_five',
      q: '问诊完成后，多久收到药品处方单？',
      a: '您好，问诊完成后医生会在30分钟内上传问诊报告和处方单。请在“个人中心-处方管理”中查看，即可购买药品。',
    }, {
      key: 'question_six',
      q: '心理倾诉的服务时间是什么时候？',
      a: '心理倾诉服务时间：10：00-22：00，服务通话限时25分钟；若咨询师不在线，请换个时间拨打。',
    }
  ],
  "2": [
    {
      key: 'question_one',
      q: '请问服务使用人必须是被保人吗？',
      a: '门诊购药服务仅限6-65周岁的被保人使用。首次问诊时，我们将会进行信息认证及人像截图比对，确保后续问诊人是您本人，来保证问诊过程安全可信。心理倾诉不限年龄限制。'
    }, {
      key: 'question_two',
      q: '请问覆盖120种疾病是哪些？',
      a: <span>你好，我们针对生活中常见的120种疾病进行诊治，可以满足您日常需求。请点击<Link className='blue' to={{ pathname: '/hospital/pdf', search: `url=${encodeURIComponent('https://cdn-qcloud.zhongan.com/a00000/za-asclepius/pdf/120diseases.pdf')}` }}>《120种疾病列表》</Link>查看。</span>
    }, {
      key: 'question_three',
      q: '如何理解每月2次，全年10次药品报销服务？',
      a: '你好，被保人每个自然月最多享2次药费报销服务，全年累计享10次。服务到期前，剩余报销次数不能累加在一起使用，每月依然最多使用2次。未使用的报销次数不做返还、兑现。'
    }, {
      key: 'question_four',
      q: '药品报销费用是怎样计算的？',
      a: <div><p>我们对责任范围内的药品费用进行实时结算。每次药品费用报销比例50%，单次最高报销500元，剩余费用则由您承担。</p><p>举例：若药费原价应付100元，由众安报销50%（即50元），未超过单次报销额度500上限，个人仅需支付50元；若药费原价应付1200元，众安计划报销50%（即600元），但超过单次报销上限500元，个人支付1200-500=700元。</p></div>
    }, {
      key: 'question_five',
      q: '问诊完成后，多久收到药品处方单？',
      a: '您好，问诊完成后医生会在30分钟内上传问诊报告和处方单。请在“个人中心-处方管理”中查看，即可购买药品。',
    }, {
      key: 'question_six',
      q: '心理倾诉的服务时间是什么时候？',
      a: '心理倾诉服务时间：10：00-22：00，服务通话限时25分钟；若咨询师不在线，请换个时间拨打。',
    }
  ],
  "3": [
    {
      key: 'question_one',
      q: '请问服务使用人必须是被保人吗？',
      a: '门诊购药服务仅限被保人使用。首次问诊时，我们将会进行信息认证及人像截图比对，确保后续问诊人是您本人，来保证问诊过程安全可信。'
    }, {
      key: 'question_two',
      q: '请问覆盖120种疾病是哪些？',
      a: <span>你好，我们针对生活中常见的120种疾病进行诊治，可以满足您日常需求。请点击<Link className='blue' to={{ pathname: '/hospital/pdf', search: `url=${encodeURIComponent('https://cdn-qcloud.zhongan.com/a00000/za-asclepius/pdf/120diseases.pdf')}` }}>《120种疾病列表》</Link>查看。</span>
    }, {
      key: 'question_three',
      q: '如何理解每月1次，全年10次药品报销服务？',
      a: '你好，被保人每个自然月最多享1次药费报销服务，全年累计享10次。服务到期前，剩余报销次数不能累加在一起使用，每月依然最多使用1次。未使用的报销次数不做返还、兑现。'
    }, {
      key: 'question_four',
      q: '药品报销费用是怎样计算的？',
      a: <div><p>我们对责任范围内的药品费用进行实时结算。每次药品费用报销比例70%，单次最高报销500元，剩余费用则由您承担。</p><p>举例：若药费原价应付100元，由众安报销70%（即70元），未超过单次报销额度500上限，个人仅需支付50元；若药费原价应付1200元，众安计划报销70%（即840元），但超过单次报销上限500元，个人支付1200-500=700元。</p></div>
    }, {
      key: 'question_five',
      q: '问诊完成后，多久收到药品处方单？',
      a: '您好，问诊完成后医生会在30分钟内上传问诊报告和处方单。请在“个人中心-处方管理”中查看，即可购买药品。',
    }
  ],
  "4": [
    {
      key: 'question_one',
      q: '请问服务使用人必须是被保人吗？',
      a: '门诊购药服务仅限被保人使用。首次问诊时，我们将会进行信息认证及人像截图比对，确保后续问诊人是您本人，来保证问诊过程安全可信。'
    }, {
      key: 'question_two',
      q: '请问覆盖120种疾病是哪些？',
      a: <span>你好，我们针对生活中常见的120种疾病进行诊治，可以满足您日常需求。请点击<Link className='blue' to={{ pathname: '/hospital/pdf', search: `url=${encodeURIComponent('https://cdn-qcloud.zhongan.com/a00000/za-asclepius/pdf/120diseases.pdf')}` }}>《120种疾病列表》</Link>查看。</span>
    }, {
      key: 'question_three',
      q: '如何理解每月2次，全年10次药品报销服务？',
      a: '你好，被保人每个自然月最多享2次药费报销服务，全年累计享10次。服务到期前，剩余报销次数不能累加在一起使用，每月依然最多使用2次。未使用的报销次数不做返还、兑现。'
    }, {
      key: 'question_four',
      q: '药品报销费用是怎样计算的？',
      a: <div><p>我们对责任范围内的药品费用进行实时结算。每次药品费用报销比例70%，单次最高报销500元，剩余费用则由您承担。</p><p>举例：若药费原价应付100元，由众安报销70%（即70元），未超过单次报销额度500上限，个人仅需支付50元；若药费原价应付1200元，众安计划报销70%（即840元），但超过单次报销上限500元，个人支付1200-500=700元。</p></div>
    }, {
      key: 'question_five',
      q: '问诊完成后，多久收到药品处方单？',
      a: '您好，问诊完成后医生会在30分钟内上传问诊报告和处方单。请在“个人中心-处方管理”中查看，即可购买药品。',
    }
  ],
}

export const PDF_Config = {
  "1": {
    intro: 'https://cdn-qcloud.zhongan.com/a00000/za-asclepius/pdf/outpatient_m_intro.pdf',
    question: 'https://cdn-qcloud.zhongan.com/a00000/za-asclepius/pdf/outpatient_question.pdf',
    disease: 'https://cdn-qcloud.zhongan.com/a00000/za-asclepius/pdf/120diseases.pdf'
  },
  "2": {
    intro: 'https://cdn-qcloud.zhongan.com/a00000/za-asclepius/pdf/outpatient_y_intro.pdf',
    question: 'https://cdn-qcloud.zhongan.com/a00000/za-asclepius/pdf/outpatient_question.pdf',
    disease: 'https://cdn-qcloud.zhongan.com/a00000/za-asclepius/pdf/120diseases.pdf'
  },
  "3": {
    intro: 'https://cdn-qcloud.zhongan.com/a00000/za-asclepius/pdf/feul_m_intro_new.pdf',
    question: 'https://cdn-qcloud.zhongan.com/a00000/za-asclepius/pdf/feul_question.pdf',
    disease: 'https://cdn-qcloud.zhongan.com/a00000/za-asclepius/pdf/120diseases.pdf'
  },
  "4": {
    intro: 'https://cdn-qcloud.zhongan.com/a00000/za-asclepius/pdf/feul_y_intro.pdf',
    question: 'https://cdn-qcloud.zhongan.com/a00000/za-asclepius/pdf/feul_question.pdf',
    disease: 'https://cdn-qcloud.zhongan.com/a00000/za-asclepius/pdf/120diseases.pdf'
  },
  "9999": {
    intro: 'https://cdn-qcloud.zhongan.com/a00000/za-asclepius/pdf/outpatient_y_intro.pdf',
    question: 'https://cdn-qcloud.zhongan.com/a00000/za-asclepius/pdf/outpatient_question.pdf',
    disease: 'https://cdn-qcloud.zhongan.com/a00000/za-asclepius/pdf/120diseases.pdf'
  }
}