import { dmEnv } from '@dm/utils';
import React, { useState, useCallback, useLayoutEffect } from 'react';
import { Link } from 'react-router-dom';
import { SvgIcon, CeilingNav, StaticToast } from 'src/components/common';
import { zaappEnv } from 'src/utils/env';
import { fetchJson } from 'src/utils/fetch';
import Storage from 'src/utils/storage';
import validate from 'src/utils/validate';
import { Button, Checkbox } from 'zarm';
import './outpatient.scss';
import { xflowPushEvent } from 'src/utils/pageTrack';

const Intro = () => (
  <div className='videointro_component'>
    <SvgIcon type='img' src={require('./images/video_inquiry_intro.png')} />
  </div>
);

const Progress = () => (
  <div className='videoprogress_component anchor-node'>
    <SvgIcon type='img' src={require('./images/video_inquiry_progress.png')} />
  </div>
);

const Question = () => {
  const [questionList] = useState(() => [
    {
      key: 'question_one',
      q: '如何确保提供的视频问诊服务是专业的,可信赖的？',
      a: '提供视频问诊的医生均是平台线下诊所的门诊医生，已持有医师资格证书和医师执业证书且独立临床经验在5年以上。视频问诊医生上岗前会经过严格筛选和专业的培训及认证，岗中会持续接受视频问诊服务质量考核和用户评级系统考核，全面确保服务质量。',
    }, {
      key: 'question_two',
      q: '视频问诊会限制时间吗？',
      a: '您好，问诊过程中不限问诊时长，可与医生深度了解病情。',
    }, {
      key: 'question_three',
      q: '请问多久会收到药品处方单？',
      a: '您好，问诊完成后医生会在30分钟内上传问诊报告和处方单。请在“个人中心-处方管理”中查看，即可购买药品。',
    },
  ]);
  return (
    <div className='question_component anchor-node'>
      <div className='card_head'>
        <div className='card_title'>常见问题</div>
      </div>

      <div className='question_list'>
        {questionList.map((item) => (<div className='question' key={item.key}>
          <div className='q'>
            <SvgIcon type='img' className='icon' src={require('./images/icon_question.png')} />
            <p>{item.q}</p>
          </div>
          <div className='a'>{item.a}</div>
        </div>))}
      </div>
    </div>
  );
};

const Attention = () => {
  const [attentionList] = useState(() => [
    '1.本服务由众安互联网医院与联合医务联合为您服务。',
    '2.根据页面提示购买支付后，完善信息即可发起问诊。',
    <span> 3.联合医务服务热线：<a href='tel:************'>************</a></span>,
  ]);
  return (
    <div className='attention_component anchor-node'>
      <div className='card_title'>服务须知</div>
      <div className='attention_list'>
        {attentionList.map((item, index) => (
          <div className='attention' key={+index}>{item}</div>
        ))}
      </div>
    </div>
  );
};

const FixBottom = (props) => {
  const [hasInquiry, setHasInquiry] = useState(0);
  const [agreement, setAgreement] = useState(true);
  const za_cs = useCallback(() => {
    location.href = validate.isAlipayApplet() ? 'https://webchat-sh.clink.cn/chat.html?accessId=1936f9be-d24f-46cf-b0d1-09d34b8bef9d&language=zh_CN' : 'https://webchat-sh.clink.cn/chat.html?accessId=1936f9be-d24f-46cf-b0d1-09d34b8bef9d&language=zh_CN';
  }, []);

  useLayoutEffect(() => {
    const checkHasInquiry = async () => {
      const res = await fetchJson({
        type: 'POST',
        url: '/api/api/v1/patient/inquiry/list',
        data: {
          inquiryStatusList: [1, 2, 3, 7, 8],
          inquiryType: 'V',
        },
      });
      if (res && res.code === '0' && res.result && res.result.length) {
        setHasInquiry(res.result.length);
      }
    };
    checkHasInquiry();
  }, []);

  const toMyInquiry = useCallback(() => {
    props.history.push({
      pathname: '/hospital/myinquiry',
      search: 'defaultType=1',
    });
  }, []);

  const toVideoInquiry = useCallback(() => {
    xflowPushEvent(['click', 'ZAHLWYY_SPWZ_JSSY', '视频问诊_介绍首页', { ZAHLWYY_CLICK_CONTENT: '视频问诊_介绍首页' }]);
    Storage.remove('inquiryCacheTime');
    Storage.remove('inquiryTags');

    if (!agreement) {
      StaticToast.warning('请阅读并同意《用户协议》和《隐私条款》');
      return;
    } else if (dmEnv.isApp()) {
      // 直营环境内可以跳转到小程序去
      location.href = `zaapp://zai.wxprogram?params={"userName":"gh_b915b88f3aa9","path":"/pages/index","type":"${zaappEnv}"}`;
    } else {
      window.reactHistory.push({
        pathname: '/hospital/videointro',
      });
    }
  }, [agreement]);

  return (
    <>
      <div className='agreement_component'>
        <Checkbox id='agreement' checked={agreement} onChange={(e: any) => {
          setAgreement(!!e.target.checked);
        }} />
        <label htmlFor='agreement'>点击开始问诊，即代表您已阅读了解并同意众安海南互联网医院<Link to={{ pathname: '/hospital/agreement' }}>《用户协议》</Link><Link to={{ pathname: '/hospital/private' }}>《个人信息保护政策》</Link></label>
      </div>
      {/* {validate.isFromMiniApplet() ? <div className='agreement_component'>
        <Checkbox id="agreement" checked={agreement} onChange={(e: any) => {
          setAgreement(!!e.target.checked);
        }} />
        <label htmlFor="agreement">点击开始问诊，即代表您已阅读了解并同意<Link to={{ pathname: '/hospital/agreement' }}>《用户协议》</Link><Link to={{ pathname: '/hospital/private' }}>《隐私条款》</Link></label>
      </div> : null} */}
      <div className='fixbottom_component'>
        {hasInquiry ? <div className='top'>
          <div className='warning'>
            <div className='left'>
              <SvgIcon className='trumpt' src={require('src/svgs/sprite-icon-trumpt.svg')} />
              <p>您有{hasInquiry}笔问诊单等待使用</p>
            </div>
            <p className='right' onClick={toMyInquiry}>继续问诊</p>
          </div>
        </div> : null}
        <div className='bottom'>
          <div className='za_wrapper' onClick={() => za_cs()}>
            <SvgIcon type='img' src={require('./images/za_cs.png')} />
          </div>
          <div className='button_wrapper'>
            {(validate.isFromMiniApplet() || dmEnv.isApp()) ? <Button theme='primary' block onClick={toVideoInquiry}>开始问诊</Button> :
              <div className='text'>
                <p>视频问诊功能本平台暂不支持</p>
                <p>请至微信 - 众安海南互联网医院小程序 使用</p>
              </div>}
          </div>
        </div>
      </div>
    </>
  );
};

const VideoIntro = (props) => (
  <div className='videointro_page'>
    <Intro />
    <CeilingNav list={['服务详情', '常见问题', '服务须知']} activeBgColor='var(--theme-success)' className='tabbar_component' />
    <Progress />
    <Question />
    <Attention />
    {/* <Agreement /> */}
    <FixBottom {...props} />
  </div>
);

export default VideoIntro;
