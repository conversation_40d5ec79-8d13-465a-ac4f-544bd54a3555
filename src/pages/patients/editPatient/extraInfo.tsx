import { shallowEqual, useSelector } from 'react-redux';
import { Card } from 'src/components/common';
// import { save_patient } from 'src/store/patients/action';
import { ApplicationState } from 'src/store';
import React, { Fragment, useCallback, useEffect, useState } from 'react';
// import storage from 'src/utils/storage';
import format from 'src/utils/format';
import { Cell } from 'zarm';
import './editpatient.scss';
import EditPatientStepThreeDetail from './patientHealth';
import { useOverflowHidden } from 'src/components/hooks';
import EditPatientStepFour from './patientFile';

const EditPatientStepThree = (props) => {
  const { patientMedicalHistory, patientAllergicHistory, patientFamilyHistory, patientHabit } = useSelector((state: ApplicationState) => {
    // patientMedicalHistory 过往病史  patientAllergicHistory 过敏史  patientFamilyHistory 家族病史  patientHabit 个人习惯
    const {
      patientHealth: { patientMedicalHistory = [], patientAllergicHistory = [], patientFamilyHistory = [], patientHabit = [] },
    } = state.patients.editPatient;
    return {
      patientMedicalHistory: format.toParse(patientMedicalHistory),
      patientAllergicHistory: format.toParse(patientAllergicHistory),
      patientFamilyHistory: format.toParse(patientFamilyHistory),
      patientHabit: format.toParse(patientHabit),
    };
  }, shallowEqual);

  const [patientHealthType, setPatientHealthType] = useState('');
  const [uploadVisible, setUploadVisible] = useState(false);

  const historyText = format.getText(patientMedicalHistory || '[]');
  const allergicHistoryText = format.getText(patientAllergicHistory || '[]');
  const familyMedicalHistoryText = format.getText(patientFamilyHistory || '[]');
  const personalHabitsText = format.getText(patientHabit || '[]');

  useEffect(() => {
    scrollTop();
  }, []);

  useOverflowHidden(!!patientHealthType);

  const toStepThreeDetail = useCallback((type) => {
    // props.history.push({
    //   pathname: '/hospital/editpatient/patienthealth',
    //   state: { type },
    //   search: `isEdit=${props.isEdit}`,
    // });
    setPatientHealthType(type);
  }, []);

  const scrollTop = useCallback(() => {
    window.scroll({
      top: 0,
      behavior: 'smooth',
    });
  }, []);

  const selectHealth = useCallback(() => {
    setUploadVisible(true);
    // props.history.push('/hospital/editpatient/patientfile');
  }, []);

  const prefixCls = 'editpatient_page';

  return (
    <Fragment>
      <Card prefixCls={`${prefixCls}__card`}>
        <div className="headers">
          <h4>健康信息</h4>
          <p className="extra">非必填</p>
        </div>
        <p className="tips">若您有时间可录入如下信息，医生可通过如下信息明确您的身体状况</p>

        <div className={`${prefixCls}__card-core`}>
          <Cell className={`select ${prefixCls}__cell custom`} title="过往病史" hasArrow onClick={() => toStepThreeDetail('patientMedicalHistory')}>
            {historyText ? <p className="text">{historyText}</p> : <p>过往病史、外伤、输血治疗</p>}
          </Cell>
          <Cell className={`select ${prefixCls}__cell custom`} title="过敏史" hasArrow onClick={() => toStepThreeDetail('patientAllergicHistory')}>
            {allergicHistoryText ? <p className="text">{allergicHistoryText}</p> : <p>药物、食物、接触过敏史</p>}
          </Cell>
          <Cell className={`select ${prefixCls}__cell custom`} title="家族病史" hasArrow onClick={() => toStepThreeDetail('patientFamilyHistory')}>
            {familyMedicalHistoryText ? <p className="text">{familyMedicalHistoryText}</p> : <p>遗传疾病</p>}
          </Cell>
          <Cell className={`select ${prefixCls}__cell custom`} title="个人习惯" hasArrow onClick={() => toStepThreeDetail('patientHabit')}>
            {personalHabitsText ? <p className="text">{personalHabitsText}</p> : <p>吸烟、饮酒、不良习惯(非必填)</p>}
          </Cell>
          <Cell className={`select ${prefixCls}__cell custom`} title="病历或检查资料" hasArrow onClick={selectHealth}>
            <p>疾病资料（病历或检查图片）</p>
          </Cell>
        </div>
      </Card>
      {
        patientHealthType && <EditPatientStepThreeDetail type={patientHealthType} close={() => setPatientHealthType('')} />
      }
      {
        uploadVisible && <EditPatientStepFour visible={uploadVisible} close={() => setUploadVisible(false)} />
      }
    </Fragment>
  );
};
export default EditPatientStepThree;
