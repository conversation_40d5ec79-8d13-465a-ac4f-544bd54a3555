import React, { useCallback } from 'react';
import { shallowEqual, useSelector, useDispatch } from 'react-redux';
import { Card, FixedButton, StaticToast, SvgIcon } from 'src/components/common';
import { ApplicationState } from 'src/store';
import { upload_file, delete_file } from 'src/store/patients/action';
import validate from 'src/utils/validate';
import { Icon, Popup } from 'zarm';
import './editpatient.scss';

import upload_button_svg from 'src/svgs/upload-button.svg';

const EditPatientStepFour = (props) => {

  const { visible , close} = props;
  const { outpatientsOrEmergency, healthScreening, prescriptionOfCM, prescriptionOfWM, others, patientNo } = useSelector((state: ApplicationState) => ({
    outpatientsOrEmergency: state.patients.editPatient.attachmentList.filter((i) => i.attachmentType === 'outpatientsOrEmergency') || [], // 门诊（急诊）病历
    healthScreening: state.patients.editPatient.attachmentList.filter((i) => i.attachmentType === 'healthScreening') || [],  // 健康体检记录
    prescriptionOfCM: state.patients.editPatient.attachmentList.filter((i) => i.attachmentType === 'prescriptionOfCM') || [],  // 中药处方
    prescriptionOfWM: state.patients.editPatient.attachmentList.filter((i) => i.attachmentType === 'prescriptionOfWM') || [],  // 西药处方
    others: state.patients.editPatient.attachmentList.filter((i) => i.attachmentType === 'others') || [],         // 其他
    patientNo: state.patients.editPatient.patientNo,    // 患者编号 绑定相关附件信息
  }), shallowEqual);
  const dispatch = useDispatch();
  // const savePatient = (onSuccess) => dispatch(save_patient(onSuccess));
  const uploadFile = (formData, attachmentType) => dispatch(upload_file(formData, attachmentType));
  const deleteFile = (id, patientNo, attachmentType) => dispatch(delete_file(id, patientNo, attachmentType));

  const deleteFileClick = useCallback((id: number, attachmentType: string) => {
    deleteFile(id, patientNo, attachmentType);
  }, [patientNo]);

  const uploadFileClick = useCallback((e, attachmentType: string) => {
    const curfile = e.target && e.target.files[0];
    const MAXSIZE = 5 * 1024 * 1024;
    if (curfile.size > MAXSIZE) {
      StaticToast.warning('图片超过大小限制');
      return;
    }
    if (!patientNo) {
      StaticToast.warning('图片无法绑定到患者上，请重新添加患者');
      return;
    }
    if (!/png|jpg|jpeg/.test(curfile.type)) {
      StaticToast.warning('只支持上传png，jpg和jpeg格式的图片');
      return;
    }
    const formData = new FormData();
    formData.append('files', curfile);
    formData.append('businessNo', patientNo);
    formData.append('attachmentType', attachmentType);
    uploadFile(formData, attachmentType);
    e.target.value = '';
  }, [patientNo]);

  // const savePatientClick = useCallback(() => {
  //   savePatient((backUrl) => {
  //     backUrl && props.history.push(backUrl);
  //   });
  // }, []);

  // const backToBaseInfo = useCallback(() => {
  //   // props.history.goBack();
  // }, []);

  const prefixCls = 'editpatient_page';
  return (
    <Popup visible={visible} className={`${prefixCls}`}>
      <div className={`${prefixCls}__health`}>
        <div className='patient_health_header'>
          健康档案录入
          <Icon onClick={close} className='patient_health_close' type='wrong'></Icon>
        </div>
        <Card prefixCls={`${prefixCls}__upload`}>
          <div className='header'>
            <p className='title'>门诊（急诊）病历</p>
            {/* <p className='length'>{outpatientsOrEmergency.length}/5</p> */}
          </div>
          <div className='photo-wrapper'>
            {outpatientsOrEmergency.length ? outpatientsOrEmergency.map((item: any, index: number) => <div className='upload-item' key={`upload-photo-${index}`}>
              <img className='upload-photo' src={`${item.attachmentDownloadUrl}`} alt='' />
              <Icon className='delete-icon' theme='danger' type='wrong-round-fill' onClick={() => deleteFileClick(item.id, 'outpatientsOrEmergency')} />
            </div>) : null}
            <div className='upload-item'>
              <div className='upload-button'>
                {validate.isIos() ?
                  <input type='file' name='file' accept='image/*' className='upload-input' onChange={(e) => uploadFileClick(e, 'outpatientsOrEmergency')} /> :
                  validate.isHuawei() ?
                    <input type='file' name='file' accept='image/*' multiple className='upload-input' onChange={(e) => uploadFileClick(e, 'outpatientsOrEmergency')} /> :
                    <input type='file' name='file' accept='image/*' capture='camera' multiple className='upload-input' onChange={(e) => uploadFileClick(e, 'outpatientsOrEmergency')} />}
                {/* {validate.isIos() ? <input type="file" name="file" accept="image/*" className="upload-input" onChange={(e) => uploadFileClick(e, "outpatientsOrEmergency")} /> : <input type="file" name="file" accept="image/*" capture="camera" multiple className="upload-input" onChange={(e) => uploadFileClick(e, "outpatientsOrEmergency")} />} */}
                <SvgIcon className='upload-cover' type='img' src={upload_button_svg} />
              </div>
            </div>
          </div>
        </Card>
        <Card prefixCls={`${prefixCls}__upload`}>
          <div className='header'>
            <p className='title'>健康体检记录</p>
            {/* <p className='length'>{healthScreening.length}/5</p> */}
          </div>
          <div className='photo-wrapper'>
            {healthScreening.length ? healthScreening.map((item: any, index: number) => <div className='upload-item' key={`upload-photo-${index}`}>
              <img className='upload-photo' src={`${item.attachmentDownloadUrl}`} alt='' />
              <Icon className='delete-icon' theme='danger' type='wrong-round-fill' onClick={() => deleteFileClick(item.id, 'healthScreening')} />
            </div>) : null}
            <div className='upload-item'>
              <div className='upload-button'>
                {validate.isIos() ?
                  <input type='file' name='file' accept='image/*' className='upload-input' onChange={(e) => uploadFileClick(e, 'healthScreening')} /> :
                  validate.isHuawei() ?
                    <input type='file' name='file' accept='image/*' multiple className='upload-input' onChange={(e) => uploadFileClick(e, 'healthScreening')} /> :
                    <input type='file' name='file' accept='image/*' capture='camera' multiple className='upload-input' onChange={(e) => uploadFileClick(e, 'healthScreening')} />}
                <SvgIcon className='upload-cover' type='img' src={upload_button_svg} />
              </div>
            </div>
          </div>
        </Card>
        <Card prefixCls={`${prefixCls}__upload`}>
          <div className='header'>
            <p className='title'>中药处方（药品清单）</p>
            {/* <p className='length'>{prescriptionOfCM.length}/5</p> */}
          </div>
          <div className='photo-wrapper'>
            {prescriptionOfCM.length ? prescriptionOfCM.map((item: any, index: number) => <div className='upload-item' key={`upload-photo-${index}`}>
              <img className='upload-photo' src={`${item.attachmentDownloadUrl}`} alt='' />
              <Icon className='delete-icon' theme='danger' type='wrong-round-fill' onClick={() => deleteFileClick(item.id, 'prescriptionOfCM')} />
            </div>) : null}
            <div className='upload-item'>
              <div className='upload-button'>
                {validate.isIos() ? <input type='file' name='file' accept='image/*' className='upload-input' onChange={(e) => uploadFileClick(e, 'prescriptionOfCM')} /> :
                  validate.isHuawei() ? <input type='file' name='file' accept='image/*' multiple className='upload-input' onChange={(e) => uploadFileClick(e, 'prescriptionOfCM')} /> :
                    <input type='file' name='file' accept='image/*' multiple capture='camera' className='upload-input' onChange={(e) => uploadFileClick(e, 'prescriptionOfCM')} />}
                <SvgIcon className='upload-cover' type='img' src={upload_button_svg} />
              </div>
            </div>
          </div>
        </Card>
        <Card prefixCls={`${prefixCls}__upload`}>
          <div className='header'>
            <p className='title'>西药处方（药品清单）</p>
            {/* <p className='length'>{prescriptionOfWM.length}/5</p> */}
          </div>
          <div className='photo-wrapper'>
            {prescriptionOfWM.length ? prescriptionOfWM.map((item: any, index: number) => <div className='upload-item' key={`upload-photo-${index}`}>
              <img className='upload-photo' src={`${item.attachmentDownloadUrl}`} alt='' />
              <Icon className='delete-icon' theme='danger' type='wrong-round-fill' onClick={() => deleteFileClick(item.id, 'prescriptionOfWM')} />
            </div>) : null}
            <div className='upload-item'>
              <div className='upload-button'>
                {validate.isIos() ? <input type='file' name='file' accept='image/*' className='upload-input' onChange={(e) => uploadFileClick(e, 'prescriptionOfWM')} /> :
                  validate.isHuawei() ? <input type='file' name='file' accept='image/*' multiple className='upload-input' onChange={(e) => uploadFileClick(e, 'prescriptionOfWM')} /> :
                    <input type='file' name='file' accept='image/*' capture='camera' multiple className='upload-input' onChange={(e) => uploadFileClick(e, 'prescriptionOfWM')} />}
                <SvgIcon className='upload-cover' type='img' src={upload_button_svg} />
              </div>
            </div>
          </div>
        </Card>
        <Card prefixCls={`${prefixCls}__upload`}>
          <div className='header'>
            <p className='title'>其他</p>
            {/* <p className='length'>{others.length}/5</p> */}
          </div>
          <div className='photo-wrapper'>
            {others.length ? others.map((item: any, index: number) => <div className='upload-item' key={`upload-photo-${index}`}>
              <img className='upload-photo' src={`${item.attachmentDownloadUrl}`} alt='' />
              <Icon className='delete-icon' theme='danger' type='wrong-round-fill' onClick={() => deleteFileClick(item.id, 'others')} />
            </div>) : null}
            <div className='upload-item'>
              <div className='upload-button'>
                {validate.isIos() ? <input type='file' name='file' accept='image/*' className='upload-input' onChange={(e) => uploadFileClick(e, 'others')} /> :
                  validate.isHuawei() ? <input type='file' name='file' accept='image/*' multiple className='upload-input' onChange={(e) => uploadFileClick(e, 'others')} /> :
                    <input type='file' name='file' accept='image/*' capture='camera' multiple className='upload-input' onChange={(e) => uploadFileClick(e, 'others')} />}
                <SvgIcon className='upload-cover' type='img' src={upload_button_svg} />
              </div>
            </div>
          </div>
        </Card>
        <FixedButton prefixCls='patient_health_btn' buttonClick={() => close('save')} text='确定' buttonShape='round' />
      </div>
    </Popup>
  );
};
export default EditPatientStepFour;
