import classnames from 'classnames';
import React, { useState, useMemo, useCallback, useEffect } from 'react';
import { shallowEqual, useSelector, useDispatch } from 'react-redux';
import { Card, FixedButton, StaticToast } from 'src/components/common';
import { ApplicationState } from 'src/store';
import { edit_patient_health } from 'src/store/patients/action';
import { Icon, Input, Popup } from 'zarm';
import './editpatient.scss';

const validate = (data) => {
  let isAuth = true;
  data.forEach((element) => {
    if (element.value === '有' && (element.tags && element.tags.length === 0) && element.additional.trim() === '') {
      if (element.key === '您是否有手术或外伤治疗') {
        StaticToast.warning('请选择或填写手术类型');
      } if (element.key === '您是否有患有或患过明确诊断的疾病') {
        StaticToast.warning('请选择或填写患有或患过明确诊断的疾病');
      } else if (element.key === '您是否有对食物过敏') {
        StaticToast.warning('请选择或填写食物过敏类型');
      } else if (element.key === '您是否有对药物过敏') {
        StaticToast.warning('请选择或填写药物过敏类型');
      } else if (element.key === '您是否有家族病史') {
        StaticToast.warning('请选择或填写家族病史');
      } else if (element.key === '您是否有不良的个人习惯') {
        StaticToast.warning('请选择或填写不良的个人习惯');
      }
      isAuth = false;
      return;
    }
  });
  return isAuth;
};

// patientMedicalHistory 过往病史  patientAllergicHistory 过敏史  patientFamilyHistory 家族病史  patientHabit 个人习惯
export const ALLERGY_DRUG = ['青霉素', '地卡因', '维生素B1', '头孢类抗生素', '破伤风抗霉素（TAT）', '阿司匹林', '普鲁卡因'];
export const ALLFAMILY_ILLNESS = ['高血压', '糖尿病', '心脏病', '脑梗', '关节置换术', '脑出血', '癌症', '哮喘', '白癜风', '近视'];
export const TYPE = {
  patientMedicalHistory: {
    title: '过往病史',
    data: [
      {
        class: '您是否有患有或患过明确诊断的疾病',
        tags: ['高血压', '糖尿病', '心脏病', '脑梗', '关节置换术', '脑出血', '癌症', '哮喘', '白癜风', '近视'],
        additional: ' ',
        placeholder: '请补充您患有或患过明确诊断的疾病',
      }, {
        class: '您是否有手术或外伤治疗',
        tags: ['骨折', '烧伤', '腹腔手术', '冠脉支架手术', '心脏搭桥手术', '颅脑手术', '开胸手术', '关节置换术'],
        additional: ' ', // 需要additional的话 注意初始化赋值补充空格 不然展示不了。
        placeholder: '请补充您的手术或外伤情况',
      }, {
        class: '您是否有过输血治疗',
      },
    ],
  },
  patientAllergicHistory: {
    title: '过敏史',
    data: [
      {
        class: '您是否有对食物过敏',
        tags: ['芒果', '牛奶', '海鲜', '笋', '黄瓜', '花粉', '油漆', '皮毛', '化妆品'],
        additional: ' ',
        placeholder: '请补充您的食物或接触物过敏源',
      }, {
        class: '您是否有对药物过敏',
        tags: ALLERGY_DRUG,
        additional: ' ',
        placeholder: '请补充您的药物过敏',
      },
    ],
  },
  patientFamilyHistory: {
    title: '家族病史',
    data: [{
      class: '您是否有家族病史',
      tags: ALLFAMILY_ILLNESS,
      additional: ' ',
      placeholder: '请补充您的家族病史',
    }],
  },
  patientHabit: {
    title: '个人习惯',
    data: [{
      class: '您是否有不良的个人习惯',
      tags: ['低头族', '久坐', '久站', '吸烟', '饮酒', '翘二郎腿', '不喜喝水', '如厕看书报', '强忍大小便'],
      additional: ' ',
      placeholder: '请补充您的不良习惯',
    }],
  },
};

const toParse = (data) => {
  if (typeof data === 'string') {
    return JSON.parse(data);
  }
  return data;
};

const EditPatientStepThreeDetail = (props) => {
  // patientMedicalHistory 过往病史  patientAllergicHistory 过敏史  patientFamilyHistory 家族病史  patientHabit 个人习惯
  const { type, close } = props;

  const renderData = useMemo(() => TYPE[type] && TYPE[type].data || [], [type]);
  const initState: Array<{ key: string, value: string, tags: string[], additional?: string }> = [];
  renderData.forEach((element) => {
    initState.push({ key: element.class, value: '无', tags: [], additional: '' });
  });

  const editPatientData = useSelector((state: ApplicationState) => toParse(state.patients.editPatient.patientHealth[type] || '[]'), shallowEqual);

  const initSubmitData = () => editPatientData[0] && editPatientData[0].value ? editPatientData : initState;
  const [submitData, setSubmiteData] = useState(initSubmitData());

  useEffect(() => {
    setSubmiteData(initSubmitData);
  }, [type]);

  // dispatch 修改患者健康信息
  const dispatch = useDispatch();
  const dispatchEditPatientInfo = (key: string, value: any) => dispatch(edit_patient_health(key, value));

  // 选择 有或无 点击事件
  const classClick = useCallback((index: number, key: string, value: any, args?: any) => {
    const copy = submitData.concat([]);
    if (args.tags && args.tags.length) {
      copy[index] = { ...args, key, value, tags: [], additional: '' };
    } else {
      copy[index] = { ...args, key, value };
    }
    setSubmiteData(copy);
  }, [submitData]);

  // 标签点击事件
  const tagClick = useCallback((index: number, value: any, args?: any) => {
    const copy = submitData.concat([]);
    if (copy[index].tags.includes(value)) {
      copy[index].tags = copy[index].tags.filter((i) => i !== value);
    } else {
      copy[index].tags.unshift(value);
    }
    setSubmiteData(copy);
  }, [submitData]);

  // 录入补充信息
  const additionalInput = useCallback((index: number, value: any, args?: any) => {
    const copy = submitData.concat([]);
    copy[index].additional = value;
    setSubmiteData(copy);
  }, [submitData]);

  // 保存并返回按钮点击事件
  const saveAndReturnClick = useCallback(() => {
    submitData.forEach((element) => {
      if (element.additional) {
        element.additional = element.additional.trim();
      }
    });
    if (validate(submitData)) {
      dispatchEditPatientInfo(type, submitData);
      // props.history.goBack();
      // props.history.push({
      //   pathname: '/hospital/editpatient/baseinfo',
      //   search,
      // })
      close && close('save');
    }
  }, [type, submitData]);

  const prefixCls = 'editpatient_page';
  return (
    <Popup visible={!!type} className={`${prefixCls}`}>
      <div className={`${prefixCls}__health`}>
        <div className='patient_health_header'>
          {TYPE[type] && TYPE[type].title}
          <Icon onClick={close} className='patient_health_close' type='wrong'></Icon>
        </div>
        {renderData.map((item, index) => {
          const submitItem = submitData.find((k) => k && item.class == k.class) || {};

          return (
            <Card prefixCls={`${prefixCls}__class ${index === 0 ? 'no-border' : ''}`} key={`editpatient_step_detail_${item.class}`}>
              <p className='class_title'>{item.class}</p>
              <div className={classnames('has_class', { active: submitItem.value && submitItem.value === '有' })}>
                <div className={classnames('tag', { active: submitItem.value && submitItem.value === '无' })} onClick={() => classClick(index, item.class, '无', item)}>无</div>
                <div className={classnames('tag', { active: submitItem.value && submitItem.value === '有' })} onClick={() => classClick(index, item.class, '有', item)}>有</div>
              </div>
              {item.tags && submitItem.value && submitItem.value === '有' &&
                <React.Fragment>
                  <div className='tag_list'>
                    {item.tags.map((v) => (
                      <div className={classnames('tag detail', { active: submitItem.tags && submitItem.tags.includes(v) })} key={`tag_${v}`} onClick={() => tagClick(index, v)}>
                        {v}
                        {submitItem.tags && submitItem.tags.includes(v) && <img className='active_img' src={require('src/images/tag_active.png')} alt='' />}
                      </div>
                    ))}
                  </div>
                  {item.additional && <div className='additional_input'>
                    <p className='title'>其他</p>
                    <div className='input_wrapper'>
                      <Input
                        showLength
                        type='text'
                        rows={4}
                        maxLength={200}
                        placeholder={item.placeholder}
                        onChange={(value) => additionalInput(index, value)}
                        value={submitItem.additional}
                      />
                    </div>
                  </div>}
                </React.Fragment>
              }
            </Card>
          );
        })}
        <FixedButton prefixCls='patient_health_btn' buttonClick={() => saveAndReturnClick()} text='确定' buttonShape='round' />
      </div>
    </Popup>
  );
};
export default EditPatientStepThreeDetail;
