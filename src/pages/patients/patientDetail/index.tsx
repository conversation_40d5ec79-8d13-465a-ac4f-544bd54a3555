import React, { useEffect, useState, useCallback } from 'react';
import { shallowEqual, useSelector, useDispatch } from 'react-redux';
import { Card } from 'src/components/common';
import { ApplicationState } from 'src/store';
import { fetch_patient_detail } from 'src/store/patients/action';
import format from 'src/utils/format';
import { Deserialize } from 'src/utils/serialization';
import { Input,ImagePreview } from 'zarm';
import './patientdetail.scss';

const PatientDetail = (props) => {
  const { location: { search = '' } } = props;
  const { patientId = '' } = Deserialize(search);

  const editPatient = useSelector((state: ApplicationState) => state.patients.editPatient, shallowEqual);

  const {BLOODSUGARMONITORTIMEENUM_OBJ, SOCIALSECURITYTYPE_OBJ, GENDER_OBJ, PATIENTRELATION_OBJ, CERTTYPE_OBJ }: any = useSelector((state: ApplicationState) => {
    const {SOCIALSECURITYTYPE_OBJ={},GENDER_OBJ={},PATIENTRELATION_OBJ={},CERTTYPE_OBJ={},BLOODSUGARMONITORTIMEENUM_OBJ={}} = state.dictionary;
    return {
      // MARRIAGE_OBJ: state.dictionary.MARRIAGE_OBJ,
      // PATIENTTYPE_OBJ: state.dictionary.PATIENTTYPE_OBJ,
      SOCIALSECURITYTYPE_OBJ,
      GENDER_OBJ,
      PATIENTRELATION_OBJ,
      CERTTYPE_OBJ,
      // BLOODGROUP_OBJ: state.dictionary.BLOODGROUP_OBJ
      BLOODSUGARMONITORTIMEENUM_OBJ,
    };
  });

  const dispatch = useDispatch();
  const fetchPatientDetail = (patientId: string) => dispatch(fetch_patient_detail(patientId,{needSensitive: true}));

  const historyText = format.getText(format.toParse(editPatient.patientHealth.patientMedicalHistory) || []);
  const allergicHistoryText = format.getText(format.toParse(editPatient.patientHealth.patientAllergicHistory) || []);
  const familyMedicalHistoryText = format.getText(format.toParse(editPatient.patientHealth.patientFamilyHistory) || []);
  const personalHabitsText = format.getText(format.toParse(editPatient.patientHealth.patientHabit) || []);
  const outpatientsOrEmergency = editPatient.attachmentList.filter((i) => i.attachmentType === 'outpatientsOrEmergency') || []; // 门诊（急诊）病历
  const healthScreening = editPatient.attachmentList.filter((i) => i.attachmentType === 'healthScreening') || [];  // 健康体检记录
  const prescriptionOfCM = editPatient.attachmentList.filter((i) => i.attachmentType === 'prescriptionOfCM') || [];  // 中药处方
  const prescriptionOfWM = editPatient.attachmentList.filter((i) => i.attachmentType === 'prescriptionOfWM') || [];  // 西药处方
  const others = editPatient.attachmentList.filter((i) => i.attachmentType === 'others') || [];         // 其他
  const [fristItem = {}]= editPatient.bloodSugarList;
  const {rangeTimeType='',bloodSugar='',highBloodPressure='',lowBloodPressure='',heartRate=''} = fristItem;
  // 预览图片的list
  const imagesList: any = []; const imagesListHealth: any = []; const imagesListPrescriptionOfCM: any = []; const imagesListPrescriptionOfWM: any = []; const imagesListOthers: any = [];

  const toRecordList = useCallback(() => {
    window.reactHistory.push({
      pathname: '/hospital/patientDetail/recordList',
      search: `patientId=${patientId}`,
    });
  }, []);

  const toEditPatient = useCallback(() => {
    props.history.push({
      pathname: '/hospital/editpatient/baseinfo',
      search: `isEdit=true&patientId=${patientId}`,
    });
  }, []);
  const [visible, setVisible] = useState({
    outpatientsOrEmergency: false,
    healthScreening: false,
    prescriptionOfCM: false,
    prescriptionOfWM: false,
    others: false,
  });
  const [pictureIndex, setPictureIndex] = useState(0);

  const hide = (key) => setVisible({
    ...visible,
    [key]: false,
  });

  const show = (key, index) => {
    setVisible({
      ...visible,
      [key]: true,
    });
    setPictureIndex(index);
  };


  useEffect(() => {
    if (patientId) {
      fetchPatientDetail(patientId);
    }
  }, []);

  if (patientId === '') {
    return null;
  }

  return (
    <div className='patientdetail_page'>
      <div className='header'>
        <p className='header-info'>信息仅供主治医生查看，请认真填写</p>
        <div className='edit-button' onClick={() => toEditPatient()}>
          <img src={require('src/images/icon_patient_detail_edit.png')} />
          <p>编辑</p>
        </div>
      </div>

      <div className='info-wrapper'>
        <div className='info-item'>
          <span className='left'>与本人关系</span>
          <span className='right'>{PATIENTRELATION_OBJ[editPatient.patientRelation] || ''}</span>
        </div>
        <div className='info-item'>
          <span className='left'>患者姓名</span>
          <span className='right'>{editPatient.patientName || ''}</span>
        </div>
        {/* <div className="info-item">
          <span className='left'>患者人群</span>
          <span className="right">{PATIENTTYPE_OBJ[editPatient.patientType] || ''}</span>
        </div> */}
        <div className='info-item'>
          <span className='left'>证件类型</span>
          <span className='right'>{CERTTYPE_OBJ[editPatient.patientCertType] || ''}</span>
        </div>
        <div className='info-item'>
          <span className='left'>证件号码</span>
          <span className='right' style={{ color: '#888888' }}>
            {editPatient.patientCertNo || ''}
          </span>
        </div>
        <div className='info-item'>
          <span className='left'>出生日期</span>
          <span className='right'>{editPatient.patientBirthday ? editPatient.patientBirthday : ''}</span>
        </div>
        {/* <div className="info-item">
          <span className='left'>手机号码</span>
          <span className="right">{editPatient.patientPhone || ''}</span>
        </div> */}
        {/* <div className="info-item">
          <span className='left'>身高</span>
          <span className="right">{editPatient.patientHeight != '' && editPatient.patientHeight ? `${editPatient.patientHeight}cm` : ''}</span>
        </div> */}
        <div className='info-item'>
          <span className='left'>体重</span>
          <span className='right'>{editPatient.patientWeight != '' && editPatient.patientWeight ? `${editPatient.patientWeight}kg` : ''}</span>
        </div>
        {/* <div className="info-item">
          <span className='left'>血型</span>
          <span className="right">{editPatient.patientBloodType ? `${BLOODGROUP_OBJ[editPatient.patientBloodType]}` : ''}</span>
        </div> */}
        <div className='info-item'>
          <span className='left'>性别</span>
          <span className='right'>{editPatient.patientGender ? GENDER_OBJ[editPatient.patientGender] : ''}</span>
        </div>
        {/* <div className="info-item">
          <span className='left'>婚姻状况</span>
          <span className="right">{MARRIAGE_OBJ[editPatient.patientMarriage] || ''}</span>
        </div> */}
        <div className='info-item'>
          <span className='left'>所在地区</span>
          <span className='right'>{editPatient.patientDistrictName || ''}</span>
        </div>
        <div className='info-item'>
          <span className='left'>社保类型</span>
          <span className='right'>{SOCIALSECURITYTYPE_OBJ[editPatient.patientSocialSecurityType] || ''}</span>
        </div>
        <Card prefixCls='desc-item'>
          <p className='title'>过往病史</p>
          {historyText ? <Input className='desc' type='text' readOnly rows={4} value={historyText} /> : <p className='no_text'>暂无资料</p>}
        </Card>
        <Card prefixCls='desc-item'>
          <p className='title'>过敏史</p>
          {allergicHistoryText ? <Input className='desc' type='text' readOnly rows={4} value={allergicHistoryText} /> : <p className='no_text'>暂无资料</p>}
        </Card>
        <Card prefixCls='desc-item'>
          <p className='title'>家族病史</p>
          {familyMedicalHistoryText ? <Input className='desc' type='text' readOnly rows={4} value={familyMedicalHistoryText} /> : <p className='no_text'>暂无资料</p>}
        </Card>
        <Card prefixCls='desc-item'>
          <p className='title'>个人习惯</p>
          {personalHabitsText ? <Input className='desc' type='text' readOnly rows={4} value={personalHabitsText} /> : <p className='no_text'>暂无资料</p>}
        </Card>
        <Card prefixCls='desc-item'>
          <p className='title'>疾病资料</p>
          <p className='sub_title hasArrow' style={{ position:'relative'}} onClick={() => toRecordList()}>互联网医院病历</p>
          {outpatientsOrEmergency.length ? <React.Fragment>
            <p className='sub_title'>门（急）诊病历</p>
            <div className='photo_wrapper'>
              {outpatientsOrEmergency.map((element, index) => {
                imagesList.push(element.attachmentDownloadUrl);
                return <img className='case' key={`case_${element.id}`} src={`${element.attachmentDownloadUrl}`} onClick={() => show('outpatientsOrEmergency', index)} alt='' />;
              }
              )}
              <ImagePreview
                visible={visible.outpatientsOrEmergency}
                images={imagesList}
                onClose={()=>{
                  hide('outpatientsOrEmergency');
                }}
                activeIndex={pictureIndex}
              />
            </div>
          </React.Fragment> :
            <div className='no_data'>
              <p className='sub_title'>门（急）诊病历</p>
              <p className='no_data_text'>暂无资料</p>
            </div>
          }

          {healthScreening.length ? <React.Fragment>
            <p className='sub_title'>健康体检记录</p>
            <div className='photo_wrapper'>
              {healthScreening.map((element, index) => {
                imagesListHealth.push(element.attachmentDownloadUrl);
                return <img className='case' key={`case_${element.id}`} src={`${element.attachmentDownloadUrl}`} alt='' onClick={() => show('healthScreening', index)} />;
              })}
              <ImagePreview
                visible={visible.healthScreening}
                images={imagesListHealth}
                onClose={()=>{
                  hide('healthScreening');
                }}
                activeIndex={pictureIndex}
              />
            </div>
          </React.Fragment> :
            <div className='no_data'>
              <p className='sub_title'>健康体检记录</p>
              <p className='no_data_text'>暂无资料</p>
            </div>
          }

          {prescriptionOfCM.length ? <React.Fragment>
            <p className='sub_title'>中药处方（药品清单）</p>
            <div className='photo_wrapper'>
              {prescriptionOfCM.map((element, index) => {
                imagesListPrescriptionOfCM.push(element.attachmentDownloadUrl);
                return <img className='case' key={`case_${element.id}`} src={`${element.attachmentDownloadUrl}`} alt='' onClick={()=>{
                  show('prescriptionOfCM',index);
                }} />;
              }
              )}
              <ImagePreview
                visible={visible.prescriptionOfCM}
                images={imagesListPrescriptionOfCM}
                onClose={()=>{
                  hide('prescriptionOfCM');
                }}
                activeIndex={pictureIndex}
              />
            </div>
          </React.Fragment> :
            <div className='no_data'>
              <p className='sub_title'>中药处方（药品清单）</p>
              <p className='no_data_text'>暂无资料</p>
            </div>
          }

          {prescriptionOfWM.length ? <React.Fragment>
            <p className='sub_title'>西药处方（药品清单）</p>
            <div className='photo_wrapper'>
              {prescriptionOfWM.map((element, index) => {
                imagesListPrescriptionOfWM.push(element.attachmentDownloadUrl);
                return <img className='case' key={`case_${element.id}`} src={`${element.attachmentDownloadUrl}`} alt='' onClick={()=>{
                  show('prescriptionOfWM',index);
                }} />;
              })}
              <ImagePreview
                visible={visible.prescriptionOfWM}
                images={imagesListPrescriptionOfWM}
                onClose={()=>{
                  hide('prescriptionOfWM');
                }}
                activeIndex={pictureIndex}
              />
            </div>
          </React.Fragment> :
            <div className='no_data'>
              <p className='sub_title'>西药处方（药品清单）</p>
              <p className='no_data_text'>暂无资料</p>
            </div>
          }

          {others.length ? <React.Fragment>
            <p className='sub_title'>其他</p>
            <div className='photo_wrapper'>
              {others.map((element, index) => {
                imagesListOthers.push(element.attachmentDownloadUrl);
                return <img className='case' key={`case_${element.id}`} src={`${element.attachmentDownloadUrl}`} alt='' onClick={()=>{
                  show('others',index);
                }} />;
              })}
              <ImagePreview
                visible={visible.others}
                images={imagesListOthers}
                onClose={()=>{
                  hide('others');
                }}
                activeIndex={pictureIndex}
              />
            </div>
          </React.Fragment> :
            <div className='no_data'>
              <p className='sub_title'>其他</p>
              <p className='no_data_text'>暂无资料</p>
            </div>
          }
        </Card>
        <Card prefixCls='desc-item'>
          <p className='title'>数据监测</p>
          {bloodSugar != '' && bloodSugar ? <React.Fragment>
            <div className='info-item'>
              <span className='left'>血糖</span>
              <span className='right'>{`${BLOODSUGARMONITORTIMEENUM_OBJ[rangeTimeType]||''} ${bloodSugar}mmol/L`}</span>
            </div>
          </React.Fragment> :
            <div className='no_data'>
              <p className='sub_title'>血糖</p>
              <p className='no_data_text'>暂无资料</p>
            </div>
          }
          {highBloodPressure != '' && lowBloodPressure != '' && highBloodPressure && lowBloodPressure ? <React.Fragment>
            <div className='info-item'>
              <span className='left'>血压</span>
              <span className='right'> { `${highBloodPressure}/${lowBloodPressure}mmHg/L`} </span>
            </div>
          </React.Fragment> :
            <div className='no_data'>
              <p className='sub_title'>血压</p>
              <p className='no_data_text'>暂无资料</p>
            </div>
          }
          {heartRate != '' && heartRate ? <React.Fragment>
            <div className='info-item'>
              <span className='left'>心率</span>
              <span className='right'> { `${heartRate}次/分`} </span>
            </div>
          </React.Fragment> :
            <div className='no_data'>
              <p className='sub_title'>心率</p>
              <p className='no_data_text'>暂无资料</p>
            </div>
          }
        </Card>
      </div>
    </div>
  );
};

export default PatientDetail;
