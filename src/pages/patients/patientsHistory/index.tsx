import { fetch_patient_inquiry, fetch_patient_detail } from 'src/store/patients/action';
import { Avatar, Card, Background, SvgIcon } from 'src/components/common';
import { useSelector, shallowEqual, useDispatch } from 'react-redux';
import React, { useEffect, useCallback } from 'react';
import { Deserialize } from 'src/utils/serialization';
import { ApplicationState } from 'src/store';
import format from 'src/utils/format';
import classnames from 'classnames';
import './patientshistory.scss';

import no_inquiry_svg from 'src/svgs/no-inquiry.svg';


const PatientsHistory = (props) => {
  const { location: { search = '' } } = props;
  const { patientId = '' } = Deserialize(search);

  const { patientInfo, patientInquiry } = useSelector((state: ApplicationState) => {
    return {
      patientInfo: state.patients.editPatient || {},
      patientInquiry: state.patients.patientInquiry || [],
    };
  }, shallowEqual);

  const { PROFESSIONALTITLE_OBJ, GENDER_OBJ }: any = useSelector((state: ApplicationState) => {
    return {
      PROFESSIONALTITLE_OBJ: state.dictionary.PROFESSIONALTITLE_OBJ,
      GENDER_OBJ: state.dictionary.GENDER_OBJ,
    }
  });

  const dispatch = useDispatch();
  const fetchInquiryList = (patientId: string) => dispatch(fetch_patient_inquiry(patientId));
  const fetchPatientDetail = (patientId: string) => dispatch(fetch_patient_detail(patientId,{"needSensitive": true}));

  useEffect(() => {
    fetchInquiryList(patientId);
    fetchPatientDetail(patientId);
  }, []);

  const toMyDoctor = useCallback(() => {
    props.history.push({
      pathname: '/hospital/mydoctor',
      state: {
        patientId: patientId
      }
    })
  }, []);

  const toPatientDetail = useCallback(() => {
    props.history.push({
      pathname: '/hospital/patientdetail',
      search: `patientId=${patientId}`
      // state: {
      //   patientId: patientId
      // }
    })
  }, []);

  if (patientId === '' || JSON.stringify(patientInfo) === '{}') {
    return null;
  }

  return (
    <div className='patientshistory_page'>
      <Background />
      <div className="base_info_wrapper">
        <Avatar prefixCls="avatar" gender={patientInfo.patientGender} age={patientInfo.age} />
        <div className="info">
          <p className="base"> {patientInfo.patientName}  {GENDER_OBJ[patientInfo.patientGender]} {patientInfo.patientBirthday ? format.GetAgeByBirthday(format.date(patientInfo.patientBirthday, 'yyyy-MM-dd')) : ''}岁</p>
          <p className="ps">添加婚育史、病史等基本健康资料</p>
          <div className="check-button" onClick={() => toPatientDetail()}>查看健康信息</div>
        </div>
      </div>
      <Card prefixCls='info_wrapper'>
        <div className="doctor_wrapper" onClick={() => toMyDoctor()}>
          <div className='left'>
            <img src={require('src/images/icon_doctor.png')} alt="" />
            <p>我的医生</p>
          </div>
          <div className='right'>
            <p>{patientInfo.doctorCount}</p>
            <img src={require('src/images/icon_arrow.png')} alt="" />
          </div>
        </div>
        <div className="inquiry_journey_wrapper">
          {patientInquiry.length ? patientInquiry.map((item: any, index: number) => {
            return <div className={classnames(
              "inquiry-item",
              { "progress": index != 0 && index != (patientInquiry.length - 1) },
              { "start": index == 0 },
              { "end": index == patientInquiry.length - 1 },
              { "one": patientInquiry.length == 1 },
            )} key={`inquiry-item-${index}`}>
              <div className="pot"></div>
              <div className="inquiry_time"><span className="time">{format.date(item.inquiryTime, 'yyyy-MM-dd')}</span></div>
              <div className="inquiry_info">{item.staffName} {PROFESSIONALTITLE_OBJ[item.staffProfessionalTitle] || item.staffProfessionalTitle}</div>
              <div className="inquiry_desc">{item.illnessDescription}</div>
            </div>
          }) : <div className='no_inquiry_cover'>
              <SvgIcon type='img' src={no_inquiry_svg} />
              <p>暂时没有问诊记录</p>
            </div>}
        </div>
      </Card>
    </div>
  );
}

export default PatientsHistory;
