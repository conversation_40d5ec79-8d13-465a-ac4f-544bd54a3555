@import "src/style/index";

.patientslist_page {
  background: linear-gradient(180deg, rgba(255, 255, 255, 1) 0%, rgba(245, 247, 254, 1) 100%);
  min-height: 100vh;

  .account {
    padding: r(15) 0 r(15) r(16);
    color: #909090;
    font-size: r(15);
  }

  .patient_item {
    margin: 0 auto r(10);
    width: r(355);
    height: r(100);
    padding: r(20) r(15);
    @include display-flex;
    @include align-items(center);
    @include justify-content(space-between);
    @include borderRadius($color: #F0F0F0, $radius: r(8));

    .info_wrapper {
      @include flex;
      @include display-flex;
      @include align-items(center);

      position: relative;
      z-index: 2;

      .select {
        width: r(20);
        height: r(20);
        border-radius: 50%;
        border: r(1) solid #9b9b9b;
      }

      .patient_avatar {
        width: r(60);
        height: r(60);
        margin: 0 r(15);
      }

      .info {
        .name {
          color: var(--color-text);
          font-size: r(19);
          margin-bottom: r(8);
        }

        .gender_age {
          color: #999;
          font-size: r(14);
        }
      }
    }

    .edit {
      padding-left: r(20);
      position: relative;
      color: var(--theme-success);
      font-size: r(14);
      z-index: 2;

      img {
        display: block;
        margin: r(6) auto r(8);
        width: r(19);
        height: r(19);
      }

      &::before {
        content: ' ';
        position: absolute;
        left: 0;
        top: 0;
        width: r(1);
        height: r(60);
        background: #f3f3f3;
      }
    }
  }

  .btn_add {
    padding: r(15) 0;
    color: var(--theme-success);
    font-size: r(15);
    @include display-flex;
    @include align-items(center);
    @include justify-content(center);
  }
}
