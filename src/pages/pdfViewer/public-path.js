/**
 * @description 设置webpack的publicPath
 * 这个js的存在，就是让以pdfViewer为入口的页面，能够获取到正确的publicPath。并且极大减少不必要的代码
 */

const cookies = {
  get: (name) => {
    let arr;
    const reg = new RegExp('(^| )' + name + '=([^;]*)(;|$)');
    if ((arr = document.cookie.match(reg))) {
      return decodeURIComponent(arr[2]);
    }
  },
};

const configs = {
  dev: {
    publicPath: '/hospital/assets',
  },
  test: {
    publicPath: '/hospital/assets',
  },
  pre: {
    publicPath: 'https://static.za-doctor.com/za-asclepius-patient-h5-pre/static',
  },
  prd: {
    publicPath: 'https://static.za-doctor.com/za-asclepius-patient-h5/static',
  },
};

// 在C端，运行时获取环境
const env = cookies.get('_e');

const config = configs[env];

// eslint-disable-next-line
__webpack_public_path__ = `${config.publicPath}/`;
