import './list.scss';
import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { FixedButton, StaticToast, Empty } from 'src/components/common';
import AvatarMan from 'src/images/avatar_man.png';
import AvatarWoman from 'src/images/avatar_woman.png';
import { Checkbox, Modal, SwipeAction } from 'zarm';
import { ApplicationState } from '../../../store';
import { delete_patient, fetch_patients_list } from '../../../store/patients/action';
import { clientV1 } from '../../../utils/client';

const List = (props) => {
  const [edit, setEdit] = useState(false);
  const [editIds, setEditIds] = useState<Record<string, boolean>>({});
  const dispatch = useDispatch();
  const patientsList = useSelector((state: ApplicationState) => state.patients.patientsList || []) || [];
  const relationMap = useSelector((state: ApplicationState) => state.dictionary.PATIENTRELATION_OBJ) || {};

  const handleEdit = () => {
    setEdit(true);
  };

  const handleCancelEdit = () => {
    setEdit(false);
    setEditIds({});
  };

  const handleDeletePatient = (patientId: number) => {

    Modal.confirm({
      title: '',
      content: '确定删除此成员档案吗？',
      onOk() {
        dispatch(delete_patient(patientId));
        return Promise.resolve(true);
      },
    });

  };

  const deleteRequest = (patientId: number | string) => clientV1.get('patient/patient/delete', { params: { patientId } });

  /**
   * 删除选中的成员档案
   *
   * 该函数执行以下操作：
   * 1. 检查是否有选中的成员档案
   * 2. 弹出确认对话框
   * 3. 遍历并删除选中的成员档案
   * 4. 重置编辑状态
   * 5. 显示成功提示
   * 6. 重新获取患者列表
   *
   * @returns {void}
   */
  const handleBatchDelete = () => {
  // 检查是否有选中的成员档案
    if(Object.keys(editIds).length <= 0) {
      return StaticToast.error('暂无可删除数据');
    }

    Modal.confirm({
      title: '',
      content: '确定删除选中的成员档案吗?',
      async onOk() {
      // 遍历所有选中的成员档案
        for (const [ id, deleted ] of Object.entries(editIds)) {
          if (deleted) {
            console.log(id);
            // 调用删除请求
            await deleteRequest(id);
          }
        }

        // 删除完成后，重置编辑状态
        handleCancelEdit();
        StaticToast.success('删除成功！');

        // 重新获取患者列表
        dispatch(fetch_patients_list());
        return Promise.resolve(true);
      },
    });
  };

  const handleDeleteCheckBoxChange = (id, checked) => {
    // 创建一个新的对象副本，避免直接修改原状态对象
    const newEditIds = { ...editIds };
    if(checked) {
      // 如果选中状态为true，将该ID添加到编辑ID对象中
      newEditIds[id] = checked;
    } else {
      // 如果选中状态为false，从编辑ID对象中移除该ID
      delete newEditIds[id];
    }
    // 更新状态，触发重新渲染
    setEditIds(newEditIds);
  };


  useEffect(() => {
    dispatch(fetch_patients_list());
  }, []);

  return (
    <div className='list'>

      {
        patientsList.length < 1 ?
          <div className='empty-cont'>
            <Empty.cardEmpty />
          </div> :
          null
      }

      <div className='list__area'>
        {
          patientsList.length > 0 &&
          <div className='list__action'>
            {
              edit ?
                <span className='text-primary text-15px' onClick={ handleCancelEdit }>退出管理</span> :
                <span className='text-primary text-15px' onClick={ handleEdit }>管理</span>
            }
          </div>
        }
        <div className='list__container'>
          {
            patientsList.map((item) => {
              const avatarImg = item.patientGender === 'M' ? AvatarMan : AvatarWoman;
              const genderText = item.patientGender === 'M' ? '男' : '女';

              return (
                <SwipeAction
                  key={ item.id }
                  right={ item.patientRelation !== 1 && !edit ? [
                    <div
                      key='1'
                      onClick={ () => handleDeletePatient(item.id) }
                      className='patient__delete'
                    >
                      删除成员
                    </div>,
                  ] : [] }
                >
                  <div className='patient__card' onClick={() => {
                    if(edit) {
                      return;
                    }
                    props.history.push({
                      pathname: '/hospital/editpatient/baseinfo',
                      search: `isEdit=true&patientId=${item.id}`,
                    });
                  }}>
                    {
                      edit &&
                      <Checkbox
                        disabled={item.patientRelation === 1}
                        checked={ editIds[item.id] }
                        onChange={ (e) => handleDeleteCheckBoxChange(item.id, e?.target.checked) }
                      />
                    }
                    <div className='patient__avatar'>
                      <img src={ avatarImg } alt='' />
                    </div>
                    <div>
                      <div className='patient__name'>
                        <span>{ item.patientName }</span>
                        <span>{ relationMap[item.patientRelation] }</span>
                      </div>
                      <div className='patient__gender-box'>
                        <span>{ genderText }</span>
                        <span className='patient__diver'>|</span>
                        { typeof item.age === 'number' && <span>{ item.age }岁</span> }
                      </div>
                    </div>
                  </div>
                </SwipeAction>
              );
            })
          }
        </div>
      </div>
      {
        edit ?
          <FixedButton
            buttonShape='round'
            text='删除'
            theme='danger'
            buttonClick={ handleBatchDelete }
          /> :
          <FixedButton
            buttonShape='round'
            text='添加成员'
            buttonClick={ () => window.reactHistory.push('/hospital/editpatient/baseinfo') }
          />
      }
    </div>
  );
};


export default List;
