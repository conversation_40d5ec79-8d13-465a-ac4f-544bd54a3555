<?xml version="1.0" encoding="UTF-8"?>
<svg width="40px" height="40px" viewBox="0 0 40 40" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>个人中心 / 处方管理 icon</title>
    <defs>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-1">
            <stop stop-color="#ABC8F4" offset="0%"></stop>
            <stop stop-color="#64A7F1" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-2">
            <stop stop-color="#ABC8F4" offset="0%"></stop>
            <stop stop-color="#64A7F1" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-3">
            <stop stop-color="#ABC8F4" offset="0%"></stop>
            <stop stop-color="#64A7F1" offset="100%"></stop>
        </linearGradient>
        <rect id="path-4" x="10" y="16" width="20" height="4" rx="2"></rect>
        <rect id="path-5" x="10" y="25" width="20" height="4" rx="2"></rect>
    </defs>
    <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="个人中心1" transform="translate(-457.000000, -376.000000)">
            <g id="个人中心-/-处方管理-icon" transform="translate(457.000000, 376.000000)">
                <rect id="矩形" stroke="#979797" fill="#D8D8D8" opacity="0" x="0.5" y="0.5" width="39" height="39"></rect>
                <rect id="形状结合" fill="url(#linearGradient-1)" x="5" y="4" width="30" height="34" rx="2.4"></rect>
                <rect id="Rectangle-14" fill="url(#linearGradient-2)" x="11" y="2" width="18" height="8" rx="1.2"></rect>
                <g id="矩形">
                    <use fill="url(#linearGradient-3)" xlink:href="#path-4"></use>
                    <use fill="#FFFFFF" xlink:href="#path-4"></use>
                </g>
                <g id="矩形">
                    <use fill="url(#linearGradient-3)" xlink:href="#path-5"></use>
                    <use fill="#FFFFFF" xlink:href="#path-5"></use>
                </g>
            </g>
        </g>
    </g>
</svg>