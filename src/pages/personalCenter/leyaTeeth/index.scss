@import "src/style/index";

.leya-teeth-page {
  width: 100%;
  min-height: 100vh;
  background-color: #f5f5f5;

  .top-img {
    width: 100%;
    height: auto;
  }

  .choice {
    margin: 15px 15px 90px;
  }

  .choice-item {
    background-color: #fff;
    margin-top: 10px;
    padding: 0 15px;
    border-radius: 8px;
    border: 1px solid #fff;
    position: relative;
    overflow: hidden;

    .icon-success {
      display: none;
    }

    &.checked {
      border: 1px solid #00bc70;

      .triangle {
        position: absolute;
        right: 0;
        top: 0;
        width: 0;
        height: 0;
        border-top: 24px solid #00bc70;
        border-left: 24px solid transparent;
      }

      .icon-success {
        display: block;
        position: absolute;
        top: 0;
        right: 0;
        width: r(14);
        height: r(14);
      }
    }
  }

  .btn-report {
    width: r(78);
  }

  .btn-check-detail {
    font-size: r(13);
    font-family: PingFangSC, PingFang SC;
    font-weight: 500;
    color: #ec9131;
  }

  .j-sb {
    display: flex;
    justify-content: space-between;
    line-height: r(58);
    align-items: center;
    height: r(58);

    &.time-part {
      height: r(50);
      line-height: r(50);
    }

    .no_rights_icon {
      width: r(14);
      height: r(14);
      margin-right: r(5);
    }
  }

  .gotoExpress {
    display: flex;
    justify-content: flex-end;
    padding-bottom: r(15);
  }

  .c-time {
    font-size: r(13);
    color: #999;
  }

  .name-part {
    border-bottom: 1px solid #ececec;
    @media (-webkit-min-device-pixel-ratio: 2) {
      border-bottom: 0.5px solid #ececec;
    }
  }

  .c-name {
    color: #1e1e1e;
    font-weight: bold;
    font-size: 16px;
  }

  .c-cancel {
    font-size: r(13);
    font-family: PingFangSC, PingFang SC;
    font-weight: 500;
    color: #9b9b9b;
    margin-left: r(26);
  }

  .c-status {
    font-weight: 500;
    font-size: 14px;
    color: #999;

    &.active {
      color: #00a864;
    }
  }

  .c-used {
    position: absolute;
    right: 0;
    top: 0;
    background: rgba(0, 188, 112, 0.1);
    font-size: r(11);
    font-family: PingFangSC, PingFang SC;
    font-weight: 500;
    color: #00bc70;
    border-radius: 0 r(6) 0 r(6);
    display: inline-block;
    line-height: normal;
    padding: r(2) r(6);
  }

  .fix-btn {
    position: fixed;
    bottom: 36px;
    left: 0;
    right: 0;
    margin: 0 30px;
  }
}
