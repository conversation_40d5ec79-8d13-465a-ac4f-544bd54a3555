/* eslint-disable @typescript-eslint/no-floating-promises */
import classnames from 'classnames';
import dayjs from 'dayjs';
import React, { useMemo, useState, useEffect } from 'react';
import { StaticToast } from 'src/components/common';
import icon_success from 'src/images/icon_pay_success.png';
import { jumpBeforeAuth } from 'src/utils/auth';
import { fetchJson } from 'src/utils/fetch';
import { Deserialize } from 'src/utils/serialization';
import { THIRD_PARTNER_AUTH_CONFIG } from 'src/utils/staticData';
// import { CDN_PREFIX } from 'src/utils/staticData';
import { Button, Modal } from 'zarm';
import './index.scss';

interface Product {
  bizKey: string;
  bizContent: string;
}

interface Detail {
  healthCheckId: string;
  appointStatus: string;
  checkStatus: string;
  thirdProductCode: string;
}

interface Response<T> {
  code: string;
  result?: T;
}

const VAS_CODE = 'TeethcleaningLY';

const LeyaTeethPage = () => {
  const {
    rightsId: _rightsId,
    userRightsId: _userRightsId,
    expireDate,
    policyNo,
  } = useMemo(() => Deserialize(window.location.search), []);
  const [productList, setProductList] = useState<Product[]>([]);
  const [detail, setDetail] = useState<Detail>({
    healthCheckId: '',
    appointStatus: '',
    checkStatus: '',
    thirdProductCode: '',
  });
  const [selectType, setSelectType] = useState('');
  const [userRightsId, setUserRightsId] = useState(_userRightsId);
  const [rightsId, setRightsId] = useState(_rightsId);

  const expireDateStr = expireDate ? decodeURIComponent(expireDate) : '';
  const isExpire = dayjs(expireDateStr).isBefore(dayjs());

  /**
   * 查询保单VAS信息
   *
   * 通过API请求获取保单的VAS（增值服务）信息，并在成功响应时更新用户权益ID
   *
   * @returns {Promise<void>} 返回一个Promise，完成后无返回值
   */
  const getPolicyVasInfo = async () => fetchJson({
    url: '/api/api/v1/patient/policy/queryPolicyVasInfo',
    type: 'POST',
    data: {
      vasCode:  VAS_CODE,
      policyNo,
    },
    isloading: true,
  }).then((res) => {
    if (res.code === '0') {
      const { result = [] } = res;
      const { userRightsId, rightsId } = result[0] || {};
      setUserRightsId(userRightsId);
      setRightsId(rightsId);
    }
  });

  const init = () => {
    if(!userRightsId){
      getPolicyVasInfo();
    }
  };

  useEffect(() => {
    if (rightsId && userRightsId) {

      Promise.all([getProductList(), getDetail()]).then(
        ([productListRes, detailRes]: [Response<Product[]>, Response<Detail>]) => {
          if (productListRes.code === '0' && detailRes.code === '0') {
            if (productListRes.result?.length === 1 && !isExpire) {
              // 如果只有一个权益且已兑换&已领取，直接跳转乐牙
              if (detailRes.result?.appointStatus === 'Y' || detailRes.result?.checkStatus === 'Y') {
                StaticToast.success('跳转中...');
                return gotoLeya(detailRes.result.thirdProductCode, true);
              }
              // 如果只有一个权益且未兑换，直接兑换并跳转乐牙
              if (detailRes.result?.appointStatus === 'N') {
                const product = productListRes.result[0];
                getExchange(product.bizKey)?.then((res) => {
                  if (res.code === '0') {
                    gotoLeya(product.bizKey, true);
                  }
                });
              }
            }
          }
        },
      );
    }
  }, [userRightsId, rightsId]);

  useEffect(() => {
    init();
  }, []);

  const isCheckDisabled = () => detail.checkStatus === 'Y';

  const check = (selectType) => {
    if (isCheckDisabled()) {
      return;
    }
    setSelectType(selectType);
  };

  const getProductList = () =>
    fetchJson({
      url: '/api/api/v1/patient/user/rights/rights/leya/config',
      type: 'POST',
      data: {
        rightsId,
        bizType: VAS_CODE,
      },
      success: (res) => {
        if (res && res.code === '0' && res.result) {
          setProductList(res.result);
        }
      },
    });

  const getDetail = () =>
    fetchJson({
      url: '/api/api/v1/patient/leya/detail',
      type: 'POST',
      data: {
        userRightId: userRightsId,
      },
      success: (res) => {
        if (res && res.code === '0' && res.result) {
          setDetail(res.result);
          // setSelectType(res.result.thirdProductCode);
        }
      },
    });

  const gotoLeya = (thirdProductCode, replaceMode = false) => {
    jumpBeforeAuth({
      isReplacePage: replaceMode,
      bizId: userRightsId,
      thirdBizNo: thirdProductCode,
      ...THIRD_PARTNER_AUTH_CONFIG.LEYA,
    });
  };

  const cancel = () => {
    const { healthCheckId } = detail;
    if (detail.checkStatus === 'Y') {
      StaticToast.error('权益已使用，无法取消');
    } else {
      Modal.confirm({
        content: '是否取消兑换？取消后可重新兑换。',
        onOk: () => {
          fetchJson({
            url: '/api/api/v1/patient/leya/cancel',
            type: 'POST',
            isloading: true,
            data: {
              id: healthCheckId,
            },
          }).then((res) => {
            if (res.code === '0') {
              getDetail();
            }
          });
        },
      });
    }
  };

  const renderBtns = (selectType) => {
    if (detail.thirdProductCode === selectType) {
      return (
        <Button className='btn-report' theme='primary' shape='round' size='xs' onClick={() => gotoLeya(selectType)}>
          {detail.checkStatus === 'N' ? '立即领取' : '查看详情'}
        </Button>
      );
    }
    return null;
  };

  const renderCancelBtns = (selectType) => {
    if (productList?.length > 1 && detail.checkStatus === 'N' && detail.thirdProductCode === selectType) {
      return (
        <span className='c-cancel' onClick={() => cancel()}>
          取消兑换
        </span>
      );
    }
    return null;
  };

  const renderAppointStatus = (selectType) => {
    if (detail.appointStatus === 'N') {
      return (
        <span className={'c-status active'}>{productList?.length > 1 ? `${productList.length} 选 1，` : ''}可兑换</span>
      );
    } else if (selectType === detail.thirdProductCode && detail.checkStatus === 'N') {
      return <span className={'c-used'}>已兑换</span>;
    } else {
      return null;
    }
  };

  const handleAppoint = () => {
    Modal.confirm({
      content: `您是否确认兑换：${productList?.find((item) => item.bizKey === selectType)?.bizContent}？`,
      onOk: () => {
        getExchange(selectType)?.then((res) => {
          if (res.code === '0') {
            StaticToast.success('兑换成功');
            gotoLeya(selectType);
          }
        });
      },
    });
  };

  const getExchange = (selected: string) => {
    if (selected === '') {
      StaticToast.error('您还未选择需要兑换的权益');
      return;
    }
    StaticToast.success('正在兑换，请稍后');
    return fetchJson({
      url: '/api/api/v1/patient/leya/grantCoupon',
      type: 'POST',
      data: {
        userRightId: userRightsId,
        thirdProductCode: selected,
      },
    });
  };

  return (
    <div className='leya-teeth-page'>
      {/* <img className='top-img' src={`${CDN_PREFIX}/static/childTeeth/banner.png`} alt='图片' /> */}
      <div className='choice'>
        {productList &&
          productList.map((item) => (
            <div
              key={item.bizKey}
              className={classnames('choice-item', { checked: selectType === item.bizKey })}
              onClick={() => check(item.bizKey)}
            >
              {detail.checkStatus === 'N' && (
                <React.Fragment>
                  <div className='triangle' />
                  <img className='icon-success' src={icon_success} alt='' />
                </React.Fragment>
              )}
              <div className='j-sb name-part'>
                <span className='c-name'>
                  {item.bizContent}
                  {renderCancelBtns(item.bizKey)}
                </span>
                {renderAppointStatus(item.bizKey)}
              </div>
              <div className='j-sb time-part'>
                {
                  <div className='c-time'>
                    {expireDateStr ? '有效期至' : ''}
                    {expireDateStr || ''}
                  </div>
                }
                {renderBtns(item.bizKey)}
              </div>
            </div>
          ))}
      </div>
      <div className='fix-btn'>
        {
          <Button
            block
            theme='primary'
            shape='round'
            disabled={detail.appointStatus !== 'N' || isExpire}
            onClick={handleAppoint}
          >
            {'立即兑换'}
          </Button>
        }
      </div>
    </div>
  );
};
export default LeyaTeethPage;
