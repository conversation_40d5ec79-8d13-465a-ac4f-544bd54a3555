@import "src/style/index";

@mixin zarm_style {
  font-size: r(15);
}

.myaddress_page {
  .edit_cell {
    .za-date-select__input,
    .za-cell__title.za-cell__title--label,
    .za-select__input,
    .za-input {
      @include zarm_style;
      @include placeholder;
    }

    .za-cell__footer {
      color: #464646;
      font-size: r(15);
    }

    &::after {
      display: none;
    }

    .za-cell__title.za-cell__title--label {
      width: r(90);
    }

    &.custom {
      p {
        @include zarm_style;
        @include line(1);

        color: var(--zarm-placeholder);
        height: inherit;
        max-width: r(200);

        &.text {
          color: var(--color-text);
        }
      }
    }
  }

  .address_wrapper {
    .address_item {
      position: relative;
      padding: r(10) r(15);

      .default {
        position: absolute;
        right: 0;
        top: 0;
        width: r(60);
        height: r(25);
        background: var(--theme-success);
        border-radius: 0 0 0 r(10);
        color: #fff;
        font-size: r(12);
        line-height: r(25);
        text-align: center;
      }

      .name {
        color: #464646;
        font-size: r(15);
        font-weight: 600;
        margin-bottom: r(8);
      }

      .address {
        @include display-flex;
        @include align-items(center);

        color: #9b9b9b;
        font-size: r(14);
        padding-bottom: r(10);
        border-bottom: r(1) solid #e6e6e6;

        .icon {
          display: inline-block;
          width: r(10);
          height: r(12.5);
          margin-right: r(8);
        }
      }

      .button_wrapper {
        @include display-flex;
        @include align-items(center);
        @include justify-content(flex-end);

        margin-top: r(11);

        .delete,
        .edit {
          @include display-flex;
          @include align-items(center);
          @include justify-content(center);

          width: r(52.5);
          height: r(21.5);
          border-radius: r(12);
          border: r(1) solid rgb(182, 182, 182);
          font-size: r(12);
          margin-left: r(10);
        }

        .delete {
          color: #9b9b9b;

          .icon {
            width: r(13);
            height: r(13);
            margin-right: r(1);
          }

          &:active {
            background: #9b9b9b;
            color: #fff;
          }
        }

        .edit {
          color: var(--theme-success);
          border-color: var(--theme-success);

          .icon {
            width: r(12);
            height: r(13);
            margin-right: r(2);
          }

          &:active {
            background: var(--theme-success);
            color: #fff;
          }
        }
      }
    }

    .no_address_cover {
      @include display-flex;
      @include align-items(center);

      flex-direction: column;
      padding-top: r(138);

      img {
        display: block;
        width: r(98);
        height: r(98);
      }

      p {
        padding-top: r(27);
        font-size: r(15);
        color: #9b9b9b;
      }
    }
  }
}
