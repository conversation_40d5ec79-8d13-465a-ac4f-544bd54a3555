import React from 'react';
import { CDN_PREFIX } from 'src/utils/staticData';
import { Skeleton } from 'zarm-v3';
import './index.scss';

const SelectFamilyMember = (props) => {
  const { tabList = [], activeTab } = props;
  const gotoSelectFamilyMember = () => {
    window.reactHistory.push({
      pathname: '/hospital/myrights/familymember',
    });
  };
  const renderAvatar = (item) => {
    let avatar = '';
    if (item.patientRelation === 4) {
      if (item.patientGender === 'M') {
        avatar = `${CDN_PREFIX}/static/myrights/<EMAIL>`;
      } else {
        avatar = `${CDN_PREFIX}/static/myrights/<EMAIL>`;
      }
    } else if (item.patientRelation === 3) {
      if (item.patientGender === 'M') {
        avatar = `${CDN_PREFIX}/static/myrights/<EMAIL>`;
      } else {
        avatar = `${CDN_PREFIX}/static/myrights/<EMAIL>`;
      }
    } else if (item.patientRelation === 1 || item.patientRelation === 2) {
      if (item.patientGender === 'M') {
        avatar = `${CDN_PREFIX}/static/myrights/<EMAIL>`;
      } else {
        avatar = `${CDN_PREFIX}/static/myrights/<EMAIL>`;
      }
    } else if (item.patientRelation === 5) {
      avatar = `${CDN_PREFIX}/static/myrights/<EMAIL>`;
    }
    return avatar;
  };
  const onSelectPatient = (item) => {
    props.onChange && props.onChange(item.patientRelation);
  };

  return (
    <div className='select_family_person_comp'>
      <div className='family_container'>
        <div className={tabList.length > 4 ? 'family_list has_scroll' : 'family_list'}>
          {
            (tabList || []).map((item) => {
              const avatar = renderAvatar(item);
              return (
                <div key={item.label} className={activeTab === item.patientRelation ? 'family_item active' : 'family_item'} onClick={() => {
                  onSelectPatient(item);
                }}>
                  <img className='family_avatar' src={avatar} alt='' />
                  <div className='family_label'>{item.label}</div>
                  {
                    item.name &&
                    <div className='family_name'>({item.name})</div>
                  }
                  {
                    activeTab === item.patientRelation && (
                      <React.Fragment>
                        <div className='bottom_line' />
                        {/* <div className='avatar_circle' /> */}
                      </React.Fragment>
                    )
                  }
                </div>
              );
            })
          }
          {
            tabList.length === 0 && (
              <>
                {
                  Array.from({ length: 1 }).map((item, index) => (
                    <div className='family_item' key={index}>
                      <Skeleton animated shape='circle' className='family_avatar' />
                      <Skeleton animated style={{ marginBottom: '0.85rem', marginTop: '0.85rem' }} className='family_label' />
                    </div>
                  ))
                }
              </>
            )
          }
        </div>
        {
          tabList.length > 4 && <div className='family_filter' />
        }
        {
          tabList.length !== 0 && (
            <div className='family_item family_plus' onClick={gotoSelectFamilyMember}>
              <img className='family_avatar' src={`${CDN_PREFIX}static/myrights/<EMAIL>`} alt='' />
              <div className='family_label family_label_normal'>为家人领取</div>
            </div>
          )
        }
      </div>
    </div>
  );
};
export default SelectFamilyMember;
