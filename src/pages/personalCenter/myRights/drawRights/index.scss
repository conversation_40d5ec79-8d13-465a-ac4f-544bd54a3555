@import "src/style/index";
$prefixCls: 'drawRights_page';

.#{$prefixCls} {
  padding-top: r(15);

  &.pages-bgcolor {
    // background: #f5f7fe;
    background: linear-gradient(180deg, rgba(255, 255, 255, 1) 0%, #f5f7fe 100%);
    min-height: 100vh;
  }

  &__guide {
    padding: r(15) r(15) r(13);
    line-height: r(22);
    color: rgba(0, 0, 0, 0.6);
    font-size: r(13);

    h3 {
      font-size: r(20);
      color: rgba(0, 0, 0, 1);
      font-weight: 600;
    }
  }

  &__card {
    &.card_component {
      &:after {
        display: none;
      }
    }

    .headers {
      padding: r(17) r(15);
      @include display-flex;
      @include justify-content(space-between);

      h4 {
        font-size: r(16);
        font-weight: 600;
      }

      .extra {
        color: rgba(0, 0, 0, 0.5);
      }
    }

    .tips {
      padding: 0 r(15);
      font-size: r(12);
      color: rgba(0, 0, 0, 0.3);
      margin-top: r(-8);
    }
  }

  &__cell {
    .za-cell__title {
      color: rgba(0, 0, 0, 0.8);
    }

    input[type=text] {
      @include placeholder;

      &::placeholder {
        font-size: r(13);
      }
    }

    &.custom p,
    .za-select--placeholder .za-select__value {
      font-size: r(13);
      color: #cecece;
    }

    &.custom {
      .text {
        color: var(--color-text);
      }
    }

    &.input {
      .za-cell__title.za-cell__title--label {
        width: r(80);
      }
    }

    &.select {
      .za-cell__title.za-cell__title--label {
        width: r(120);
      }
    }

    &.special {
      .za-cell__title.za-cell__title--label {
        width: r(80);
      }
    }

    &.za-cell {
      &::after {
        display: none;
      }

      & + .za-cell {
        &::after {
          left: 0;
          display: block;
          border-color: #f0f0f0;
        }
      }
    }

    &.required {
      .za-cell__title {
        &::before {
          content: "*";
          color: #f00;
          margin-right: 2px;
          line-height: 1.5;
        }
      }
    }

    &.cell-security {
      padding: r(10) 0;

      .za-cell__title {
        padding-top: 3px;
      }
    }
  }

  &__cell-relation {
    margin-left: r(-6);

    .security-button {
      margin-bottom: r(6);
    }

    .button {
      display: inline-block;
      min-width: r(47);
      margin-right: r(6);
      padding: r(3) r(12);
      background: rgba(236, 145, 49, 0.06);
      color: rgba(236, 145, 49, 1);
      font-size: r(12);
      border-radius: r(26);
      border: 1px solid rgba(236, 145, 49, 0.32);
      transition: background-color 0.2s linear;
      margin-bottom: r(6);
    }

    .disabled {
      opacity: 0.6;
    }

    .not-selected {
      background: rgba(0, 0, 0, 0.04);
      color: rgba(0, 0, 0, 0.8);
      border-color: transparent;
      border: 1px solid rgba(0, 0, 0, 0.1);
    }

    .svg {
      vertical-align: -0.25em;
      margin-right: r(5);
    }

    .gender {
      min-width: r(65);
      margin-bottom: 0;
    }
  }

  /* extraInfo */
  &__card {
    margin-top: 0;
    @include borderRadius($color: #F0F0F0, $radius: r(8));

    padding: 1px;

    &-core {
      position: relative;
      z-index: 2;

      &.za-cell {
        &::after {
          left: 0;
          display: block;
          border-color: #f0f0f0;
        }
      }
    }

    .unit {
      width: r(60);
      font-size: r(12);
    }
  }
}
