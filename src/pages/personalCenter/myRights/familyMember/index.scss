@import 'src/style/index';

.empty-cont {
  padding-top: r(180);
}

.family_member_page {
  .member_tips {
    font-size: 14px;
    color: #999;
    padding: r(20) r(15) r(15) r(15);
  }

  .list_container {
    margin: 0 r(15);
  }

  .patient {
    &__card {
      align-items: center;
      display: flex;
      background: #fff;
      margin-bottom: r(16);
      padding: r(10) r(15);
      border-radius: 8px;
      border: 1px solid #e6e6e6;
      @media (-webkit-min-device-pixel-ratio: 2) {
        border-bottom: 0.5px solid #e6e6e6;
      }

      .btn_get_right {
        font-size: 12px;
        padding: r(2) r(8);
      }
    }

    &__md {
      flex: 1;
    }

    &__avatar {
      width: r(54);
      height: r(54);
      margin-right: r(12);

      img {
        width: 100%;
        height: 100%;
      }
    }

    &__name {
      display: flex;
      margin-bottom: r(6);
      align-items: center;

      span:first-child {
        font-size: r(18);
        font-weight: 600;
        line-height: r(25);
        color: #1e1e1e;
        margin-right: r(8);
      }

      span:last-child {
        border-radius: r(2);
        border: 1px solid #ec9131;
        font-size: r(11);
        font-weight: 400;
        color: #ec9131;
        line-height: r(15);
        padding: r(1) r(4);
      }
    }

    &__diver {
      margin: 0 r(4);
    }

    &__gender-box {
      color: #999;
      font-size: 12px;
    }
  }
}
