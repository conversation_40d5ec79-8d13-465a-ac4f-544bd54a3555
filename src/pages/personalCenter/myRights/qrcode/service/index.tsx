import React, { useCallback, useEffect, useLayoutEffect, useRef, useState } from 'react';
import './index.scss';
import { shallowEqual, useSelector } from 'react-redux';
import { CompatibleFileInput, StaticToast, SvgIcon } from 'src/components/common';
import iconSuccess from 'src/pages/personalCenter/myRights/pupuguan/images/icon-success.png';
import { ApplicationState } from 'src/store';
import { cookies, Deserialize, fetchJson } from 'src/utils';
import bridge from 'src/utils/bridge';
import { STATIC_CDN_PREFIX } from 'src/utils/staticData';
import { session } from 'src/utils/storage';
import { Carousel, Checkbox, Modal } from 'zarm';
import { QrCodeRightsVasCodeListObj } from '../constant';

const imgMap = [
  {
    resCode: 'idCard',
    resName: '身份证正/反面',
  },
  {
    resCode: 'caseIllnessReport',
    resName: '相关门诊及住院病历等材料',
  },
  {
    resCode: 'imageReport',
    resName: '医学影像及影像报告',
  },
  {
    resCode: 'analysisReport',
    resName: '化验单和检查报告',
  },
  {
    resCode: 'inquiryDiagnosisReport',
    resName: '病理报告',
  },
  {
    resCode: 'other',
    resName: '其他诊疗材料（医生可能需要的）',
  },
];

const QRCodeServiceCache = '$$QRCodeServiceCache';

const QRCodeService = ({history, match}) => {

  const { search } = window.location;
  const { params: { vasCode = '' } } = match;

  const { userRightsId, policyNo, itemCode: itemCodeSearch = '', name = '', cardNo } = Deserialize(search);

  const [bizData, setBizData] = useState('');
  const [useName, setUseName] = useState(decodeURIComponent(name));
  const [businessNo, setBusinessNo] = useState('');
  const [attachmentList, setAttachmentList] = useState<any[]>([]);
  const [consultConclusionAttach, setConsultConclusionAttach] = useState<any[]>([]);
  const [modalVisible, setModalVisible] = useState(false);
  const [showImageCarousel, setShowImageCarousel] = useState(false);
  const [agreement, setAgreement] = useState(true);
  const [showMore, setShowMore] = useState(true);
  const [hasEdit, setHasEdit] = useState(!cardNo);
  const [itemCode, setItemCode] = useState(itemCodeSearch);
  const [detail, setDetail] = useState<any>({});
  const [currentImg, setCurrentImg] = useState('');
  const [preWidth, setPreWidth] = useState('');
  const [hasMoreBtn, setHasMoreBtn] = useState(false);
  const textRef = useRef<any>(null);
  const preRef = useRef<any>(null);
  const [replenish, setReplenish] = useState(false);

  const {
    RIGHTSAPPLYATTACHMENTENUM,
    RIGHTSAPPLYCODEENUM_OBJ,
  }: any = useSelector((state: ApplicationState) => {
    const { RIGHTSAPPLYATTACHMENTENUM = [], RIGHTSAPPLYCODEENUM_OBJ = {} } = state.dictionary;

    return {
      RIGHTSAPPLYATTACHMENTENUM,
      RIGHTSAPPLYCODEENUM_OBJ,
    };
  }, shallowEqual);


  const getByType = () => fetchJson({
    url: '/api/api/v1/patient/bizno/getByType',
    type: 'POST',
    isloading: false,
    data: {
      bizNoType: 'MEDICAL_SERVICE_CARD_NO',
      params: {
        sequenceNo: '1',
        productCode: 'channel',
      },
    },
  }).then((res) => {
    if(res.code === '0') {
      setBusinessNo(res.result);
    }
  });

  const getApplyDetail = () => {
    if(!cardNo) {
      return;
    }
    fetchJson({
      url: '/api/api/v1/cancer/cancer/apply/detail?cardNo=' + cardNo,
      type: 'POST',
      isloading: false,
    }).then((res) => {
      if(res.code === '0') {
        const r = res.result || {};
        setAttachmentList(r.applyAttachment);
        setConsultConclusionAttach(r.consultConclusionAttach);
        setReplenish(r.examineStatus === 'ATTACHMENT_REPLENISH');
        setUseName(r.useName);
        setBizData(r.bizData);
        setItemCode(r.itemCode);
        setDetail(r);
        setBusinessNo(cardNo);
      }
    });
  };

  useLayoutEffect(() => {
    const element = textRef.current;
    if(!element || !preRef.current) {
      return;
    }
    setPreWidth(window.getComputedStyle(preRef.current).getPropertyValue('width'));
    const style = window.getComputedStyle(element);
    const lineHeight = parseInt(style.getPropertyValue('line-height'));
    const elementHeight = element.offsetHeight;
    const lineCount = Math.floor(elementHeight / lineHeight);
    setHasMoreBtn(lineCount > 5);
  }, [detail.consultConclusion, preWidth]);


  useEffect(() => {
    const cache = session.get(QRCodeServiceCache) || {};
    const { businessNo } = cache;
    setBusinessNo(businessNo || '');
    setBizData(cache.bizData || '');
    setAttachmentList(cache.attachmentList || []);
    setUseName(cache.useName || useName);
    getApplyDetail();

    if(cardNo || businessNo) {
      return;
    }

    getByType();
  }, []);

  const checkAttachment = () => {
    for (const item of RIGHTSAPPLYATTACHMENTENUM) {
      const attachment = attachmentList.filter((a) => a.attachmentType === item.resCode);
      if(!attachment.length && item.resCode !== 'other') {
        StaticToast.error(`请上传${(imgMap.filter((i) => i.resCode === item.resCode)[0] || {}).resName || item.resName}`);
        return false;
      }
    }
    return true;
  };

  const submit = () => {
    if(!useName) {
      return StaticToast.error('请输入使用人姓名');
    }
    if(!agreement) {
      return StaticToast.error('并阅读和同意《隐私政策》中的相关规定');
    }
    if(!checkAttachment()) {
      return;
    }
    fetchJson({
      url: cardNo ? '/api/api/v1/cancer/cancer/update/apply' : '/api/api/v1/cancer/cancer/submit/apply',
      type: 'POST',
      isloading: false,
      data: {
        cardId: detail.cardId,
        cardNo: businessNo,
        channelResourceCode: cookies.get('channelResourceCode'),
        itemCode,
        policyNo: policyNo || detail.policyNo,
        userRightsId: userRightsId || detail.userRightsId,
        vasCode: vasCode || detail.vasCode,
        attachmentList,
        bizData,
        useName,
      },
    }).then((res) => {
      if(res.code === '0') {
        session.remove(QRCodeServiceCache);
        setModalVisible(true);
      }
    });
  };

  const photoClick = useCallback((item) => {
    if (item.attachmentDownloadUrl.includes('.pdf')) {
      window.location.href = `/hospital/pdfViewer?url=${encodeURIComponent(item.attachmentDownloadUrl)}`;
      return;
    }
    setCurrentImg(item.attachmentDownloadUrl);
    setShowImageCarousel(true);
  }, [attachmentList]);

  const del = (index) => {
    attachmentList.splice(index, 1);
    setAttachmentList([...attachmentList]);
  };

  useEffect(() => {
    bridge.setTitle(QrCodeRightsVasCodeListObj[vasCode] || '众安互联网医院');
  }, []);

  return (
    <div className='QRCodeService'>
      {
        !cardNo && hasEdit &&
        <div className='QRCodeService-head QRCodeService-card'>
          <div className='QRCodeService-head_use'>
            <div className='QRCodeService-head_label'>使用人</div>
            <input maxLength={20} placeholder='请输入' value={useName} disabled={!hasEdit || !!name} onChange={(e) => setUseName(e.target.value)} className='QRCodeService-head_name'></input>
          </div>
          <div className='QRCodeService-head_use'>
            <div className='QRCodeService-head_label'>服务申请项目</div>
            <div className='QRCodeService-head_name'>{RIGHTSAPPLYCODEENUM_OBJ[itemCode]}</div>
          </div>
          <div className='QRCodeService-head_notice'>
            <img className='QRCodeService-head_notice-icon' src={require('../images/notice.png')}>
            </img>
            <p>请上传以下材料，只支持图片格式PNG、JPG、JPEG<br></br><span className='QRCodeService-head_notice-small'>（为了更快完成申请，请确保资料拍摄清晰、完整）</span></p>
          </div>
        </div>
      }

      {
        cardNo &&
        <div className='QRCodeService-user'>
          <div className='QRCodeService-user-item'>
            <span>使用人</span>
            <span>{useName}</span>
          </div>
          <div className='QRCodeService-user-item'>
            <span>服务申请项目</span>
            <span>{RIGHTSAPPLYCODEENUM_OBJ[itemCode]}</span>
          </div>
        </div>
      }

      {
        cardNo &&
        <>
          {
            !!consultConclusionAttach.length &&
            <div className='QRCodeService-consult QRCodeService-report QRCodeService-card focus'>
              <div className='QRCodeService-report_title'>
                <img src={require('../images/report.webp')} alt='' />
                <p>咨询报告</p>
              </div>
              <div className='QRCodeService-consult_attach'>
                {
                  consultConclusionAttach.map((item, index) => (
                    <div className='QRCodeService-data_item' key={index}>
                      <img
                        onClick={() => photoClick(item)}
                        key={item.attachmentDownloadUrl}
                        src={
                          item.attachmentDownloadUrl.includes('.pdf')
                            ? require('../images/pdf-icon.webp')
                            : item.attachmentDownloadUrl + '?x-oss-process=image/auto-orient,1'
                        }
                        className='QRCodeService-data_img' alt='' />
                    </div>
                  ))
                }
              </div>
            </div>
          }
          {
            (detail.examineAdvise || (detail.examineStatus === 'PASS' || detail.examineStatus === 'COMPLETED')) &&
            <div className='QRCodeService-report QRCodeService-card focus'>
              <div className='QRCodeService-report_title'>
                <img src={require('../images/comment.png')} alt='' />
                <p>审核意见</p>
              </div>
              <div className='QRCodeService-report_content'>
                <div className='QRCodeService-report_detail'>
                  {detail.examineAdvise ? detail.examineAdvise : (detail.examineStatus === 'PASS' || detail.examineStatus === 'COMPLETED') ? '通过' : ''}
                </div>
              </div>
            </div>
          }
        </>
      }

      {
        imgMap.map((item, index) => item.resCode === 'idCard' ? (
          <div className='QRCodeService-card QRCodeService-data' key={item.resCode}>
            <div className='QRCodeService-data_title required'>{index+1}.&nbsp;{item.resName}</div>
            <div className='QRCodeService-data_content QRCodeService-data_idCard'>
              {
                !!businessNo && ['idCardFront', 'idCardBack'].map((idCardType) => (
                  <div key={idCardType}>
                    {
                      attachmentList.filter((k) => k.attachmentType === idCardType).map((attachment, index) => (
                        <div className='QRCodeService-data_item idCard' key={attachment.attachmentDownloadUrl}>
                          {
                            hasEdit &&
                            <img
                              className='QRCodeService-data_del'
                              onClick={(e) => {
                                e.stopPropagation();
                                del(attachmentList.findIndex((a) => a.attachmentType === idCardType));
                              }} src={require('../images/del.png')}
                              alt='' />
                          }
                          <img
                            onClick={() => photoClick(attachment)}
                            key={attachment.attachmentDownloadUrl}
                            src={attachment.attachmentDownloadUrl + '?x-oss-process=image/auto-orient,1'}
                            className='QRCodeService-data_img' alt='' />
                        </div>
                      ))
                    }
                    <label className='QRCodeService-data_label' htmlFor={idCardType} key={idCardType}>
                      {
                        hasEdit && !attachmentList.filter((k) => k.attachmentType === idCardType).length &&
                    <SvgIcon className='QRCodeService-data_btn' type='img' src={require(`../images/${idCardType}.png`)} />
                      }
                      <CompatibleFileInput
                        id={idCardType}
                        className='input_image'
                        businessNo={businessNo}
                        maxAttachmentTotal={1}
                        attachmentType={idCardType}
                        onUploadSuccess={(r) => {
                          setAttachmentList((current) => [...current, {
                            bizType: 'applyRightsAttachment',
                            id: r.id,
                            attachmentDownloadUrl: r.attachmentDownloadUrl,
                            attachmentType: idCardType,
                          }]);
                        }} />
                    </label>
                  </div>
                ))
              }
            </div>
          </div>
        ) : (
          <div className='QRCodeService-card QRCodeService-data' key={item.resCode}>
            <div className={`QRCodeService-data_title ${item.resCode !== 'other' && 'required'}`}>{index+1}.&nbsp;{item.resName}</div>
            <div className='QRCodeService-data_content'>
              {
                attachmentList
                  .filter((k) => k.attachmentType === item.resCode)
                  .map((attachment, index) => (
                    <div className='QRCodeService-data_item' key={attachment.attachmentDownloadUrl}>
                      {
                        hasEdit &&
                      <img className='QRCodeService-data_del' onClick={(e) => {
                        e.stopPropagation();
                        del(attachmentList.findIndex((a) => a.attachmentDownloadUrl === attachment.attachmentDownloadUrl));
                      }} src={require('../images/del.png')} alt='' />
                      }
                      <img
                        key={attachment.attachmentType}
                        onClick={(e) => {
                          e.stopPropagation();
                          photoClick(attachment);
                        }}
                        src={attachment.attachmentDownloadUrl + '?x-oss-process=image/auto-orient,1'}
                        className='QRCodeService-data_img' alt='' />
                    </div>
                  ))
              }
              {
                !!businessNo && <label className='QRCodeService-data_label' htmlFor={item.resCode}>
                  {
                    hasEdit && attachmentList.filter((k) => k.attachmentType === item.resCode).length < 9 &&
                    <SvgIcon className='QRCodeService-data_btn' type='img' src={require('../images/upload.png')} />
                  }
                  <CompatibleFileInput id={item.resCode}
                    maxAttachmentTotal={9}
                    className='input_image'
                    hasAttachmentTotal={attachmentList.filter((k) => k.attachmentType === item.resCode).length}
                    businessNo={businessNo}
                    attachmentType={item.resCode}
                    onUploadSuccess={(r) => {
                      setAttachmentList((curr) => [...curr, {
                        bizType: 'applyRightsAttachment',
                        id: r.id,
                        attachmentDownloadUrl: r.attachmentDownloadUrl,
                        attachmentType: item.resCode,
                      }]);
                    }} />
                </label>
              }
              <p className='QRCodeService-data_desc'>最多上传9张</p>
            </div>
          </div>
        ))
      }

      <div className='QRCodeService-card QRCodeService-question'>
        <div className='QRCodeService-question_title'>请输入想咨询的问题</div>
        <div className='QRCodeService-question_content'>
          <textarea
            maxLength={300}
            rows={6}
            value={bizData}
            disabled={!hasEdit}
            placeholder='请输入想咨询的问题供医生针对回复，有多个时使用分号隔开'
            onChange={(e) => {
              setBizData(e.target.value);
            }}></textarea>
          <span>{bizData.length}/300</span>
        </div>
      </div>

      {
        hasEdit &&
        <div className={'QRCodeService__agreement'}>
          <Checkbox
            id='agreement'
            checked={agreement}
            shape='round'
            onChange={(e: any) => {
              setAgreement(!!e.target.checked);
            }}
          />
          <label htmlFor='agreement'>本人承诺申请信息的真实性，并阅读和同意
            <span
              onClick={() => {
                session.set(QRCodeServiceCache, {
                  attachmentList,
                  useName,
                  bizData,
                  businessNo,
                });
                history.push({
                  pathname: '/hospital/private',
                });
              }}
            ><span>《隐私政策》</span></span>
              和
            <span
              onClick={() => {
                session.set(QRCodeServiceCache, {
                  attachmentList,
                  useName,
                  bizData,
                  businessNo,
                });
                history.push({
                  pathname: '/hospital/pdf',
                  search: `url=${encodeURIComponent(STATIC_CDN_PREFIX + 'pdf/hospitalAuth/qrcode_user_auth.pdf')}`,
                });
              }}
            ><span>《个人信息授权书》</span></span>
              中的相关规定</label>
        </div>
      }

      {
        hasEdit &&
        <div className='QRCodeService-footer'>
          {
            cardNo ?
              <div className='QRCodeService-footer_btn' onClick={submit}>提交资料</div> :
              <div onClick={submit} className='QRCodeService-footer_btn'>提交资料</div>
          }
        </div>
      }

      {
        replenish &&
        <div className='QRCodeService-footer'>
          <div className='QRCodeService-footer_btn' onClick={() => {
            setHasEdit(true);
            setReplenish(false);
          }}>更新资料</div>
        </div>
      }

      <Modal
        className='previewimage-component__imagemodal condition_description_modal'
        visible={showImageCarousel}
        maskClosable
        onCancel={() => {
          setShowImageCarousel(false);
        }}
      >
        <div
          onClick={() => {
            setShowImageCarousel(false);
          }}
        >
          <Carousel
            className='image_modal_carousel'
            showPagination={false}
          >
            {[currentImg].map((photo) => (
              <div className='carousel__item__pic' key={photo}>
                <img className='carousel__item__img' src={`${photo}?x-oss-process=image/auto-orient,1`} alt='' draggable={false} />
              </div>
            ))}
          </Carousel>
        </div>
      </Modal>

      <Modal className='QRCodeService-modal' visible={modalVisible}>
        <div className='m-container'>
          <div className='m-icon-box'>
            <img className='m-icon' src={iconSuccess} alt='' />
          </div>
          <div className='m-title'>申请已提交</div>
          <div className='m-tips'>我们会通过企微跟您联系并尽快给出反馈结果，请耐心等待</div>
        </div>
        <div className='m-btn' onClick={() => {
          setModalVisible(false);
          history.go(-1);
        }}>我知道了</div>

      </Modal>
    </div>
  );
};

export default QRCodeService;
