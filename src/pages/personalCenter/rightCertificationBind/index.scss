@import "src/style/index";

.rightCertificationBind {
  position: relative;
  min-height: 100vh;
  width: 100%;
  text-align: center;
  background-color: #fff;

  &-title {
    padding-top: r(50);

    img {
      width: r(177/2);
    }

    p {
      font-size: r(17);
      font-family: AlibabaPuHuiTiM;
      color: #666;
      margin-top: r(14);
    }
  }

  &-form {
    padding: 0 r(20);
    margin-top: r(42);

    &__item {
      margin-top: r(12);
      background: #fbfbfb;
      border-radius: 8px;
      padding: r(12) r(14);
      display: flex;
      justify-content: space-between;
      align-items: center;

      label {
        width: 6em;
        text-align: left;
        font-size: r(15);
        font-family: PingFangSC, PingFang SC;
        font-weight: 500;
        color: #666;
      }

      input {
        flex: 1;
        border: none;
        font-size: r(15);
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        color: #666;
        background-color: transparent;
        padding-left: r(20);

        &::placeholder {
          color: #bcbcbc;
        }
      }
    }

    &__desc {
      font-size: r(12);
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      color: #efb179;
      margin-top: r(15);
    }

    &__button {
      margin-top: r(20);
      padding: r(10) 0;
      font-size: r(16);
    }
  }
}
