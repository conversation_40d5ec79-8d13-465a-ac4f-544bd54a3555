import React, { useRef, useState, useEffect, useCallback } from 'react';
import { StaticToast } from 'src/components/common';
import { fetchJson } from 'src/utils/fetch';
import validate from 'src/utils/validate';
import './index.scss';
import { Button, Input, Modal } from 'zarm';
import { Deserialize } from 'src/utils';

const val = (data) => {
  const { name = '', certNo = '' } = data;
  if (name.trim() === '') {
    StaticToast.warning('您还未输入姓名');
    return false;
  } else if (!validate.isUsername(name)) {
    StaticToast.warning('您输入的姓名格式错误');
    return false;
  } else if (certNo.trim() === '') {
    StaticToast.warning('您还未输入身份证号码');
    return false;
  } else if (!validate.isIdCard(certNo)) {
    StaticToast.warning('您输入的身份证号码格式错误');
    return false;
  }
  return true;
};

const Certification = (props) => {
  const { location: { search = '' } } = props;
  const { rightsId = '', userRightsId = '', returnUrl = '' } = Deserialize(search);
  const [form, setForm] = useState({
    name: '',
    certNo: '',
  });

  const formRef = useRef({
    name: '',
    certNo: '',
  });

  useEffect(() => {
    formRef.current = form;
  });

  const submit = useCallback(() => {
    if (val(formRef.current)) {
      Modal.confirm({
        title: '再次确认',
        content: '最终权益使用人姓名和身份证号需和填写信息一致，否则无法使用。请确认无误',
        onOk: () => new Promise((resolve, reject) => {
          const { name, certNo } = formRef.current;
          fetchJson({
            url: '/api/api/v1/patient/user/rights/realname',
            type: 'POST',
            data: {
              name,
              certNo,
              rightsId,
              userRightsId,
            },
            isloading: true,
            success: (res) => {
              if (res && res.code === '0') {
                resolve(true);
                if(returnUrl) {
                  window.location.replace(decodeURIComponent(returnUrl));
                  return;
                }
                props.history.replace({
                  pathname: '/hospital/myrights',
                });
              } else {
                resolve(false);
                const { message = '绑定失败' } = res;
                StaticToast.warning(message);
              }
            },
            error: () => {
            },
          });
        }),
      });
    }
  }, [formRef]);

  return (
    <div className='rightCertificationBind'>
      <div className='rightCertificationBind-title'>
        <img src={require('./images/logo.png')}></img>
        <p>该权益需绑定使用人身份信息</p>
      </div>
      <div className='rightCertificationBind-form'>
        <div className='rightCertificationBind-form__item'>
          <label htmlFor=''>姓名</label>
          <Input placeholder='权益使用人姓名' value={form.name} maxLength={10} onChange={(value) => setForm((prevForm) => ({
            ...prevForm,
            name: value.trim(),
          }))}></Input>
        </div>
        <div className='rightCertificationBind-form__item'>
          <label htmlFor=''>身份证号</label>
          <Input placeholder='权益使用人身份证号' value={form.certNo} maxLength={18} onChange={(value) => setForm((prevForm) => ({
            ...prevForm,
            certNo: value.trim(),
          }))}></Input>
        </div>
        <div className='rightCertificationBind-form__desc'>
          该权益仅限本人使用，登记后无法更改，请确认填写无误
        </div>
        <Button shape='round' block className='rightCertificationBind-form__button' theme='primary' size='lg' onClick={submit}>确认无误，提交</Button>
      </div>
    </div>
  );
};

export default Certification;
