import React, { useEffect, useRef, useState } from 'react';
import { fetchJson, validate } from 'src/utils';
import { Button } from 'zarm-v3';
import CloseIcon from './images/close-icon.webp';
import PersonIcon from './images/person-icon.webp';
import './index.scss';
import { StaticToast } from 'src/components/common';

const AccountInfo = [
  '实名认证信息',
  '权益信息',
  '订单信息',
  '服务使用记录',
  '兑换码',
  '优惠券',
];
const DeleteAccount = (props) => {
  const { history } = props;
  const [showModal, setShowModal] = useState(false);
  const deleteAccountRef = useRef<any>(null);
  const [isLoading, toggleIsLoading] = useState(false);
  const [accountPhone, setAccountPhone] = useState();

  const getAccountInfo = () => {
    fetchJson({
      type: 'GET',
      url: '/api/api/v1/patient/user/getAccountInfo?needSensitive=true',
    }).then((res) => {
      if (res.code === '0') {
        setAccountPhone(res.result.accountPhone);
      }
    });
  };

  const handleDeleteAccount = (action) => {
    switch (action.key) {
    case 'delete':
      toggleIsLoading(true);
      fetchJson({
        type: 'POST',
        url: '/api/api/v1/patient/user/withdraw',
      }).then((res) => {
        if (res.code === '0') {
          StaticToast.success('操作成功');
          setShowModal(false);

          setTimeout(() => {
            if (validate.isFromMiniApplet()) {
              return wx.miniProgram.reLaunch({ url: '/pages/index?channelResourceCode=HYXCX' });
            }
            history.push('/hospital/home');
          }, 1000);
        }
      }).finally(() => toggleIsLoading(false));
    case 'cancel':
    default:
      setShowModal(false);
    }
  };

  useEffect(() => {
    getAccountInfo();
  }, []);

  return (
    <div className='delete-account' ref={(e) => deleteAccountRef.current = e}>
      <img className='delete-account__icon' src={PersonIcon} alt='' />
      <div className='delete-account__title'>将{accountPhone}所绑定的账号注销</div>
      <div className='delete-account__attention'>
        <div className='delete-account__attention-title'>请注意，注销账号将清空以下信息且无法找回</div>
        <div className='delete-account__list'>
          {
            AccountInfo.map((item, index) => <div key={index} className='delete-account__list-item'>{item}</div>)
          }
        </div>
      </div>
      <div className='delete-account__question'>
        <div className='delete-account__question-title'>常见问题Q&A</div>
        <div className='delete-account__question-content'>若您有业务相关的疑问，请到<b>个人中心-在线客服</b>,直接联系人工客服进行处理，无需注销账号</div>
      </div>
      <div className='delete-account__btn'>
        <Button className='delete-account__btn-cancel' theme='primary' block shape='round' onClick={() => history.go(-1)}>我再想想</Button>
        <Button block onClick={() => setShowModal(true)} shape='round'>仍然注销</Button>
      </div>

      {
        showModal && (
          <div className='delete-account__modal'>
            <div className='delete-account__modal-mask' onClick={() => setShowModal(false)}></div>
            <div className='delete-account__modal-content'>
              <div className='delete-account__modal-title'>是否确认注销账号，注销后所有信息都将清空且不可恢复</div>
              <div className='delete-account__modal-btns'>
                <Button className='action-btn confirm-btn' shape='round' loading={isLoading} onClick={() => handleDeleteAccount({ key: 'delete' })}>确认注销</Button>
                <Button className='action-btn' shape='round' theme='primary' onClick={() => handleDeleteAccount({ key: 'cancel' })}>暂不注销</Button>
              </div>
              <img src={CloseIcon} className='delete-account__modal-close' alt='关闭' onClick={() => setShowModal(false)}></img>
            </div>
          </div>
        )
      }
    </div>
  );
};

export default DeleteAccount;
